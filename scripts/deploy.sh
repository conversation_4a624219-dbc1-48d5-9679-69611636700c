#!/bin/bash

# IT资产管理系统部署脚本
# 使用方法: ./scripts/deploy.sh [start|stop|restart|logs|status]

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    log_info "依赖检查通过"
}

# 启动服务
start_services() {
    log_info "启动 IT资产管理系统..."
    
    cd "$PROJECT_ROOT"
    
    # 创建必要的目录
    mkdir -p logs
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功！"
        log_info "前端访问地址: http://localhost:3000"
        log_info "后端API地址: http://localhost:8000"
        log_info "API文档地址: http://localhost:8000/docs"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止 IT资产管理系统..."
    
    cd "$PROJECT_ROOT"
    docker-compose down
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 IT资产管理系统..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    cd "$PROJECT_ROOT"
    docker-compose logs -f
}

# 查看状态
show_status() {
    cd "$PROJECT_ROOT"
    
    log_info "服务状态:"
    docker-compose ps
    
    echo
    log_info "系统资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 更新服务
update_services() {
    log_info "更新 IT资产管理系统..."
    
    cd "$PROJECT_ROOT"
    
    # 停止服务
    docker-compose down
    
    # 拉取最新镜像
    docker-compose pull
    
    # 重新构建
    docker-compose build --no-cache
    
    # 启动服务
    docker-compose up -d
    
    log_success "更新完成"
}

# 清理数据
clean_data() {
    log_warning "这将删除所有数据，包括数据库数据！"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd "$PROJECT_ROOT"
        docker-compose down -v
        docker system prune -f
        log_success "数据清理完成"
    else
        log_info "操作已取消"
    fi
}

# 备份数据
backup_data() {
    log_info "备份数据库数据..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups"
    mkdir -p "$BACKUP_DIR"
    
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    cd "$PROJECT_ROOT"
    docker-compose exec -T postgres pg_dump -U postgres rs_asset > "$BACKUP_FILE"
    
    log_success "数据备份完成: $BACKUP_FILE"
}

# 显示帮助信息
show_help() {
    echo "IT资产管理系统部署脚本"
    echo
    echo "使用方法:"
    echo "  $0 [命令]"
    echo
    echo "可用命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  logs      查看日志"
    echo "  status    查看状态"
    echo "  update    更新服务"
    echo "  backup    备份数据"
    echo "  clean     清理数据"
    echo "  help      显示帮助信息"
    echo
}

# 主函数
main() {
    case "${1:-}" in
        start)
            check_dependencies
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            check_dependencies
            restart_services
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        update)
            check_dependencies
            update_services
            ;;
        backup)
            backup_data
            ;;
        clean)
            clean_data
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            log_error "请指定命令"
            show_help
            exit 1
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"