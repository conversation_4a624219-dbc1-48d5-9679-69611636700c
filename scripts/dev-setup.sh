#!/bin/bash

# IT资产管理系统开发环境配置脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js版本
check_nodejs() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    log_success "Node.js 版本检查通过: $(node -v)"
}

# 检查Python版本
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python 3.10+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 10 ]); then
        log_error "Python 版本过低，需要 3.10+，当前版本: $PYTHON_VERSION"
        exit 1
    fi
    
    log_success "Python 版本检查通过: $PYTHON_VERSION"
}

# 安装前端依赖
setup_frontend() {
    log_info "配置前端开发环境..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_error "package.json 不存在"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install
    
    log_success "前端依赖安装完成"
}

# 安装后端依赖
setup_backend() {
    log_info "配置后端开发环境..."
    
    cd "$PROJECT_ROOT/backend"
    
    # 检查requirements.txt是否存在
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt 不存在"
        exit 1
    fi
    
    # 创建虚拟环境（可选）
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境并安装依赖
    log_info "安装后端依赖..."
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    
    log_success "后端依赖安装完成"
}

# 创建环境配置文件
setup_env_files() {
    log_info "创建环境配置文件..."
    
    # 后端环境配置
    if [ ! -f "$PROJECT_ROOT/backend/.env" ]; then
        cat > "$PROJECT_ROOT/backend/.env" << EOF
# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/rs_asset

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 开发模式
DEBUG=true
ENVIRONMENT=development

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,https://localhost:3000
EOF
        log_success "创建后端环境配置文件: backend/.env"
    else
        log_info "后端环境配置文件已存在"
    fi
    
    # 前端环境配置
    if [ ! -f "$PROJECT_ROOT/frontend/.env.local" ]; then
        cat > "$PROJECT_ROOT/frontend/.env.local" << EOF
# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=IT资产管理系统
NEXT_PUBLIC_APP_VERSION=1.0.0

# 开发模式
NODE_ENV=development
EOF
        log_success "创建前端环境配置文件: frontend/.env.local"
    else
        log_info "前端环境配置文件已存在"
    fi
}

# 创建开发脚本
create_dev_scripts() {
    log_info "创建开发脚本..."
    
    # 前端开发脚本
    cat > "$PROJECT_ROOT/scripts/dev-frontend.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")/../frontend"
echo "启动前端开发服务器..."
npm run dev
EOF
    
    # 后端开发脚本
    cat > "$PROJECT_ROOT/scripts/dev-backend.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")/../backend"
echo "启动后端开发服务器..."
if [ -d "venv" ]; then
    source venv/bin/activate
fi
uvicorn main:app --reload --host 0.0.0.0 --port 8000
EOF
    
    # 数据库开发脚本
    cat > "$PROJECT_ROOT/scripts/dev-database.sh" << 'EOF'
#!/bin/bash
echo "启动开发数据库..."
docker run --name rs-asset-postgres-dev \
  -e POSTGRES_DB=rs_asset \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15

docker run --name rs-asset-redis-dev \
  -p 6379:6379 \
  -d redis:7-alpine

echo "数据库服务已启动"
echo "PostgreSQL: localhost:5432"
echo "Redis: localhost:6379"
EOF
    
    # 添加执行权限
    chmod +x "$PROJECT_ROOT/scripts/dev-frontend.sh"
    chmod +x "$PROJECT_ROOT/scripts/dev-backend.sh"
    chmod +x "$PROJECT_ROOT/scripts/dev-database.sh"
    
    log_success "开发脚本创建完成"
}

# 显示开发指南
show_dev_guide() {
    echo
    log_success "开发环境配置完成！"
    echo
    echo "开发指南:"
    echo "1. 启动数据库服务:"
    echo "   ./scripts/dev-database.sh"
    echo
    echo "2. 启动后端服务:"
    echo "   ./scripts/dev-backend.sh"
    echo
    echo "3. 启动前端服务:"
    echo "   ./scripts/dev-frontend.sh"
    echo
    echo "访问地址:"
    echo "- 前端: http://localhost:3000"
    echo "- 后端API: http://localhost:8000"
    echo "- API文档: http://localhost:8000/docs"
    echo
    echo "配置文件:"
    echo "- 后端配置: backend/.env"
    echo "- 前端配置: frontend/.env.local"
    echo
}

# 主函数
main() {
    log_info "开始配置IT资产管理系统开发环境..."
    
    check_nodejs
    check_python
    setup_frontend
    setup_backend
    setup_env_files
    create_dev_scripts
    show_dev_guide
}

main "$@"