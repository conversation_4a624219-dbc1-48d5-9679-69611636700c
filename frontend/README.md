# RS Asset Frontend

A modern dashboard interface built with Next.js and Chakra UI, inspired by <PERSON><PERSON><PERSON>'s design.

## Features

- 🎨 Modern and clean UI design
- 📱 Fully responsive layout
- 🎯 Dashboard with status cards and tables
- 🔍 Search functionality
- 🔔 Notification system
- 👤 User profile management

## Tech Stack

- Next.js 14
- TypeScript
- Chakra UI
- Phosphor Icons

## Getting Started

1. Install dependencies:
```bash
npm install
# or
yarn install
```

2. Run the development server:
```bash
npm run dev
# or
yarn dev
```

3. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

```
frontend/
├── public/          # Static files
├── src/
│   ├── app/        # Next.js app directory
│   ├── components/ # React components
│   └── theme.ts    # Chakra UI theme configuration
├── package.json
└── tsconfig.json
```

## Development

- `npm run dev` - Start development server
- `npm run build` - Build production bundle
- `npm run start` - Start production server
- `npm run lint` - Run ESLint 