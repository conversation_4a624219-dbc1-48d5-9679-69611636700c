import { query } from '@/lib/db'
import { SnmpManager } from 'net-snmp'

interface SNMPDeviceConfig {
  host: string
  community: string
  oids: string[]
  interval: number
}

export class SNMPService {
  private manager: SnmpManager
  
  constructor() {
    this.manager = new SnmpManager()
  }

  // 添加监控设备
  async addDevice(device: SNMPDeviceConfig) {
    await query(
      `INSERT INTO monitored_devices 
      (host, community, oids, interval, type, created_at) 
      VALUES ($1, $2, $3, $4, 'snmp', NOW())`,
      [device.host, device.community, device.oids, device.interval]
    )
    
    // 启动轮询
    this.startPolling(device)
  }

  // 启动轮询
  private startPolling(device: SNMPDeviceConfig) {
    const session = this.manager.createSession(device.host, device.community)
    
    setInterval(async () => {
      try {
        const results = await this.getValues(session, device.oids)
        
        // 存储采集数据
        await query(
          `INSERT INTO snmp_data 
          (host, oid, value, timestamp) 
          VALUES ${device.oids.map((_, i) => `($1, $${i*3+2}, $${i*3+3}, NOW())`).join(',')}`,
          [device.host, ...device.oids.flatMap(oid => [oid, results[oid]])]
        )
      } catch (err) {
        console.error(`SNMP采集失败: ${device.host}`, err)
      }
    }, device.interval * 1000)
  }

  // 获取SNMP值
  private async getValues(session: any, oids: string[]) {
    return new Promise((resolve, reject) => {
      session.get(oids, (err: any, results: any) => {
        if (err) return reject(err)
        resolve(results)
      })
    })
  }

  // 获取设备列表
  async getDevices() {
    return query(
      `SELECT * FROM monitored_devices WHERE type = 'snmp'`
    )
  }
}

export const snmpService = new SNMPService()