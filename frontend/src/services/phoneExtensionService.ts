import axios from 'axios';

const BASE_URL = '/api/phone-extensions';

export interface PhoneExtension {
  id: string;
  extension_number: string;
  name: string;
  department: string;
  type: 'internal' | 'external';
  status: 'active' | 'inactive';
  notes?: string;
}

export interface CreatePhoneExtensionDTO {
  extension_number: string;
  name: string;
  department: string;
  type: 'internal' | 'external';
  status: 'active' | 'inactive';
  notes?: string;
}

export interface UpdatePhoneExtensionDTO extends CreatePhoneExtensionDTO {}

export const getAllExtensions = async (): Promise<PhoneExtension[]> => {
  const response = await axios.get(BASE_URL);
  return response.data;
};

export const getExtension = async (id: string): Promise<PhoneExtension> => {
  const response = await axios.get(`${BASE_URL}/${id}`);
  return response.data;
};

export const createExtension = async (data: CreatePhoneExtensionDTO): Promise<PhoneExtension> => {
  const response = await axios.post(BASE_URL, data);
  return response.data;
};

export const updateExtension = async (id: string, data: UpdatePhoneExtensionDTO): Promise<PhoneExtension> => {
  const response = await axios.put(`${BASE_URL}/${id}`, data);
  return response.data;
};

export const deleteExtension = async (id: string): Promise<void> => {
  await axios.delete(`${BASE_URL}/${id}`);
};

export const searchExtensions = async (query: string): Promise<PhoneExtension[]> => {
  const response = await axios.get(`${BASE_URL}/search`, { params: { query } });
  return response.data;
};

export const filterExtensionsByType = async (type: string): Promise<PhoneExtension[]> => {
  const response = await axios.get(`${BASE_URL}/filter`, { params: { type } });
  return response.data;
};

export const exportExtensions = async (): Promise<void> => {
  const response = await axios.get(`${BASE_URL}/export`, { responseType: 'blob' });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'phone-extensions.xlsx');
  document.body.appendChild(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
}; 