import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://***********:8003';

// SNMP监控项类型
export interface SNMPItem {
  id: number;
  device_id: string;
  name: string;
  key: string;
  oid: string;
  unit: string;
  item_type: string;
  location: string;
  position?: string;
  description?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// 获取所有SNMP监控项
export const getSNMPItems = async (): Promise<SNMPItem[]> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/snmp-items`);
    return response.data;
  } catch (error) {
    console.error('获取SNMP监控项失败:', error);
    return [];
  }
};

// 获取单个SNMP监控项
export const getSNMPItem = async (id: number): Promise<SNMPItem | null> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/snmp-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`获取SNMP监控项 ${id} 失败:`, error);
    return null;
  }
};

// 创建SNMP监控项
export const createSNMPItem = async (item: Omit<SNMPItem, 'id' | 'created_at' | 'updated_at'>): Promise<SNMPItem | null> => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/snmp-items`, item);
    return response.data;
  } catch (error) {
    console.error('创建SNMP监控项失败:', error);
    return null;
  }
};

// 更新SNMP监控项
export const updateSNMPItem = async (id: number, item: Partial<SNMPItem>): Promise<SNMPItem | null> => {
  try {
    const response = await axios.put(`${API_BASE_URL}/api/snmp-items/${id}`, item);
    return response.data;
  } catch (error) {
    console.error(`更新SNMP监控项 ${id} 失败:`, error);
    return null;
  }
};

// 删除SNMP监控项
export const deleteSNMPItem = async (id: number): Promise<boolean> => {
  try {
    await axios.delete(`${API_BASE_URL}/api/snmp-items/${id}`);
    return true;
  } catch (error) {
    console.error(`删除SNMP监控项 ${id} 失败:`, error);
    return false;
  }
};
