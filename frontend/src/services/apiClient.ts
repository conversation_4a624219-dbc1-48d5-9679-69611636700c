import axios from 'axios';

// 设置API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8003';

console.log('API基础URL:', API_BASE_URL);

// 创建axios实例
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30秒超时
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加调试信息
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`, config.params || config.data || {});
    return config;
  },
  (error) => {
    console.error('API请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API响应: ${response.config.method?.toUpperCase()} ${response.config.url} - 状态码: ${response.status}`);
    return response;
  },
  (error) => {
    // 处理错误响应
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('API错误:', error.response.status, error.response.data);
      console.error('请求URL:', error.config.baseURL + error.config.url);
      console.error('请求方法:', error.config.method?.toUpperCase());
      console.error('请求数据:', error.config.data);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('API请求无响应:', error.request);
      console.error('请求URL:', error.config?.baseURL + error.config?.url);
      console.error('请求方法:', error.config?.method?.toUpperCase());
    } else {
      // 请求配置出错
      console.error('API请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);
