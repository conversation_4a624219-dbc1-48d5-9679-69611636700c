import axios from 'axios';

const API_BASE_URL = '/api/network-config';

export interface NetworkDevice {
  id: string;
  name: string;
  ip: string;
  type: string;
  model: string;
  status: 'online' | 'offline' | 'warning';
  username?: string;
  password?: string;
  ssh_key?: string;
  use_ssh_key: boolean;
  port: number;
  last_backup?: string;
  created_at: string;
  updated_at: string;
}

export interface ConfigBackup {
  id: string;
  device: string;
  device_name: string;
  timestamp: string;
  filename: string;
  file: string;
  size: string;
  status: 'success' | 'failed';
  error_message?: string;
  is_auto: boolean;
}

export interface BackupSchedule {
  id: string;
  devices: string[];
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
  time: string;
  retention_days: number;
  only_changed: boolean;
  notify_on_failure: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 模拟数据
const mockDevices: NetworkDevice[] = [
  { 
    id: '1', 
    name: 'Core-Switch-01', 
    ip: '***********', 
    type: 'huawei', 
    model: 'S6720-30C-EI-24S-AC', 
    status: 'online', 
    use_ssh_key: false, 
    port: 22,
    last_backup: '2023-05-15T14:30:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-05-15T14:30:00Z'
  },
  { 
    id: '2', 
    name: 'Core-Switch-02', 
    ip: '***********', 
    type: 'huawei', 
    model: 'S6720-30C-EI-24S-AC', 
    status: 'online', 
    use_ssh_key: false, 
    port: 22,
    last_backup: '2023-05-15T14:35:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-05-15T14:35:00Z'
  },
  { 
    id: '3', 
    name: 'Access-Switch-01', 
    ip: '***********0', 
    type: 'huawei', 
    model: 'S5700-28C-EI', 
    status: 'online', 
    use_ssh_key: false, 
    port: 22,
    last_backup: '2023-05-14T10:15:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-05-14T10:15:00Z'
  },
  { 
    id: '4', 
    name: 'Access-Switch-02', 
    ip: '***********1', 
    type: 'huawei', 
    model: 'S5700-28C-EI', 
    status: 'warning', 
    use_ssh_key: false, 
    port: 22,
    last_backup: '2023-05-10T09:45:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-05-10T09:45:00Z'
  },
  { 
    id: '5', 
    name: 'Router-01', 
    ip: '***********54', 
    type: 'huawei', 
    model: 'AR2220E', 
    status: 'online', 
    use_ssh_key: false, 
    port: 22,
    last_backup: '2023-05-15T15:00:00Z',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-05-15T15:00:00Z'
  },
  { 
    id: '6', 
    name: 'Router-02', 
    ip: '*************', 
    type: 'huawei', 
    model: 'AR2220E', 
    status: 'offline', 
    use_ssh_key: false, 
    port: 22,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-05-01T00:00:00Z'
  },
];

const mockBackups: ConfigBackup[] = [
  { 
    id: '1', 
    device: '1', 
    device_name: 'Core-Switch-01', 
    timestamp: '2023-05-15T14:30:00Z', 
    filename: 'Core-Switch-01_20230515_143000.cfg', 
    file: '/media/config_backups/1/Core-Switch-01_20230515_143000.cfg', 
    size: '45 KB', 
    status: 'success', 
    is_auto: false 
  },
  { 
    id: '2', 
    device: '2', 
    device_name: 'Core-Switch-02', 
    timestamp: '2023-05-15T14:35:00Z', 
    filename: 'Core-Switch-02_20230515_143500.cfg', 
    file: '/media/config_backups/2/Core-Switch-02_20230515_143500.cfg', 
    size: '44 KB', 
    status: 'success', 
    is_auto: false 
  },
  { 
    id: '3', 
    device: '3', 
    device_name: 'Access-Switch-01', 
    timestamp: '2023-05-14T10:15:00Z', 
    filename: 'Access-Switch-01_20230514_101500.cfg', 
    file: '/media/config_backups/3/Access-Switch-01_20230514_101500.cfg', 
    size: '32 KB', 
    status: 'success', 
    is_auto: true 
  },
  { 
    id: '4', 
    device: '4', 
    device_name: 'Access-Switch-02', 
    timestamp: '2023-05-10T09:45:00Z', 
    filename: 'Access-Switch-02_20230510_094500.cfg', 
    file: '/media/config_backups/4/Access-Switch-02_20230510_094500.cfg', 
    size: '31 KB', 
    status: 'failed', 
    error_message: '连接超时',
    is_auto: true 
  },
  { 
    id: '5', 
    device: '5', 
    device_name: 'Router-01', 
    timestamp: '2023-05-15T15:00:00Z', 
    filename: 'Router-01_20230515_150000.cfg', 
    file: '/media/config_backups/5/Router-01_20230515_150000.cfg', 
    size: '28 KB', 
    status: 'success', 
    is_auto: false 
  },
];

export const networkConfigService = {
  // 设备相关API
  async getDevices(): Promise<NetworkDevice[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/devices/`);
      return response.data;
    } catch (error) {
      console.error('获取设备列表失败:', error);
      return mockDevices;
    }
  },

  async getDevice(id: string): Promise<NetworkDevice> {
    try {
      const response = await axios.get(`${API_BASE_URL}/devices/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`获取设备 ${id} 失败:`, error);
      const device = mockDevices.find(d => d.id === id);
      if (!device) throw new Error(`设备 ${id} 不存在`);
      return device;
    }
  },

  async createDevice(device: Omit<NetworkDevice, 'id' | 'created_at' | 'updated_at'>): Promise<NetworkDevice> {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices/`, device);
      return response.data;
    } catch (error) {
      console.error('创建设备失败:', error);
      throw error;
    }
  },

  async updateDevice(id: string, device: Partial<NetworkDevice>): Promise<NetworkDevice> {
    try {
      const response = await axios.patch(`${API_BASE_URL}/devices/${id}/`, device);
      return response.data;
    } catch (error) {
      console.error(`更新设备 ${id} 失败:`, error);
      throw error;
    }
  },

  async deleteDevice(id: string): Promise<void> {
    try {
      await axios.delete(`${API_BASE_URL}/devices/${id}/`);
    } catch (error) {
      console.error(`删除设备 ${id} 失败:`, error);
      throw error;
    }
  },

  async backupDevice(id: string): Promise<ConfigBackup> {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices/${id}/backup/`);
      return response.data;
    } catch (error) {
      console.error(`备份设备 ${id} 失败:`, error);
      throw error;
    }
  },

  async backupMultipleDevices(deviceIds: string[]): Promise<{
    success_count: number;
    failed_count: number;
    backups: ConfigBackup[];
  }> {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices/backup_multiple/`, {
        device_ids: deviceIds
      });
      return response.data;
    } catch (error) {
      console.error('批量备份设备失败:', error);
      throw error;
    }
  },

  async checkDeviceStatus(id: string): Promise<{ status: string }> {
    try {
      const response = await axios.get(`${API_BASE_URL}/devices/${id}/check_status/`);
      return response.data;
    } catch (error) {
      console.error(`检查设备 ${id} 状态失败:`, error);
      const device = mockDevices.find(d => d.id === id);
      return { status: device?.status || 'unknown' };
    }
  },

  // 备份相关API
  async getBackups(params?: {
    device_id?: string;
    status?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ConfigBackup[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/backups/`, { params });
      return response.data;
    } catch (error) {
      console.error('获取备份列表失败:', error);
      if (params?.device_id) {
        return mockBackups.filter(b => b.device === params.device_id);
      }
      return mockBackups;
    }
  },

  async getBackup(id: string): Promise<ConfigBackup> {
    try {
      const response = await axios.get(`${API_BASE_URL}/backups/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`获取备份 ${id} 失败:`, error);
      const backup = mockBackups.find(b => b.id === id);
      if (!backup) throw new Error(`备份 ${id} 不存在`);
      return backup;
    }
  },

  async downloadBackup(id: string): Promise<Blob> {
    try {
      const response = await axios.get(`${API_BASE_URL}/backups/${id}/download/`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error(`下载备份 ${id} 失败:`, error);
      throw error;
    }
  },

  async viewBackupContent(id: string): Promise<{ content: string }> {
    try {
      const response = await axios.get(`${API_BASE_URL}/backups/${id}/view/`);
      return response.data;
    } catch (error) {
      console.error(`查看备份 ${id} 内容失败:`, error);
      throw error;
    }
  },

  async compareBackups(backup1Id: string, backup2Id: string): Promise<{ diff: string }> {
    try {
      const response = await axios.post(`${API_BASE_URL}/backups/compare/`, {
        backup1_id: backup1Id,
        backup2_id: backup2Id
      });
      return response.data;
    } catch (error) {
      console.error(`比较备份 ${backup1Id} 和 ${backup2Id} 失败:`, error);
      throw error;
    }
  },

  // 备份计划相关API
  async getSchedules(): Promise<BackupSchedule[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/schedules/`);
      return response.data;
    } catch (error) {
      console.error('获取备份计划列表失败:', error);
      return [];
    }
  },

  async getSchedule(id: string): Promise<BackupSchedule> {
    try {
      const response = await axios.get(`${API_BASE_URL}/schedules/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`获取备份计划 ${id} 失败:`, error);
      throw error;
    }
  },

  async createSchedule(schedule: Omit<BackupSchedule, 'id' | 'created_at' | 'updated_at'>): Promise<BackupSchedule> {
    try {
      const response = await axios.post(`${API_BASE_URL}/schedules/`, schedule);
      return response.data;
    } catch (error) {
      console.error('创建备份计划失败:', error);
      throw error;
    }
  },

  async updateSchedule(id: string, schedule: Partial<BackupSchedule>): Promise<BackupSchedule> {
    try {
      const response = await axios.patch(`${API_BASE_URL}/schedules/${id}/`, schedule);
      return response.data;
    } catch (error) {
      console.error(`更新备份计划 ${id} 失败:`, error);
      throw error;
    }
  },

  async deleteSchedule(id: string): Promise<void> {
    try {
      await axios.delete(`${API_BASE_URL}/schedules/${id}/`);
    } catch (error) {
      console.error(`删除备份计划 ${id} 失败:`, error);
      throw error;
    }
  }
};

export default networkConfigService;
