import { apiClient } from './apiClient';

// 环境监控数据类型
export interface EnvironmentData {
  id: number;
  device_id: string;
  location: string;
  floor: string;
  temperature: number;
  humidity: number;
  smoke: boolean;
  water: boolean;
  status: string;
  timestamp: string;

  // 冷通道温度数据
  cold_aisle1_temp1?: number;
  cold_aisle1_temp2?: number;
  cold_aisle2_temp1?: number;
  cold_aisle2_temp2?: number;

  // 冷通道湿度数据
  cold_aisle1_humidity1?: number;
  cold_aisle1_humidity2?: number;
  cold_aisle2_humidity1?: number;
  cold_aisle2_humidity2?: number;
}

// UPS监控数据类型
export interface UPSData {
  id: number;
  device_id: string;
  name: string;
  location: string;
  status: string;
  load: number;
  battery_level: number;
  battery_time_remaining: number;
  input_voltage: number;
  output_voltage: number;
  input_frequency: number;
  output_frequency: number;
  temperature: number;
  battery_voltage: number;
  timestamp: string;
}

// 市电监控数据类型
export interface MainsPowerData {
  id: number;
  device_id: string;
  location: string;
  status: string;
  voltage: number;
  frequency: number;
  current: number;
  power: number;
  power_factor: number;
  energy_consumption: number;
  timestamp: string;
}

// 监控设备配置类型
export interface MonitoringDevice {
  id: number;
  device_id: string;
  name: string;
  device_type: string;
  location: string;
  floor?: string;
  protocol: string;
  host: string;
  port: number;
  community?: string;
  version: number;
  oids?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// 历史数据查询参数
export interface HistoricalDataQuery {
  device_id: string;
  start_time?: string;
  end_time?: string;
  interval?: string;
  limit?: number;
}

// 环境监控API
export const getEnvironmentData = async (floor?: string): Promise<EnvironmentData[]> => {
  try {
    const params: any = {};
    if (floor) params.floor = floor;

    const response = await apiClient.get('/api/monitoring/environment', { params });
    return response.data;
  } catch (error) {
    console.error('获取环境监控数据失败:', error);
    return [];
  }
};

export const getEnvironmentHistory = async (
  device_id: string,
  start_time?: string,
  end_time?: string,
  limit: number = 100
): Promise<EnvironmentData[]> => {
  try {
    const params: any = { device_id };
    if (start_time) params.start_time = start_time;
    if (end_time) params.end_time = end_time;
    params.limit = limit;

    const response = await apiClient.get('/api/monitoring/environment/history', { params });
    return response.data;
  } catch (error) {
    console.error('获取环境监控历史数据失败:', error);
    return [];
  }
};

// UPS监控API
export const getUPSData = async (device_id?: string): Promise<UPSData[]> => {
  try {
    const params: any = {};
    if (device_id) params.device_id = device_id;

    const response = await apiClient.get('/api/monitoring/ups', { params });
    return response.data;
  } catch (error) {
    console.error('获取UPS监控数据失败:', error);
    return [];
  }
};

export const getUPSHistory = async (
  device_id: string,
  start_time?: string,
  end_time?: string,
  limit: number = 100
): Promise<UPSData[]> => {
  try {
    const params: any = { device_id };
    if (start_time) params.start_time = start_time;
    if (end_time) params.end_time = end_time;
    params.limit = limit;

    const response = await apiClient.get('/api/monitoring/ups/history', { params });
    return response.data;
  } catch (error) {
    console.error('获取UPS监控历史数据失败:', error);
    return [];
  }
};

// 市电监控API
export const getMainsPowerData = async (device_id?: string): Promise<MainsPowerData[]> => {
  try {
    const params: any = {};
    if (device_id) params.device_id = device_id;

    const response = await apiClient.get('/api/monitoring/mains', { params });
    return response.data;
  } catch (error) {
    console.error('获取市电监控数据失败:', error);
    return [];
  }
};

export const getMainsPowerHistory = async (
  device_id: string,
  start_time?: string,
  end_time?: string,
  limit: number = 100
): Promise<MainsPowerData[]> => {
  try {
    const params: any = { device_id };
    if (start_time) params.start_time = start_time;
    if (end_time) params.end_time = end_time;
    params.limit = limit;

    const response = await apiClient.get('/api/monitoring/mains/history', { params });
    return response.data;
  } catch (error) {
    console.error('获取市电监控历史数据失败:', error);
    return [];
  }
};

// 监控设备配置API
export const getMonitoringDevices = async (device_type?: string, status?: string): Promise<MonitoringDevice[]> => {
  try {
    const params: any = {};
    if (device_type) params.device_type = device_type;
    if (status) params.status = status;

    const response = await apiClient.get('/api/monitoring/devices', { params });
    return response.data;
  } catch (error) {
    console.error('获取监控设备配置失败:', error);
    return [];
  }
};
