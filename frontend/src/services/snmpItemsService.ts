import { apiClient } from './apiClient';

// SNMP监控项类型
export interface SNMPItem {
  id: number;
  device_id: string;
  name: string;
  key: string;
  oid: string;
  unit?: string;
  item_type: string;
  location: string;
  position?: string;
  description?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// 创建SNMP监控项请求
export interface SNMPItemCreateRequest {
  device_id: string;
  name: string;
  key: string;
  oid: string;
  unit?: string;
  item_type: string;
  location: string;
  position?: string;
  description?: string;
  status?: string;
}

// 更新SNMP监控项请求
export interface SNMPItemUpdateRequest {
  name?: string;
  key?: string;
  oid?: string;
  unit?: string;
  item_type?: string;
  location?: string;
  position?: string;
  description?: string;
  status?: string;
}

// 批量创建SNMP监控项请求
export interface SNMPItemBatchCreateRequest {
  items: SNMPItemCreateRequest[];
}

// 获取SNMP监控项列表
export const getSNMPItems = async (
  device_id?: string,
  item_type?: string,
  location?: string,
  status?: string
): Promise<SNMPItem[]> => {
  let url = '/api/snmp-items';
  const params = new URLSearchParams();
  
  if (device_id) params.append('device_id', device_id);
  if (item_type) params.append('item_type', item_type);
  if (location) params.append('location', location);
  if (status) params.append('status', status);
  
  if (params.toString()) {
    url += `?${params.toString()}`;
  }
  
  const response = await apiClient.get<SNMPItem[]>(url);
  return response.data;
};

// 获取SNMP监控项详情
export const getSNMPItem = async (id: number): Promise<SNMPItem> => {
  const response = await apiClient.get<SNMPItem>(`/api/snmp-items/${id}`);
  return response.data;
};

// 创建SNMP监控项
export const createSNMPItem = async (item: SNMPItemCreateRequest): Promise<SNMPItem> => {
  const response = await apiClient.post<SNMPItem>('/api/snmp-items', item);
  return response.data;
};

// 批量创建SNMP监控项
export const createSNMPItemsBatch = async (batch: SNMPItemBatchCreateRequest): Promise<SNMPItem[]> => {
  const response = await apiClient.post<SNMPItem[]>('/api/snmp-items/batch', batch);
  return response.data;
};

// 更新SNMP监控项
export const updateSNMPItem = async (id: number, item: SNMPItemUpdateRequest): Promise<SNMPItem> => {
  const response = await apiClient.put<SNMPItem>(`/api/snmp-items/${id}`, item);
  return response.data;
};

// 删除SNMP监控项
export const deleteSNMPItem = async (id: number): Promise<void> => {
  await apiClient.delete(`/api/snmp-items/${id}`);
};
