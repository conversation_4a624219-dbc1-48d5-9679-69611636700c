// 模拟设备数据
const mockDevices = [
  {
    id: '1',
    name: 'Server-01',
    type: '服务器',
    ip_address: '*************',
    status: 'normal',
    cpu_usage: 45,
    memory_usage: 60,
    active_alerts: 0,
  },
  {
    id: '2',
    name: 'Server-02',
    type: '服务器',
    ip_address: '*************',
    status: 'warning',
    cpu_usage: 85,
    memory_usage: 75,
    active_alerts: 2,
  },
  {
    id: '3',
    name: 'Switch-01',
    type: '交换机',
    ip_address: '*************',
    status: 'normal',
    cpu_usage: 30,
    memory_usage: 45,
    active_alerts: 0,
  },
];

// 模拟告警数据
const mockAlerts = [
  {
    id: '1',
    device_id: '2',
    device_name: 'Server-02',
    type: 'warning',
    message: 'CPU使用率超过80%',
    created_at: '2024-03-20 14:30:00',
    status: 'active',
  },
  {
    id: '2',
    device_id: '2',
    device_name: 'Server-02',
    type: 'warning',
    message: '内存使用率超过70%',
    created_at: '2024-03-20 14:35:00',
    status: 'active',
  },
];

// 获取所有设备
export const fetchDevices = async () => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  return mockDevices;
};

// 获取设备告警
export const fetchDeviceAlerts = async (deviceId: string) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockAlerts.filter(alert => alert.device_id === deviceId);
};

// 获取单个设备信息
export const fetchDeviceById = async (deviceId: string) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockDevices.find(device => device.id === deviceId);
};

// 获取所有告警
export const fetchAllAlerts = async () => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  return mockAlerts;
}; 