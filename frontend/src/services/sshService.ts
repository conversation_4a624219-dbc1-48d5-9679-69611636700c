import { query } from '@/lib/db'
import { Client } from 'ssh2'

interface SSHDeviceConfig {
  host: string
  port: number
  username: string
  password: string
  commands: string[]
  interval: number
}

export class SSHService {
  // 添加监控设备
  async addDevice(device: SSHDeviceConfig) {
    await query(
      `INSERT INTO monitored_devices 
      (host, port, username, password, commands, interval, type, created_at) 
      VALUES ($1, $2, $3, $4, $5, $6, 'ssh', NOW())`,
      [
        device.host, 
        device.port || 22,
        device.username,
        device.password,
        device.commands,
        device.interval
      ]
    )
    
    // 启动轮询
    this.startPolling(device)
  }

  // 启动轮询
  private startPolling(device: SSHDeviceConfig) {
    setInterval(async () => {
      const conn = new Client()
      
      try {
        const results = await new Promise((resolve, reject) => {
          const results: Record<string, string> = {}
          let commandsExecuted = 0
          
          conn.on('ready', () => {
            device.commands.forEach(command => {
              conn.exec(command, (err, stream) => {
                if (err) return reject(err)
                
                let output = ''
                stream.on('data', (data: string) => output += data)
                stream.on('close', () => {
                  results[command] = output.trim()
                  commandsExecuted++
                  
                  if (commandsExecuted === device.commands.length) {
                    conn.end()
                    resolve(results)
                  }
                })
              })
            })
          })
          
          conn.connect({
            host: device.host,
            port: device.port || 22,
            username: device.username,
            password: device.password
          })
        })
        
        // 存储采集数据
        await query(
          `INSERT INTO ssh_data 
          (host, command, output, timestamp) 
          VALUES ${device.commands.map((_, i) => `($1, $${i*2+2}, $${i*2+3}, NOW())`).join(',')}`,
          [device.host, ...device.commands.flatMap(cmd => [cmd, results[cmd]])]
        )
      } catch (err) {
        console.error(`SSH采集失败: ${device.host}`, err)
      }
    }, device.interval * 1000)
  }

  // 获取设备列表
  async getDevices() {
    return query(
      `SELECT * FROM monitored_devices WHERE type = 'ssh'`
    )
  }
}

export const sshService = new SSHService()