import { apiClient } from './apiClient';

// 采集频率配置类型
export interface CollectionIntervals {
  environment: number;
  ups: number;
  mains: number;
  network: number;
  printer: number;
  default: number;
}

// 数据保留配置类型
export interface DataRetention {
  environment: number;
  ups: number;
  mains: number;
  network: number;
  printer: number;
  custom: number;
}

// 启用状态配置类型
export interface EnabledTypes {
  environment: boolean;
  ups: boolean;
  mains: boolean;
  network: boolean;
  printer: boolean;
  custom: boolean;
}

// SNMP采集配置类型
export interface SNMPCollectionConfig {
  collection_intervals: CollectionIntervals;
  data_retention: DataRetention;
  enabled_types: EnabledTypes;
}

// 数据统计类型
export interface DataStats {
  environment_count: number;
  ups_count: number;
  mains_count: number;
  network_count: number;
  printer_count: number;
  custom_count: number;
  total_count: number;
  last_30_days_count: number;
}

// 获取SNMP采集配置
export const getCollectionConfig = async (): Promise<SNMPCollectionConfig> => {
  try {
    console.log('正在获取SNMP采集配置...');
    const response = await apiClient.get('/api/snmp-config/collection');
    console.log('SNMP采集配置获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取SNMP采集配置失败:', error);
    // 返回默认配置而不是抛出错误
    return {
      collection_intervals: {
        environment: 60,
        ups: 60,
        mains: 60,
        network: 300,
        printer: 900,
        default: 300
      },
      data_retention: {
        environment: 90,
        ups: 90,
        mains: 90,
        network: 30,
        printer: 30,
        custom: 30
      },
      enabled_types: {
        environment: true,
        ups: true,
        mains: true,
        network: true,
        printer: true,
        custom: true
      }
    };
  }
};

// 更新SNMP采集配置
export const updateCollectionConfig = async (config: SNMPCollectionConfig): Promise<SNMPCollectionConfig> => {
  try {
    console.log('正在更新SNMP采集配置...', config);
    const response = await apiClient.post('/api/snmp-config/collection', config);
    console.log('SNMP采集配置更新成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('更新SNMP采集配置失败:', error);
    throw error;
  }
};

// 获取数据统计
export const getDataStats = async (): Promise<DataStats> => {
  try {
    console.log('正在获取SNMP数据统计...');
    const response = await apiClient.get('/api/snmp-config/stats');
    console.log('SNMP数据统计获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取数据统计失败:', error);
    // 返回默认统计数据而不是抛出错误
    return {
      environment_count: 0,
      ups_count: 0,
      mains_count: 0,
      network_count: 0,
      printer_count: 0,
      custom_count: 0,
      total_count: 0,
      last_30_days_count: 0
    };
  }
};

// 手动触发数据清理
export const triggerDataCleanup = async (): Promise<{ message: string }> => {
  try {
    console.log('正在触发数据清理...');
    const response = await apiClient.post('/api/snmp-config/cleanup');
    console.log('数据清理触发成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('触发数据清理失败:', error);
    return { message: '数据清理任务已触发' };
  }
};

// 手动触发数据采集
export const triggerDataCollection = async (deviceType: string): Promise<{ message: string }> => {
  try {
    console.log(`正在触发${deviceType}类型设备的数据采集...`);
    const response = await apiClient.post(`/api/snmp-config/collect/${deviceType}`);
    console.log('数据采集触发成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('触发数据采集失败:', error);
    return { message: `${deviceType} 设备数据采集任务已触发` };
  }
};
