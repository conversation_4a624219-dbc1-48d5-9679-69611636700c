import axios from 'axios';

// 预测性维护分析
export interface PredictiveMaintenanceResult {
  deviceId: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  nextMaintenanceDate: string;
  recommendedAction: string;
  potentialIssues: string[];
  confidence: number;
}

export const getPredictiveMaintenance = async (deviceId: string): Promise<PredictiveMaintenanceResult> => {
  try {
    const response = await axios.post('/api/ai/predictive-maintenance', { deviceId });
    return response.data;
  } catch (error) {
    console.error('预测性维护分析失败:', error);
    throw error;
  }
};

// 工单智能分类
export interface WorkOrderClassification {
  type: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  estimatedTime: string;
  suggestedAssignee?: string;
  suggestedSolution?: string;
  confidence: number;
}

export const classifyWorkOrder = async (description: string): Promise<WorkOrderClassification> => {
  try {
    const response = await axios.post('/api/ai/classify-work-order', { description });
    return response.data;
  } catch (error) {
    console.error('工单分类失败:', error);
    throw error;
  }
};

// 知识库集成
export interface KnowledgeBaseSuggestion {
  id: string;
  title: string;
  solution: string;
  relevance: number;
  source: string;
  tags: string[];
}

export const getKnowledgeBaseSuggestions = async (
  problemDescription: string,
  deviceType: string
): Promise<KnowledgeBaseSuggestion[]> => {
  try {
    const response = await axios.post('/api/ai/knowledge-base', {
      problem: problemDescription,
      deviceType
    });
    return response.data.suggestions;
  } catch (error) {
    console.error('获取知识库建议失败:', error);
    throw error;
  }
};

// 异常分析增强
export interface EnhancedAnalysisResult {
  summary: string;
  rootCauses: string[];
  recommendations: string[];
  relatedIssues: {
    id: string;
    description: string;
    similarity: number;
  }[];
}

export const getEnhancedAnalysis = async (inspectionResults: any): Promise<EnhancedAnalysisResult> => {
  try {
    // 实际环境中应该调用API
    // const response = await axios.post('/api/ai/enhanced-analysis', { results: inspectionResults });
    // return response.data;

    // 模拟数据
    return {
      summary: "系统整体运行状况良好，但存在一些需要关注的问题。",
      rootCauses: [
        "CPU利用率过高，可能是由于后台进程占用资源",
        "磁盘空间不足，需要清理临时文件"
      ],
      recommendations: [
        "检查并终止不必要的后台进程",
        "清理临时文件和日志",
        "考虑增加内存容量"
      ],
      relatedIssues: [
        {
          id: "ISSUE-2023-001",
          description: "类似的CPU占用问题在上个月也出现过，当时是由于日志服务异常导致",
          similarity: 0.85
        }
      ]
    };
  } catch (error) {
    console.error('增强分析失败:', error);
    throw error;
  }
};

// 设备健康评分
export interface DeviceHealthScore {
  score: number; // 0-100
  category: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  factors: {
    name: string;
    impact: number; // -100 to 100
    description: string;
  }[];
  trend: 'improving' | 'stable' | 'declining';
  historicalScores: {
    date: string;
    score: number;
  }[];
}

export const getDeviceHealthScore = async (deviceId: string): Promise<DeviceHealthScore> => {
  try {
    const response = await axios.post('/api/ai/device-health', { deviceId });
    return response.data;
  } catch (error) {
    console.error('获取设备健康评分失败:', error);
    throw error;
  }
};
