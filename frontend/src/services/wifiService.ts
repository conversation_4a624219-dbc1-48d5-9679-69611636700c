import axios from 'axios';
import { deviceService } from './device';

const API_BASE_URL = '/api/wireless';

export interface WiFiDevice {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'warning';
  position: {
    x: number;
    y: number;
    floor: string;
  };
  model: string;
  ip: string;
  mac: string;
  signal: number;
  clients: number;
  channel: number;
  bandwidth: string;
  power: number;
  snmpData?: Record<string, any>;
}

/**
 * WiFi设备服务，使用SNMP获取数据
 */
export const wifiService = {
  /**
   * 获取所有WiFi设备
   */
  async getAllDevices(): Promise<WiFiDevice[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/devices`);
      return response.data;
    } catch (error) {
      console.error('Error fetching WiFi devices:', error);
      
      // Return mock data for development
      return mockWiFiDevices;
    }
  },

  /**
   * 获取特定楼层的WiFi设备
   */
  async getDevicesByFloor(floor: string): Promise<WiFiDevice[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/devices/floor/${floor}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching WiFi devices for floor ${floor}:`, error);
      
      // Return filtered mock data for development
      return mockWiFiDevices.filter(device => device.position.floor === floor);
    }
  },

  /**
   * 更新WiFi设备位置
   */
  async updateDevicePosition(deviceId: string, position: { x: number; y: number; floor: string }): Promise<void> {
    try {
      await axios.patch(`${API_BASE_URL}/devices/${deviceId}/position`, { position });
    } catch (error) {
      console.error(`Error updating position for device ${deviceId}:`, error);
    }
  },

  /**
   * 通过SNMP刷新设备数据
   */
  async refreshDeviceData(deviceId: string): Promise<WiFiDevice> {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices/${deviceId}/refresh`);
      return response.data;
    } catch (error) {
      console.error(`Error refreshing data for device ${deviceId}:`, error);
      
      // Return mock data for development
      const device = mockWiFiDevices.find(d => d.id === deviceId);
      if (!device) throw new Error(`Device with ID ${deviceId} not found`);
      return device;
    }
  },

  /**
   * 添加新WiFi设备
   */
  async addDevice(device: Omit<WiFiDevice, 'id'>): Promise<WiFiDevice> {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices`, device);
      return response.data;
    } catch (error) {
      console.error('Error adding WiFi device:', error);
      
      // Return mock data for development
      const newDevice: WiFiDevice = {
        ...device,
        id: `wifi-${Math.floor(Math.random() * 10000)}`,
      };
      return newDevice;
    }
  },

  /**
   * 删除WiFi设备
   */
  async deleteDevice(deviceId: string): Promise<void> {
    try {
      await axios.delete(`${API_BASE_URL}/devices/${deviceId}`);
    } catch (error) {
      console.error(`Error deleting WiFi device ${deviceId}:`, error);
    }
  },

  /**
   * 根据状态获取显示颜色
   */
  getStatusColor(status: string): string {
    return deviceService.getStatusColor(status);
  }
};

// Mock data for development
export const mockWiFiDevices: WiFiDevice[] = [
  {
    id: 'wifi-1',
    name: 'AP-1F-01',
    status: 'online',
    position: {
      x: 150,
      y: 100,
      floor: '1F'
    },
    model: 'Cisco AIR-AP3802I-H-K9',
    ip: '*************',
    mac: '00:11:22:33:44:55',
    signal: 85,
    clients: 12,
    channel: 36,
    bandwidth: '80MHz',
    power: 20,
    snmpData: {
      uptime: '23 days, 4 hours',
      cpuUsage: '23%',
      memoryUsage: '45%',
      temperature: '42°C'
    }
  },
  {
    id: 'wifi-2',
    name: 'AP-1F-02',
    status: 'online',
    position: {
      x: 350,
      y: 100,
      floor: '1F'
    },
    model: 'Cisco AIR-AP3802I-H-K9',
    ip: '*************',
    mac: '00:11:22:33:44:56',
    signal: 90,
    clients: 8,
    channel: 40,
    bandwidth: '80MHz',
    power: 20,
    snmpData: {
      uptime: '15 days, 2 hours',
      cpuUsage: '19%',
      memoryUsage: '38%',
      temperature: '39°C'
    }
  },
  {
    id: 'wifi-3',
    name: 'AP-1F-03',
    status: 'warning',
    position: {
      x: 550,
      y: 100,
      floor: '1F'
    },
    model: 'Cisco AIR-AP3802I-H-K9',
    ip: '*************',
    mac: '00:11:22:33:44:57',
    signal: 65,
    clients: 15,
    channel: 44,
    bandwidth: '80MHz',
    power: 20,
    snmpData: {
      uptime: '7 days, 14 hours',
      cpuUsage: '72%',
      memoryUsage: '81%',
      temperature: '58°C'
    }
  },
  {
    id: 'wifi-4',
    name: 'AP-1F-04',
    status: 'offline',
    position: {
      x: 750,
      y: 100,
      floor: '1F'
    },
    model: 'Cisco AIR-AP3802I-H-K9',
    ip: '*************',
    mac: '00:11:22:33:44:58',
    signal: 0,
    clients: 0,
    channel: 48,
    bandwidth: '80MHz',
    power: 20,
    snmpData: {
      lastSeen: '2023-06-15T14:30:00Z',
      lastStatus: 'Device unreachable'
    }
  }
]; 