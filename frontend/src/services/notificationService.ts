import { AlertLog } from '@/types/alert';
import { NotificationChannel } from '@/types/notification';
import { NotificationTemplate } from '@/types/notificationTemplate';
import { toast } from '@/components/ui/use-toast';

// 注册 Service Worker
export const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/service-worker.js');
      console.log('Service Worker 注册成功:', registration);
      toast({
        title: '提示',
        description: '后台通知服务已启动。',
      });
    } catch (error) {
      console.error('Service Worker 注册失败:', error);
      toast({
        title: '错误',
        description: '后台通知服务启动失败。',
        variant: 'destructive',
      });
    }
  }
};

// 检查和请求通知权限
export const checkNotificationPermission = async () => {
  if ('Notification' in window) {
    let permission = Notification.permission;
    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }

    if (permission === 'granted') {
      console.log('通知权限已授予。');
      toast({
        title: '提示',
        description: '已获得通知权限，您将收到相关提醒。',
      });
    } else {
      console.log('通知权限被拒绝。');
      toast({
        title: '警告',
        description: '通知权限被拒绝，您可能无法及时收到重要提醒。',
        variant: 'destructive',
      });
    }
  }
};

// 临时模拟数据库查询函数
const query = async <T>(sql: string, params?: any[]): Promise<T[]> => {
  console.log('模拟数据库查询:', sql, params)
  return [] as T[]
}

export class NotificationService {
  // 获取通知模板
  private static async getNotificationTemplate(type: string) {
    try {
      const result = await query<NotificationTemplate>(
        `SELECT * FROM notification_templates
         WHERE type = $1 AND is_default = true
         LIMIT 1`,
        [type]
      )
      return result[0]
    } catch (err) {
      console.error('获取通知模板失败:', err)
      return null
    }
  }

  // 替换模板变量
  private static replaceTemplateVariables(template: string, alert: AlertLog) {
    return template
      .replace(/\{alert\.name\}/g, alert.ruleName || '未知规则')
      .replace(/\{alert\.metric\}/g, alert.metric)
      .replace(/\{alert\.value\}/g, alert.value)
      .replace(/\{alert\.threshold\}/g, alert.threshold || '未知')
      .replace(/\{alert\.severity\}/g, alert.severity)
      .replace(/\{alert\.time\}/g, new Date(alert.triggeredAt).toLocaleString())
      .replace(/\{device\.name\}/g, alert.deviceName || '未知设备')
      .replace(/\{device\.ip\}/g, alert.deviceIp || '未知IP')
      .replace(/\{device\.type\}/g, alert.deviceType || '未知类型')
  }

  // 发送报警通知
  static async sendAlertNotification(alertLog: AlertLog) {
    try {
      // 获取所有激活的通知渠道
      const channels = await query<NotificationChannel>(
        'SELECT * FROM notification_channels WHERE is_active = true'
      )

      // 为每个渠道发送通知
      for (const channel of channels) {
        switch (channel.type) {
          case 'email':
            await this.sendEmailNotification(channel, alertLog)
            break
          case 'webhook':
            await this.sendWebhookNotification(channel, alertLog)
            break
          case 'slack':
            await this.sendSlackNotification(channel, alertLog)
            break
          case 'sms':
            await this.sendSMSNotification(channel, alertLog)
            break
        }
      }

      // 记录通知发送状态
      await query(
        'UPDATE alert_logs SET notified = true WHERE id = $1',
        [alertLog.id]
      )
    } catch (err) {
      console.error('发送通知失败:', err)
      toast({
        title: "通知发送失败",
        description: err instanceof Error ? err.message : "无法发送报警通知",
        variant: "destructive",
      })
    }
  }

  private static async sendEmailNotification(channel: NotificationChannel, alert: AlertLog) {
    // 获取默认邮件模板
    const template = await this.getNotificationTemplate('email')

    // 替换模板变量
    const subject = this.replaceTemplateVariables(template?.subject || '报警通知', alert)
    const content = this.replaceTemplateVariables(template?.content || '', alert)

    console.log('发送邮件通知:', {
      to: channel.config.recipients,
      subject,
      content
    })
    // 实际项目中这里应该调用邮件服务API
  }

  private static async sendWebhookNotification(channel: NotificationChannel, alert: AlertLog) {
    // 实现Webhook通知逻辑
    console.log('发送Webhook通知:', channel, alert)
    // 实际项目中这里应该调用Webhook API
  }

  private static async sendSlackNotification(channel: NotificationChannel, alert: AlertLog) {
    // 实现Slack通知逻辑
    console.log('发送Slack通知:', channel, alert)
    // 实际项目中这里应该调用Slack API
  }

  private static async sendSMSNotification(channel: NotificationChannel, alert: AlertLog) {
    // 实现短信通知逻辑
    console.log('发送短信通知:', channel, alert)
    // 实际项目中这里应该调用短信服务API
  }
}