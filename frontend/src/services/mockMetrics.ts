import { DeviceMetric, MetricData } from '@/types/monitoring';

// Function to generate random value within a range
const randomValue = (min: number, max: number, fixed: number = 2): number => {
  return +(Math.random() * (max - min) + min).toFixed(fixed);
};

// Function to generate a timestamp
const generateTimestamp = (minutesAgo: number): string => {
  const date = new Date();
  date.setMinutes(date.getMinutes() - minutesAgo);
  return date.toISOString();
};

// Generate mock CPU usage data for a device
export const generateCpuUsageData = (deviceId: string, dataPoints: number = 24): MetricData => {
  const data = {
    metric_name: 'cpu_usage',
    data_points: Array(dataPoints).fill(0).map((_, i) => ({
      timestamp: generateTimestamp(dataPoints - i),
      value: randomValue(10, 95, 1)
    })),
    unit: '%'
  };
  
  return data;
};

// Generate mock memory usage data for a device
export const generateMemoryUsageData = (deviceId: string, dataPoints: number = 24): MetricData => {
  const data = {
    metric_name: 'memory_usage',
    data_points: Array(dataPoints).fill(0).map((_, i) => ({
      timestamp: generateTimestamp(dataPoints - i),
      value: randomValue(20, 85, 1)
    })),
    unit: '%'
  };
  
  return data;
};

// Generate mock disk usage data for a device
export const generateDiskUsageData = (deviceId: string, dataPoints: number = 24): MetricData => {
  const data = {
    metric_name: 'disk_usage',
    data_points: Array(dataPoints).fill(0).map((_, i) => ({
      timestamp: generateTimestamp(dataPoints - i),
      value: randomValue(30, 90, 1)
    })),
    unit: '%'
  };
  
  return data;
};

// Generate mock temperature data for a device
export const generateTemperatureData = (deviceId: string, dataPoints: number = 24): MetricData => {
  const data = {
    metric_name: 'temperature',
    data_points: Array(dataPoints).fill(0).map((_, i) => ({
      timestamp: generateTimestamp(dataPoints - i),
      value: randomValue(25, 45, 1)
    })),
    unit: '°C'
  };
  
  return data;
};

// Generate mock network traffic data for a device
export const generateNetworkTrafficData = (deviceId: string, dataPoints: number = 24): MetricData => {
  const data = {
    metric_name: 'network_traffic',
    data_points: Array(dataPoints).fill(0).map((_, i) => ({
      timestamp: generateTimestamp(dataPoints - i),
      value: randomValue(10, 800, 0)
    })),
    unit: 'Mbps'
  };
  
  return data;
};

// Generate all metrics for a device
export const generateAllMetrics = (deviceId: string): MetricData[] => {
  return [
    generateCpuUsageData(deviceId),
    generateMemoryUsageData(deviceId),
    generateDiskUsageData(deviceId),
    generateTemperatureData(deviceId),
    generateNetworkTrafficData(deviceId)
  ];
};

// Get current metric values
export const getCurrentMetrics = (deviceId: string): DeviceMetric[] => {
  const metrics = [
    {
      id: 1,
      device_id: deviceId,
      metric_name: 'cpu_usage',
      metric_value: randomValue(10, 95, 1),
      unit: '%',
      timestamp: new Date().toISOString()
    },
    {
      id: 2,
      device_id: deviceId,
      metric_name: 'memory_usage',
      metric_value: randomValue(20, 85, 1),
      unit: '%',
      timestamp: new Date().toISOString()
    },
    {
      id: 3,
      device_id: deviceId,
      metric_name: 'disk_usage',
      metric_value: randomValue(30, 90, 1),
      unit: '%',
      timestamp: new Date().toISOString()
    },
    {
      id: 4,
      device_id: deviceId,
      metric_name: 'temperature',
      metric_value: randomValue(25, 45, 1),
      unit: '°C',
      timestamp: new Date().toISOString()
    },
    {
      id: 5,
      device_id: deviceId,
      metric_name: 'network_traffic',
      metric_value: randomValue(10, 800, 0),
      unit: 'Mbps',
      timestamp: new Date().toISOString()
    }
  ];
  
  return metrics;
};

// Get threshold values for metrics
export const getMetricThresholds = (deviceId: string) => {
  return {
    cpu_usage: { warning: 80, critical: 90, comparison: 'gt' as const },
    memory_usage: { warning: 70, critical: 85, comparison: 'gt' as const },
    disk_usage: { warning: 80, critical: 90, comparison: 'gt' as const },
    temperature: { warning: 35, critical: 40, comparison: 'gt' as const },
    network_traffic: { warning: 600, critical: 750, comparison: 'gt' as const }
  };
}; 