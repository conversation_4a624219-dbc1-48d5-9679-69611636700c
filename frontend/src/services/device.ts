import axios from 'axios';

const API_BASE_URL = '/api/device_monitor';

export interface DeviceStatus {
  status: 'normal' | 'warning' | 'error' | 'unknown';
  last_update: string;
}

export interface DeviceInfo {
  device_id: string;
  status: 'normal' | 'warning' | 'error' | 'unknown';
  metrics: Record<string, any>;
  last_update: string;
}

export interface DeviceConfig {
  device_id: string;
  type: 'snmp' | 'ssh' | 'wmi';
  host: string;
  [key: string]: any;
}

/**
 * 设备API服务
 */
export const deviceService = {
  /**
   * 获取所有设备状态
   */
  async getAllDevices(): Promise<Record<string, DeviceStatus>> {
    const response = await axios.get(`${API_BASE_URL}/devices/`);
    return response.data;
  },

  /**
   * 获取设备详细信息
   */
  async getDeviceInfo(deviceId: string): Promise<DeviceInfo> {
    const response = await axios.get(`${API_BASE_URL}/devices/${deviceId}/`);
    return response.data;
  },

  /**
   * 获取设备性能指标
   */
  async getDeviceMetrics(deviceId: string): Promise<Record<string, any>> {
    const response = await axios.get(`${API_BASE_URL}/devices/${deviceId}/metrics/`);
    return response.data;
  },

  /**
   * 刷新设备数据
   */
  async refreshDevice(deviceId: string): Promise<DeviceInfo> {
    const response = await axios.post(`${API_BASE_URL}/devices/${deviceId}/refresh/`);
    return response.data;
  },

  /**
   * 添加新设备
   */
  async addDevice(deviceConfig: DeviceConfig): Promise<void> {
    await axios.post(`${API_BASE_URL}/devices/`, deviceConfig);
  },

  /**
   * 删除设备
   */
  async deleteDevice(deviceId: string): Promise<void> {
    await axios.delete(`${API_BASE_URL}/devices/${deviceId}/`);
  },

  /**
   * 根据设备状态获取显示颜色
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'normal':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  },

  /**
   * 格式化设备指标显示
   */
  formatMetricValue(key: string, value: any): string {
    // 根据指标类型进行格式化
    if (typeof value === 'number') {
      if (key.includes('usage') || key.includes('percent')) {
        return `${value.toFixed(2)}%`;
      } else if (key.includes('memory') && key.includes('_kb')) {
        return `${(value / 1024).toFixed(2)} MB`;
      } else if (key.includes('_gb')) {
        return `${value.toFixed(2)} GB`;
      } else if (key.includes('bytes')) {
        if (value > 1024 * 1024) {
          return `${(value / (1024 * 1024)).toFixed(2)} MB`;
        } else if (value > 1024) {
          return `${(value / 1024).toFixed(2)} KB`;
        } else {
          return `${value.toFixed(2)} B`;
        }
      }
      return value.toString();
    }
    return value;
  }
}; 