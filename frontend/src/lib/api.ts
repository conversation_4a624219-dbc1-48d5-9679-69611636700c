// API配置
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// 检查API服务是否可用
let isApiAvailable = true;
const API_TIMEOUT = 10000; // 10秒超时
const MAX_RETRIES = 2;

/**
 * 通用的API请求函数
 */
export async function fetchApi<T>(
  endpoint: string,
  options: RequestInit = {},
  useCache: boolean = false,
  retryCount: number = 0
): Promise<T> {
  // 如果API不可用且已尝试重试，则使用模拟数据
  if (!isApiAvailable && process.env.NODE_ENV === 'development' && retryCount >= MAX_RETRIES) {
    console.warn(`API不可用，使用模拟数据: ${endpoint}`);
    return getMockData<T>(endpoint);
  }

  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 创建一个可取消的请求
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

  try {
    const response = await fetch(url, {
      ...defaultOptions,
      ...options,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API请求失败: ${response.status}`);
    }

    isApiAvailable = true; // 标记API可用
    return response.json();
  } catch (error) {
    // 处理连接错误
    if (
      error instanceof Error &&
      (error.name === 'AbortError' || 
       error.message.includes('Failed to fetch') ||
       error.message.includes('Network request failed'))
    ) {
      isApiAvailable = false;
      console.error(`API连接错误 (${url}): ${error.message}`);
      
      // 尝试重试
      if (retryCount < MAX_RETRIES) {
        console.log(`重试API请求 (${retryCount + 1}/${MAX_RETRIES}): ${endpoint}`);
        return fetchApi<T>(endpoint, options, useCache, retryCount + 1);
      }
      
      // 在开发环境中使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        console.warn(`无法连接API，使用模拟数据: ${endpoint}`);
        return getMockData<T>(endpoint);
      }
    }
    
    throw error;
  }
}

/**
 * 获取模拟数据（开发/测试环境使用）
 */
function getMockData<T>(endpoint: string): T {
  // 根据endpoint返回适当的模拟数据
  if (endpoint.includes('/devices')) {
    return {
      devices: [
        { 
          id: 'mock-1', 
          name: '模拟服务器1', 
          type: 'server',
          status: 'normal',
          uStart: 10,
          uSize: 2
        },
        { 
          id: 'mock-2', 
          name: '模拟网络设备', 
          type: 'network',
          status: 'warning',
          uStart: 15,
          uSize: 1
        }
      ]
    } as unknown as T;
  }
  
  if (endpoint.includes('/rack')) {
    return {
      id: 'mock-rack',
      name: '模拟机柜',
      totalU: 42,
      usedU: 10,
      devices: [
        { 
          id: 'mock-1', 
          name: '模拟服务器1', 
          type: 'server',
          status: 'normal',
          positionStart: 10,
          positionEnd: 11,
          model: 'Dell R740'
        },
        { 
          id: 'mock-2', 
          name: '模拟网络设备', 
          type: 'network',
          status: 'warning',
          positionStart: 15,
          positionEnd: 15,
          model: 'Cisco 9300'
        }
      ]
    } as unknown as T;
  }
  
  return {} as T;
} 