import { Pool } from 'pg'

const pool = new Pool({
  user: 'user_PwWEyE',
  host: 'localhost',
  database: 'asset_management',
  password: 'password_QM8NyB',
  port: 5432,
})

export const query = async (text: string, params?: any[]) => {
  const client = await pool.connect()
  try {
    const res = await client.query(text, params)
    return res.rows
  } finally {
    client.release()
  }
}

export default pool