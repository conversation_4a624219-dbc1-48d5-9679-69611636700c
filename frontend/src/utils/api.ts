import axios from 'axios';
import { PhoneExtension, PhoneExtensionUpdateData } from '@/types/phoneExtension';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const getPhoneExtensions = async (cabinetNumber: number): Promise<PhoneExtension[]> => {
  const response = await axios.get(`${API_BASE_URL}/phone-extensions/${cabinetNumber}`);
  return response.data;
};

export const updatePhoneExtension = async (
  id: number,
  data: PhoneExtensionUpdateData
): Promise<PhoneExtension> => {
  const response = await axios.patch(`${API_BASE_URL}/phone-extensions/${id}`, data);
  return response.data;
}; 