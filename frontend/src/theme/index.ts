import { extendTheme } from '@chakra-ui/react'
import { createTheme, responsiveFontSizes } from '@mui/material/styles'
import { Theme } from '@mui/material/styles'

// Get stored theme from localStorage or default to 'light'
const storedTheme = typeof window !== 'undefined' ? localStorage.getItem('chakra-ui-color-mode') : 'light'
const initialColorMode = storedTheme || 'light'

// Chakra theme
export const chakraTheme = extendTheme({
  config: {
    initialColorMode,
    useSystemColorMode: false,
  },
  colors: {
    komodo: {
      green: '#00C389',
      darkGray: '#1A202C',
      lightGray: '#F7FAFC',
      border: '#E2E8F0',
      hover: {
        light: '#F7FAFC',
        dark: '#2D3748'
      }
    }
  },
  styles: {
    global: (props: any) => ({
      body: {
        bg: props.colorMode === 'dark' ? 'komodo.darkGray' : 'white',
        color: props.colorMode === 'dark' ? 'white' : 'gray.800',
        transition: 'background-color 0.3s ease-in-out, color 0.3s ease-in-out'
      },
      '*': {
        transition: 'background-color 0.3s ease-in-out, border-color 0.3s ease-in-out'
      }
    })
  },
  components: {
    Button: {
      baseStyle: {
        borderRadius: 'md',
        fontWeight: 'medium'
      },
      variants: {
        solid: (props: any) => ({
          bg: 'komodo.green',
          color: 'white',
          _hover: {
            bg: 'green.600',
            _disabled: {
              bg: 'komodo.green'
            }
          }
        })
      }
    },
    Input: {
      variants: {
        outline: (props: any) => ({
          field: {
            borderRadius: 'md',
            borderColor: props.colorMode === 'dark' ? 'gray.600' : 'gray.200',
            _hover: {
              borderColor: props.colorMode === 'dark' ? 'gray.500' : 'gray.300'
            },
            _focus: {
              borderColor: 'komodo.green',
              boxShadow: '0 0 0 1px var(--chakra-colors-komodo-green)'
            }
          }
        })
      }
    },
    Select: {
      baseStyle: {
        field: {
          paddingRight: '2rem'
        },
        icon: {
          width: '1.5rem',
          height: '1.5rem',
          insetEnd: '0.5rem',
          position: 'absolute',
          top: '50%',
          transform: 'translateY(-50%)',
          pointerEvents: 'none',
          color: 'currentColor',
          // 确保只显示一个图标
          '& > *:not(:first-of-type)': {
            display: 'none !important'
          }
        }
      },
      parts: ['field', 'icon'],
      defaultProps: {
        icon: { children: null } // 移除默认图标
      },
      variants: {
        outline: (props: any) => ({
          field: {
            borderRadius: 'md',
            borderColor: props.colorMode === 'dark' ? 'gray.600' : 'gray.200',
            _hover: {
              borderColor: props.colorMode === 'dark' ? 'gray.500' : 'gray.300'
            },
            _focus: {
              borderColor: 'komodo.green',
              boxShadow: '0 0 0 1px var(--chakra-colors-komodo-green)'
            }
          }
        })
      }
    },
    Table: {
      variants: {
        simple: (props: any) => ({
          th: {
            borderColor: props.colorMode === 'dark' ? 'gray.600' : 'gray.200',
            color: props.colorMode === 'dark' ? 'gray.400' : 'gray.600',
            fontSize: 'sm'
          },
          td: {
            borderColor: props.colorMode === 'dark' ? 'gray.600' : 'gray.200'
          }
        })
      }
    }
  }
})

// MUI theme
const baseTheme = createTheme({
  breakpoints: {
    keys: ['xs', 'sm', 'md', 'lg', 'xl'],
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
    unit: 'px',
  },
  palette: {
    mode: initialColorMode as 'light' | 'dark',
    primary: {
      main: '#00C389', // komodo.green
    },
    secondary: {
      main: '#1A202C', // komodo.darkGray
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '6px',
        },
      },
    },
    MuiSnackbar: {
      styleOverrides: {
        root: {
          '& .MuiSnackbarContent-root': {
            backgroundColor: '#00C389',
          },
        },
      },
    },
  },
})

// Apply responsive font sizes and export the theme
export const muiTheme: Theme = responsiveFontSizes(baseTheme)