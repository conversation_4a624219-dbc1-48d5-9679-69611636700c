import { extendTheme, type ThemeConfig } from '@chakra-ui/react'

const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: true,
}

const colors = {
  komodo: {
    darkGray: '#1A202C',
    lightGray: '#F7FAFC',
    primary: '#3182CE',
    secondary: '#4FD1C5',
    accent: '#F6AD55',
  },
}

const components = {
  Badge: {
    baseStyle: {
      textTransform: 'none',
    },
  },
  Button: {
    baseStyle: {
      fontWeight: 'normal',
    },
  },
}

export const theme = extendTheme({
  config,
  colors,
  components,
  styles: {
    global: (props: any) => ({
      body: {
        bg: props.colorMode === 'dark' ? 'komodo.darkGray' : 'komodo.lightGray',
      },
    }),
  },
}) 