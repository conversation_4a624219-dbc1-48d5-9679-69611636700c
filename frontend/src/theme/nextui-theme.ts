'use client';

import { NextUIPluginConfig } from '@nextui-org/react';

// 从localStorage获取存储的主题或默认为'light'
const getInitialColorMode = () => {
  if (typeof window !== 'undefined') {
    const storedTheme = localStorage.getItem('theme-mode');
    return storedTheme || 'light';
  }
  return 'light';
};

// NextUI主题配置
export const nextUITheme: NextUIPluginConfig = {
  themes: {
    light: {
      layout: {
        radius: {
          small: '4px',
          medium: '6px',
          large: '8px',
        },
        borderWidth: {
          small: '1px',
          medium: '2px',
          large: '3px',
        },
      },
      colors: {
        // 主要颜色
        primary: {
          DEFAULT: '#33bb73', // komodo.green
          50: '#e6f7ee',
          100: '#c3ebd6',
          200: '#9fdfbd',
          300: '#7bd3a4',
          400: '#57c78c',
          500: '#33bb73', // 主要绿色
          600: '#2a9b5f',
          700: '#207b4b',
          800: '#155b36',
          900: '#0b3b22',
          foreground: '#FFFFFF',
        },
        // 次要颜色
        secondary: {
          DEFAULT: '#207b4b', // komodo.darkGreen
          50: '#e6f0eb',
          100: '#c3d9cf',
          200: '#9fc2b3',
          300: '#7bab97',
          400: '#57947b',
          500: '#207b4b',
          600: '#1a6b41',
          700: '#145a37',
          800: '#0e492d',
          900: '#083823',
          foreground: '#FFFFFF',
        },
        // 成功状态
        success: {
          DEFAULT: '#33bb73',
          foreground: '#FFFFFF',
        },
        // 警告状态
        warning: {
          DEFAULT: '#ff922b',
          foreground: '#FFFFFF',
        },
        // 错误状态
        danger: {
          DEFAULT: '#f03e3e',
          foreground: '#FFFFFF',
        },
        // 信息状态
        info: {
          DEFAULT: '#1c7ed6',
          foreground: '#FFFFFF',
        },
        // 背景颜色
        background: {
          DEFAULT: '#FFFFFF',
        },
        // 前景颜色
        foreground: {
          DEFAULT: '#1A202C', // komodo.darkGray
        },
        // 边框颜色
        border: {
          DEFAULT: '#E2E8F0', // komodo.border
        },
        // 分割线颜色
        divider: {
          DEFAULT: '#E2E8F0',
        },
        // 焦点颜色
        focus: {
          DEFAULT: '#33bb73',
        },
        // 悬停颜色
        hover: {
          DEFAULT: '#F7FAFC', // komodo.hover.light
        },
        // 禁用状态
        disabled: {
          DEFAULT: '#adb5bd',
          foreground: '#868e96',
        },
      },
    },
    dark: {
      layout: {
        radius: {
          small: '4px',
          medium: '6px',
          large: '8px',
        },
        borderWidth: {
          small: '1px',
          medium: '2px',
          large: '3px',
        },
      },
      colors: {
        // 主要颜色
        primary: {
          DEFAULT: '#33bb73', // komodo.green
          50: '#0b3b22',
          100: '#155b36',
          200: '#207b4b',
          300: '#2a9b5f',
          400: '#33bb73',
          500: '#57c78c',
          600: '#7bd3a4',
          700: '#9fdfbd',
          800: '#c3ebd6',
          900: '#e6f7ee',
          foreground: '#FFFFFF',
        },
        // 次要颜色
        secondary: {
          DEFAULT: '#207b4b', // komodo.darkGreen
          50: '#083823',
          100: '#0e492d',
          200: '#145a37',
          300: '#1a6b41',
          400: '#207b4b',
          500: '#57947b',
          600: '#7bab97',
          700: '#9fc2b3',
          800: '#c3d9cf',
          900: '#e6f0eb',
          foreground: '#FFFFFF',
        },
        // 成功状态
        success: {
          DEFAULT: '#57c78c',
          foreground: '#FFFFFF',
        },
        // 警告状态
        warning: {
          DEFAULT: '#ffa94d',
          foreground: '#FFFFFF',
        },
        // 错误状态
        danger: {
          DEFAULT: '#ff6b6b',
          foreground: '#FFFFFF',
        },
        // 信息状态
        info: {
          DEFAULT: '#4dabf7',
          foreground: '#FFFFFF',
        },
        // 背景颜色
        background: {
          DEFAULT: '#1A202C', // komodo.darkGray
        },
        // 前景颜色
        foreground: {
          DEFAULT: '#FFFFFF',
        },
        // 边框颜色
        border: {
          DEFAULT: '#2D3748', // 深色边框
        },
        // 分割线颜色
        divider: {
          DEFAULT: '#2D3748',
        },
        // 焦点颜色
        focus: {
          DEFAULT: '#33bb73',
        },
        // 悬停颜色
        hover: {
          DEFAULT: '#2D3748', // komodo.hover.dark
        },
        // 禁用状态
        disabled: {
          DEFAULT: '#4a5568',
          foreground: '#718096',
        },
      },
    },
  },
};

export default nextUITheme;
