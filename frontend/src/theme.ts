'use client'

import { createTheme } from '@mui/material/styles'

// 获取存储的主题或默认为'light'
const getInitialColorMode = () => {
  if (typeof window !== 'undefined') {
    const storedTheme = localStorage.getItem('theme-mode');
    return storedTheme || 'light';
  }
  return 'light';
};

// Material-UI theme
const muiTheme = createTheme({
  palette: {
    mode: (typeof window !== 'undefined' &&
           localStorage.getItem('theme-mode') === 'dark') ? 'dark' : 'light',
    primary: {
      main: '#33bb73', // 与Komodo绿色匹配
    },
    secondary: {
      main: '#207b4b', // 深绿色
    },
    error: {
      main: '#f03e3e',
    },
    warning: {
      main: '#ff922b',
    },
    info: {
      main: '#1c7ed6',
    },
    success: {
      main: '#33bb73',
    },
  },
  shape: {
    borderRadius: 8,
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  },
})

export { muiTheme }