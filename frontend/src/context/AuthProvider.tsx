import React, { createContext, useContext, useState, useEffect } from 'react'

interface AuthContextType {
  user: any
  access: string | null
  refresh: string | null
  login: (username: string, password: string) => Promise<boolean>
  logout: () => void
  refreshToken: () => Promise<void>
  permissions: string[]
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  access: null,
  refresh: null,
  login: async () => false,
  logout: () => {},
  refreshToken: async () => {},
  permissions: [],
})

export const useAuth = () => useContext(AuthContext)

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<any>(null)
  const [access, setAccess] = useState<string | null>(null)
  const [refresh, setRefresh] = useState<string | null>(null)
  const [permissions, setPermissions] = useState<string[]>([])

  useEffect(() => {
    const acc = localStorage.getItem('access')
    const ref = localStorage.getItem('refresh')
    if (acc && ref) {
      setAccess(acc)
      setRefresh(ref)
      fetchUser(acc)
    }
  }, [])

  const login = async (username: string, password: string) => {
    const res = await fetch('/api/accounts/token/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
    })
    if (res.ok) {
      const data = await res.json()
      setAccess(data.access)
      setRefresh(data.refresh)
      localStorage.setItem('access', data.access)
      localStorage.setItem('refresh', data.refresh)
      await fetchUser(data.access)
      return true
    }
    return false
  }

  const fetchUser = async (token: string) => {
    const res = await fetch('/api/accounts/me/', {
      headers: { Authorization: `Bearer ${token}` },
    })
    if (res.ok) {
      const data = await res.json()
      setUser(data)
      setPermissions(data.permissions || [])
    }
  }

  const logout = () => {
    setUser(null)
    setAccess(null)
    setRefresh(null)
    setPermissions([])
    localStorage.removeItem('access')
    localStorage.removeItem('refresh')
  }

  const refreshToken = async () => {
    if (!refresh) return
    const res = await fetch('/api/accounts/token/refresh/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh }),
    })
    if (res.ok) {
      const data = await res.json()
      setAccess(data.access)
      localStorage.setItem('access', data.access)
      if (user) fetchUser(data.access)
    } else {
      logout()
    }
  }

  return (
    <AuthContext.Provider value={{ user, access, refresh, login, logout, refreshToken, permissions }}>
      {children}
    </AuthContext.Provider>
  )
} 