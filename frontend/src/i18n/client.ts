'use client'

import { useState, useEffect } from 'react'
import { enUS } from './en-US'
import { zhCN } from './zh-CN'

// Define translation type
type TranslationType = typeof enUS & typeof zhCN

// Define available languages
const languages: Record<string, TranslationType> = {
  'en-US': enUS as TranslationType,
  'zh-CN': zhCN as TranslationType,
}

type LanguageKey = keyof typeof languages

// Get user's preferred language or use default
const getUserLanguage = (): LanguageKey => {
  if (typeof window !== 'undefined') {
    const savedLang = localStorage.getItem('appLanguage') as LanguageKey
    if (savedLang && languages[savedLang]) {
      return savedLang
    }
    
    // Try to get browser language
    const browserLang = navigator.language
    if (browserLang.startsWith('zh')) {
      return 'zh-CN'
    }
  }
  
  return 'en-US' // Default language
}

export const useTranslation = () => {
  const [language, setLanguage] = useState<LanguageKey>('en-US')
  
  useEffect(() => {
    setLanguage(getUserLanguage())
  }, [])
  
  const changeLanguage = (newLanguage: LanguageKey) => {
    if (languages[newLanguage]) {
      setLanguage(newLanguage)
      localStorage.setItem('appLanguage', newLanguage)
    }
  }
  
  const t = (key: string): string => {
    const translations = languages[language]
    return translations[key as keyof TranslationType] || key
  }
  
  return {
    t,
    language,
    changeLanguage,
    languages: Object.keys(languages) as LanguageKey[]
  }
} 