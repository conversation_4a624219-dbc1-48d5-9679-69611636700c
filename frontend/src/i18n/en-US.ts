export const enUS = {
  // 导航菜单
  'dashboard': 'Dashboard',
  'resources': 'Resources Management',
  'network': 'Network Management',
  'wireless': 'Wireless Network',
  'assets': 'Asset Management',
  'asset.map': 'Asset Map',
  'asset.list': 'Asset List',
  'stacks': 'Technology Stacks',
  'deployments': 'Deployment Management',
  'procedures': 'Operation Procedures',
  'notifications': 'Notification Center',
  'updates': 'System Updates',
  'settings': 'System Settings',

  // 机柜空间状态
  'space.sufficient': 'Space Sufficient',
  'space.limited': 'Space Limited',
  'space.critical': 'Space Critical',

  // 仪表盘状态
  'total.devices': 'Total Devices',
  'normal.running': 'Normal Running',
  'warning.devices': 'Warning Devices',
  'maintenance.devices': 'Maintenance Devices',
  'system.status': 'System Status',
  'environment.monitoring': 'Environment Monitoring',
  'core.services': 'Core Services',
  'temperature': 'Temperature',
  'power': 'Power',
  'CPU': 'CPU Usage',
  'memory': 'Memory Usage',

  // 资源管理页面
  'datacenter.layout': 'Data Center Layout',
  'cold.aisle': 'Cold Aisle 1 Maintenance Group',
  'rack.layout': 'Rack Layout',
  'rack.status': 'Rack Status',
  'rack.number': 'Rack Number',
  'rack.space': 'Rack Space',
  'rack.power': 'Rack Power',
  'rack.temperature': 'Rack Temperature',
  'rack.humidity': 'Rack Humidity',
  'device.count': 'Device Count',
  'device.status': 'Device Status',
  'device.type': 'Device Type',
  'device.model': 'Device Model',
  'device.vendor': 'Device Vendor',
  'device.location': 'Device Location',
  'device.ip': 'IP Address',
  'device.mac': 'MAC Address',
  'device.sn': 'Serial Number',
  'device.os': 'Operating System',
  'device.cpu': 'CPU',
  'device.memory': 'Memory',
  'device.disk': 'Disk',
  'device.network': 'Network',

  // 网络管理子菜单
  'network.fault.impact': 'Fault Impact Analysis',
  'network.path.visualization': 'Network Path Visualization',
  'terminal.info': 'Terminal Information Management',
  'network.config.backup': 'Network Configuration Backup',
  'config.management': 'Configuration Management',
  'digital.ip.management': 'Digital IP Management',
  'phone.extension.management': 'Phone Extension Management',
  'vm.management': 'Virtual Machine Management',
  'printer.management': 'Printer Management',
  'bandwidth.monitoring': 'Network Bandwidth Monitoring',

  // 服务状态
  'service.running': 'Running',
  'service.stopped': 'Stopped',
  'service.error': 'Error',
  'service.warning': 'Warning',
  'service.unknown': 'Unknown',

  // 通用操作
  'view': 'View',
  'edit': 'Edit',
  'delete': 'Delete',
  'add': 'Add',
  'search': 'Search',
  'filter': 'Filter',
  'sort': 'Sort',
  'refresh': 'Refresh',
  'save': 'Save',
  'cancel': 'Cancel',
  'confirm': 'Confirm',
  'back': 'Back',
  'next': 'Next',
  'previous': 'Previous',
  'finish': 'Finish',

  // 主题设置
  'theme': 'Theme Settings',
  'dark.mode': 'Dark Mode',
  'light.mode': 'Light Mode',
  'dark.mode.enabled': 'Dark Mode Enabled',
  'dark.mode.disabled': 'Light Mode Enabled',

  // 语言设置
  'language': 'Language Settings',
  'select.language': 'Select Language',

  // 状态文本
  'status.online': 'Online',
  'status.offline': 'Offline',
  'status.error': 'Error',
  'status.warning': 'Warning',
  'status.normal': 'Normal',
  'status.unknown': 'Unknown',

  // Wireless Network Page
  'ap.location.map': 'AP Location Map',
  'ap.name': 'AP Name',
  'ap.number': 'AP Number',
  'ap.status': 'AP Status',
  'ap.online': 'Online',
  'ap.offline': 'Offline',
  'ap.warning': 'Warning',
  'ap.floor': 'Floor',
  'ap.location': 'Location',
  'ap.model': 'Model',
  'ap.ip': 'IP Address',
  'ap.mac': 'MAC Address',
  'ap.signal': 'Signal Strength',
  'ap.clients': 'Connected Clients',
  'ap.channel': 'Channel',
  'ap.bandwidth': 'Bandwidth',
  'ap.power': 'Transmit Power',
  'clickToAddDevice': 'Click to Add WiFi Device',
  'addDevice': 'Add WiFi Device',
  'confirmAddDevice': 'Confirm adding new WiFi device?',
  'deviceAdded': 'WiFi device added',
  'errorAddingDevice': 'Failed to add WiFi device',
  'refreshing': 'Refreshing',
  'deviceRefreshed': 'Device data updated',
  'errorRefreshingDevice': 'Failed to refresh device data',
  'errorUpdatingPosition': 'Failed to update device position',
  'deviceDetails': 'WiFi Device Details',
  'ap.actions': 'Actions',
  'snmpData': 'SNMP Data',
  'error': 'Error',
  'success': 'Success',
}