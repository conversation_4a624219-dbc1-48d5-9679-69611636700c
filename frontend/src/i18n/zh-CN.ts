export const zhCN = {
  // 导航菜单
  'dashboard': '仪表盘',
  'resources': '资源管理',
  'network': '网络管理',
  'wireless': '无线网络',
  'assets': '资产管理',
  'asset.map': '资产地图',
  'asset.list': '资产清单',
  'stacks': '技术栈',
  'deployments': '部署管理',
  'procedures': '运维流程',
  'notifications': '通知中心',
  'updates': '系统更新',
  'settings': '系统设置',

  // 机柜空间状态
  'space.sufficient': '空间充足',
  'space.limited': '空间有限',
  'space.critical': '空间紧张',

  // 仪表盘状态
  'total.devices': '设备总数',
  'normal.running': '正常运行',
  'warning.devices': '告警设备',
  'maintenance.devices': '维护设备',
  'system.status': '系统状态',
  'environment.monitoring': '环境监控',
  'core.services': '核心服务',
  'temperature': '温度',
  'power': '功率',
  'CPU': 'CPU使用率',
  'memory': '内存使用率',

  // 资源管理页面
  'datacenter.layout': '数据中心布局',
  'cold.aisle': '冷通道 1 运维组',
  'rack.layout': '机柜布局',
  'rack.status': '机柜状态',
  'rack.number': '机柜编号',
  'rack.space': '机柜空间',
  'rack.power': '机柜功率',
  'rack.temperature': '机柜温度',
  'rack.humidity': '机柜湿度',
  'device.count': '设备数量',
  'device.status': '设备状态',
  'device.type': '设备类型',
  'device.model': '设备型号',
  'device.vendor': '设备厂商',
  'device.location': '设备位置',
  'device.ip': 'IP地址',
  'device.mac': 'MAC地址',
  'device.sn': '序列号',
  'device.os': '操作系统',
  'device.cpu': 'CPU',
  'device.memory': '内存',
  'device.disk': '磁盘',
  'device.network': '网络',

  // 网络管理子菜单
  'network.fault.impact': '故障影响分析',
  'network.path.visualization': '网络路径可视化',
  'terminal.info': '终端信息管理',
  'network.config.backup': '网络配置备份',
  'config.management': '配置管理',
  'digital.ip.management': '数字IP管理',
  'phone.extension.management': '电话分机管理',
  'vm.management': '虚拟机管理',
  'printer.management': '打印机管理',
  'bandwidth.monitoring': '网络带宽监控',

  // 服务状态
  'service.running': '运行中',
  'service.stopped': '已停止',
  'service.error': '错误',
  'service.warning': '警告',
  'service.unknown': '未知',

  // 通用操作
  'view': '查看',
  'edit': '编辑',
  'delete': '删除',
  'add': '添加',
  'search': '搜索',
  'filter': '筛选',
  'sort': '排序',
  'refresh': '刷新',
  'save': '保存',
  'cancel': '取消',
  'confirm': '确认',
  'back': '返回',
  'next': '下一步',
  'previous': '上一步',
  'finish': '完成',

  // 主题设置
  'theme': '主题设置',
  'dark.mode': '深色模式',
  'light.mode': '浅色模式',
  'dark.mode.enabled': '已启用深色模式',
  'dark.mode.disabled': '已启用浅色模式',

  // 语言设置
  'language': '语言设置',
  'select.language': '选择语言',

  // 状态文本
  'status.online': '在线',
  'status.offline': '离线',
  'status.error': '错误',
  'status.warning': '警告',
  'status.normal': '正常',
  'status.unknown': '未知',
  'status.maintenance': '维护中',

  // 机柜布局和设备管理
  'available': '可用空间',
  'rack': '机柜',
  'add.device': '添加设备',
  'edit.device': '编辑设备',
  'device.name': '设备名称',
  'type.server': '服务器',
  'type.network': '网络设备',
  'type.storage': '存储设备',
  'type.other': '其他设备',
  'start.position': '起始位置',
  'u.size': 'U位大小',
  'manufacturer': '制造商',
  'model': '型号',
  'serial.number': '序列号',
  'description': '描述',
  'confirm.delete': '确认删除',
  'delete.confirmation': '确定要删除',
  'no.devices': '暂无设备',
  'device.monitoring': '设备监控',
  'performance.anomaly': '性能异常',
  'device.fault': '设备故障',
  'idle': '空闲',
  'standard.rack': '标准机柜',
  'online': '已上线',

  // 机柜悬停提示
  '总容量': '总容量',
  '已使用': '已使用',
  '剩余': '剩余',
  '空间': '空间',
  '型号': '型号',
  '位置': '位置',
  'IP地址': 'IP地址',
  '温度': '温度',
  '功率': '功率',
  '列间空调': '列间空调',
  '配电柜': '配电柜',

  // 无线网络页面
  'ap.location.map': 'AP位置图',
  'ap.name': 'AP名称',
  'ap.number': 'AP编号',
  'ap.status': 'AP状态',
  'ap.online': '在线',
  'ap.offline': '离线',
  'ap.warning': '告警',
  'ap.floor': '楼层',
  'ap.location': '位置',
  'ap.model': '型号',
  'ap.ip': 'IP地址',
  'ap.mac': 'MAC地址',
  'ap.signal': '信号强度',
  'ap.clients': '连接终端数',
  'ap.channel': '信道',
  'ap.bandwidth': '带宽',
  'ap.power': '发射功率',
  'clickToAddDevice': '点击添加WiFi设备',
  'addDevice': '添加WiFi设备',
  'confirmAddDevice': '确认添加新的WiFi设备？',
  'deviceAdded': 'WiFi设备已添加',
  'errorAddingDevice': '添加WiFi设备失败',
  'refreshing': '刷新中',
  'deviceRefreshed': '设备数据已更新',
  'errorRefreshingDevice': '刷新设备数据失败',
  'errorUpdatingPosition': '更新设备位置失败',
  'deviceDetails': 'WiFi设备详情',
  'ap.actions': '操作',
  'snmpData': 'SNMP数据',
  'error': '错误',
  'success': '成功',
}