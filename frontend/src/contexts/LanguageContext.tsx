'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react'

// 导入翻译文件
const zhCN = {
  // 导航菜单
  'dashboard': '仪表盘',
  'resources': '资源管理',
  'network': '网络管理',
  'wireless': '无线网络',
  // 其他翻译...
}

const enUS = {
  // 导航菜单
  'dashboard': 'Dashboard',
  'resources': 'Resources Management',
  'network': 'Network Management',
  'wireless': 'Wireless Network',
  // 其他翻译...
}

// 支持的语言
export type Language = 'zh-CN' | 'en-US'

// 语言上下文接口
interface LanguageContextType {
  currentLanguage: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

// 创建上下文
const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// 翻译数据类型
interface Translations {
  [key: string]: {
    [key: string]: string
  }
}

// 翻译键值对
const translations: Translations = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 提供者组件
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('zh-CN')

  // 初始化时从localStorage读取语言设置
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && (savedLanguage === 'zh-CN' || savedLanguage === 'en-US')) {
      setCurrentLanguage(savedLanguage)
    }
  }, [])

  // 改变语言并保存到localStorage
  const handleSetLanguage = (lang: Language) => {
    setCurrentLanguage(lang)
    localStorage.setItem('language', lang)
  }

  // 翻译函数
  const t = useCallback((key: string): string => {
    if (!key) return '';

    try {
      const translation = translations[currentLanguage];
      if (!translation) return key;

      return translation[key] || key;
    } catch (error) {
      console.error('Translation error:', error);
      return key;
    }
  }, [currentLanguage])

  return (
    <LanguageContext.Provider value={{ currentLanguage, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

// 自定义钩子，用于在组件中使用语言上下文
export const useTranslation = (): LanguageContextType => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useTranslation must be used within a LanguageProvider')
  }
  return context
}