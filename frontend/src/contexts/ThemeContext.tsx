'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 主题上下文接口
interface ThemeContextType {
  isDarkMode: boolean
  toggleTheme: () => void
}

// 创建上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// 提供者组件
export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [isDarkMode, setIsDarkMode] = useState(false)

  // 初始化时从localStorage获取主题设置
  useEffect(() => {
    // 检查localStorage
    const storedTheme = localStorage.getItem('theme')

    // 检查系统偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    // 如果localStorage中有设置，使用它；否则使用系统偏好
    const initialDarkMode = storedTheme
      ? storedTheme === 'dark'
      : prefersDark

    setIsDarkMode(initialDarkMode)

    // 应用主题到document
    if (initialDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [])

  // 切换主题
  const toggleTheme = () => {
    setIsDarkMode(prev => {
      const newDarkMode = !prev

      // 保存到localStorage
      localStorage.setItem('theme', newDarkMode ? 'dark' : 'light')

      // 应用到document
      if (newDarkMode) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }

      return newDarkMode
    })
  }

  return (
    <ThemeContext.Provider value={{ isDarkMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

// 自定义钩子，用于在组件中使用主题上下文
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}