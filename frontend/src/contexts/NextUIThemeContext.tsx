'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 主题上下文接口
interface NextUIThemeContextType {
  isDarkMode: boolean
  toggleTheme: () => void
}

// 创建上下文
const NextUIThemeContext = createContext<NextUIThemeContextType | undefined>(undefined)

// 提供者组件
export const NextUIThemeProvider = ({ children }: { children: ReactNode }) => {
  const [isDarkMode, setIsDarkMode] = useState(false)
  
  // 初始化时设置暗黑模式状态
  useEffect(() => {
    // 获取存储的主题
    const storedTheme = localStorage.getItem('theme-mode');
    
    // 如果有存储的主题，使用它
    if (storedTheme) {
      setIsDarkMode(storedTheme === 'dark');
    } 
    // 否则，检查系统偏好
    else {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(prefersDark);
    }
  }, [])
  
  // 切换主题并保存到localStorage
  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    
    // 更新localStorage
    localStorage.setItem('theme-mode', newMode ? 'dark' : 'light');
    
    // 更新HTML类
    if (newMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }
  
  // 初始化时设置HTML类
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);
  
  return (
    <NextUIThemeContext.Provider value={{ isDarkMode, toggleTheme }}>
      {children}
    </NextUIThemeContext.Provider>
  )
}

// 使用主题的Hook
export const useNextUITheme = () => {
  const context = useContext(NextUIThemeContext)
  if (context === undefined) {
    throw new Error('useNextUITheme must be used within a NextUIThemeProvider')
  }
  return context
}
