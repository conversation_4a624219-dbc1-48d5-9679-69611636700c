import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Grid,
  GridItem,
  Heading,
  Input,
  InputGroup,
  InputLeftElement,
  List,
  ListItem,
  Text,
  Flex,
  useColorModeValue,
  VStack,
  Badge,
  Skeleton,
} from '@chakra-ui/react'
import { SearchIcon } from '@chakra-ui/icons'
import { useTranslation } from 'react-i18next'
import DeviceDetail, { Device } from '../components/DeviceDetail'
import { Port } from '../components/PortList'

// Mock data for demonstration
const mockDevices: Device[] = [
  {
    id: '1',
    name: 'Core Switch 01',
    model: 'Cisco Catalyst 9300',
    serialNumber: 'ABC123456',
    ipAddress: '***********',
    location: 'Main Data Center',
    status: 'online',
    lastSeen: '2023-10-01T10:30:00Z',
    firmware: 'v16.9.4',
    ports: [
      { id: '1', name: 'GigabitEthernet1/0/1', type: 'Ethernet', speed: '1Gbps', status: 'up', connectedTo: 'Access Switch 01' },
      { id: '2', name: 'GigabitEthernet1/0/2', type: 'Ethernet', speed: '1Gbps', status: 'up', connectedTo: 'Access Switch 02' },
      { id: '3', name: 'GigabitEthernet1/0/3', type: 'Ethernet', speed: '1Gbps', status: 'down', connectedTo: '' },
      { id: '4', name: 'GigabitEthernet1/0/4', type: 'Ethernet', speed: '1Gbps', status: 'up', connectedTo: 'Server Rack 01' },
    ]
  },
  {
    id: '2',
    name: 'Edge Router 01',
    model: 'Cisco ASR 1000',
    serialNumber: 'DEF789012',
    ipAddress: '*************',
    location: 'Main Data Center',
    status: 'online',
    lastSeen: '2023-10-01T10:35:00Z',
    firmware: 'v16.9.4',
    ports: [
      { id: '1', name: 'GigabitEthernet0/0/0', type: 'Ethernet', speed: '1Gbps', status: 'up', connectedTo: 'ISP Link' },
      { id: '2', name: 'GigabitEthernet0/0/1', type: 'Ethernet', speed: '1Gbps', status: 'up', connectedTo: 'Core Switch 01' },
      { id: '3', name: 'GigabitEthernet0/0/2', type: 'Ethernet', speed: '1Gbps', status: 'disabled', connectedTo: '' },
    ]
  },
  {
    id: '3',
    name: 'Access Switch 01',
    model: 'Cisco Catalyst 2960',
    serialNumber: 'GHI345678',
    ipAddress: '***********',
    location: 'Floor 1',
    status: 'offline',
    lastSeen: '2023-09-30T22:10:00Z',
    firmware: 'v15.2',
    ports: [
      { id: '1', name: 'FastEthernet0/1', type: 'Ethernet', speed: '100Mbps', status: 'down', connectedTo: '' },
      { id: '2', name: 'FastEthernet0/2', type: 'Ethernet', speed: '100Mbps', status: 'down', connectedTo: '' },
      { id: '3', name: 'GigabitEthernet0/1', type: 'Ethernet', speed: '1Gbps', status: 'down', connectedTo: 'Core Switch 01' },
    ]
  },
  {
    id: '4',
    name: 'Access Switch 02',
    model: 'Cisco Catalyst 2960',
    serialNumber: 'JKL901234',
    ipAddress: '***********',
    location: 'Floor 2',
    status: 'maintenance',
    lastSeen: '2023-10-01T09:15:00Z',
    firmware: 'v15.2',
    ports: [
      { id: '1', name: 'FastEthernet0/1', type: 'Ethernet', speed: '100Mbps', status: 'up', connectedTo: 'Workstation 2A' },
      { id: '2', name: 'FastEthernet0/2', type: 'Ethernet', speed: '100Mbps', status: 'up', connectedTo: 'Workstation 2B' },
      { id: '3', name: 'GigabitEthernet0/1', type: 'Ethernet', speed: '1Gbps', status: 'up', connectedTo: 'Core Switch 01' },
    ]
  }
]

const DevicesPage: React.FC = () => {
  const { t } = useTranslation()
  const [devices, setDevices] = useState<Device[]>([])
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([])
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const hoverBgColor = useColorModeValue('gray.50', 'gray.700')
  const selectedBgColor = useColorModeValue('blue.50', 'blue.900')

  useEffect(() => {
    // Simulate API call
    const fetchDevices = async () => {
      setIsLoading(true)
      try {
        // In a real app, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        setDevices(mockDevices)
        setFilteredDevices(mockDevices)
      } catch (error) {
        console.error('Error fetching devices:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDevices()
  }, [])

  useEffect(() => {
    if (searchQuery) {
      const filtered = devices.filter(device => 
        device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.ipAddress.includes(searchQuery) ||
        device.model.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredDevices(filtered)
    } else {
      setFilteredDevices(devices)
    }
  }, [searchQuery, devices])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  const handleDeviceSelect = (device: Device) => {
    setSelectedDevice(device)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green'
      case 'offline':
        return 'red'
      case 'maintenance':
        return 'orange'
      default:
        return 'gray'
    }
  }

  const renderDeviceSkeletons = () => {
    return Array(4).fill(0).map((_, i) => (
      <Skeleton key={i} height="60px" mb={2} borderRadius="md" />
    ))
  }

  return (
    <Container maxW="container.xl" py={6}>
      <Heading mb={6}>{t('devices')}</Heading>
      
      <Grid templateColumns={{ base: '1fr', md: '350px 1fr' }} gap={6}>
        <GridItem>
          <VStack align="stretch" spacing={4}>
            <InputGroup>
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="gray.400" />
              </InputLeftElement>
              <Input
                placeholder={t('search_devices')}
                value={searchQuery}
                onChange={handleSearch}
                borderColor={borderColor}
              />
            </InputGroup>
            
            <Box 
              borderWidth="1px" 
              borderRadius="lg" 
              borderColor={borderColor}
              bg={bgColor}
              height="calc(100vh - 220px)"
              overflowY="auto"
            >
              {isLoading ? (
                <Box p={4}>
                  {renderDeviceSkeletons()}
                </Box>
              ) : filteredDevices.length > 0 ? (
                <List spacing={0}>
                  {filteredDevices.map(device => (
                    <ListItem 
                      key={device.id}
                      p={3}
                      cursor="pointer"
                      bg={selectedDevice?.id === device.id ? selectedBgColor : 'transparent'}
                      _hover={{ bg: selectedDevice?.id !== device.id ? hoverBgColor : selectedBgColor }}
                      borderBottomWidth="1px"
                      borderBottomColor={borderColor}
                      onClick={() => handleDeviceSelect(device)}
                    >
                      <Flex justify="space-between" align="center">
                        <Box>
                          <Text fontWeight="medium">{device.name}</Text>
                          <Text fontSize="sm" color="gray.500">{device.ipAddress}</Text>
                        </Box>
                        <Badge colorScheme={getStatusColor(device.status)}>
                          {t(device.status)}
                        </Badge>
                      </Flex>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Box p={4} textAlign="center">
                  <Text>{t('no_devices_found')}</Text>
                </Box>
              )}
            </Box>
          </VStack>
        </GridItem>
        
        <GridItem>
          <DeviceDetail 
            device={selectedDevice} 
            isLoading={isLoading} 
          />
        </GridItem>
      </Grid>
    </Container>
  )
}

export default DevicesPage 