import { useEffect } from 'react';
import type { AppProps } from 'next/app';
import { ChakraProvider } from '@chakra-ui/react';
import { NotificationProvider } from '../contexts/NotificationContext';
import { ThemeProvider } from '../contexts/ThemeContext';
import { checkNotificationPermission, registerServiceWorker } from '../services/notificationService';

function MyApp({ Component, pageProps }: AppProps) {
  // 注册Service Worker和请求通知权限
  useEffect(() => {
    const initializeApp = async () => {
      // 注册Service Worker
      await registerServiceWorker();

      // 请求通知权限
      await checkNotificationPermission();
    };

    initializeApp();
  }, []);

  return (
    <ChakraProvider>
      <ThemeProvider>
        <NotificationProvider>
          <Component {...pageProps} />
        </NotificationProvider>
      </ThemeProvider>
    </ChakraProvider>
  );
}

export default MyApp;
