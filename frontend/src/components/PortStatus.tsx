import React from 'react'
import { Box, Tooltip, useColorModeValue } from '@chakra-ui/react'

interface PortStatusProps {
  status: 'connected' | 'disconnected' | 'error' | 'disabled'
}

const PortStatus: React.FC<PortStatusProps> = ({ status }) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'connected':
        return { color: 'green.500', label: '已连接' }
      case 'disconnected':
        return { color: 'gray.400', label: '未连接' }
      case 'error':
        return { color: 'red.500', label: '错误' }
      case 'disabled':
        return { color: 'yellow.500', label: '已禁用' }
      default:
        return { color: 'gray.400', label: '未知' }
    }
  }

  const { color, label } = getStatusInfo()
  const bgLight = useColorModeValue(`${color.split('.')[0]}.100`, `${color.split('.')[0]}.900`)

  return (
    <Tooltip label={label}>
      <Box
        display="inline-block"
        w="12px"
        h="12px"
        borderRadius="50%"
        bg={color}
        boxShadow={`0 0 0 4px ${bgLight}`}
      />
    </Tooltip>
  )
}

export default PortStatus 