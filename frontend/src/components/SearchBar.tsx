import {
  InputGroup,
  InputLeftElement,
  Input,
  useColorMode,
  Box,
  Text
} from '@chakra-ui/react'
import { MagnifyingGlass } from '@phosphor-icons/react'

interface SearchBarProps {
  placeholder?: string
  onSearch?: (value: string) => void
}

const SearchBar = ({ placeholder = 'Search', onSearch }: SearchBarProps) => {
  const { colorMode } = useColorMode()

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSearch) {
      onSearch(e.target.value)
    }
  }

  return (
    <Box position="relative">
      <InputGroup size="md">
        <InputLeftElement
          pointerEvents="none"
          children={
            <MagnifyingGlass 
              size={20} 
              color={colorMode === 'dark' ? '#718096' : '#4A5568'} 
            />
          }
        />
        <Input
          pl="40px"
          type="text"
          placeholder={placeholder}
          onChange={handleSearch}
          bg={colorMode === 'dark' ? 'gray.800' : 'gray.50'}
          border="1px"
          borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
          _placeholder={{ 
            color: colorMode === 'dark' ? 'gray.500' : 'gray.400',
            fontSize: 'sm'
          }}
          _hover={{
            borderColor: colorMode === 'dark' ? 'gray.600' : 'gray.300'
          }}
          _focus={{
            borderColor: 'komodo.green',
            boxShadow: 'none'
          }}
          fontSize="sm"
          borderRadius="md"
        />
      </InputGroup>
      <Text
        position="absolute"
        right="10px"
        top="50%"
        transform="translateY(-50%)"
        fontSize="xs"
        color="gray.500"
        pointerEvents="none"
      >
        shift + s
      </Text>
    </Box>
  )
}

export default SearchBar 