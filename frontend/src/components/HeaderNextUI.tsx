'use client'

import {
  Navbar,
  Navbar<PERSON>ontent,
  Input,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Avatar,
  Tooltip,
  Badge,
} from '@nextui-org/react'
import { MagnifyingGlass, Bell, User, Sun, Moon } from '@phosphor-icons/react'
import { useTranslation } from '@/contexts/LanguageContext'
import { useNextUITheme } from '@/contexts/NextUIThemeContext'

const HeaderNextUI = () => {
  const { t } = useTranslation()
  const { isDarkMode, toggleTheme } = useNextUITheme()

  return (
    <Navbar
      className="bg-white dark:bg-komodo-darkGray border-b border-gray-200 dark:border-gray-700 py-2 px-6 w-full shadow-sm sticky top-0 z-50"
      maxWidth="full"
      isBordered
    >
      <NavbarContent className="gap-4 justify-end">
        <div className="flex items-center gap-4">
          <Input
            classNames={{
              base: "max-w-[300px]",
              inputWrapper: "h-8 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600",
            }}
            placeholder={t('search')}
            size="sm"
            startContent={<MagnifyingGlass className="text-gray-500 dark:text-gray-400" size={16} />}
            type="search"
            variant="filled"
          />

          <Tooltip content={isDarkMode ? t('light_mode') : t('dark_mode')}>
            <Button
              isIconOnly
              aria-label={isDarkMode ? t('light_mode') : t('dark_mode')}
              className="text-gray-600 dark:text-yellow-200"
              onClick={toggleTheme}
              size="sm"
              variant="light"
            >
              {isDarkMode ? <Sun size={18} weight="fill" /> : <Moon size={18} weight="fill" />}
            </Button>
          </Tooltip>

          <Tooltip content={t('notification')}>
            <Button
              isIconOnly
              aria-label={t('notification')}
              className="text-gray-600 dark:text-gray-300 relative"
              size="sm"
              variant="light"
            >
              <Bell size={18} weight="fill" />
              <Badge 
                content="" 
                color="danger" 
                size="sm" 
                className="absolute top-1 right-1 min-w-unit-2 min-h-unit-2 p-0"
              />
            </Button>
          </Tooltip>

          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <Button
                className="px-2 text-gray-700 dark:text-gray-300"
                size="sm"
                variant="light"
                startContent={
                  <Avatar 
                    size="sm" 
                    name="User"
                    className="w-6 h-6 bg-komodo-green text-white"
                  />
                }
              >
                <span className="hidden md:block text-sm">{t('user')}</span>
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="User Actions">
              <DropdownItem key="profile">{t('profile')}</DropdownItem>
              <DropdownItem key="settings">{t('settings')}</DropdownItem>
              <DropdownItem key="logout" className="text-danger">{t('logout')}</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </NavbarContent>
    </Navbar>
  )
}

export default HeaderNextUI
