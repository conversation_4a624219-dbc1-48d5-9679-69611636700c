<template>
  <div class="qrcode-container">
    <div class="qrcode-wrapper">
      <div ref="qrcodeRef" class="qrcode"></div>
      <div class="equipment-info">
        <div class="equipment-name">{{ equipment.name }}</div>
        <div class="equipment-code">{{ equipment.code }}</div>
        <div v-if="showExtendedInfo" class="equipment-extended-info">
          <div v-if="equipment.type" class="info-item">类型: {{ equipment.type }}</div>
          <div v-if="equipment.model" class="info-item">型号: {{ equipment.model }}</div>
          <div v-if="equipment.department" class="info-item">部门: {{ equipment.department }}</div>
          <div v-if="equipment.status" class="info-item">状态: {{ equipment.status }}</div>
        </div>
      </div>
    </div>
    <div class="qrcode-options">
      <el-checkbox v-model="showExtendedInfo" label="显示更多信息" @change="generateQRCode" />
      <el-select v-model="printSize" placeholder="打印尺寸" size="small" style="width: 120px; margin-left: 10px;">
        <el-option label="小尺寸 (40mm)" value="small" />
        <el-option label="中等 (60mm)" value="medium" />
        <el-option label="大尺寸 (80mm)" value="large" />
      </el-select>
    </div>
    <div class="actions">
      <el-button type="primary" size="small" @click="downloadQRCode">下载二维码</el-button>
      <el-button size="small" @click="printQRCode">打印二维码</el-button>
      <el-button type="success" size="small" @click="printMultiple">批量打印</el-button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import QRCode from 'qrcode'

export default {
  name: 'EquipmentQRCode',
  props: {
    equipment: {
      type: Object,
      required: true
    },
    equipmentList: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const qrcodeRef = ref(null)
    let qrcode = null
    const showExtendedInfo = ref(false)
    const printSize = ref('medium')
    
    // 生成二维码
    const generateQRCode = () => {
      if (qrcodeRef.value) {
        // 清除之前的二维码
        if (qrcodeRef.value.innerHTML) {
          qrcodeRef.value.innerHTML = ''
        }
        
        // 生成设备信息URL，实际使用时应该是访问设备详情的完整URL
        const equipmentUrl = window.location.origin + '/equipment/detail/' + props.equipment.id
        
        // 如果显示扩展信息，将额外信息编码到URL中
        let finalUrl = equipmentUrl
        if (showExtendedInfo.value) {
          finalUrl = equipmentUrl + '?extended=true'
          if (props.equipment.name) finalUrl += '&name=' + encodeURIComponent(props.equipment.name)
          if (props.equipment.code) finalUrl += '&code=' + encodeURIComponent(props.equipment.code)
          if (props.equipment.type) finalUrl += '&type=' + encodeURIComponent(props.equipment.type)
          if (props.equipment.model) finalUrl += '&model=' + encodeURIComponent(props.equipment.model)
          if (props.equipment.department) finalUrl += '&department=' + encodeURIComponent(props.equipment.department)
          if (props.equipment.status) finalUrl += '&status=' + encodeURIComponent(props.equipment.status)
        }
        
        // 创建新的二维码
        qrcode = new QRCode(qrcodeRef.value, {
          text: finalUrl,
          width: 180,
          height: 180,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        })
      }
    }
    
    // 下载二维码
    const downloadQRCode = () => {
      if (!qrcodeRef.value) return
      
      const canvas = qrcodeRef.value.querySelector('canvas')
      if (canvas) {
        const url = canvas.toDataURL('image/png')
        const a = document.createElement('a')
        a.download = '设备二维码_' + (props.equipment.code || '') + '.png'
        a.href = url
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    }
    
    // 打印二维码
    const printQRCode = () => {
      const printWindow = window.open('', '_blank')
      const canvas = qrcodeRef.value.querySelector('canvas')
      
      if (canvas && printWindow) {
        const url = canvas.toDataURL('image/png')
        const equipName = props.equipment.name || ''
        const equipCode = props.equipment.code || ''
        const equipType = props.equipment.type || ''
        const equipModel = props.equipment.model || ''
        const equipDepartment = props.equipment.department || ''
        const equipStatus = props.equipment.status || ''
        
        // 根据选择的尺寸设置样式
        let qrSize, fontSize, containerWidth
        switch(printSize.value) {
          case 'small':
            qrSize = '40'
            fontSize = '10px'
            containerWidth = '45'
            break
          case 'large':
            qrSize = '80'
            fontSize = '14px'
            containerWidth = '85'
            break
          default: // medium
            qrSize = '60'
            fontSize = '12px'
            containerWidth = '65'
        }
        
        // 使用document.write创建打印页面
        printWindow.document.write('<!DOCTYPE html>')
        printWindow.document.write('<html>')
        printWindow.document.write('<head>')
        printWindow.document.write('<title>设备二维码 - ' + equipName + '</title>')
        printWindow.document.write('<style>')
        printWindow.document.write(
          '@page { size: ' + containerWidth + 'mm auto; margin: 0; }' +
          'body { margin: 0; display: flex; justify-content: center; align-items: center; height: 100vh; }' +
          '.print-container { text-align: center; width: ' + containerWidth + 'mm; padding: 5mm; box-sizing: border-box; page-break-after: always; }' +
          '.qr-image { width: ' + qrSize + 'mm; height: ' + qrSize + 'mm; }' +
          '.equipment-info { margin-top: 3mm; font-family: Arial, sans-serif; }' +
          '.equipment-name { font-size: ' + fontSize + '; font-weight: bold; }' +
          '.equipment-code { font-size: ' + fontSize + '; color: #666; margin-top: 1mm; }' +
          '.equipment-extended-info { margin-top: 2mm; font-size: ' + fontSize + '; color: #333; }' +
          '.info-item { margin-top: 0.5mm; }'
        )
        printWindow.document.write('</style>')
        printWindow.document.write('<script>')
        printWindow.document.write('setTimeout(function() { window.print(); window.close(); }, 500);')
        printWindow.document.write('<\/script>')
        printWindow.document.write('</head>')
        printWindow.document.write('<body>')
        printWindow.document.write('<div class="print-container">')
        printWindow.document.write('<img class="qr-image" src="' + url + '" />')
        printWindow.document.write('<div class="equipment-info">')
        printWindow.document.write('<div class="equipment-name">' + equipName + '</div>')
        printWindow.document.write('<div class="equipment-code">' + equipCode + '</div>')
        
        // 如果显示扩展信息，则添加到打印页面
        if (showExtendedInfo.value) {
          printWindow.document.write('<div class="equipment-extended-info">')
          if (equipType) printWindow.document.write('<div class="info-item">类型: ' + equipType + '</div>')
          if (equipModel) printWindow.document.write('<div class="info-item">型号: ' + equipModel + '</div>')
          if (equipDepartment) printWindow.document.write('<div class="info-item">部门: ' + equipDepartment + '</div>')
          if (equipStatus) printWindow.document.write('<div class="info-item">状态: ' + equipStatus + '</div>')
          printWindow.document.write('</div>')
        }
        
        printWindow.document.write('</div>')
        printWindow.document.write('</div>')
        printWindow.document.write('</body>')
        printWindow.document.write('</html>')
        
        printWindow.document.close()
      }
    }
    
    // 批量打印二维码
    const printMultiple = () => {
      const printWindow = window.open('', '_blank')
      const canvas = qrcodeRef.value.querySelector('canvas')
      
      if (!canvas || !printWindow) return
      
      // 获取当前设备的二维码图像
      const currentEquipUrl = canvas.toDataURL('image/png')
      
      // 根据选择的尺寸设置样式
      let qrSize, fontSize, containerWidth
      switch(printSize.value) {
        case 'small':
          qrSize = '40'
          fontSize = '10px'
          containerWidth = '45'
          break
        case 'large':
          qrSize = '80'
          fontSize = '14px'
          containerWidth = '85'
          break
        default: // medium
          qrSize = '60'
          fontSize = '12px'
          containerWidth = '65'
      }
      
      // 创建打印页面头部
      printWindow.document.write('<!DOCTYPE html>')
      printWindow.document.write('<html>')
      printWindow.document.write('<head>')
      printWindow.document.write('<title>批量打印设备二维码</title>')
      printWindow.document.write('<style>')
      printWindow.document.write(
        '@page { size: ' + containerWidth + 'mm auto; margin: 0; }' +
        'body { margin: 0; font-family: Arial, sans-serif; }' +
        '.print-container { text-align: center; width: ' + containerWidth + 'mm; padding: 5mm; box-sizing: border-box; page-break-after: always; }' +
        '.qr-image { width: ' + qrSize + 'mm; height: ' + qrSize + 'mm; }' +
        '.equipment-info { margin-top: 3mm; }' +
        '.equipment-name { font-size: ' + fontSize + '; font-weight: bold; }' +
        '.equipment-code { font-size: ' + fontSize + '; color: #666; margin-top: 1mm; }' +
        '.equipment-extended-info { margin-top: 2mm; font-size: ' + fontSize + '; color: #333; }' +
        '.info-item { margin-top: 0.5mm; }'
      )
      printWindow.document.write('</style>')
      
      printWindow.document.write('<script>')
      printWindow.document.write('setTimeout(function() { window.print(); window.close(); }, 500);')
      printWindow.document.write('<\/script>')
      printWindow.document.write('</head>')
      printWindow.document.write('<body>')
      
      // 首先添加当前设备的二维码
      const currentEquip = props.equipment
      printWindow.document.write('<div class="print-container">')
      printWindow.document.write('<img class="qr-image" src="' + currentEquipUrl + '" />')
      printWindow.document.write('<div class="equipment-info">')
      printWindow.document.write('<div class="equipment-name">' + (currentEquip.name || '') + '</div>')
      printWindow.document.write('<div class="equipment-code">' + (currentEquip.code || '') + '</div>')
      
      if (showExtendedInfo.value) {
        printWindow.document.write('<div class="equipment-extended-info">')
        if (currentEquip.type) printWindow.document.write('<div class="info-item">类型: ' + currentEquip.type + '</div>')
        if (currentEquip.model) printWindow.document.write('<div class="info-item">型号: ' + currentEquip.model + '</div>')
        if (currentEquip.department) printWindow.document.write('<div class="info-item">部门: ' + currentEquip.department + '</div>')
        if (currentEquip.status) printWindow.document.write('<div class="info-item">状态: ' + currentEquip.status + '</div>')
        printWindow.document.write('</div>')
      }
      
      printWindow.document.write('</div>')
      printWindow.document.write('</div>')
      
      // 如果有设备列表，则添加列表中的其他设备（最多添加10个）
      const equipmentList = props.equipmentList || []
      const maxItems = Math.min(equipmentList.length, 10)
      
      for (let i = 0; i < maxItems; i++) {
        const equip = equipmentList[i]
        if (equip.id === currentEquip.id) continue // 跳过当前设备
        
        // 为每个设备创建一个URL
        const equipUrl = window.location.origin + '/equipment/detail/' + equip.id
        
        printWindow.document.write('<div class="print-container">')
        printWindow.document.write('<div class="qr-image" id="qrcode-' + i + '"></div>')
        printWindow.document.write('<div class="equipment-info">')
        printWindow.document.write('<div class="equipment-name">' + (equip.name || '') + '</div>')
        printWindow.document.write('<div class="equipment-code">' + (equip.code || '') + '</div>')
        
        if (showExtendedInfo.value) {
          printWindow.document.write('<div class="equipment-extended-info">')
          if (equip.type) printWindow.document.write('<div class="info-item">类型: ' + equip.type + '</div>')
          if (equip.model) printWindow.document.write('<div class="info-item">型号: ' + equip.model + '</div>')
          if (equip.department) printWindow.document.write('<div class="info-item">部门: ' + equip.department + '</div>')
          if (equip.status) printWindow.document.write('<div class="info-item">状态: ' + equip.status + '</div>')
          printWindow.document.write('</div>')
        }
        
        printWindow.document.write('</div>')
        printWindow.document.write('</div>')
        
        // 添加脚本来生成其他设备的二维码
        printWindow.document.write('<script>')
        printWindow.document.write(
          '(function() {' +
          'var qrcode = new QRCode(document.getElementById("qrcode-' + i + '"), {' +
          'text: "' + equipUrl + '",' +
          'width: ' + (parseInt(qrSize) * 3.78) + ',' +
          'height: ' + (parseInt(qrSize) * 3.78) + ',' +
          'colorDark: "#000000",' +
          'colorLight: "#ffffff",' +
          'correctLevel: QRCode.CorrectLevel.H' +
          '});' +
          '})();'
        )
        printWindow.document.write('<\/script>')
      }
      
      printWindow.document.write('</body>')
      printWindow.document.write('</html>')
      printWindow.document.close()
    }
    
    // 监听设备信息变化，重新生成二维码
    watch(() => props.equipment, () => {
      generateQRCode()
    }, { deep: true })
    
    onMounted(() => {
      generateQRCode()
    })
    
    return {
      qrcodeRef,
      showExtendedInfo,
      printSize,
      generateQRCode,
      downloadQRCode,
      printQRCode,
      printMultiple
    }
  }
}
</script>

<style scoped>
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.qrcode-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.qrcode {
  margin-bottom: 15px;
}

.equipment-info {
  text-align: center;
}

.equipment-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.equipment-code {
  font-size: 14px;
  color: #666;
}

.qrcode-options {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.actions {
  display: flex;
  gap: 10px;
}

.equipment-extended-info {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  text-align: left;
}

.info-item {
  margin-top: 2px;
}
</style>