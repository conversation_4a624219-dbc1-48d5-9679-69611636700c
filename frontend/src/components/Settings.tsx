import {
  Box,
  VStack,
  HStack,
  Text,
  Switch,
  Select,
  Button,
  useColorMode,
  Divider,
  Icon,
  Grid,
  GridItem
} from '@chakra-ui/react'
import { Moon, Sun, Globe, Bell, Lock, User, Gear } from '@phosphor-icons/react'
import { useTranslation, Language } from '@/contexts/LanguageContext'

export const Settings = () => {
  const { colorMode, toggleColorMode } = useColorMode()
  const { t, setLanguage } = useTranslation()

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as Language
    setLanguage(value)
  }

  return (
    <Box maxW="800px" mx="auto" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={6}>
            {t('settings')}
          </Text>
        </Box>

        <Grid templateColumns="repeat(2, 1fr)" gap={6}>
          {/* 外观设置 */}
          <GridItem
            p={6}
            borderWidth="1px"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            borderRadius="lg"
            bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          >
            <VStack align="stretch" spacing={4}>
              <HStack>
                <Icon as={colorMode === 'dark' ? Moon : Sun} fontSize="20" />
                <Text fontWeight="medium">{t('appearance')}</Text>
              </HStack>
              
              <HStack justify="space-between">
                <Text fontSize="sm">{t('darkMode')}</Text>
                <Switch
                  isChecked={colorMode === 'dark'}
                  onChange={toggleColorMode}
                  colorScheme="green"
                />
              </HStack>
            </VStack>
          </GridItem>

          {/* 语言设置 */}
          <GridItem
            p={6}
            borderWidth="1px"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            borderRadius="lg"
            bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          >
            <VStack align="stretch" spacing={4}>
              <HStack>
                <Icon as={Globe} fontSize="20" />
                <Text fontWeight="medium">{t('language')}</Text>
              </HStack>
              
              <Select
                size="sm"
                onChange={handleLanguageChange}
                defaultValue="zh-CN"
              >
                <option value="zh-CN">中文</option>
                <option value="en-US">English</option>
              </Select>
            </VStack>
          </GridItem>

          {/* 通知设置 */}
          <GridItem
            p={6}
            borderWidth="1px"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            borderRadius="lg"
            bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          >
            <VStack align="stretch" spacing={4}>
              <HStack>
                <Icon as={Bell} fontSize="20" />
                <Text fontWeight="medium">{t('notifications')}</Text>
              </HStack>
              
              <VStack align="stretch" spacing={3}>
                <HStack justify="space-between">
                  <Text fontSize="sm">{t('emailNotifications')}</Text>
                  <Switch defaultChecked colorScheme="green" />
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="sm">{t('pushNotifications')}</Text>
                  <Switch defaultChecked colorScheme="green" />
                </HStack>
              </VStack>
            </VStack>
          </GridItem>

          {/* 安全设置 */}
          <GridItem
            p={6}
            borderWidth="1px"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            borderRadius="lg"
            bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          >
            <VStack align="stretch" spacing={4}>
              <HStack>
                <Icon as={Lock} fontSize="20" />
                <Text fontWeight="medium">{t('security')}</Text>
              </HStack>
              
              <VStack align="stretch" spacing={3}>
                <Button size="sm" variant="outline">
                  {t('changePassword')}
                </Button>
                <Button size="sm" variant="outline">
                  {t('twoFactorAuth')}
                </Button>
              </VStack>
            </VStack>
          </GridItem>
        </Grid>

        <Box
          mt={8}
          p={6}
          borderWidth="1px"
          borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
          borderRadius="lg"
          bg={colorMode === 'dark' ? 'gray.800' : 'white'}
        >
          <VStack align="stretch" spacing={4}>
            <HStack>
              <Icon as={Gear} fontSize="20" />
              <Text fontWeight="medium">{t('advanced')}</Text>
            </HStack>
            
            <Divider />
            
            <VStack align="stretch" spacing={3}>
              <Button size="sm" variant="outline" colorScheme="red">
                {t('clearCache')}
              </Button>
              <Button size="sm" variant="outline" colorScheme="red">
                {t('resetSettings')}
              </Button>
            </VStack>
          </VStack>
        </Box>
      </VStack>
    </Box>
  )
} 