import React from 'react';
import { Box, Text } from '@chakra-ui/react';
import CustomSelect from './CustomSelect';

const SelectExample: React.FC = () => {
  return (
    <Box p={4}>
      <Text mb={2}>选择严重性级别：</Text>
      <CustomSelect placeholder="选择严重性级别">
        <option value="info">信息</option>
        <option value="warning">警告</option>
        <option value="error">错误</option>
        <option value="critical">严重</option>
      </CustomSelect>
    </Box>
  );
};

export default SelectExample;
