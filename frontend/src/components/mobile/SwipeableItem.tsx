import React, { ReactNode } from 'react';
import { Box, Flex, Text, IconButton, useColorModeValue } from '@chakra-ui/react';
import { useSwipeable } from 'react-swipeable';
import { DeleteIcon, EditIcon, ViewIcon } from '@chakra-ui/icons';

interface SwipeAction {
  icon: ReactNode;
  label: string;
  color: string;
  onClick: () => void;
}

interface SwipeableItemProps {
  children: ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeStart?: () => void;
  onSwipeEnd?: () => void;
  threshold?: number;
}

const SwipeableItem: React.FC<SwipeableItemProps> = ({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeStart,
  onSwipeEnd,
  threshold = 100
}) => {
  const [offset, setOffset] = React.useState(0);
  const [swiping, setSwiping] = React.useState(false);
  const [actionTriggered, setActionTriggered] = React.useState(false);
  
  const maxLeftOffset = leftActions.length * 80;
  const maxRightOffset = rightActions.length * -80;
  
  const bgColor = useColorModeValue('white', 'gray.800');
  
  const handlers = useSwipeable({
    onSwiping: (event) => {
      if (!swiping) {
        setSwiping(true);
        onSwipeStart?.();
      }
      
      // 限制滑动范围
      let newOffset = offset + event.deltaX;
      if (newOffset > maxLeftOffset) newOffset = maxLeftOffset;
      if (newOffset < maxRightOffset) newOffset = maxRightOffset;
      
      setOffset(newOffset);
    },
    onSwiped: (event) => {
      setSwiping(false);
      onSwipeEnd?.();
      
      // 如果滑动距离超过阈值，保持打开状态
      if (Math.abs(offset) > threshold) {
        // 向左滑动（显示右侧操作）
        if (offset < 0 && rightActions.length > 0) {
          const actionIndex = Math.min(
            Math.floor(Math.abs(offset) / 80),
            rightActions.length - 1
          );
          
          if (Math.abs(offset) > threshold * 2 && !actionTriggered) {
            // 触发操作
            rightActions[actionIndex].onClick();
            setActionTriggered(true);
            setTimeout(() => {
              setOffset(0);
              setActionTriggered(false);
            }, 300);
          } else {
            // 保持打开状态
            setOffset(actionIndex * -80 - 80);
          }
        }
        // 向右滑动（显示左侧操作）
        else if (offset > 0 && leftActions.length > 0) {
          const actionIndex = Math.min(
            Math.floor(offset / 80),
            leftActions.length - 1
          );
          
          if (offset > threshold * 2 && !actionTriggered) {
            // 触发操作
            leftActions[actionIndex].onClick();
            setActionTriggered(true);
            setTimeout(() => {
              setOffset(0);
              setActionTriggered(false);
            }, 300);
          } else {
            // 保持打开状态
            setOffset(actionIndex * 80 + 80);
          }
        } else {
          setOffset(0);
        }
      } else {
        // 回到原位
        setOffset(0);
      }
    },
    preventDefaultTouchmoveEvent: true,
    trackMouse: false
  });
  
  // 点击内容区域时关闭操作区
  const handleContentClick = () => {
    if (offset !== 0) {
      setOffset(0);
    }
  };
  
  return (
    <Box position="relative" overflow="hidden" width="100%">
      {/* 左侧操作区 */}
      <Flex
        position="absolute"
        left={0}
        top={0}
        bottom={0}
        zIndex={1}
        width={`${leftActions.length * 80}px`}
        height="100%"
      >
        {leftActions.map((action, index) => (
          <Flex
            key={`left-${index}`}
            width="80px"
            bg={action.color}
            color="white"
            alignItems="center"
            justifyContent="center"
            flexDirection="column"
            onClick={action.onClick}
            cursor="pointer"
          >
            {action.icon}
            <Text fontSize="xs" mt={1}>{action.label}</Text>
          </Flex>
        ))}
      </Flex>
      
      {/* 右侧操作区 */}
      <Flex
        position="absolute"
        right={0}
        top={0}
        bottom={0}
        zIndex={1}
        width={`${rightActions.length * 80}px`}
        height="100%"
      >
        {rightActions.map((action, index) => (
          <Flex
            key={`right-${index}`}
            width="80px"
            bg={action.color}
            color="white"
            alignItems="center"
            justifyContent="center"
            flexDirection="column"
            onClick={action.onClick}
            cursor="pointer"
          >
            {action.icon}
            <Text fontSize="xs" mt={1}>{action.label}</Text>
          </Flex>
        ))}
      </Flex>
      
      {/* 内容区域 */}
      <Box
        {...handlers}
        position="relative"
        zIndex={2}
        bg={bgColor}
        transform={`translateX(${offset}px)`}
        transition={swiping ? 'none' : 'transform 0.3s ease'}
        onClick={handleContentClick}
      >
        {children}
      </Box>
    </Box>
  );
};

export default SwipeableItem;
