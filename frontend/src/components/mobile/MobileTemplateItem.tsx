import React from 'react';
import { Box, Heading, Text, Badge, Flex, useColorModeValue, Menu, MenuButton, MenuList, MenuItem, IconButton } from '@chakra-ui/react';
import { DeleteIcon, EditIcon, ViewIcon, CheckIcon, RepeatIcon, ChevronDownIcon } from '@chakra-ui/icons';
import SwipeableItem from './SwipeableItem';
import { InspectionTemplate } from '../../store/templateStore';

interface MobileTemplateItemProps {
  template: InspectionTemplate;
  onEdit: () => void;
  onDelete: () => void;
  onExecute: () => void;
  onInspect: () => void;
  onFix?: () => void;
}

const MobileTemplateItem: React.FC<MobileTemplateItemProps> = ({
  template,
  onEdit,
  onDelete,
  onExecute,
  onInspect,
  onFix
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 左侧滑动操作
  const leftActions = [
    {
      icon: <EditIcon />,
      label: '编辑',
      color: 'blue.500',
      onClick: onEdit
    }
  ];

  // 右侧滑动操作
  const rightActions = [
    {
      icon: <ViewIcon />,
      label: '执行',
      color: 'teal.500',
      onClick: onExecute
    },
    {
      icon: <DeleteIcon />,
      label: '删除',
      color: 'red.500',
      onClick: onDelete
    }
  ];

  return (
    <SwipeableItem
      leftActions={leftActions}
      rightActions={rightActions}
    >
      <Box
        p={4}
        borderWidth="1px"
        borderColor={borderColor}
        borderRadius="lg"
        boxShadow="sm"
        bg={bgColor}
      >
        <Flex justify="space-between" align="center" mb={2}>
          <Heading size="md" noOfLines={1}>{template.name}</Heading>
          <Badge colorScheme="teal" fontSize="sm">
            {template.content?.length || 0} 项
          </Badge>
        </Flex>

        <Text fontSize="sm" color="gray.500" mb={2} noOfLines={2}>
          {template.description || '无描述'}
        </Text>

        <Flex fontSize="xs" color="gray.500" mb={1}>
          <Text>{Array.isArray(template.device_types) ? template.device_types.join(', ') : ''}</Text>
          <Text ml="auto">{template.created_at ? new Date(template.created_at).toLocaleDateString() : ""}</Text>
        </Flex>

        <Flex justify="space-between" align="center" mt={3}>
          <Text fontSize="xs" color="gray.400">
            左右滑动查看更多操作
          </Text>

          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="操作"
              icon={<ChevronDownIcon />}
              variant="ghost"
              size="sm"
            />
            <MenuList>
              <MenuItem icon={<EditIcon />} onClick={onEdit}>编辑</MenuItem>
              <MenuItem icon={<ViewIcon />} onClick={onExecute}>执行巡检</MenuItem>
              <MenuItem icon={<CheckIcon />} onClick={onInspect}>一键巡检</MenuItem>
              {onFix && <MenuItem icon={<RepeatIcon />} onClick={onFix}>一键修复</MenuItem>}
              <MenuItem icon={<DeleteIcon />} onClick={onDelete} color="red.500">删除</MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </Box>
    </SwipeableItem>
  );
};

export default MobileTemplateItem;
