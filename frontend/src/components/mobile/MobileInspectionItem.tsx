import React from 'react';
import {
  Box,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Checkbox,
  IconButton,
  Heading,
  Collapse,
  useDisclosure,
  Text
} from '@chakra-ui/react';
import { DeleteIcon, ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { useSwipeable } from 'react-swipeable';
import { InspectionItem } from '../../store/templateStore';

interface MobileInspectionItemProps {
  item: InspectionItem;
  idx: number;
  handleItemChange: (idx: number, key: keyof InspectionItem, value: any) => void;
  removeItem: (idx: number) => void;
  errors: { [key: string]: string };
}

const MobileInspectionItem: React.FC<MobileInspectionItemProps> = ({
  item,
  idx,
  handleItemChange,
  removeItem,
  errors
}) => {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });
  
  // 滑动删除功能
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      if (idx > 0) { // 防止删除第一个项目
        removeItem(idx);
      }
    },
    preventDefaultTouchmoveEvent: true,
    trackMouse: true
  });
  
  return (
    <Box 
      {...swipeHandlers}
      position="relative"
      overflow="hidden"
      bg="white"
      borderRadius="lg"
      boxShadow="md"
      mb={4}
      borderWidth="1px"
      borderColor={errors[`item_${idx}`] || errors[`check_command_${idx}`] || errors[`expected_result_${idx}`] ? "red.300" : "gray.200"}
    >
      {/* 头部 - 始终可见 */}
      <Flex 
        p={4} 
        justify="space-between" 
        align="center" 
        bg={isOpen ? "teal.50" : "white"}
        borderBottomWidth={isOpen ? "1px" : "0"}
        borderBottomColor="gray.200"
        onClick={onToggle}
        cursor="pointer"
      >
        <Flex align="center">
          <Text fontWeight="bold" fontSize="md" noOfLines={1}>
            {item.item || `检查项 #${idx + 1}`}
          </Text>
          {(errors[`item_${idx}`] || errors[`check_command_${idx}`] || errors[`expected_result_${idx}`]) && (
            <Box ml={2} color="red.500" fontSize="sm">
              ⚠️
            </Box>
          )}
        </Flex>
        <Flex>
          {idx > 0 && (
            <IconButton
              aria-label="删除检查项"
              icon={<DeleteIcon />}
              size="sm"
              colorScheme="red"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                removeItem(idx);
              }}
              mr={2}
            />
          )}
          {isOpen ? <ChevronUpIcon boxSize={6} /> : <ChevronDownIcon boxSize={6} />}
        </Flex>
      </Flex>
      
      {/* 详细内容 - 可折叠 */}
      <Collapse in={isOpen} animateOpacity>
        <Box p={4}>
          <FormControl isRequired isInvalid={!!errors[`item_${idx}`]} mb={3}>
            <FormLabel fontWeight="bold">检查内容</FormLabel>
            <Input 
              value={item.item} 
              onChange={e => handleItemChange(idx, "item", e.target.value)}
              bg="white"
              borderColor="gray.300"
            />
            {errors[`item_${idx}`] && <Box color="red.500" fontSize="sm" mt={1}>{errors[`item_${idx}`]}</Box>}
          </FormControl>
          
          <FormControl isRequired isInvalid={!!errors[`check_command_${idx}`]} mb={3}>
            <FormLabel fontWeight="bold">检查命令</FormLabel>
            <Input 
              value={item.check_command} 
              onChange={e => handleItemChange(idx, "check_command", e.target.value)}
              bg="white"
              borderColor="gray.300"
            />
            {errors[`check_command_${idx}`] && <Box color="red.500" fontSize="sm" mt={1}>{errors[`check_command_${idx}`]}</Box>}
          </FormControl>
          
          <FormControl isRequired isInvalid={!!errors[`expected_result_${idx}`]} mb={3}>
            <FormLabel fontWeight="bold">预期结果</FormLabel>
            <Input 
              value={item.expected_result} 
              onChange={e => handleItemChange(idx, "expected_result", e.target.value)}
              bg="white"
              borderColor="gray.300"
            />
            {errors[`expected_result_${idx}`] && <Box color="red.500" fontSize="sm" mt={1}>{errors[`expected_result_${idx}`]}</Box>}
          </FormControl>
          
          <FormControl mb={3}>
            <Checkbox 
              isChecked={item.auto_fix} 
              onChange={e => handleItemChange(idx, "auto_fix", e.target.checked)}
              colorScheme="teal"
            >
              允许自动修复
            </Checkbox>
          </FormControl>
          
          {item.auto_fix && (
            <FormControl isRequired={item.auto_fix} isInvalid={!!errors[`fix_command_${idx}`]} mb={3}>
              <FormLabel fontWeight="bold">修复命令</FormLabel>
              <Textarea 
                value={item.fix_command || ""} 
                onChange={e => handleItemChange(idx, "fix_command", e.target.value)}
                bg="white"
                borderColor="gray.300"
                rows={2}
              />
              {errors[`fix_command_${idx}`] && <Box color="red.500" fontSize="sm" mt={1}>{errors[`fix_command_${idx}`]}</Box>}
            </FormControl>
          )}
          
          <FormControl mb={1}>
            <FormLabel fontWeight="bold">备注</FormLabel>
            <Textarea 
              value={item.remark || ""} 
              onChange={e => handleItemChange(idx, "remark", e.target.value)}
              bg="white"
              borderColor="gray.300"
              rows={2}
            />
          </FormControl>
        </Box>
      </Collapse>
      
      {/* 滑动删除提示 */}
      <Box
        position="absolute"
        right={0}
        top={0}
        bottom={0}
        width="80px"
        bg="red.500"
        color="white"
        display="flex"
        alignItems="center"
        justifyContent="center"
        transform="translateX(100%)"
        transition="transform 0.2s"
        _groupHover={{ transform: "translateX(0)" }}
        pointerEvents="none"
      >
        <DeleteIcon mr={2} />
        <Text>删除</Text>
      </Box>
    </Box>
  );
};

export default MobileInspectionItem;
