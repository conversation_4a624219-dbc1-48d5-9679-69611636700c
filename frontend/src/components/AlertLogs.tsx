'use client'

import { useState, useEffect } from 'react'
import { AlertLog } from '@/types/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { Loader2, AlertTriangle, Info, AlertOctagon, CheckCircle2 } from 'lucide-react'

interface AlertLogsProps {
  deviceId?: number
}

export function AlertLogs({ deviceId }: AlertLogsProps) {
  const [logs, setLogs] = useState<AlertLog[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedLog, setSelectedLog] = useState<AlertLog | null>(null)

  const loadLogs = async () => {
    try {
      setLoading(true)
      const url = deviceId 
        ? `/api/alerts/logs?deviceId=${deviceId}`
        : '/api/alerts/logs'
      const response = await fetch(url)
      const data = await response.json()
      setLogs(data)
    } catch (err) {
      console.error('加载报警日志失败:', err)
      toast({
        title: "加载失败",
        description: "无法加载报警日志",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadLogs()
    // 每分钟刷新一次日志
    const interval = setInterval(loadLogs, 60000)
    return () => clearInterval(interval)
  }, [deviceId])

  const handleResolve = async (logId: number) => {
    try {
      const response = await fetch(`/api/alerts/logs/${logId}/resolve`, {
        method: 'POST'
      })
      
      if (response.ok) {
        toast({
          title: "处理成功",
          description: "报警已标记为已解决",
        })
        loadLogs()
      }
    } catch (err) {
      toast({
        title: "处理失败",
        description: "无法更新报警状态",
        variant: "destructive",
      })
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertOctagon className="h-4 w-4 text-destructive" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">严重</Badge>
      case 'warning':
        return <Badge variant="secondary">警告</Badge>
      default:
        return <Badge>信息</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>报警日志</CardTitle>
          <Button variant="outline" size="sm" onClick={loadLogs}>
            刷新
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>严重程度</TableHead>
              <TableHead>指标</TableHead>
              <TableHead>触发值</TableHead>
              <TableHead>触发时间</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {logs.map((log) => (
              <TableRow key={log.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getSeverityIcon(log.severity)}
                    {getSeverityBadge(log.severity)}
                  </div>
                </TableCell>
                <TableCell>{log.metric}</TableCell>
                <TableCell>{log.value}</TableCell>
                <TableCell>
                  {new Date(log.triggeredAt).toLocaleString()}
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={log.status === 'resolved' ? 'outline' : 'default'}
                    className="gap-1"
                  >
                    {log.status === 'resolved' ? (
                      <>
                        <CheckCircle2 className="h-3 w-3" />
                        已解决
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="h-3 w-3" />
                        未处理
                      </>
                    )}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setSelectedLog(log)}
                        >
                          详情
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>报警详情</DialogTitle>
                          <DialogDescription>
                            查看报警的详细信息
                          </DialogDescription>
                        </DialogHeader>
                        {selectedLog && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-muted-foreground">严重程度</p>
                                <div className="flex items-center gap-2 mt-1">
                                  {getSeverityIcon(selectedLog.severity)}
                                  {getSeverityBadge(selectedLog.severity)}
                                </div>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">状态</p>
                                <Badge 
                                  variant={selectedLog.status === 'resolved' ? 'outline' : 'default'}
                                  className="mt-1 gap-1"
                                >
                                  {selectedLog.status === 'resolved' ? (
                                    <>
                                      <CheckCircle2 className="h-3 w-3" />
                                      已解决
                                    </>
                                  ) : (
                                    <>
                                      <AlertTriangle className="h-3 w-3" />
                                      未处理
                                    </>
                                  )}
                                </Badge>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">监控指标</p>
                                <p className="mt-1">{selectedLog.metric}</p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">触发值</p>
                                <p className="mt-1">{selectedLog.value}</p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">触发时间</p>
                                <p className="mt-1">
                                  {new Date(selectedLog.triggeredAt).toLocaleString()}
                                </p>
                              </div>
                              {selectedLog.resolvedAt && (
                                <div>
                                  <p className="text-sm text-muted-foreground">解决时间</p>
                                  <p className="mt-1">
                                    {new Date(selectedLog.resolvedAt).toLocaleString()}
                                  </p>
                                </div>
                              )}
                            </div>
                            {selectedLog.status === 'open' && (
                              <div className="flex justify-end">
                                <Button 
                                  onClick={() => selectedLog.id && handleResolve(selectedLog.id)}
                                >
                                  标记为已解决
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                    {log.status === 'open' && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => log.id && handleResolve(log.id)}
                      >
                        解决
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}