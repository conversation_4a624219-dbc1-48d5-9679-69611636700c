'use client'

import * as React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useTranslation } from '@/contexts/LanguageContext'
import { useTheme } from 'next-themes'
import {
  House,
  Cube,
  Package,
  Wrench,
  Gear,
  Bell,
  Globe,
  WifiHigh,
  Database,
  Warning,
  Lightning,
  CaretRight,
} from '@phosphor-icons/react'

interface NavItemProps {
  icon: React.ElementType
  translationKey: string
  href: string
  isActive?: boolean
  className?: string
}

const NavItem = ({ icon: Icon, translationKey, href, isActive, className }: NavItemProps) => {
  const { t } = useTranslation()

  // 为不同的菜单项分配不同的颜色
  const getMenuColor = (key: string): string => {
    const colorMap: Record<string, string> = {
      'dashboard': 'from-primary-400 to-primary-600',
      'resources': 'from-secondary-400 to-secondary-600',
      'network': 'from-info-400 to-info-600',
      'wireless': 'from-accent-400 to-accent-600',
      'vulnerability.scan': 'from-destructive-400 to-destructive-600',
      'assets': 'from-success-400 to-success-600',
      'deployments': 'from-warning-400 to-warning-600',
      'notifications': 'from-colorful-pink to-colorful-purple',
      'settings': 'from-colorful-blue to-colorful-indigo',
      'power.monitoring': 'from-colorful-teal to-colorful-green',
      'smart.ops': 'from-colorful-orange to-colorful-yellow'
    }
    return colorMap[key] || 'from-gray-400 to-gray-600'
  }

  const getChineseTitle = (key: string): string => {
    const menuTitles: Record<string, string> = {
      'dashboard': '仪表盘',
      'resources': '资源管理',
      'network': '网络管理',
      'network.topology': '网络拓扑',
      'wireless': '无线网络',
      'vulnerability.scan': '漏洞扫描',
      'assets': '资产管理',
      'deployments': '部署',
      'procedures': '操作规程',
      'notifications': '通知',
      'updates': '更新',
      'settings': '设置',
      'power.monitoring': '基础设施监控',
      'smart.ops': '智能运维',
      'mobile.demo': '移动端演示'
    }
    return menuTitles[key] || t(key)
  }

  return (
    <Link href={href} className="no-underline">
      <Button
        variant="ghost"
        className={cn(
          'relative w-full justify-start gap-2 px-2 transition-all duration-300 overflow-hidden',
          isActive
            ? `bg-gradient-to-r ${getMenuColor(translationKey)} text-white`
            : 'hover:bg-accent/20',
          className
        )}
      >
        {isActive && (
          <div className="absolute left-0 top-1/2 h-[70%] w-[3px] -translate-y-1/2 rounded-r-sm bg-white" />
        )}
        <Icon
          className={cn(
            'h-5 w-5 transition-all duration-300',
            isActive ? 'text-white' : 'text-muted-foreground'
          )}
        />
        <span
          className={cn(
            'text-sm transition-all duration-300',
            isActive ? 'font-medium text-white' : 'text-muted-foreground'
          )}
        >
          {getChineseTitle(translationKey)}
        </span>
      </Button>
    </Link>
  )
}

interface SubNavItemProps {
  translationKey: string
  href: string
  isActive?: boolean
  className?: string
}

const SubNavItem = ({ translationKey, href, isActive, className }: SubNavItemProps) => {
  const { t } = useTranslation()

  // 为子菜单项分配不同的颜色
  const getSubMenuColor = (key: string): string => {
    // 根据父菜单类型分配不同的颜色系列
    if (key.startsWith('network')) {
      return 'from-info-300 to-info-500'
    } else if (key.includes('monitoring')) {
      return 'from-accent-300 to-accent-500'
    } else if (key.includes('asset')) {
      return 'from-success-300 to-success-500'
    } else if (key.includes('inspection')) {
      return 'from-warning-300 to-warning-500'
    } else if (key.includes('maintenance')) {
      return 'from-secondary-300 to-secondary-500'
    } else if (key.includes('capacity')) {
      return 'from-primary-300 to-primary-500'
    }
    return 'from-gray-300 to-gray-500'
  }

  const getChineseSubTitle = (key: string): string => {
    const subMenuTitles: Record<string, string> = {
      'network.fault.impact': '故障影响',
      'network.path.visualization': '路径可视化',
      'terminal.info': '终端信息',
      'network.config.backup': '配置备份',
      'config.management': '配置管理',
      'digital.ip.management': '数字IP管理',
      'phone.extension.management': '电话分机管理',
      'vm.management': '虚拟机管理',
      'printer.management': '打印机管理',
      'wireless.monitoring': '无线网络监控',
      'bandwidth.monitoring': '网络带宽监控',
      'environment.monitoring': '环境监控',
      'ups.monitoring': 'UPS监控',
      'mains.power.monitoring': '市电监控',
      'snmp.config.management': 'SNMP配置管理',
      'snmp.collection.management': 'SNMP采集管理',
      'asset.management.system': '资产概览',
      'asset.register': '资产登记',
      'asset.map': '资产地图',
      'auto.inspection': '自动巡检',
      'inspection.template': '巡检模板',
      'one.click.inspection': '一键巡检',
      'inspection.report': '巡检报告',
      'inspection.anomaly': '巡检异常',
      'maintenance.health': '维护健康',
      'maintenance.prediction': '维护预测',
      'maintenance.suggestion': '维护建议',
      'capacity.trend': '容量趋势',
      'capacity.suggestion': '容量建议',
      'capacity.optimization': '容量优化'
    }
    return subMenuTitles[key] || t(key)
  }

  return (
    <Link href={href} className="no-underline">
      <Button
        variant="ghost"
        className={cn(
          'relative w-full justify-start pl-9 transition-all duration-300',
          isActive
            ? `bg-gradient-to-r ${getSubMenuColor(translationKey)} text-white`
            : 'hover:bg-accent/10',
          className
        )}
      >
        {isActive && (
          <div className="absolute left-0 top-1/2 h-[70%] w-[3px] -translate-y-1/2 rounded-r-sm bg-white" />
        )}
        <span
          className={cn(
            'text-sm transition-all duration-300',
            isActive ? 'font-medium text-white' : 'text-muted-foreground'
          )}
        >
          {getChineseSubTitle(translationKey)}
        </span>
      </Button>
    </Link>
  )
}

const NetworkSubmenu = () => {
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/network')

  return (
    <AccordionItem value="network" className="border-none">
      <AccordionTrigger
        className={cn(
          'flex w-full items-center gap-2 px-2 py-2 transition-all duration-300',
          isActive
            ? 'bg-gradient-to-r from-info-400 to-info-600 text-white'
            : 'hover:bg-accent/20'
        )}
      >
        <Globe
          className={cn(
            'h-5 w-5 transition-all duration-300',
            isActive ? 'text-white' : 'text-muted-foreground'
          )}
        />
        <span
          className={cn(
            'flex-1 text-sm transition-all duration-300',
            isActive ? 'font-medium text-white' : 'text-muted-foreground'
          )}
        >
          网络管理
        </span>
      </AccordionTrigger>
      <AccordionContent className="pb-1 pt-0 px-0">
        <SubNavItem
          translationKey="network.fault.impact"
          href="/network/fault-impact"
          isActive={pathname === '/network/fault-impact'}
        />
        <SubNavItem
          translationKey="network.path.visualization"
          href="/network/path-visualization"
          isActive={pathname === '/network/path-visualization'}
        />
        <SubNavItem
          translationKey="terminal.info"
          href="/network/terminal-info"
          isActive={pathname === '/network/terminal-info'}
        />
        <SubNavItem
          translationKey="network.config.backup"
          href="/network/config-backup"
          isActive={pathname === '/network/config-backup'}
        />
        <SubNavItem
          translationKey="config.management"
          href="/network/config-management"
          isActive={pathname === '/network/config-management'}
        />
        <SubNavItem
          translationKey="digital.ip.management"
          href="/network/digital-ip"
          isActive={pathname === '/network/digital-ip'}
        />
        <SubNavItem
          translationKey="phone.extension.management"
          href="/network/phone-extensions"
          isActive={pathname === '/network/phone-extensions'}
        />
        <SubNavItem
          translationKey="vm.management"
          href="/network/vm-management"
          isActive={pathname === '/network/vm-management'}
        />
        <SubNavItem
          translationKey="printer.management"
          href="/network/printer-monitoring"
          isActive={pathname === '/network/printer-monitoring'}
        />
        <SubNavItem
          translationKey="wireless.monitoring"
          href="/network/wireless-monitoring"
          isActive={pathname === '/network/wireless-monitoring'}
        />
        <SubNavItem
          translationKey="bandwidth.monitoring"
          href="/network/bandwidth-monitoring"
          isActive={pathname === '/network/bandwidth-monitoring'}
        />
      </AccordionContent>
    </AccordionItem>
  )
}

const PowerMonitoringSubmenu = () => {
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/power-monitoring')

  return (
    <AccordionItem value="power-monitoring" className="border-none">
      <AccordionTrigger
        className={cn(
          'flex w-full items-center gap-2 px-2 py-2 transition-all duration-300',
          isActive
            ? 'bg-gradient-to-r from-colorful-teal to-colorful-green text-white'
            : 'hover:bg-accent/20'
        )}
      >
        <Lightning
          className={cn(
            'h-5 w-5 transition-all duration-300',
            isActive ? 'text-white' : 'text-muted-foreground'
          )}
        />
        <span
          className={cn(
            'flex-1 text-sm transition-all duration-300',
            isActive ? 'font-medium text-white' : 'text-muted-foreground'
          )}
        >
          基础设施监控
        </span>
      </AccordionTrigger>
      <AccordionContent className="pb-1 pt-0 px-0">
        <SubNavItem
          translationKey="environment.monitoring"
          href="/power-monitoring/environment"
          isActive={pathname === '/power-monitoring/environment'}
        />
        <SubNavItem
          translationKey="ups.monitoring"
          href="/power-monitoring/ups"
          isActive={pathname === '/power-monitoring/ups' || pathname.startsWith('/power-monitoring/ups/')}
        />
        <SubNavItem
          translationKey="mains.power.monitoring"
          href="/power-monitoring/mains"
          isActive={pathname === '/power-monitoring/mains'}
        />
        <SubNavItem
          translationKey="snmp.collection.management"
          href="/power-monitoring/snmp-collection"
          isActive={pathname === '/power-monitoring/snmp-collection'}
        />
      </AccordionContent>
    </AccordionItem>
  )
}

const AssetsSubmenu = () => {
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/resources/asset')

  return (
    <AccordionItem value="assets" className="border-none">
      <AccordionTrigger
        className={cn(
          'flex w-full items-center gap-2 px-2 py-2 transition-all duration-300',
          isActive
            ? 'bg-gradient-to-r from-success-400 to-success-600 text-white'
            : 'hover:bg-accent/20'
        )}
      >
        <Database
          className={cn(
            'h-5 w-5 transition-all duration-300',
            isActive ? 'text-white' : 'text-muted-foreground'
          )}
        />
        <span
          className={cn(
            'flex-1 text-sm transition-all duration-300',
            isActive ? 'font-medium text-white' : 'text-muted-foreground'
          )}
        >
          资产管理
        </span>
      </AccordionTrigger>
      <AccordionContent className="pb-1 pt-0 px-0">
        <SubNavItem
          translationKey="asset.management.system"
          href="/assets"
          isActive={pathname === '/assets' || pathname.startsWith('/assets/')}
        />
        <SubNavItem
          translationKey="asset.map"
          href="/resources/asset-map"
          isActive={pathname === '/resources/asset-map'}
        />
      </AccordionContent>
    </AccordionItem>
  )
}

const SmartOpsSubmenu = () => {
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/smart-ops')
  const [openGroups, setOpenGroups] = React.useState({
    inspection: false,
    prediction: false,
    capacity: false,
  })

  const toggleGroup = (group: keyof typeof openGroups) => {
    setOpenGroups(prev => ({
      ...prev,
      [group]: !prev[group],
    }))
  }

  return (
    <AccordionItem value="smart-ops" className="border-none">
      <AccordionTrigger
        className={cn(
          'flex w-full items-center gap-2 px-2 py-2 transition-all duration-300',
          isActive
            ? 'bg-gradient-to-r from-colorful-orange to-colorful-yellow text-white'
            : 'hover:bg-accent/20'
        )}
      >
        <Wrench
          className={cn(
            'h-5 w-5 transition-all duration-300',
            isActive ? 'text-white' : 'text-muted-foreground'
          )}
        />
        <span
          className={cn(
            'flex-1 text-sm transition-all duration-300',
            isActive ? 'font-medium text-white' : 'text-muted-foreground'
          )}
        >
          智能运维
        </span>
      </AccordionTrigger>
      <AccordionContent className="pb-1 pt-0 px-0">
        <div className="space-y-1">
          <Button
            variant="ghost"
            className={cn(
              "flex w-full items-center justify-between px-4 py-2 transition-all duration-300",
              openGroups.inspection
                ? "bg-gradient-to-r from-warning-300 to-warning-500 text-white"
                : "hover:bg-accent/10"
            )}
            onClick={() => toggleGroup('inspection')}
          >
            <span className={cn(
              'text-sm font-bold transition-all duration-300',
              openGroups.inspection ? 'text-white' : 'text-muted-foreground'
            )}>
              自动化巡检
            </span>
            <CaretRight
              className={cn(
                'h-4 w-4 transition-all duration-300',
                openGroups.inspection ? 'text-white rotate-90' : 'text-muted-foreground'
              )}
            />
          </Button>
          {openGroups.inspection && (
            <div className="space-y-1">
              <SubNavItem
                translationKey="inspection.template"
                href="/smart-ops/inspection-template"
                isActive={pathname === '/smart-ops/inspection-template'}
              />
              <SubNavItem
                translationKey="one.click.inspection"
                href="/smart-ops/one-click-inspection"
                isActive={pathname === '/smart-ops/one-click-inspection'}
              />
              <SubNavItem
                translationKey="inspection.report"
                href="/smart-ops/inspection-report"
                isActive={pathname === '/smart-ops/inspection-report'}
              />
              <SubNavItem
                translationKey="inspection.anomaly"
                href="/smart-ops/inspection-anomaly"
                isActive={pathname === '/smart-ops/inspection-anomaly'}
              />
              <SubNavItem
                translationKey="mobile.demo"
                href="/mobile-demo"
                isActive={pathname === '/mobile-demo'}
              />
            </div>
          )}

          <Button
            variant="ghost"
            className={cn(
              "flex w-full items-center justify-between px-4 py-2 transition-all duration-300",
              openGroups.prediction
                ? "bg-gradient-to-r from-secondary-300 to-secondary-500 text-white"
                : "hover:bg-accent/10"
            )}
            onClick={() => toggleGroup('prediction')}
          >
            <span className={cn(
              'text-sm font-bold transition-all duration-300',
              openGroups.prediction ? 'text-white' : 'text-muted-foreground'
            )}>
              预测性维护
            </span>
            <CaretRight
              className={cn(
                'h-4 w-4 transition-all duration-300',
                openGroups.prediction ? 'text-white rotate-90' : 'text-muted-foreground'
              )}
            />
          </Button>
          {openGroups.prediction && (
            <div className="space-y-1">
              <SubNavItem
                translationKey="maintenance.health"
                href="/smart-ops/maintenance-health"
                isActive={pathname === '/smart-ops/maintenance-health'}
              />
              <SubNavItem
                translationKey="maintenance.prediction"
                href="/smart-ops/maintenance-prediction"
                isActive={pathname === '/smart-ops/maintenance-prediction'}
              />
              <SubNavItem
                translationKey="maintenance.suggestion"
                href="/smart-ops/maintenance-suggestion"
                isActive={pathname === '/smart-ops/maintenance-suggestion'}
              />
            </div>
          )}

          <Button
            variant="ghost"
            className={cn(
              "flex w-full items-center justify-between px-4 py-2 transition-all duration-300",
              openGroups.capacity
                ? "bg-gradient-to-r from-primary-300 to-primary-500 text-white"
                : "hover:bg-accent/10"
            )}
            onClick={() => toggleGroup('capacity')}
          >
            <span className={cn(
              'text-sm font-bold transition-all duration-300',
              openGroups.capacity ? 'text-white' : 'text-muted-foreground'
            )}>
              容量规划
            </span>
            <CaretRight
              className={cn(
                'h-4 w-4 transition-all duration-300',
                openGroups.capacity ? 'text-white rotate-90' : 'text-muted-foreground'
              )}
            />
          </Button>
          {openGroups.capacity && (
            <div className="space-y-1">
              <SubNavItem
                translationKey="capacity.trend"
                href="/smart-ops/capacity-trend"
                isActive={pathname === '/smart-ops/capacity-trend'}
              />
              <SubNavItem
                translationKey="capacity.suggestion"
                href="/smart-ops/capacity-suggestion"
                isActive={pathname === '/smart-ops/capacity-suggestion'}
              />
              <SubNavItem
                translationKey="capacity.optimization"
                href="/smart-ops/capacity-optimization"
                isActive={pathname === '/smart-ops/capacity-optimization'}
              />
            </div>
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  )
}

const ShadcnSidebar = () => {
  const pathname = usePathname() ?? '/'
  const { theme } = useTheme()

  return (
    <aside className="fixed left-0 top-0 z-30 h-screen w-60 border-r bg-background shadow-lg">
      <div className="flex h-full flex-col">
        <div className="border-b p-4 bg-gradient-to-r from-primary-500 to-secondary-500">
          <div className="flex items-center gap-3 pb-3">
            <div className="h-7 w-1 rounded-sm bg-white" />
            <div className="flex flex-col items-start">
              <h1 className="text-lg font-bold tracking-tight text-white">
                燃石医学
              </h1>
              <p className="text-xs font-medium uppercase tracking-wider text-white/80">
                Burning Rock
              </p>
            </div>
          </div>
        </div>

        <ScrollArea className="flex-1 py-2">
          <div className="space-y-1 px-2">
            <NavItem
              icon={House}
              translationKey="dashboard"
              href="/"
              isActive={pathname === '/'}
            />
            <NavItem
              icon={Cube}
              translationKey="resources"
              href="/resources"
              isActive={pathname.startsWith('/resources')}
            />

            <Accordion type="multiple" className="space-y-1">
              <NetworkSubmenu />
              <SmartOpsSubmenu />
              <PowerMonitoringSubmenu />
              <AssetsSubmenu />
            </Accordion>

            <NavItem
              icon={WifiHigh}
              translationKey="wireless"
              href="/wireless"
              isActive={pathname.startsWith('/wireless')}
            />
            <NavItem
              icon={Warning}
              translationKey="vulnerability.scan"
              href="/vulnerability-scan"
              isActive={pathname.startsWith('/vulnerability-scan')}
            />
            <NavItem
              icon={Package}
              translationKey="deployments"
              href="/deployments"
              isActive={pathname.startsWith('/deployments')}
            />
            <NavItem
              icon={Bell}
              translationKey="notifications"
              href="/notifications"
              isActive={pathname.startsWith('/notifications')}
            />
            <NavItem
              icon={Gear}
              translationKey="settings"
              href="/settings"
              isActive={pathname.startsWith('/settings')}
            />
          </div>
        </ScrollArea>
      </div>
    </aside>
  )
}

export default ShadcnSidebar