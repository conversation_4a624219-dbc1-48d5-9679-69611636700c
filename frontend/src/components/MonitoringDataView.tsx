'use client'

import { useState, useEffect } from 'react'
import { useTheme } from 'next-themes'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { Skeleton } from '@/components/ui/skeleton'
import { Monitor, RefreshCw } from 'lucide-react'

interface MonitoringDataViewProps {
  deviceId: number
  type: 'snmp' | 'ssh'
  oidOrCommand: string
}

export function MonitoringDataView({ 
  deviceId, 
  type,
  oidOrCommand
}: MonitoringDataViewProps) {
  const { theme } = useTheme()
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [isClient, setIsClient] = useState(false)

  const loadData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId,
          type,
          limit: 50
        })
      })
      const result = await response.json()
      
      // 过滤特定OID或命令的数据
      const filteredData = result.filter((item: any) => 
        type === 'snmp' 
          ? item.oid === oidOrCommand
          : item.command === oidOrCommand
      )
      
      // 格式化图表数据
      const chartData = filteredData.map((item: any) => ({
        timestamp: new Date(item.timestamp).toLocaleTimeString(),
        value: type === 'snmp' ? parseFloat(item.value) : item.output.length
      }))
      
      setData(chartData.reverse())
    } catch (err) {
      console.error('加载监控数据失败:', err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    loadData()
  }, [deviceId, type, oidOrCommand])

  const handleRefresh = () => {
    setRefreshing(true)
    loadData()
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">
          {oidOrCommand}
        </CardTitle>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent className="h-[300px]">
        {loading ? (
          <Skeleton className="h-full w-full" />
        ) : data.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <p className="text-muted-foreground">暂无数据</p>
          </div>
        ) : (
          <div suppressHydrationWarning>
            {isClient && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke={theme === 'dark' ? '#8884d8' : '#4f46e5'} 
                    activeDot={{ r: 8 }} 
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}