'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon, Wrench, Clock, Users, FileText } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>bs<PERSON>ist,
  <PERSON><PERSON><PERSON>rig<PERSON>,
} from '@/components/ui/tabs'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'

interface AssetCategory {
  id: number
  name: string
  code: string
  description?: string
  level: number
  parent_id?: number
  displayName?: string
}

interface MaintenanceAssetFormData {
  name: string
  code: string
  category_id: number
  maintenance_type: string
  contract_number: string
  service_provider: string
  provider_contact: string
  provider_phone: string
  provider_email?: string
  service_scope: string
  service_level: string
  response_time: number
  resolution_time: number
  coverage_assets: string
  maintenance_frequency: string
  contract_amount: number
  currency: string
  payment_method: string
  payment_schedule: string
  start_date: Date
  end_date: Date
  renewal_notice_days: number
  auto_renewal: boolean
  escalation_procedure?: string
  penalty_clause?: string
  termination_clause?: string
  performance_metrics?: string
  reporting_requirements?: string
  emergency_contact?: string
  emergency_phone?: string
  department: string
  responsible_person: string
  backup_contact?: string
  approval_required: boolean
  status: string
  description?: string
}

const maintenanceAssetSchema = z.object({
  name: z.string().min(1, '维保合同名称不能为空'),
  code: z.string().min(1, '维保合同编码不能为空'),
  category_id: z.number().min(1, '请选择维保分类'),
  maintenance_type: z.string().min(1, '请选择维保类型'),
  contract_number: z.string().min(1, '合同编号不能为空'),
  service_provider: z.string().min(1, '服务提供商不能为空'),
  provider_contact: z.string().min(1, '联系人不能为空'),
  provider_phone: z.string().min(1, '联系电话不能为空'),
  provider_email: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
  service_scope: z.string().min(1, '服务范围不能为空'),
  service_level: z.string().min(1, '请选择服务级别'),
  response_time: z.number().min(1, '响应时间必须大于0'),
  resolution_time: z.number().min(1, '解决时间必须大于0'),
  coverage_assets: z.string().min(1, '覆盖资产不能为空'),
  maintenance_frequency: z.string().min(1, '请选择维保频率'),
  contract_amount: z.number().min(0, '合同金额不能为负数'),
  currency: z.string(),
  payment_method: z.string().min(1, '请选择付款方式'),
  payment_schedule: z.string().min(1, '请选择付款计划'),
  start_date: z.date({ required_error: '请选择开始日期' }),
  end_date: z.date({ required_error: '请选择结束日期' }),
  renewal_notice_days: z.number().min(1, '续约提醒天数必须大于0'),
  auto_renewal: z.boolean(),
  escalation_procedure: z.string().optional(),
  penalty_clause: z.string().optional(),
  termination_clause: z.string().optional(),
  performance_metrics: z.string().optional(),
  reporting_requirements: z.string().optional(),
  emergency_contact: z.string().optional(),
  emergency_phone: z.string().optional(),
  department: z.string().min(1, '部门不能为空'),
  responsible_person: z.string().min(1, '负责人不能为空'),
  backup_contact: z.string().optional(),
  approval_required: z.boolean(),
  status: z.string(),
  description: z.string().optional(),
}).refine((data) => data.end_date > data.start_date, {
  message: "结束日期必须晚于开始日期",
  path: ["end_date"],
}).refine((data) => data.resolution_time >= data.response_time, {
  message: "解决时间应该大于等于响应时间",
  path: ["resolution_time"],
})

interface MaintenanceAssetFormProps {
  onSubmit: (data: MaintenanceAssetFormData) => Promise<void>
  initialData?: Partial<MaintenanceAssetFormData>
  isLoading?: boolean
}

export function MaintenanceAssetForm({ onSubmit, initialData, isLoading = false }: MaintenanceAssetFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [hierarchicalCategories, setHierarchicalCategories] = useState<AssetCategory[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [autoGenerateCode, setAutoGenerateCode] = useState(true)

  const form = useForm<MaintenanceAssetFormData>({
    resolver: zodResolver(maintenanceAssetSchema),
    defaultValues: {
      name: '',
      code: '',
      category_id: 0,
      maintenance_type: '',
      contract_number: '',
      service_provider: '',
      provider_contact: '',
      provider_phone: '',
      service_scope: '',
      service_level: '',
      response_time: 4,
      resolution_time: 24,
      coverage_assets: '',
      maintenance_frequency: '',
      contract_amount: 0,
      currency: 'CNY',
      payment_method: '',
      payment_schedule: '',
      start_date: new Date(),
      end_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      renewal_notice_days: 30,
      auto_renewal: false,
      department: '',
      responsible_person: '',
      approval_required: false,
      status: 'active',
      ...initialData
    }
  })

  // 获取维保分类
  useEffect(() => {
    const buildHierarchicalCategories = (categories: AssetCategory[]) => {
      const categoryMap = new Map<number, AssetCategory>()
      const rootCategories: AssetCategory[] = []
      const result: AssetCategory[] = []

      // 创建分类映射
      categories.forEach(category => {
        categoryMap.set(category.id, { ...category, level: 1 })
      })

      // 确定层级和父子关系
      categories.forEach(category => {
        const cat = categoryMap.get(category.id)!
        if (category.parent_id && categoryMap.has(category.parent_id)) {
          const parent = categoryMap.get(category.parent_id)!
          cat.level = (parent.level || 1) + 1
        } else {
          rootCategories.push(cat)
        }
      })

      // 递归构建层级结构
      const addCategoryAndChildren = (category: AssetCategory) => {
        const indent = '  '.repeat((category.level || 1) - 1)
        category.displayName = `${indent}${category.name} (${category.code})`
        result.push(category)

        // 添加子分类
        const children = categories
          .filter(c => c.parent_id === category.id)
          .map(c => categoryMap.get(c.id)!)
          .sort((a, b) => a.name.localeCompare(b.name))

        children.forEach(child => addCategoryAndChildren(child))
      }

      // 按名称排序根分类
      rootCategories.sort((a, b) => a.name.localeCompare(b.name))
      rootCategories.forEach(category => addCategoryAndChildren(category))

      return result
    }

    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/asset-categories')
        if (response.ok) {
          const data = await response.json()
          // 筛选维保相关分类
          const maintenanceCategories = data.filter((cat: AssetCategory) => 
            cat.code.startsWith('MT') || cat.name.includes('维保') || cat.name.includes('维护')
          )
          setCategories(maintenanceCategories)
          setHierarchicalCategories(buildHierarchicalCategories(maintenanceCategories))
        }
      } catch (error) {
        console.error('获取资产分类失败:', error)
        toast({
          title: '错误',
          description: '获取资产分类失败',
          variant: 'destructive'
        })
      }
    }
    fetchCategories()
  }, [])

  // 自动生成维保编码
  const generateMaintenanceCode = (categoryId: number, maintenanceType: string, provider: string) => {
    if (!autoGenerateCode) return
    
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      const prefix = category.code || 'MT'
      const year = new Date().getFullYear().toString().slice(-2)
      const month = (new Date().getMonth() + 1).toString().padStart(2, '0')
      const typePrefix = maintenanceType.substring(0, 2).toUpperCase()
      const providerPrefix = provider.substring(0, 2).toUpperCase()
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const code = `${prefix}-${year}${month}${typePrefix}${providerPrefix}${random}`
      form.setValue('code', code)
    }
  }

  // 监听字段变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name: fieldName }) => {
      if (fieldName === 'category_id' || fieldName === 'maintenance_type' || fieldName === 'service_provider') {
        const categoryId = value.category_id
        const maintenanceType = value.maintenance_type
        const provider = value.service_provider
        if (categoryId && maintenanceType && provider && autoGenerateCode) {
          generateMaintenanceCode(categoryId, maintenanceType, provider)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, categories, autoGenerateCode])

  // 计算合同状态
  const getContractStatus = (startDate: Date, endDate: Date) => {
    const now = new Date()
    const daysToExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    if (now < startDate) return { status: 'pending', color: 'secondary', text: '未开始' }
    if (now > endDate) return { status: 'expired', color: 'destructive', text: '已过期' }
    if (daysToExpiry <= 30) return { status: 'expiring', color: 'destructive', text: '即将到期' }
    if (daysToExpiry <= 90) return { status: 'warning', color: 'secondary', text: '临近到期' }
    return { status: 'active', color: 'default', text: '正常' }
  }

  const handleSubmit = async (data: MaintenanceAssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      toast({
        title: '成功',
        description: '维保资产登记成功',
      })
      form.reset()
    } catch (error) {
      console.error('维保资产登记失败:', error)
      toast({
        title: '错误',
        description: '维保资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>维保合同名称 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入维保合同名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                维保合同编码 *
                <Switch
                  checked={autoGenerateCode}
                  onCheckedChange={setAutoGenerateCode}
                  size="sm"
                />
                <span className="text-xs text-muted-foreground">自动生成</span>
              </FormLabel>
              <FormControl>
                <Input 
                  placeholder="请输入维保合同编码" 
                  {...field} 
                  disabled={autoGenerateCode}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>维保分类 *</FormLabel>
              <Select onValueChange={(value) => field.onChange(Number(value))} value={field.value?.toString()}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择维保分类" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hierarchicalCategories.map((category) => (
                    <SelectItem 
                      key={category.id} 
                      value={category.id.toString()}
                      className={category.level === 1 ? "font-bold" : "text-sm"}
                    >
                      {category.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="maintenance_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>维保类型 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择维保类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="hardware_maintenance">硬件维保</SelectItem>
                  <SelectItem value="software_maintenance">软件维保</SelectItem>
                  <SelectItem value="network_maintenance">网络维保</SelectItem>
                  <SelectItem value="system_maintenance">系统维保</SelectItem>
                  <SelectItem value="facility_maintenance">设施维保</SelectItem>
                  <SelectItem value="security_maintenance">安全维保</SelectItem>
                  <SelectItem value="comprehensive_maintenance">综合维保</SelectItem>
                  <SelectItem value="emergency_maintenance">应急维保</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="contract_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同编号 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入合同编号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="service_level"
          render={({ field }) => (
            <FormItem>
              <FormLabel>服务级别 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择服务级别" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="basic">基础服务</SelectItem>
                  <SelectItem value="standard">标准服务</SelectItem>
                  <SelectItem value="premium">高级服务</SelectItem>
                  <SelectItem value="enterprise">企业服务</SelectItem>
                  <SelectItem value="7x24">7x24服务</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="response_time"
          render={({ field }) => (
            <FormItem>
              <FormLabel>响应时间（小时）*</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="响应时间" 
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="resolution_time"
          render={({ field }) => (
            <FormItem>
              <FormLabel>解决时间（小时）*</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="解决时间" 
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 位置信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">服务位置信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="service_location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>服务位置 *</FormLabel>
                <FormControl>
                  <Input placeholder="请输入服务位置" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="building"
            render={({ field }) => (
              <FormItem>
                <FormLabel>建筑物</FormLabel>
                <FormControl>
                  <Input placeholder="请输入建筑物" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="floor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>楼层</FormLabel>
                <FormControl>
                  <Input placeholder="请输入楼层" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="room"
            render={({ field }) => (
              <FormItem>
                <FormLabel>房间</FormLabel>
                <FormControl>
                  <Input placeholder="请输入房间" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="department"
            render={({ field }) => (
              <FormItem>
                <FormLabel>服务部门</FormLabel>
                <FormControl>
                  <Input placeholder="请输入服务部门" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <FormField
        control={form.control}
        name="service_scope"
        render={({ field }) => (
          <FormItem>
            <FormLabel>服务范围 *</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请详细描述服务范围" 
                className="min-h-[80px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="coverage_assets"
        render={({ field }) => (
          <FormItem>
            <FormLabel>覆盖资产 *</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请列出维保覆盖的资产清单" 
                className="min-h-[80px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="maintenance_frequency"
        render={({ field }) => (
          <FormItem>
            <FormLabel>维保频率 *</FormLabel>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="请选择维保频率" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="daily">每日</SelectItem>
                <SelectItem value="weekly">每周</SelectItem>
                <SelectItem value="monthly">每月</SelectItem>
                <SelectItem value="quarterly">每季度</SelectItem>
                <SelectItem value="semi_annual">每半年</SelectItem>
                <SelectItem value="annual">每年</SelectItem>
                <SelectItem value="on_demand">按需</SelectItem>
                <SelectItem value="emergency_only">仅应急</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )

  const renderServiceProvider = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="service_provider"
          render={({ field }) => (
            <FormItem>
              <FormLabel>服务提供商 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入服务提供商名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="provider_contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系人 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="provider_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系电话 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系电话" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="provider_email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系邮箱</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系邮箱" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="emergency_contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel>应急联系人</FormLabel>
              <FormControl>
                <Input placeholder="请输入应急联系人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="emergency_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>应急电话</FormLabel>
              <FormControl>
                <Input placeholder="请输入应急电话" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>部门 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入部门" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>负责人 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入负责人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="backup_contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel>备用联系人</FormLabel>
              <FormControl>
                <Input placeholder="请输入备用联系人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )

  const renderContractTerms = () => {
    const startDate = form.watch('start_date')
    const endDate = form.watch('end_date')
    const contractStatus = startDate && endDate ? getContractStatus(startDate, endDate) : null
    
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="contract_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>合同金额</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    step="0.01"
                    placeholder="请输入合同金额" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>货币类型</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择货币类型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                    <SelectItem value="USD">美元 (USD)</SelectItem>
                    <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                    <SelectItem value="JPY">日元 (JPY)</SelectItem>
                    <SelectItem value="HKD">港币 (HKD)</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="payment_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>付款方式 *</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择付款方式" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="bank_transfer">银行转账</SelectItem>
                    <SelectItem value="check">支票</SelectItem>
                    <SelectItem value="cash">现金</SelectItem>
                    <SelectItem value="credit_card">信用卡</SelectItem>
                    <SelectItem value="online_payment">在线支付</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="payment_schedule"
          render={({ field }) => (
            <FormItem>
              <FormLabel>付款计划 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择付款计划" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="monthly">按月付款</SelectItem>
                  <SelectItem value="quarterly">按季度付款</SelectItem>
                  <SelectItem value="semi_annual">按半年付款</SelectItem>
                  <SelectItem value="annual">按年付款</SelectItem>
                  <SelectItem value="milestone">按里程碑付款</SelectItem>
                  <SelectItem value="completion">完成后付款</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>开始日期 *</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择开始日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="flex items-center gap-2">
                  结束日期 *
                  {contractStatus && (
                    <Badge variant={contractStatus.color as any}>
                      {contractStatus.text}
                    </Badge>
                  )}
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择结束日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date()}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="renewal_notice_days"
            render={({ field }) => (
              <FormItem>
                <FormLabel>续约提醒（天）</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="续约提醒天数" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="auto_renewal"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">自动续约</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    合同到期时自动续约
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="approval_required"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">需要审批</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    维保申请需要审批
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="escalation_procedure"
          render={({ field }) => (
            <FormItem>
              <FormLabel>升级程序</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请描述问题升级处理程序" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="penalty_clause"
          render={({ field }) => (
            <FormItem>
              <FormLabel>违约条款</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请描述违约责任和处罚条款" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="termination_clause"
          render={({ field }) => (
            <FormItem>
              <FormLabel>终止条款</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请描述合同终止条件和程序" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="performance_metrics"
          render={({ field }) => (
            <FormItem>
              <FormLabel>绩效指标</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请描述服务绩效评估指标" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="reporting_requirements"
          render={({ field }) => (
            <FormItem>
              <FormLabel>报告要求</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请描述维保报告和文档要求" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>状态</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择状态" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="active">正常</SelectItem>
                    <SelectItem value="pending">待生效</SelectItem>
                    <SelectItem value="suspended">暂停</SelectItem>
                    <SelectItem value="expired">已过期</SelectItem>
                    <SelectItem value="terminated">已终止</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>备注说明</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入备注说明" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Wrench className="h-4 w-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="provider" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              服务商信息
            </TabsTrigger>
            <TabsTrigger value="contract" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              合同条款
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="mt-6">
            {renderBasicInfo()}
          </TabsContent>
          
          <TabsContent value="provider" className="mt-6">
            {renderServiceProvider()}
          </TabsContent>
          
          <TabsContent value="contract" className="mt-6">
            {renderContractTerms()}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button type="button" variant="outline" onClick={() => form.reset()}>
            重置
          </Button>
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting ? '登记中...' : '确认登记'}
          </Button>
        </div>
      </form>
    </Form>
  )
}