'use client'

import React, { useState, useEffect } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  Text,
  Divider,
  Switch,
  FormHelperText,
  useColorModeValue,
  Box,
  Flex,
  Icon,
  Tooltip,
  Textarea,
  Checkbox,
  CheckboxGroup,
  Stack,
  Badge,
} from '@chakra-ui/react'
import {
  EnvelopeSimple,
  ChatCircle,
  ChatCircleDots,
  Chats,
  Robot,
  ChatTeardrop,
  Info,
  Warning,
  X,
  Prohibit,
  Check,
  Plus,
  PencilSimple,
  Bell,
  Gear,
  ArrowRight,
  CaretRight
} from '@phosphor-icons/react'

// 通知渠道类型
interface NotificationChannel {
  id: string
  name: string
  type: 'email' | 'wechat' | 'feishu' | 'dingtalk' | 'webhook' | 'sms'
  enabled: boolean
  config: {
    [key: string]: string
  }
}

// 通知规则类型
interface NotificationRule {
  id: string
  name: string
  description: string
  enabled: boolean
  severity: 'info' | 'warning' | 'error' | 'critical'
  channels: string[] // 通知渠道ID列表
}

interface NotificationRuleModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (rule: NotificationRule) => void
  rule?: NotificationRule | null
  channels: NotificationChannel[]
}

export default function NotificationRuleModal({
  isOpen,
  onClose,
  onSave,
  rule = null,
  channels
}: NotificationRuleModalProps) {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 表单状态
  const [formData, setFormData] = useState<Omit<NotificationRule, 'id'>>({
    name: '',
    description: '',
    enabled: true,
    severity: 'info',
    channels: []
  })

  // 初始化表单数据
  useEffect(() => {
    if (rule) {
      setFormData({
        name: rule.name,
        description: rule.description,
        enabled: rule.enabled,
        severity: rule.severity,
        channels: [...rule.channels]
      })
    } else {
      setFormData({
        name: '',
        description: '',
        enabled: true,
        severity: 'info',
        channels: []
      })
    }
  }, [rule, isOpen])

  // 处理表单变更
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // 处理启用状态变更
  const handleEnabledChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      enabled: e.target.checked
    }))
  }

  // 处理通知渠道选择
  const handleChannelsChange = (selectedChannels: string[]) => {
    setFormData(prev => ({
      ...prev,
      channels: selectedChannels
    }))
  }

  // 处理保存
  const handleSave = () => {
    onSave(rule ? { ...formData, id: rule.id } : { ...formData, id: '' })
  }

  // 获取严重性图标
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'info':
        return <Icon as={Info} color="blue.500" />
      case 'warning':
        return <Icon as={Warning} color="yellow.500" />
      case 'error':
        return <Icon as={X} color="orange.500" />
      case 'critical':
        return <Icon as={Prohibit} color="red.500" />
      default:
        return <Icon as={Info} color="blue.500" />
    }
  }

  // 获取严重性文本
  const getSeverityText = (severity: string) => {
    switch (severity) {
      case 'info':
        return '信息'
      case 'warning':
        return '警告'
      case 'error':
        return '错误'
      case 'critical':
        return '严重'
      default:
        return '信息'
    }
  }

  // 获取严重性颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info':
        return 'blue'
      case 'warning':
        return 'yellow'
      case 'error':
        return 'orange'
      case 'critical':
        return 'red'
      default:
        return 'blue'
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay backdropFilter="blur(2px)" />
      <ModalContent bg={bgColor} mx="auto" maxW="800px">
        <ModalHeader
          bg={useColorModeValue('gray.50', 'gray.800')}
          borderBottomWidth="1px"
          borderColor={borderColor}
          borderTopRadius="md"
          py={4}
        >
          <Flex align="center">
            <Icon
              as={rule ? PencilSimple : Plus}
              mr={2}
              color={useColorModeValue('purple.500', 'purple.300')}
            />
            <Text>{rule ? '编辑通知规则' : '添加通知规则'}</Text>
          </Flex>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody py={6}>
          <Box mb={6}>
            <Text fontSize="sm" color="gray.600">
              配置通知规则，决定系统在什么情况下通过哪些渠道发送通知。通知规则可以根据事件的严重性级别选择不同的通知渠道。
            </Text>
          </Box>

          <Flex gap={6} direction={{ base: 'column', md: 'row' }}>
            {/* 左侧 - 规则信息 */}
            <Box flex="1">
              <Box
                p={4}
                bg={useColorModeValue('gray.50', 'gray.700')}
                borderRadius="md"
                borderWidth="1px"
                borderColor={borderColor}
                mb={4}
              >
                <Text fontWeight="medium" mb={4}>规则信息</Text>

                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>规则名称</FormLabel>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="通知规则名称"
                      bg={useColorModeValue('white', 'gray.800')}
                    />
                    <FormHelperText>为通知规则设置一个易于识别的名称</FormHelperText>
                  </FormControl>

                  <FormControl>
                    <FormLabel>规则描述</FormLabel>
                    <Textarea
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      placeholder="描述此规则的触发条件和用途"
                      rows={3}
                      bg={useColorModeValue('white', 'gray.800')}
                    />
                    <FormHelperText>详细描述此规则的触发条件和用途，便于其他管理员理解</FormHelperText>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>严重性级别</FormLabel>
                    <Box position="relative">
                      <Select
                        name="severity"
                        value={formData.severity}
                        onChange={handleChange}
                        bg={useColorModeValue('white', 'gray.800')}
                        pl={10}
                      >
                        <option value="info">信息</option>
                        <option value="warning">警告</option>
                        <option value="error">错误</option>
                        <option value="critical">严重</option>
                      </Select>
                      <Box position="absolute" left={3} top="50%" transform="translateY(-50%)" pointerEvents="none">
                        {getSeverityIcon(formData.severity)}
                      </Box>
                    </Box>

                    <Box
                      mt={3}
                      p={3}
                      borderRadius="md"
                      bg={useColorModeValue(`${getSeverityColor(formData.severity)}.50`, 'gray.700')}
                      borderWidth="1px"
                      borderColor={useColorModeValue(`${getSeverityColor(formData.severity)}.100`, 'gray.600')}
                    >
                      <Flex align="center">
                        {getSeverityIcon(formData.severity)}
                        <Text fontWeight="medium" ml={2}>{getSeverityText(formData.severity)}</Text>
                      </Flex>
                      <Text fontSize="sm" color="gray.600" mt={1}>
                        {formData.severity === 'info' && '信息级别的通知，用于一般性的系统事件和提醒'}
                        {formData.severity === 'warning' && '警告级别的通知，用于需要关注但不紧急的问题'}
                        {formData.severity === 'error' && '错误级别的通知，用于需要及时处理的问题'}
                        {formData.severity === 'critical' && '严重级别的通知，用于需要立即处理的紧急问题'}
                      </Text>
                    </Box>
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="enabled" mb="0">
                      启用此规则
                    </FormLabel>
                    <Box position="relative" zIndex={1}>
                      <Switch
                        id="enabled"
                        isChecked={formData.enabled}
                        onChange={handleEnabledChange}
                        colorScheme="green"
                        size="md"
                        cursor="pointer"
                      />
                    </Box>
                  </FormControl>
                </VStack>
              </Box>
            </Box>

            {/* 右侧 - 通知渠道 */}
            <Box flex="1">
              <Box
                p={4}
                bg={useColorModeValue('gray.50', 'gray.700')}
                borderRadius="md"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <Flex justify="space-between" align="center" mb={4}>
                  <Text fontWeight="medium">通知渠道</Text>
                  <Badge colorScheme="blue">
                    已选择 {formData.channels.length} 个渠道
                  </Badge>
                </Flex>

                <Text fontSize="sm" color="gray.600" mb={4}>
                  选择此规则触发时要使用的通知渠道。可以选择多个渠道同时发送通知。
                </Text>

                <Box
                  borderWidth="1px"
                  borderColor={borderColor}
                  borderRadius="md"
                  bg={useColorModeValue('white', 'gray.800')}
                  maxH="300px"
                  overflowY="auto"
                  mb={4}
                >
                  {channels.length > 0 ? (
                    <CheckboxGroup
                      colorScheme="blue"
                      value={formData.channels}
                      onChange={handleChannelsChange}
                    >
                      <VStack spacing={0} align="stretch" divider={<Divider />}>
                        {channels.map(channel => (
                          <Box
                            key={channel.id}
                            p={3}
                            transition="background-color 0.2s"
                            _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                            opacity={!channel.enabled ? 0.6 : 1}
                          >
                            <Checkbox
                              value={channel.id}
                              isDisabled={!channel.enabled}
                              colorScheme="blue"
                              size="lg"
                            >
                              <Flex align="center" ml={2}>
                                <Box
                                  borderRadius="full"
                                  bg={
                                    channel.type === 'email' ? 'blue.100' :
                                    channel.type === 'wechat' ? 'green.100' :
                                    channel.type === 'feishu' ? 'cyan.100' :
                                    channel.type === 'dingtalk' ? 'red.100' :
                                    channel.type === 'webhook' ? 'purple.100' :
                                    'orange.100'
                                  }
                                  p={2}
                                  mr={3}
                                >
                                  {channel.type === 'email' && <Icon as={EnvelopeSimple} boxSize={4} color="blue.500" />}
                                  {channel.type === 'wechat' && <Icon as={ChatCircle} boxSize={4} color="green.500" />}
                                  {channel.type === 'feishu' && <Icon as={ChatCircleDots} boxSize={4} color="blue.400" />}
                                  {channel.type === 'dingtalk' && <Icon as={Chats} boxSize={4} color="red.500" />}
                                  {channel.type === 'webhook' && <Icon as={Robot} boxSize={4} color="purple.500" />}
                                  {channel.type === 'sms' && <Icon as={ChatTeardrop} boxSize={4} color="orange.500" />}
                                </Box>
                                <Box>
                                  <Text fontWeight="medium">{channel.name}</Text>
                                  <Text fontSize="xs" color="gray.500">
                                    {channel.type === 'email' && '邮件通知'}
                                    {channel.type === 'wechat' && '企业微信机器人'}
                                    {channel.type === 'feishu' && '飞书机器人'}
                                    {channel.type === 'dingtalk' && '钉钉机器人'}
                                    {channel.type === 'webhook' && 'Webhook'}
                                    {channel.type === 'sms' && '短信通知'}
                                  </Text>
                                </Box>
                                {!channel.enabled && (
                                  <Badge ml="auto" colorScheme="gray" fontSize="xs">
                                    已禁用
                                  </Badge>
                                )}
                              </Flex>
                            </Checkbox>
                          </Box>
                        ))}
                      </VStack>
                    </CheckboxGroup>
                  ) : (
                    <Flex
                      direction="column"
                      align="center"
                      justify="center"
                      py={6}
                      px={4}
                    >
                      <Icon as={Bell} boxSize={8} color="gray.400" mb={3} />
                      <Text color="gray.500" textAlign="center" fontSize="sm">
                        暂无可用的通知渠道，请先添加通知渠道
                      </Text>
                    </Flex>
                  )}
                </Box>

                {formData.channels.length > 0 && (
                  <Box
                    p={3}
                    borderRadius="md"
                    bg={useColorModeValue('blue.50', 'blue.900')}
                    borderWidth="1px"
                    borderColor={useColorModeValue('blue.100', 'blue.800')}
                  >
                    <Flex align="center" mb={2}>
                      <Icon as={Bell} color="blue.500" mr={2} />
                      <Text fontWeight="medium">已选择的通知渠道</Text>
                    </Flex>
                    <Flex wrap="wrap" gap={2}>
                      {formData.channels.map(channelId => {
                        const channel = channels.find(c => c.id === channelId)
                        return channel ? (
                          <Badge
                            key={channelId}
                            colorScheme="blue"
                            variant="solid"
                            borderRadius="full"
                            px={2}
                            py={1}
                          >
                            {channel.name}
                          </Badge>
                        ) : null
                      })}
                    </Flex>
                  </Box>
                )}
              </Box>
            </Box>
          </Flex>

          {/* 规则流程预览 */}
          {formData.name && formData.channels.length > 0 && (
            <Box
              mt={6}
              p={4}
              borderRadius="md"
              bg={useColorModeValue('gray.50', 'gray.700')}
              borderWidth="1px"
              borderColor={borderColor}
            >
              <Text fontWeight="medium" mb={3}>规则流程预览</Text>
              <Flex align="center" justify="space-between">
                <Box
                  p={3}
                  borderRadius="md"
                  bg={useColorModeValue(`${getSeverityColor(formData.severity)}.50`, 'gray.700')}
                  borderWidth="1px"
                  borderColor={useColorModeValue(`${getSeverityColor(formData.severity)}.100`, 'gray.600')}
                  flex="1"
                >
                  <Flex align="center">
                    {getSeverityIcon(formData.severity)}
                    <Text fontWeight="medium" ml={2}>{formData.name}</Text>
                  </Flex>
                </Box>

                <Icon as={ArrowRight} mx={4} color="gray.400" boxSize={6} />

                <Flex
                  flex="1"
                  bg={useColorModeValue('blue.50', 'blue.900')}
                  p={3}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={useColorModeValue('blue.100', 'blue.800')}
                  align="center"
                  justify="center"
                >
                  <Icon as={Bell} color="blue.500" mr={2} />
                  <Text fontWeight="medium">{formData.channels.length} 个通知渠道</Text>
                </Flex>
              </Flex>
            </Box>
          )}
        </ModalBody>

        <ModalFooter
          borderTopWidth="1px"
          borderColor={borderColor}
          bg={useColorModeValue('gray.50', 'gray.800')}
          borderBottomRadius="md"
          py={4}
        >
          <Button variant="outline" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button
            colorScheme="purple"
            onClick={handleSave}
            leftIcon={<Icon as={Check} />}
            isDisabled={!formData.name || formData.channels.length === 0}
          >
            保存
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
