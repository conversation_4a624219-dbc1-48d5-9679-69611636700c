'use client'

import React, { useState } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  Text,
  Textarea,
  useColorModeValue,
  Box,
  Flex,
  Icon,
  Badge,
  FormHelperText,
  Checkbox,
  CheckboxGroup,
  Stack,
} from '@chakra-ui/react'
import {
  Info,
  Warning,
  X,
  Prohibit,
  EnvelopeSimple,
  ChatCircle,
  ChatCircleDots,
  Chats,
  Robot,
} from '@phosphor-icons/react'

// 通知渠道类型
interface NotificationChannel {
  id: string
  name: string
  type: 'email' | 'wechat' | 'feishu' | 'dingtalk' | 'webhook' | 'sms'
  enabled: boolean
  config: {
    [key: string]: string
  }
}

// 测试通知类型
interface TestNotification {
  title: string
  message: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  channels: string[] // 通知渠道ID列表
}

interface TestNotificationModalProps {
  isOpen: boolean
  onClose: () => void
  onSend: (notification: TestNotification) => void
  channels: NotificationChannel[]
}

export default function TestNotificationModal({
  isOpen,
  onClose,
  onSend,
  channels
}: TestNotificationModalProps) {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 表单状态
  const [formData, setFormData] = useState<TestNotification>({
    title: '测试通知',
    message: '这是一条测试通知消息，用于验证通知渠道是否正常工作。',
    severity: 'info',
    channels: []
  })

  // 处理表单变更
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // 处理通知渠道选择
  const handleChannelsChange = (selectedChannels: string[]) => {
    setFormData(prev => ({
      ...prev,
      channels: selectedChannels
    }))
  }

  // 处理发送
  const handleSend = () => {
    onSend(formData)
  }

  // 获取严重性图标
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'info':
        return <Icon as={Info} color="blue.500" />
      case 'warning':
        return <Icon as={Warning} color="yellow.500" />
      case 'error':
        return <Icon as={X} color="orange.500" />
      case 'critical':
        return <Icon as={Prohibit} color="red.500" />
      default:
        return <Icon as={Info} color="blue.500" />
    }
  }

  // 获取严重性文本
  const getSeverityText = (severity: string) => {
    switch (severity) {
      case 'info':
        return '信息'
      case 'warning':
        return '警告'
      case 'error':
        return '错误'
      case 'critical':
        return '严重'
      default:
        return '信息'
    }
  }

  // 获取严重性颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info':
        return 'blue'
      case 'warning':
        return 'yellow'
      case 'error':
        return 'orange'
      case 'critical':
        return 'red'
      default:
        return 'blue'
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg" isCentered>
      <ModalOverlay backdropFilter="blur(2px)" />
      <ModalContent bg={bgColor} mx="auto">
        <ModalHeader>
          发送测试通知
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text>
              发送测试通知以验证通知渠道配置是否正确。
            </Text>

            <FormControl isRequired>
              <FormLabel>通知标题</FormLabel>
              <Input
                name="title"
                value={formData.title}
                onChange={handleChange}
                placeholder="测试通知标题"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>通知内容</FormLabel>
              <Textarea
                name="message"
                value={formData.message}
                onChange={handleChange}
                placeholder="测试通知内容"
                rows={3}
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>严重性级别</FormLabel>
              <Select
                name="severity"
                value={formData.severity}
                onChange={handleChange}
              >
                <option value="info">信息</option>
                <option value="warning">警告</option>
                <option value="error">错误</option>
                <option value="critical">严重</option>
              </Select>
              <Flex align="center" mt={2}>
                {getSeverityIcon(formData.severity)}
                <Badge ml={2} colorScheme={getSeverityColor(formData.severity)}>
                  {getSeverityText(formData.severity)}
                </Badge>
              </Flex>
            </FormControl>

            <FormControl isRequired>
              <FormLabel>选择通知渠道</FormLabel>
              <FormHelperText mb={3}>
                选择要测试的通知渠道
              </FormHelperText>

              <CheckboxGroup
                colorScheme="blue"
                value={formData.channels}
                onChange={handleChannelsChange}
              >
                <Stack spacing={2}>
                  {channels.filter(channel => channel.enabled).map(channel => (
                    <Checkbox
                      key={channel.id}
                      value={channel.id}
                    >
                      <Flex align="center">
                        {channel.type === 'email' && <Icon as={EnvelopeSimple} boxSize={4} color="blue.500" mr={2} />}
                        {channel.type === 'wechat' && <Icon as={ChatCircle} boxSize={4} color="green.500" mr={2} />}
                        {channel.type === 'feishu' && <Icon as={ChatCircleDots} boxSize={4} color="blue.400" mr={2} />}
                        {channel.type === 'dingtalk' && <Icon as={Chats} boxSize={4} color="red.500" mr={2} />}
                        {channel.type === 'webhook' && <Icon as={Robot} boxSize={4} color="purple.500" mr={2} />}
                        {channel.name}
                      </Flex>
                    </Checkbox>
                  ))}
                </Stack>
              </CheckboxGroup>

              {channels.filter(channel => channel.enabled).length === 0 && (
                <Box
                  borderWidth="1px"
                  borderColor={borderColor}
                  borderRadius="md"
                  p={4}
                  bg="gray.50"
                >
                  <Text color="gray.500" textAlign="center">
                    暂无可用的通知渠道，请先添加并启用通知渠道
                  </Text>
                </Box>
              )}
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSend}
            isDisabled={formData.channels.length === 0}
          >
            发送测试通知
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
