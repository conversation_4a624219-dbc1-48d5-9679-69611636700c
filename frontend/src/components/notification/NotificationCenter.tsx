import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Drawer,
  <PERSON>er<PERSON><PERSON>,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON>lay,
  DrawerContent,
  DrawerCloseButton,
  IconButton,
  Badge,
  VStack,
  Text,
  Flex,
  Divider,
  useDisclosure,
  useColorModeValue,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel
} from '@chakra-ui/react';
import { BellIcon, CheckIcon, DeleteIcon } from '@chakra-ui/icons';
import { Notification, NotificationType } from '../../services/notificationService';

// 模拟通知数据
const mockNotifications: Notification[] = [
  {
    id: '1',
    title: '巡检完成',
    message: '设备 Server-001 的巡检已完成，发现 2 个问题',
    type: 'info',
    timestamp: Date.now() - 1000 * 60 * 5, // 5分钟前
    read: false,
    link: '/smart-ops/inspection-results/123'
  },
  {
    id: '2',
    title: '告警通知',
    message: '设备 Router-002 的CPU使用率超过90%',
    type: 'warning',
    timestamp: Date.now() - 1000 * 60 * 30, // 30分钟前
    read: true,
    link: '/assets/devices/router-002'
  },
  {
    id: '3',
    title: '自动修复成功',
    message: '设备 Server-003 的磁盘空间问题已自动修复',
    type: 'success',
    timestamp: Date.now() - 1000 * 60 * 60 * 2, // 2小时前
    read: false,
    link: '/smart-ops/inspection-results/456'
  },
  {
    id: '4',
    title: '设备离线',
    message: '设备 Switch-004 已离线，请检查网络连接',
    type: 'error',
    timestamp: Date.now() - 1000 * 60 * 60 * 24, // 1天前
    read: true,
    link: '/assets/devices/switch-004'
  }
];

// 通知项组件
interface NotificationItemProps {
  notification: Notification;
  onRead: (id: string) => void;
  onDelete: (id: string) => void;
  onSelect: (notification: Notification) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onRead,
  onDelete,
  onSelect
}) => {
  const bgColor = useColorModeValue(
    notification.read ? 'white' : 'blue.50',
    notification.read ? 'gray.800' : 'blue.900'
  );
  
  const typeColors = {
    info: 'blue',
    success: 'green',
    warning: 'orange',
    error: 'red'
  };
  
  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 1000 * 60) {
      return '刚刚';
    } else if (diff < 1000 * 60 * 60) {
      return `${Math.floor(diff / (1000 * 60))}分钟前`;
    } else if (diff < 1000 * 60 * 60 * 24) {
      return `${Math.floor(diff / (1000 * 60 * 60))}小时前`;
    } else {
      return new Date(timestamp).toLocaleDateString();
    }
  };
  
  return (
    <Box
      p={3}
      bg={bgColor}
      borderRadius="md"
      borderLeftWidth="4px"
      borderLeftColor={`${typeColors[notification.type]}.500`}
      boxShadow="sm"
      position="relative"
      cursor="pointer"
      onClick={() => onSelect(notification)}
      _hover={{ boxShadow: 'md' }}
    >
      <Flex justify="space-between" align="center" mb={1}>
        <Text fontWeight="bold">{notification.title}</Text>
        <Badge colorScheme={typeColors[notification.type]}>
          {notification.type === 'info' && '信息'}
          {notification.type === 'success' && '成功'}
          {notification.type === 'warning' && '警告'}
          {notification.type === 'error' && '错误'}
        </Badge>
      </Flex>
      
      <Text fontSize="sm" noOfLines={2} mb={2}>
        {notification.message}
      </Text>
      
      <Flex justify="space-between" align="center" fontSize="xs" color="gray.500">
        <Text>{formatTime(notification.timestamp)}</Text>
        <Flex>
          {!notification.read && (
            <IconButton
              aria-label="标记为已读"
              icon={<CheckIcon />}
              size="xs"
              variant="ghost"
              colorScheme="blue"
              mr={1}
              onClick={(e) => {
                e.stopPropagation();
                onRead(notification.id);
              }}
            />
          )}
          <IconButton
            aria-label="删除通知"
            icon={<DeleteIcon />}
            size="xs"
            variant="ghost"
            colorScheme="red"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(notification.id);
            }}
          />
        </Flex>
      </Flex>
    </Box>
  );
};

// 通知中心组件
interface NotificationCenterProps {
  onNavigate?: (url: string) => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ onNavigate }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // 计算未读通知数量
  useEffect(() => {
    const count = notifications.filter(n => !n.read).length;
    setUnreadCount(count);
  }, [notifications]);
  
  // 标记通知为已读
  const handleMarkAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };
  
  // 删除通知
  const handleDelete = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };
  
  // 标记所有通知为已读
  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };
  
  // 清空所有通知
  const handleClearAll = () => {
    setNotifications([]);
  };
  
  // 选择通知
  const handleSelect = (notification: Notification) => {
    // 标记为已读
    handleMarkAsRead(notification.id);
    
    // 如果有链接，则导航到该链接
    if (notification.link && onNavigate) {
      onNavigate(notification.link);
      onClose();
    }
  };
  
  return (
    <>
      <Box position="relative" display="inline-block">
        <IconButton
          aria-label="通知中心"
          icon={<BellIcon />}
          onClick={onOpen}
          variant="ghost"
          size="md"
        />
        {unreadCount > 0 && (
          <Badge
            position="absolute"
            top="-2px"
            right="-2px"
            colorScheme="red"
            borderRadius="full"
            fontSize="xs"
          >
            {unreadCount}
          </Badge>
        )}
      </Box>
      
      <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="md">
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader>
            <Flex justify="space-between" align="center">
              <Text>通知中心</Text>
              <Flex>
                <Button size="sm" variant="outline" mr={2} onClick={handleMarkAllAsRead}>
                  全部已读
                </Button>
                <Button size="sm" colorScheme="red" variant="outline" onClick={handleClearAll}>
                  清空
                </Button>
              </Flex>
            </Flex>
          </DrawerHeader>
          
          <DrawerBody>
            <Tabs isFitted variant="enclosed" colorScheme="teal">
              <TabList mb={4}>
                <Tab>全部</Tab>
                <Tab>未读 {unreadCount > 0 && `(${unreadCount})`}</Tab>
              </TabList>
              
              <TabPanels>
                <TabPanel p={0}>
                  {notifications.length > 0 ? (
                    <VStack spacing={3} align="stretch">
                      {notifications.map(notification => (
                        <NotificationItem
                          key={notification.id}
                          notification={notification}
                          onRead={handleMarkAsRead}
                          onDelete={handleDelete}
                          onSelect={handleSelect}
                        />
                      ))}
                    </VStack>
                  ) : (
                    <Flex
                      direction="column"
                      align="center"
                      justify="center"
                      h="300px"
                      color="gray.500"
                    >
                      <Text>没有通知</Text>
                    </Flex>
                  )}
                </TabPanel>
                
                <TabPanel p={0}>
                  {notifications.filter(n => !n.read).length > 0 ? (
                    <VStack spacing={3} align="stretch">
                      {notifications
                        .filter(n => !n.read)
                        .map(notification => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onRead={handleMarkAsRead}
                            onDelete={handleDelete}
                            onSelect={handleSelect}
                          />
                        ))}
                    </VStack>
                  ) : (
                    <Flex
                      direction="column"
                      align="center"
                      justify="center"
                      h="300px"
                      color="gray.500"
                    >
                      <Text>没有未读通知</Text>
                    </Flex>
                  )}
                </TabPanel>
              </TabPanels>
            </Tabs>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default NotificationCenter;
