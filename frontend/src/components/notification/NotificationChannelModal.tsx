'use client'

import React, { useState, useEffect } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  Text,
  Divider,
  Switch,
  FormHelperText,
  useColorModeValue,
  Box,
  Flex,
  Icon,
  Tooltip,
  InputGroup,
  InputRightElement,
  IconButton,
} from '@chakra-ui/react'
import {
  EnvelopeSimple,
  ChatCircle,
  ChatCircleDots,
  Chats,
  Robot,
  ChatTeardrop,
  Eye,
  EyeSlash,
  Check,
  Plus,
  PencilSimple
} from '@phosphor-icons/react'

// 通知渠道类型
interface NotificationChannel {
  id: string
  name: string
  type: 'email' | 'wechat' | 'feishu' | 'dingtalk' | 'webhook' | 'sms'
  enabled: boolean
  config: {
    [key: string]: string
  }
}

interface NotificationChannelModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (channel: NotificationChannel) => void
  channel?: NotificationChannel | null
}

export default function NotificationChannelModal({
  isOpen,
  onClose,
  onSave,
  channel = null
}: NotificationChannelModalProps) {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 表单状态
  const [formData, setFormData] = useState<Omit<NotificationChannel, 'id'>>({
    name: '',
    type: 'email',
    enabled: true,
    config: {}
  })

  // 密码可见性状态
  const [showPassword, setShowPassword] = useState(false)

  // 初始化表单数据
  useEffect(() => {
    if (channel) {
      setFormData({
        name: channel.name,
        type: channel.type,
        enabled: channel.enabled,
        config: {...channel.config}
      })
    } else {
      setFormData({
        name: '',
        type: 'email',
        enabled: true,
        config: {}
      })
    }
  }, [channel, isOpen])

  // 处理表单变更
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // 处理配置变更
  const handleConfigChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [name]: value
      }
    }))
  }

  // 处理启用状态变更
  const handleEnabledChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      enabled: e.target.checked
    }))
  }

  // 处理保存
  const handleSave = () => {
    onSave(channel ? { ...formData, id: channel.id } : { ...formData, id: '' })
  }

  // 渲染不同类型的配置表单
  const renderConfigForm = () => {
    switch (formData.type) {
      case 'email':
        return (
          <>
            <FormControl isRequired>
              <FormLabel>邮箱地址</FormLabel>
              <Input
                name="email"
                value={formData.config.email || ''}
                onChange={handleConfigChange}
                placeholder="例如: <EMAIL>"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>SMTP服务器</FormLabel>
              <Input
                name="smtp_server"
                value={formData.config.smtp_server || ''}
                onChange={handleConfigChange}
                placeholder="例如: smtp.example.com"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>SMTP端口</FormLabel>
              <Input
                name="smtp_port"
                value={formData.config.smtp_port || ''}
                onChange={handleConfigChange}
                placeholder="例如: 587"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>用户名</FormLabel>
              <Input
                name="username"
                value={formData.config.username || ''}
                onChange={handleConfigChange}
                placeholder="SMTP用户名"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>密码</FormLabel>
              <InputGroup>
                <Input
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.config.password || ''}
                  onChange={handleConfigChange}
                  placeholder="SMTP密码"
                />
                <InputRightElement>
                  <IconButton
                    aria-label={showPassword ? '隐藏密码' : '显示密码'}
                    icon={showPassword ? <Icon as={EyeSlash} /> : <Icon as={Eye} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </InputRightElement>
              </InputGroup>
            </FormControl>
          </>
        )

      case 'wechat':
        return (
          <>
            <FormControl isRequired>
              <FormLabel>Webhook URL</FormLabel>
              <Input
                name="webhook_url"
                value={formData.config.webhook_url || ''}
                onChange={handleConfigChange}
                placeholder="企业微信机器人Webhook URL"
              />
              <FormHelperText>
                在企业微信群中添加机器人后获取的Webhook地址
              </FormHelperText>
            </FormControl>

            <FormControl>
              <FormLabel>密钥</FormLabel>
              <InputGroup>
                <Input
                  name="secret"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.config.secret || ''}
                  onChange={handleConfigChange}
                  placeholder="可选: 安全设置中的签名密钥"
                />
                <InputRightElement>
                  <IconButton
                    aria-label={showPassword ? '隐藏密钥' : '显示密钥'}
                    icon={showPassword ? <Icon as={EyeSlash} /> : <Icon as={Eye} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </InputRightElement>
              </InputGroup>
              <FormHelperText>
                如果机器人启用了签名验证，需要填写此项
              </FormHelperText>
            </FormControl>
          </>
        )

      case 'feishu':
        return (
          <>
            <FormControl isRequired>
              <FormLabel>Webhook URL</FormLabel>
              <Input
                name="webhook_url"
                value={formData.config.webhook_url || ''}
                onChange={handleConfigChange}
                placeholder="飞书机器人Webhook URL"
              />
              <FormHelperText>
                在飞书群中添加自定义机器人后获取的Webhook地址
              </FormHelperText>
            </FormControl>

            <FormControl>
              <FormLabel>签名密钥</FormLabel>
              <InputGroup>
                <Input
                  name="secret"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.config.secret || ''}
                  onChange={handleConfigChange}
                  placeholder="可选: 安全设置中的签名密钥"
                />
                <InputRightElement>
                  <IconButton
                    aria-label={showPassword ? '隐藏密钥' : '显示密钥'}
                    icon={showPassword ? <Icon as={EyeSlash} /> : <Icon as={Eye} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </InputRightElement>
              </InputGroup>
              <FormHelperText>
                如果机器人启用了签名验证，需要填写此项
              </FormHelperText>
            </FormControl>
          </>
        )

      case 'dingtalk':
        return (
          <>
            <FormControl isRequired>
              <FormLabel>Webhook URL</FormLabel>
              <Input
                name="webhook_url"
                value={formData.config.webhook_url || ''}
                onChange={handleConfigChange}
                placeholder="钉钉机器人Webhook URL"
              />
              <FormHelperText>
                在钉钉群中添加自定义机器人后获取的Webhook地址
              </FormHelperText>
            </FormControl>

            <FormControl>
              <FormLabel>安全令牌</FormLabel>
              <InputGroup>
                <Input
                  name="secret"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.config.secret || ''}
                  onChange={handleConfigChange}
                  placeholder="可选: 安全设置中的签名密钥"
                />
                <InputRightElement>
                  <IconButton
                    aria-label={showPassword ? '隐藏密钥' : '显示密钥'}
                    icon={showPassword ? <Icon as={EyeSlash} /> : <Icon as={Eye} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </InputRightElement>
              </InputGroup>
              <FormHelperText>
                如果机器人启用了签名验证，需要填写此项
              </FormHelperText>
            </FormControl>
          </>
        )

      case 'webhook':
        return (
          <>
            <FormControl isRequired>
              <FormLabel>Webhook URL</FormLabel>
              <Input
                name="webhook_url"
                value={formData.config.webhook_url || ''}
                onChange={handleConfigChange}
                placeholder="HTTP Webhook URL"
              />
            </FormControl>

            <FormControl>
              <FormLabel>请求方法</FormLabel>
              <Select
                name="method"
                value={formData.config.method || 'POST'}
                onChange={handleConfigChange}
              >
                <option value="POST">POST</option>
                <option value="GET">GET</option>
                <option value="PUT">PUT</option>
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel>认证令牌</FormLabel>
              <InputGroup>
                <Input
                  name="auth_token"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.config.auth_token || ''}
                  onChange={handleConfigChange}
                  placeholder="可选: 认证令牌"
                />
                <InputRightElement>
                  <IconButton
                    aria-label={showPassword ? '隐藏令牌' : '显示令牌'}
                    icon={showPassword ? <Icon as={EyeSlash} /> : <Icon as={Eye} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </InputRightElement>
              </InputGroup>
            </FormControl>
          </>
        )

      case 'sms':
        return (
          <>
            <FormControl isRequired>
              <FormLabel>手机号码</FormLabel>
              <Input
                name="phone_number"
                value={formData.config.phone_number || ''}
                onChange={handleConfigChange}
                placeholder="例如: 13800138000"
              />
            </FormControl>

            <FormControl>
              <FormLabel>短信服务提供商</FormLabel>
              <Select
                name="provider"
                value={formData.config.provider || 'aliyun'}
                onChange={handleConfigChange}
              >
                <option value="aliyun">阿里云</option>
                <option value="tencent">腾讯云</option>
                <option value="other">其他</option>
              </Select>
            </FormControl>

            <FormControl isRequired>
              <FormLabel>AccessKey ID</FormLabel>
              <Input
                name="access_key_id"
                value={formData.config.access_key_id || ''}
                onChange={handleConfigChange}
                placeholder="API访问密钥ID"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel>AccessKey Secret</FormLabel>
              <InputGroup>
                <Input
                  name="access_key_secret"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.config.access_key_secret || ''}
                  onChange={handleConfigChange}
                  placeholder="API访问密钥Secret"
                />
                <InputRightElement>
                  <IconButton
                    aria-label={showPassword ? '隐藏密钥' : '显示密钥'}
                    icon={showPassword ? <Icon as={EyeSlash} /> : <Icon as={Eye} />}
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </InputRightElement>
              </InputGroup>
            </FormControl>

            <FormControl>
              <FormLabel>短信模板ID</FormLabel>
              <Input
                name="template_id"
                value={formData.config.template_id || ''}
                onChange={handleConfigChange}
                placeholder="短信模板ID"
              />
            </FormControl>
          </>
        )

      default:
        return null
    }
  }

  // 获取渠道类型图标和颜色
  const getChannelTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return { icon: EnvelopeSimple, color: 'blue.500' }
      case 'wechat':
        return { icon: ChatCircle, color: 'green.500' }
      case 'feishu':
        return { icon: ChatCircleDots, color: 'blue.400' }
      case 'dingtalk':
        return { icon: Chats, color: 'red.500' }
      case 'webhook':
        return { icon: Robot, color: 'purple.500' }
      case 'sms':
        return { icon: ChatTeardrop, color: 'orange.500' }
      default:
        return { icon: EnvelopeSimple, color: 'blue.500' }
    }
  }

  // 获取渠道类型名称
  const getChannelTypeName = (type: string) => {
    switch (type) {
      case 'email':
        return '邮件通知'
      case 'wechat':
        return '企业微信机器人'
      case 'feishu':
        return '飞书机器人'
      case 'dingtalk':
        return '钉钉机器人'
      case 'webhook':
        return 'Webhook'
      case 'sms':
        return '短信通知'
      default:
        return '未知类型'
    }
  }

  // 获取渠道类型描述
  const getChannelTypeDescription = (type: string) => {
    switch (type) {
      case 'email':
        return '通过SMTP服务器发送邮件通知，支持HTML格式邮件'
      case 'wechat':
        return '通过企业微信机器人向群聊发送消息通知'
      case 'feishu':
        return '通过飞书机器人向群聊发送消息通知'
      case 'dingtalk':
        return '通过钉钉机器人向群聊发送消息通知'
      case 'webhook':
        return '通过HTTP请求发送通知到自定义Webhook地址'
      case 'sms':
        return '通过短信服务向指定手机号发送短信通知'
      default:
        return ''
    }
  }

  const { icon: TypeIcon, color: typeColor } = getChannelTypeIcon(formData.type)

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay backdropFilter="blur(2px)" />
      <ModalContent bg={bgColor} mx="auto" maxW="800px">
        <ModalHeader
          bg={useColorModeValue('gray.50', 'gray.800')}
          borderBottomWidth="1px"
          borderColor={borderColor}
          borderTopRadius="md"
          py={4}
        >
          <Flex align="center">
            <Icon
              as={channel ? PencilSimple : Plus}
              mr={2}
              color={useColorModeValue('blue.500', 'blue.300')}
            />
            <Text>{channel ? '编辑通知渠道' : '添加通知渠道'}</Text>
          </Flex>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody py={6}>
          <Box mb={6}>
            <Text fontSize="sm" color="gray.600">
              配置通知渠道，用于接收系统告警和事件通知。不同类型的通知渠道有不同的配置要求。
            </Text>
          </Box>

          <Flex gap={6} direction={{ base: 'column', md: 'row' }}>
            {/* 左侧 - 基本信息 */}
            <Box flex="1">
              <Box
                p={4}
                bg={useColorModeValue('gray.50', 'gray.700')}
                borderRadius="md"
                borderWidth="1px"
                borderColor={borderColor}
                mb={4}
              >
                <Text fontWeight="medium" mb={4}>基本信息</Text>

                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel>名称</FormLabel>
                    <Input
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="通知渠道名称"
                      bg={useColorModeValue('white', 'gray.800')}
                    />
                    <FormHelperText>为通知渠道设置一个易于识别的名称</FormHelperText>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>类型</FormLabel>
                    <Box position="relative">
                      <Select
                        name="type"
                        value={formData.type}
                        onChange={handleChange}
                        bg={useColorModeValue('white', 'gray.800')}
                        pl={10}
                      >
                        <option value="email">邮件通知</option>
                        <option value="wechat">企业微信机器人</option>
                        <option value="feishu">飞书机器人</option>
                        <option value="dingtalk">钉钉机器人</option>
                        <option value="webhook">Webhook</option>
                        <option value="sms">短信通知</option>
                      </Select>
                      <Box position="absolute" left={3} top="50%" transform="translateY(-50%)" pointerEvents="none">
                        <Icon as={TypeIcon} color={typeColor} />
                      </Box>
                    </Box>
                  </FormControl>

                  <Box
                    p={3}
                    borderRadius="md"
                    bg={useColorModeValue(`${typeColor.split('.')[0]}.50`, 'gray.700')}
                    borderWidth="1px"
                    borderColor={useColorModeValue(`${typeColor.split('.')[0]}.100`, 'gray.600')}
                  >
                    <Flex align="center" mb={2}>
                      <Icon as={TypeIcon} color={typeColor} mr={2} />
                      <Text fontWeight="medium">{getChannelTypeName(formData.type)}</Text>
                    </Flex>
                    <Text fontSize="sm" color="gray.600">
                      {getChannelTypeDescription(formData.type)}
                    </Text>
                  </Box>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="enabled" mb="0">
                      启用此通知渠道
                    </FormLabel>
                    <Switch
                      id="enabled"
                      isChecked={formData.enabled}
                      onChange={handleEnabledChange}
                      colorScheme="green"
                      size="md"
                      cursor="pointer"
                      zIndex={1}
                    />
                  </FormControl>
                </VStack>
              </Box>
            </Box>

            {/* 右侧 - 配置信息 */}
            <Box flex="1">
              <Box
                p={4}
                bg={useColorModeValue('gray.50', 'gray.700')}
                borderRadius="md"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <Text fontWeight="medium" mb={4}>配置信息</Text>
                <VStack spacing={4} align="stretch">
                  {renderConfigForm()}
                </VStack>
              </Box>
            </Box>
          </Flex>
        </ModalBody>

        <ModalFooter
          borderTopWidth="1px"
          borderColor={borderColor}
          bg={useColorModeValue('gray.50', 'gray.800')}
          borderBottomRadius="md"
          py={4}
        >
          <Button variant="outline" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSave}
            leftIcon={<Icon as={Check} />}
            isDisabled={!formData.name}
          >
            保存
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
