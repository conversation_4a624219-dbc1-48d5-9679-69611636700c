<template>
  <div class="equipment-form">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入设备名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编号" prop="code">
            <el-input v-model="form.code" placeholder="请输入设备编号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择设备类型" style="width: 100%">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格型号" prop="model">
            <el-input v-model="form.model" placeholder="请输入规格型号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属部门" prop="department">
            <el-select v-model="form.department" placeholder="请选择所属部门" style="width: 100%">
              <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人" prop="owner">
            <el-input v-model="form.owner" placeholder="请输入负责人"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="启用时间" prop="startDate">
            <el-date-picker v-model="form.startDate" type="date" placeholder="选择日期" style="width: 100%"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择设备状态" style="width: 100%">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="设备描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入设备描述"></el-input>
      </el-form-item>
      
      <el-form-item label="设备参数">
        <div class="parameters-table">
          <el-table :data="form.parameters" border style="width: 100%">
            <el-table-column label="参数名称" width="180">
              <template #default="scope">
                <el-input v-model="scope.row.name" placeholder="参数名称"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="参数值">
              <template #default="scope">
                <el-input v-model="scope.row.value" placeholder="参数值"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeParameter(scope.$index)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-parameter">
            <el-button type="primary" plain size="small" @click="addParameter">添加参数</el-button>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="设备图片">
        <el-upload
          action="#"
          list-type="picture-card"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="handleFileChange"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
      
      <el-form-item label="3D模型">
        <el-upload
          action="#"
          :auto-upload="false"
          :on-change="handleModelChange"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">支持.obj/.gltf/.glb格式，大小不超过10MB</div>
          </template>
        </el-upload>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, defineEmits } from 'vue'

export default {
  name: 'EquipmentForm',
  props: {
    equipmentData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const formRef = ref(null)
    
    // 表单数据
    const form = reactive({
      name: props.equipmentData.name || '',
      code: props.equipmentData.code || '',
      type: props.equipmentData.type || '',
      model: props.equipmentData.model || '',
      department: props.equipmentData.department || '',
      owner: props.equipmentData.owner || '',
      startDate: props.equipmentData.startDate || '',
      status: props.equipmentData.status || '正常',
      description: props.equipmentData.description || '',
      parameters: props.equipmentData.parameters || []
    })
    
    // 文件列表
    const fileList = ref([])
    
    // 表单验证规则
    const rules = {
      name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
      code: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
      type: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
      department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
      status: [{ required: true, message: '请选择设备状态', trigger: 'change' }]
    }
    
    // 选项数据
    const typeOptions = [
      { value: '水泵', label: '水泵' },
      { value: '电机', label: '电机' },
      { value: '空压机', label: '空压机' },
      { value: '控制阀', label: '控制阀' },
      { value: '传感器', label: '传感器' }
    ]
    
    const departmentOptions = [
      { value: '生产部', label: '生产部' },
      { value: '设备部', label: '设备部' },
      { value: '质检部', label: '质检部' },
      { value: '研发部', label: '研发部' }
    ]
    
    const statusOptions = [
      { value: '正常', label: '正常' },
      { value: '故障', label: '故障' },
      { value: '维修中', label: '维修中' },
      { value: '待检', label: '待检' }
    ]
    
    // 添加参数
    const addParameter = () => {
      form.parameters.push({
        name: '',
        value: ''
      })
    }
    
    // 移除参数
    const removeParameter = (index) => {
      form.parameters.splice(index, 1)
    }
    
    // 处理文件变更
    const handleFileChange = (file, fileList) => {
      fileList.value = fileList
    }
    
    // 处理3D模型变更
    const handleModelChange = (file) => {
      console.log('3D模型文件:', file)
      // TODO: 处理3D模型文件上传
    }
    
    // 提交表单
    const submitForm = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          // 构建提交的数据
          const formData = {
            ...form,
            // 处理其他需要特殊处理的字段
          }
          
          // 触发保存事件
          emit('save', formData)
        }
      })
    }
    
    // 重置表单
    const resetForm = () => {
      formRef.value.resetFields()
      form.parameters = []
      fileList.value = []
    }
    
    return {
      formRef,
      form,
      rules,
      fileList,
      typeOptions,
      departmentOptions,
      statusOptions,
      addParameter,
      removeParameter,
      handleFileChange,
      handleModelChange,
      submitForm,
      resetForm
    }
  }
}
</script>

<style scoped>
.equipment-form {
  padding: 20px;
}
.parameters-table {
  margin-bottom: 10px;
}
.add-parameter {
  margin-top: 10px;
  text-align: center;
}
</style>