'use client'

import * as React from 'react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import { Bell, Moon, Search, Settings, Sun, User } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'

export default function ShadcnHeader() {
  const { theme, setTheme } = useTheme()
  const [notificationCount, setNotificationCount] = React.useState(3)

  return (
    <header className="sticky top-0 z-20 flex h-16 items-center justify-between border-b bg-background px-4 md:px-6 shadow-md">
      <div className="flex items-center gap-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-primary" />
          <Input
            type="search"
            placeholder="搜索..."
            className="w-[200px] md:w-[300px] pl-8 bg-background border-primary/30 focus-visible:ring-primary/50 transition-all duration-300 hover:border-primary/50"
          />
        </div>
      </div>
      <div className="flex items-center gap-4">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="relative hover:bg-accent/20 transition-all duration-300"
                onClick={() => setNotificationCount(0)}
              >
                <Bell className="h-5 w-5 text-info hover:text-info-600 transition-all duration-300" />
                {notificationCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 flex items-center justify-center bg-gradient-to-r from-destructive-400 to-destructive-600 border-white border"
                  >
                    {notificationCount}
                  </Badge>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent className="bg-gradient-to-r from-info-500 to-info-600 text-white border-none">
              <p>通知</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="hover:bg-accent/20 transition-all duration-300"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              >
                <Sun className="h-5 w-5 rotate-0 scale-100 transition-all text-warning hover:text-warning-600 dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all text-secondary hover:text-secondary-600 dark:rotate-0 dark:scale-100" />
                <span className="sr-only">切换主题</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent className="bg-gradient-to-r from-warning-500 to-warning-600 text-white border-none dark:from-secondary-500 dark:to-secondary-600">
              <p>切换主题</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-8 w-8 rounded-full ring-2 ring-primary/30 hover:ring-primary/70 transition-all duration-300"
            >
              <Avatar className="h-8 w-8 border-2 border-transparent hover:border-primary/50 transition-all duration-300">
                <AvatarImage src="/avatar.jpg" alt="用户头像" />
                <AvatarFallback className="bg-gradient-to-r from-primary-400 to-primary-600 text-white">BR</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-56 border border-primary/20 shadow-lg shadow-primary/10 animate-in fade-in-80 slide-in-from-top-5 duration-300"
            align="end"
            forceMount
          >
            <DropdownMenuLabel className="font-normal bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 rounded-t-md">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none text-primary-700 dark:text-primary-300">管理员</p>
                <p className="text-xs leading-none text-primary-600/70 dark:text-primary-400/70">
                  <EMAIL>
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-primary/10" />
            <DropdownMenuItem asChild className="focus:bg-primary-50 dark:focus:bg-primary-900/30 transition-colors duration-200">
              <Link href="/profile" className="cursor-pointer">
                <User className="mr-2 h-4 w-4 text-primary-600 dark:text-primary-400" />
                <span>个人资料</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild className="focus:bg-primary-50 dark:focus:bg-primary-900/30 transition-colors duration-200">
              <Link href="/settings" className="cursor-pointer">
                <Settings className="mr-2 h-4 w-4 text-primary-600 dark:text-primary-400" />
                <span>设置</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-primary/10" />
            <DropdownMenuItem className="cursor-pointer text-destructive focus:text-destructive focus:bg-destructive-50 dark:focus:bg-destructive-900/30 transition-colors duration-200">
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}