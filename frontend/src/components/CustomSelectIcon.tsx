import React from 'react';
import { Icon, useColorModeValue } from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';

export const CustomSelectIcon = (props: any) => {
  const color = useColorModeValue('gray.500', 'gray.400');
  
  return (
    <Icon
      as={ChevronDownIcon}
      color={color}
      w="1.5rem"
      h="1.5rem"
      {...props}
    />
  );
};

export default CustomSelectIcon;
