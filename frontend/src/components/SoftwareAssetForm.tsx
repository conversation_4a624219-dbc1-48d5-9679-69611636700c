'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon, FileText, Shield, Database, Monitor } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/tabs'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'

interface AssetCategory {
  id: number
  name: string
  code: string
  description?: string
  level: number
  parent_id?: number
}

interface SoftwareAssetFormData {
  name: string
  code: string
  category_id: number
  software_type: string
  version: string
  vendor: string
  license_type: string
  license_key?: string
  license_count: number
  used_licenses: number
  purchase_date?: Date
  expiration_date?: Date
  support_end_date?: Date
  installation_path?: string
  system_requirements?: string
  description?: string
  cost?: number
  department?: string
  responsible_person?: string
  status: string
  is_critical: boolean
  auto_update_enabled: boolean
}

const softwareAssetSchema = z.object({
  name: z.string().min(1, '软件名称不能为空'),
  code: z.string().min(1, '软件编码不能为空'),
  category_id: z.number().min(1, '请选择软件分类'),
  software_type: z.string().min(1, '请选择软件类型'),
  version: z.string().min(1, '版本号不能为空'),
  vendor: z.string().min(1, '厂商不能为空'),
  license_type: z.string().min(1, '请选择许可证类型'),
  license_key: z.string().optional(),
  license_count: z.number().min(1, '许可证数量必须大于0'),
  used_licenses: z.number().min(0, '已使用许可证数量不能为负数'),
  purchase_date: z.date().optional(),
  expiration_date: z.date().optional(),
  support_end_date: z.date().optional(),
  installation_path: z.string().optional(),
  system_requirements: z.string().optional(),
  description: z.string().optional(),
  cost: z.number().optional(),
  department: z.string().optional(),
  responsible_person: z.string().optional(),
  status: z.string(),
  is_critical: z.boolean(),
  auto_update_enabled: z.boolean(),
})

interface SoftwareAssetFormProps {
  onSubmit: (data: SoftwareAssetFormData) => Promise<void>
  initialData?: Partial<SoftwareAssetFormData>
  isLoading?: boolean
}

export function SoftwareAssetForm({ onSubmit, initialData, isLoading = false }: SoftwareAssetFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [autoGenerateCode, setAutoGenerateCode] = useState(true)

  const form = useForm<SoftwareAssetFormData>({
    resolver: zodResolver(softwareAssetSchema),
    defaultValues: {
      name: '',
      code: '',
      category_id: 0,
      software_type: '',
      version: '',
      vendor: '',
      license_type: '',
      license_count: 1,
      used_licenses: 0,
      status: 'active',
      is_critical: false,
      auto_update_enabled: false,
      ...initialData
    }
  })

  // 获取软件分类（筛选软件相关分类）
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/asset-categories')
        if (response.ok) {
          const data = await response.json()
          // 过滤软件相关分类
          const softwareCategories = data.filter((cat: AssetCategory) =>
            cat.name.includes('软件') || cat.code.includes('SW')
          )
          setCategories(softwareCategories)
        }
      } catch (error) {
        console.error('获取资产分类失败:', error)
        toast({
          title: '错误',
          description: '获取资产分类失败',
          variant: 'destructive'
        })
      }
    }
    fetchCategories()
  }, [])

  // 构建层级化的分类选项
  const buildHierarchicalCategories = (categories: AssetCategory[], parentId: number | null = null, level: number = 0): any[] => {
    const result: any[] = []
    const children = categories.filter(cat => cat.parent_id === parentId)
    
    children.forEach(category => {
      const indent = '　'.repeat(level) // 使用全角空格缩进
      result.push({
        ...category,
        displayName: `${indent}${category.name} (${category.code})`,
        level
      })
      
      // 递归添加子分类
      const subCategories = buildHierarchicalCategories(categories, category.id, level + 1)
      result.push(...subCategories)
    })
    
    return result
  }

  const hierarchicalCategories = buildHierarchicalCategories(categories)

  // 自动生成软件编码
  const generateSoftwareCode = (categoryId: number, name: string, vendor: string) => {
    if (!autoGenerateCode) return
    
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      const prefix = category.code || 'SW'
      const timestamp = Date.now().toString().slice(-4)
      const vendorPrefix = vendor.substring(0, 2).toUpperCase()
      const namePrefix = name.substring(0, 2).toUpperCase()
      const code = `${prefix}-${vendorPrefix}${namePrefix}${timestamp}`
      form.setValue('code', code)
    }
  }

  // 监听字段变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name: fieldName }) => {
      if (fieldName === 'category_id' || fieldName === 'name' || fieldName === 'vendor') {
        const categoryId = value.category_id
        const softwareName = value.name
        const vendor = value.vendor
        if (categoryId && softwareName && vendor && autoGenerateCode) {
          generateSoftwareCode(categoryId, softwareName, vendor)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, categories, autoGenerateCode])

  const handleSubmit = async (data: SoftwareAssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      toast({
        title: '成功',
        description: '软件资产登记成功',
      })
      form.reset()
    } catch (error) {
      console.error('软件资产登记失败:', error)
      toast({
        title: '错误',
        description: '软件资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>软件名称 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入软件名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                软件编码 *
                <Switch
                  checked={autoGenerateCode}
                  onCheckedChange={setAutoGenerateCode}
                  size="sm"
                />
                <span className="text-xs text-muted-foreground">自动生成</span>
              </FormLabel>
              <FormControl>
                <Input 
                  placeholder="请输入软件编码" 
                  {...field} 
                  disabled={autoGenerateCode}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>软件分类 *</FormLabel>
              <Select onValueChange={(value) => field.onChange(Number(value))} value={field.value?.toString()}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择软件分类" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hierarchicalCategories.map((category) => (
                    <SelectItem 
                      key={category.id} 
                      value={category.id.toString()}
                      className={category.level > 0 ? 'text-sm' : 'font-medium'}
                    >
                      {category.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="software_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>软件类型 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择软件类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="operating_system">操作系统</SelectItem>
                  <SelectItem value="database">数据库软件</SelectItem>
                  <SelectItem value="office">办公软件</SelectItem>
                  <SelectItem value="security">安全软件</SelectItem>
                  <SelectItem value="development">开发工具</SelectItem>
                  <SelectItem value="middleware">中间件</SelectItem>
                  <SelectItem value="application">应用软件</SelectItem>
                  <SelectItem value="utility">系统工具</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="vendor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>厂商 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入厂商名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="version"
          render={({ field }) => (
            <FormItem>
              <FormLabel>版本号 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入版本号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>软件状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择软件状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="active">正常使用</SelectItem>
                  <SelectItem value="maintenance">维护中</SelectItem>
                  <SelectItem value="deprecated">已弃用</SelectItem>
                  <SelectItem value="expired">已过期</SelectItem>
                  <SelectItem value="retired">已退役</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 位置信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">位置信息</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>安装位置</FormLabel>
                <FormControl>
                  <Input placeholder="请输入软件安装位置" {...field} />
                </FormControl>
                <FormDescription>
                  软件安装的服务器或设备位置
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="department"
            render={({ field }) => (
              <FormItem>
                <FormLabel>使用部门</FormLabel>
                <FormControl>
                  <Input placeholder="请输入使用部门" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>负责人</FormLabel>
              <FormControl>
                <Input placeholder="请输入负责人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )

  const renderLicenseInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="license_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>许可证类型 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择许可证类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="perpetual">永久许可</SelectItem>
                  <SelectItem value="subscription">订阅许可</SelectItem>
                  <SelectItem value="concurrent">并发许可</SelectItem>
                  <SelectItem value="named_user">指定用户</SelectItem>
                  <SelectItem value="site">站点许可</SelectItem>
                  <SelectItem value="oem">OEM许可</SelectItem>
                  <SelectItem value="trial">试用许可</SelectItem>
                  <SelectItem value="free">免费许可</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="license_key"
          render={({ field }) => (
            <FormItem>
              <FormLabel>许可证密钥</FormLabel>
              <FormControl>
                <Input placeholder="请输入许可证密钥" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="license_count"
          render={({ field }) => (
            <FormItem>
              <FormLabel>许可证总数 *</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入许可证总数" 
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="used_licenses"
          render={({ field }) => (
            <FormItem>
              <FormLabel>已使用许可证</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入已使用许可证数量" 
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="purchase_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>购买日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择购买日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="expiration_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>到期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择到期日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="support_end_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>支持结束日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择支持结束日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )

  const renderAdditionalInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>使用部门</FormLabel>
              <FormControl>
                <Input placeholder="请输入使用部门" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>负责人</FormLabel>
              <FormControl>
                <Input placeholder="请输入负责人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="cost"
          render={({ field }) => (
            <FormItem>
              <FormLabel>采购成本</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入采购成本" 
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="installation_path"
        render={({ field }) => (
          <FormItem>
            <FormLabel>安装路径</FormLabel>
            <FormControl>
              <Input placeholder="请输入软件安装路径" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="system_requirements"
        render={({ field }) => (
          <FormItem>
            <FormLabel>系统要求</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请输入系统要求和依赖" 
                className="min-h-[100px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>备注说明</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请输入备注说明" 
                className="min-h-[100px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="is_critical"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">关键软件</FormLabel>
                <div className="text-sm text-muted-foreground">
                  标记为业务关键软件
                </div>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="auto_update_enabled"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">自动更新</FormLabel>
                <div className="text-sm text-muted-foreground">
                  启用软件自动更新
                </div>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </div>
  )

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="license" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              许可证信息
            </TabsTrigger>
            <TabsTrigger value="additional" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              附加信息
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="mt-6">
            {renderBasicInfo()}
          </TabsContent>
          
          <TabsContent value="license" className="mt-6">
            {renderLicenseInfo()}
          </TabsContent>
          
          <TabsContent value="additional" className="mt-6">
            {renderAdditionalInfo()}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button type="button" variant="outline" onClick={() => form.reset()}>
            重置
          </Button>
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting ? '登记中...' : '确认登记'}
          </Button>
        </div>
      </form>
    </Form>
  )
}