'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  Box, 
  Button,
  useDisclosure,
  useColorModeValue,
  Flex,
  Text,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  useToast,
  IconButton,
  Tooltip,
  Select,
  Badge
} from '@chakra-ui/react'
import { WifiHigh, Plus, ArrowsClockwise, DotsSixVertical } from '@phosphor-icons/react'
import { useTranslation } from '@/i18n/client'
import WiFiDevice from './WiFiDevice'
import WiFiDeviceDetails from './WiFiDeviceDetails'
import { keyframes } from '@emotion/react'

// Floor plan image URLs
const getFloorPlanImage = (floor: string) => {
  // Map of floor names to their corresponding image paths
  const floorImageMap: Record<string, string> = {
    '1F': '/images/1F.png',
    '2F': '/images/2f.png', // lowercase 'f' for 2F
    '3F': '/images/3F.png',
    '4F': '/images/4F.png',
    '5F': '/images/5F.png',
    '6F': '/images/6F.png',
    '7F': '/images/7F.png',
    '8F': '/images/8F.png',
  };
  
  return floorImageMap[floor] || `/images/${floor}.png`;
}

// Define device type
interface Device {
  id: string
  name: string
  status: 'online' | 'offline' | 'warning'
  position: { x: number, y: number }
  floor: string
  ip?: string
  mac?: string
  model?: string
  location?: string
  channel?: string
  bandwidth?: string
  power?: string
  clients?: number
  signal?: string
}

export default function WirelessMapPage() {
  const { t } = useTranslation()
  const toast = useToast()
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const [devices, setDevices] = useState<Device[]>([])
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [currentFloor, setCurrentFloor] = useState('1F')
  const [imageFallback, setImageFallback] = useState(false)
  const { isOpen: isDetailOpen, onOpen: onDetailOpen, onClose: onDetailClose } = useDisclosure()
  const { 
    isOpen: isConfirmOpen, 
    onOpen: onConfirmOpen, 
    onClose: onConfirmClose 
  } = useDisclosure()
  const cancelRef = useRef<HTMLButtonElement>(null)
  
  // Theme colors
  const mapBorderColor = useColorModeValue('gray.200', 'gray.600')
  const mapBgColor = useColorModeValue('gray.50', 'gray.800')
  
  // For testing image loading
  const imageRef = useRef<HTMLImageElement>(null)
  
  // Check if the floor plan image is loading correctly
  useEffect(() => {
    const testImg = new Image()
    testImg.onload = () => {
      console.log(`Image loaded successfully: ${getFloorPlanImage(currentFloor)}`)
      setImageFallback(false)
    }
    testImg.onerror = () => {
      console.error(`Failed to load image: ${getFloorPlanImage(currentFloor)}`)
      setImageFallback(true)
    }
    testImg.src = getFloorPlanImage(currentFloor)
  }, [currentFloor])
  
  // Load devices from storage or server on component mount
  useEffect(() => {
    const loadDevices = async () => {
      try {
        // In a real application, you would fetch this from an API
        // For now, we'll use local storage
        const savedDevices = localStorage.getItem('wirelessDevices')
        if (savedDevices) {
          setDevices(JSON.parse(savedDevices))
        }
      } catch (error) {
        console.error('Failed to load devices:', error)
      }
    }
    
    loadDevices()
  }, [])
  
  // Save devices when they change
  useEffect(() => {
    if (devices.length > 0) {
      localStorage.setItem('wirelessDevices', JSON.stringify(devices))
    }
  }, [devices])
  
  // Handle positioning a new device where the user clicked
  const handleMapClick = (e: React.MouseEvent) => {
    // This function is no longer used - devices are only added by dragging the WiFi icon
  }
  
  // Add new device
  const handleAddDevice = () => {
    if (!selectedDevice) return
    
    try {
      setDevices(prev => [...prev, selectedDevice])
      onConfirmClose()
      
      toast({
        title: t('success'),
        description: t('deviceAdded'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorAddingDevice'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    }
  }
  
  // Update device position when dragged
  const handlePositionChange = (id: string, newPosition: { x: number, y: number }) => {
    try {
      setDevices(prev => 
        prev.map(device => 
          device.id === id 
            ? { ...device, position: newPosition } 
            : device
        )
      )
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorUpdatingPosition'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    }
  }
  
  // Handle selecting a device
  const handleSelectDevice = (device: Device) => {
    setSelectedDevice(device)
  }
  
  // Handle double-click to view/edit device details
  const handleDeviceDoubleClick = (device: Device) => {
    setSelectedDevice(device)
    onDetailOpen()
  }
  
  // Handle save device after editing
  const handleSaveDevice = (updatedDevice: Device) => {
    try {
      setDevices(prev => 
        prev.map(device => 
          device.id === updatedDevice.id 
            ? { ...updatedDevice, position: device.position, floor: device.floor } 
            : device
        )
      )
      
      toast({
        title: t('success'),
        description: t('deviceRefreshed'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorRefreshingDevice'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    }
  }
  
  // Handle delete device
  const handleDeleteDevice = (deviceId: string) => {
    try {
      setDevices(prev => prev.filter(device => device.id !== deviceId))
      setSelectedDevice(null)
      
      toast({
        title: t('success'),
        description: t('deviceDeleted'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorDeletingDevice'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    }
  }
  
  // Handle floor change
  const handleFloorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCurrentFloor(e.target.value)
  }
  
  // Refresh all devices data
  const handleRefreshAll = async () => {
    setIsRefreshing(true)
    
    try {
      // In a real application, you would fetch updated data from the server
      // Simulate a network request
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: t('success'),
        description: t('deviceRefreshed'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorRefreshingDevice'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    } finally {
      setIsRefreshing(false)
    }
  }
  
  const [isDragging, setIsDragging] = useState(false)
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 })
  const dragIconRef = useRef<HTMLDivElement>(null)
  
  // Define pulse animation
  const pulseAnimation = keyframes`
    0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.4); }
    70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(66, 153, 225, 0); }
    100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); }
  `
  
  return (
    <Flex direction="column" h="100%" w="100%">
      <Flex justify="space-between" mb={4} align="center">
        <Flex alignItems="center">
          <Text fontSize="xl" fontWeight="bold" mr={4}>{t('ap.location.map')}</Text>
          <Select 
            value={currentFloor} 
            onChange={handleFloorChange} 
            w="80px"
            size="sm"
          >
            <option value="1F">1F</option>
            <option value="2F">2F</option>
            <option value="3F">3F</option>
            <option value="4F">4F</option>
            <option value="5F">5F</option>
            <option value="6F">6F</option>
            <option value="7F">7F</option>
            <option value="8F">8F</option>
          </Select>
        </Flex>
      </Flex>
      
      <Box
        ref={mapContainerRef}
        position="relative"
        borderWidth="1px"
        borderColor={mapBorderColor}
        borderRadius="md"
        bg={mapBgColor}
        flex="1"
        overflow="hidden"
        backgroundImage={imageFallback ? 'none' : `url(${getFloorPlanImage(currentFloor)})`}
        backgroundSize="contain"
        backgroundPosition="center"
        backgroundRepeat="no-repeat"
      >
        {/* Fallback image element if background image fails */}
        {imageFallback && (
          <Box 
            position="absolute" 
            top="0" 
            left="0" 
            width="100%" 
            height="100%" 
            display="flex" 
            justifyContent="center" 
            alignItems="center"
            pointerEvents="none"
          >
            <img
              ref={imageRef}
              src={getFloorPlanImage(currentFloor)}
              alt={`Floor ${currentFloor} plan`}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain',
                opacity: 0.8
              }}
              onError={() => {
                console.error('Even the fallback image failed to load')
              }}
            />
          </Box>
        )}
        
        {/* Enhanced draggable WiFi icon */}
        <Box
          ref={dragIconRef}
          position="absolute"
          top="6px"
          right="6px"
          zIndex={10}
        >
          <Tooltip label={t('clickOrDragToAddDevice')} placement="left" hasArrow>
            <Flex
              direction="column"
              alignItems="center"
              p={0.5}
              bg={useColorModeValue('white', 'gray.700')}
              borderRadius="md"
              boxShadow="sm"
              cursor="grab"
              _hover={{
                boxShadow: 'md',
                transform: 'translateY(-1px)'
              }}
              _active={{
                cursor: 'grabbing',
                transform: 'translateY(0px)'
              }}
              transition="all 0.2s"
              draggable="true"
              onClick={() => {
                try {
                  // Create a device at a default position when clicked
                  const defaultX = 100;
                  const defaultY = 100;
                  
                  // Create new device object
                  const newDevice: Device = {
                    id: `device-${Date.now()}`,
                    name: `AP-${currentFloor}-${devices.filter(d => d.floor === currentFloor).length + 1}`,
                    status: 'online' as const,
                    position: { x: defaultX, y: defaultY },
                    floor: currentFloor
                  };
                  
                  // Add device directly to the map
                  setDevices(prevDevices => [...prevDevices, newDevice]);
                  
                  // Show success toast
                  toast({
                    title: t('success'),
                    description: t('deviceAdded'),
                    status: 'success',
                    duration: 2000,
                    isClosable: true,
                  });
                } catch (error) {
                  console.error('Error adding device:', error);
                  toast({
                    title: t('error'),
                    description: t('errorAddingDevice'),
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                  });
                }
              }}
              onDragStart={(e) => {
                setIsDragging(true);
                e.dataTransfer.setData('text/plain', 'wifi-device');
                
                try {
                  // Create a better looking drag image
                  const dragImage = document.createElement('div');
                  dragImage.innerHTML = `
                    <div style="
                      background-color: #4299E1; 
                      color: white; 
                      width: 20px; 
                      height: 20px; 
                      border-radius: 50%; 
                      display: flex; 
                      align-items: center; 
                      justify-content: center;
                      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                      font-size: 10px;
                    ">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                        <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                        <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                        <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                        <circle cx="12" cy="20" r="1"></circle>
                      </svg>
                    </div>
                  `;
                  const ghostElement = dragImage.firstElementChild as HTMLElement;
                  document.body.appendChild(ghostElement);
                  
                  e.dataTransfer.setDragImage(ghostElement, 10, 10);
                  
                  // Schedule removal of the ghost element
                  setTimeout(() => {
                    if (document.body.contains(ghostElement)) {
                      document.body.removeChild(ghostElement);
                    }
                  }, 0);
                } catch (error) {
                  console.error('Error creating drag image:', error);
                  // Fallback to default drag image if creating custom one fails
                }
              }}
              onDragEnd={(e) => {
                setIsDragging(false);
              }}
              animation={isDragging ? `${pulseAnimation} 2s infinite` : 'none'}
            >
              <Box 
                borderRadius="full" 
                bg="blue.500" 
                color="white" 
                p={1} 
                position="relative"
                overflow="visible"
                w="20px"
                h="20px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <WifiHigh size={12} weight="fill" />
                <Box
                  position="absolute"
                  top="-2px"
                  right="-2px"
                  borderRadius="full"
                  bg="red.500"
                  color="white"
                  fontSize="6px"
                  fontWeight="bold"
                  w="10px"
                  h="10px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Plus size={6} weight="bold" />
                </Box>
              </Box>
              <Text fontSize="8px" color="blue.500" fontWeight="bold" mt={0.5}>
                {t('addAP')}
              </Text>
            </Flex>
          </Tooltip>
        </Box>
        
        {/* Handle drag and drop events on the map */}
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          zIndex={1}
          onDragOver={(e) => {
            // Prevent default to allow drop
            e.preventDefault();
            // Change cursor to indicate valid drop target
            e.dataTransfer.dropEffect = "copy";
            
            // Track the position for visual feedback
            if (mapContainerRef.current) {
              const rect = mapContainerRef.current.getBoundingClientRect();
              setDragPosition({
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
              });
            }
          }}
          onDragEnter={(e) => {
            e.preventDefault();
          }}
          onDragLeave={(e) => {
            e.preventDefault();
          }}
          onDrop={(e) => {
            e.preventDefault();
            setIsDragging(false);
            
            // Check if the dropped item is a WiFi device
            const deviceType = e.dataTransfer.getData('text/plain');
            if (deviceType === 'wifi-device') {
              try {
                // Get drop position relative to the container
                const rect = mapContainerRef.current?.getBoundingClientRect();
                if (!rect) return;
                
                const x = e.clientX - rect.left - 10; // Center the icon
                const y = e.clientY - rect.top - 10;
                
                // Create a new device at the drop position
                const newDevice: Device = {
                  id: `device-${Date.now()}`,
                  name: `AP-${currentFloor}-${devices.filter(d => d.floor === currentFloor).length + 1}`,
                  status: 'online' as const,
                  position: { x, y },
                  floor: currentFloor
                };
                
                // Add device directly to the map
                setDevices(prevDevices => [...prevDevices, newDevice]);
                
                // Show success toast
                toast({
                  title: t('success'),
                  description: t('deviceAdded'),
                  status: 'success',
                  duration: 2000,
                  isClosable: true,
                });
              } catch (error) {
                console.error('Error adding device:', error);
                toast({
                  title: t('error'),
                  description: t('errorAddingDevice'),
                  status: 'error',
                  duration: 3000,
                  isClosable: true,
                });
              }
            }
          }}
        >
          {/* Visual indicator of where the device will be placed */}
          {isDragging && (
            <Box
              position="absolute"
              left={`${dragPosition.x - 10}px`}
              top={`${dragPosition.y - 10}px`}
              width="20px"
              height="20px"
              borderRadius="full"
              border="2px dashed"
              borderColor="blue.500"
              zIndex={2}
              pointerEvents="none"
              bg="blue.50"
              opacity={0.7}
            />
          )}
        </Box>
        
        {/* Render all WiFi devices for the current floor */}
        {devices
          .filter(device => device.floor === currentFloor)
          .map(device => (
            <WiFiDevice
              key={device.id}
              id={device.id}
              name={device.name}
              status={device.status}
              initialPosition={device.position}
              onPositionChange={handlePositionChange}
              onSelect={() => handleSelectDevice(device)}
              isSelected={selectedDevice?.id === device.id}
              onDoubleClick={() => handleDeviceDoubleClick(device)}
              isDetailsOpen={isDetailOpen && selectedDevice?.id === device.id}
            />
          ))}
      </Box>
      
      {/* Device Details Modal */}
      {selectedDevice && (
        <WiFiDeviceDetails
          device={selectedDevice}
          isOpen={isDetailOpen}
          onClose={onDetailClose}
          onSave={handleSaveDevice}
          onDelete={handleDeleteDevice}
        />
      )}
      
      {/* Confirmation Dialog for Adding New Device */}
      <AlertDialog
        isOpen={isConfirmOpen}
        leastDestructiveRef={cancelRef}
        onClose={onConfirmClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t('addDevice')}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t('confirmAddDevice')}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onConfirmClose}>
                {t('cancel')}
              </Button>
              <Button colorScheme="blue" onClick={handleAddDevice} ml={3}>
                {t('confirm')}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Flex>
  )
} 