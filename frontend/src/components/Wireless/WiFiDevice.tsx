'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  Box,
  Tooltip,
  useColorModeValue,
  Badge,
} from '@chakra-ui/react'
import { WifiHigh, Warning } from '@phosphor-icons/react'

interface WiFiDeviceProps {
  id: string
  name: string
  status: 'online' | 'offline' | 'warning'
  initialPosition: { x: number, y: number }
  onPositionChange: (id: string, newPosition: { x: number, y: number }) => void
  onSelect: () => void
  isSelected: boolean
  onDoubleClick?: () => void
  isDetailsOpen?: boolean
}

export default function WiFiDevice({
  id,
  name,
  status,
  initialPosition,
  onPositionChange,
  onSelect,
  isSelected,
  onDoubleClick,
  isDetailsOpen = false
}: WiFiDeviceProps) {
  const [position, setPosition] = useState(initialPosition)
  const [isDragging, setIsDragging] = useState(false)
  const deviceRef = useRef<HTMLDivElement>(null)
  const dragStartPos = useRef({ x: 0, y: 0 })
  const initialPos = useRef({ x: 0, y: 0 })
  const hasMoved = useRef(false)
  const lastClickTime = useRef(0)
  
  // Colors based on the theme
  const deviceBg = useColorModeValue('white', 'gray.800')
  const deviceBorder = useColorModeValue('gray.200', 'gray.700')
  const selectedBorder = useColorModeValue('blue.500', 'blue.300')
  
  // Get color based on status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green.500'
      case 'offline':
        return 'gray.500'
      case 'warning':
        return 'yellow.500'
      default:
        return 'gray.500'
    }
  }

  // Handle mouse down to start dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    
    // Check for double click
    const currentTime = new Date().getTime()
    const isDoubleClick = currentTime - lastClickTime.current < 300
    lastClickTime.current = currentTime
    
    if (isDoubleClick && onDoubleClick) {
      onDoubleClick()
      return
    }
    
    setIsDragging(true)
    hasMoved.current = false
    
    const parentRect = (deviceRef.current?.parentElement as HTMLElement)?.getBoundingClientRect()
    if (!parentRect) return
    
    // Store starting positions
    dragStartPos.current = {
      x: e.clientX,
      y: e.clientY
    }
    
    initialPos.current = { ...position }
    
    // Add mouse move and mouse up event listeners
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }
  
  // Handle mouse move during dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return
    
    const deltaX = e.clientX - dragStartPos.current.x
    const deltaY = e.clientY - dragStartPos.current.y
    
    // Check if the user has moved the device enough to consider it a drag
    if (Math.abs(deltaX) > 3 || Math.abs(deltaY) > 3) {
      hasMoved.current = true
    }
    
    const parentRect = (deviceRef.current?.parentElement as HTMLElement)?.getBoundingClientRect()
    if (!parentRect) return
    
    // Calculate new position ensuring it stays within the parent container
    const newX = Math.max(0, Math.min(initialPos.current.x + deltaX, parentRect.width - 30))
    const newY = Math.max(0, Math.min(initialPos.current.y + deltaY, parentRect.height - 30))
    
    // Update position immediately to prevent return to original position
    const newPosition = { x: newX, y: newY };
    setPosition(newPosition)
    
    // Continuously update the position while dragging to ensure it persists
    if (hasMoved.current) {
      onPositionChange(id, newPosition)
    }
  }
  
  // Handle mouse up to end dragging
  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false)
      
      // Remove event listeners
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      
      // If this was just a click, not a drag, select the device
      if (!hasMoved.current) {
        onSelect()
      }
      // Final position update (redundant but ensures consistency)
      else {
        onPositionChange(id, position)
      }
    }
  }

  // Clean up event listeners when component unmounts
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [])

  // Update position if initialPosition changes from parent
  useEffect(() => {
    setPosition(initialPosition)
  }, [initialPosition])

  return (
    <Tooltip label={name} placement="top" isDisabled={isDetailsOpen}>
      <Box
        ref={deviceRef}
        position="absolute"
        left={`${position.x}px`}
        top={`${position.y}px`}
        width="30px"
        height="30px"
        borderRadius="md"
        bg={deviceBg}
        borderWidth="2px"
        borderColor={isSelected ? selectedBorder : deviceBorder}
        display={isDetailsOpen ? "none" : "flex"}
        alignItems="center"
        justifyContent="center"
        cursor="move"
        boxShadow={isDragging ? 'lg' : 'md'}
        transition="box-shadow 0.2s"
        zIndex={isSelected ? 10 : 5}
        onMouseDown={handleMouseDown}
        userSelect="none"
        visibility={isDetailsOpen ? "hidden" : "visible"}
        opacity={isDetailsOpen ? 0 : 1}
        pointerEvents={isDetailsOpen ? 'none' : 'auto'}
      >
        <Box position="relative">
          <WifiHigh size={18} weight="bold" color={getStatusColor(status)} />
          {status === 'warning' && (
            <Box position="absolute" top="-5px" right="-5px">
              <Warning size={10} weight="fill" color="yellow.500" />
            </Box>
          )}
        </Box>
      </Box>
    </Tooltip>
  )
} 