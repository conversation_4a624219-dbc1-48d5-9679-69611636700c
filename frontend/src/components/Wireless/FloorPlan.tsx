'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  useColorModeValue,
  Image,
} from '@chakra-ui/react'

interface FloorPlanProps {
  floor: string
  children?: React.ReactNode
  onClick?: (e: React.MouseEvent) => void
}

export default function FloorPlan({ floor, children, onClick }: FloorPlanProps) {
  const [isClient, setIsClient] = useState(false)
  const strokeColorValue = useColorModeValue('gray.300', 'gray.600')
  const fillColorValue = useColorModeValue('gray.50', 'gray.900')
  
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  // Use default colors on server, actual colors on client
  const strokeColor = isClient ? strokeColorValue : 'gray.300'
  const fillColor = isClient ? fillColorValue : 'gray.50'

  // Only show the drawing for floors other than 1F
  // For 1F we'll use the 1F.png image
  const shouldShowDrawing = floor !== '1F'
  
  // Get the correct image path
  const getFloorImagePath = (floor: string) => {
    // First try the images subdirectory
    return `/images/${floor}.png`;
  }

  // Simplified floor plan paths based on the provided CAD drawings
  const floorPaths = {
    // Main outline of the floor
    outline: 'M 50 50 H 950 V 550 H 50 V 50',
    // Major internal walls and divisions
    walls: [
      'M 200 50 V 550', // Vertical wall 1
      'M 400 50 V 550', // Vertical wall 2
      'M 600 50 V 550', // Vertical wall 3
      'M 800 50 V 550', // Vertical wall 4
      'M 50 200 H 950', // Horizontal wall 1
      'M 50 400 H 950', // Horizontal wall 2
    ],
    // Room labels and areas
    rooms: [
      { x: 125, y: 125, label: '办公区' },
      { x: 325, y: 125, label: '会议室' },
      { x: 525, y: 125, label: '休息区' },
      { x: 725, y: 125, label: '设备间' },
      { x: 125, y: 325, label: '培训室' },
      { x: 325, y: 325, label: '储藏室' },
      { x: 525, y: 325, label: '打印室' },
      { x: 725, y: 325, label: '茶水间' },
    ],
  }

  return (
    <Box 
      position="relative" 
      width="100%" 
      height="600px" 
      onClick={onClick}
      cursor={onClick ? 'pointer' : 'default'}
    >
      {floor === '1F' ? (
        // For 1F, use the 1F.png image
        <Image 
          src={getFloorImagePath(floor)}
          alt={`${floor} Floor Plan`}
          width="100%"
          height="100%"
          objectFit="contain"
          fallback={
            <Box 
              width="100%" 
              height="100%" 
              display="flex" 
              alignItems="center" 
              justifyContent="center"
              bg={fillColor}
              border={`1px solid`}
              borderColor={strokeColor}
            >
              <Box>无法加载 {floor} 楼层平面图</Box>
            </Box>
          }
        />
      ) : (
        // For other floors, use the SVG drawing
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 1000 600"
          preserveAspectRatio="xMidYMid meet"
          suppressHydrationWarning
        >
          {/* Floor outline */}
          <path
            d={floorPaths.outline}
            fill={fillColor}
            stroke={strokeColor}
            strokeWidth="2"
            suppressHydrationWarning
          />

          {/* Internal walls */}
          {floorPaths.walls.map((path, index) => (
            <path
              key={index}
              d={path}
              fill="none"
              stroke={strokeColor}
              strokeWidth="2"
              strokeDasharray="5,5"
              suppressHydrationWarning
            />
          ))}

          {/* Room labels */}
          {floorPaths.rooms.map((room, index) => (
            <text
              key={index}
              x={room.x}
              y={room.y}
              fill={isClient ? useColorModeValue('gray.600', 'gray.400') : 'gray.600'}
              suppressHydrationWarning
              fontSize="12"
              textAnchor="middle"
            >
              {room.label}
            </text>
          ))}
        </svg>
      )}

      {/* AP markers will be rendered here */}
      <Box position="absolute" top="0" left="0" width="100%" height="100%">
        {children}
      </Box>
    </Box>
  )
}