'use client'

import React, { useState, useEffect } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Grid,
  GridItem,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  useToast,
  Text,
  Badge,
  Flex,
  Box,
  Divider,
  VStack,
  HStack,
  SimpleGrid,
  useColorModeValue,
  Icon,
  Tooltip,
  InputGroup,
  InputLeftAddon,
  FormHelperText,
  Switch,
  IconButton,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Editable,
  EditableInput,
  EditablePreview
} from '@chakra-ui/react'
import { useTranslation } from '@/i18n/client'
import { WifiHigh, ArrowsOutLineVertical, Cpu, Storefront, Broadcast, ChartLineUp, <PERSON>, <PERSON>, Trash, PencilSimple } from '@phosphor-icons/react'

interface WiFiDevice {
  id: string
  name: string
  status: 'online' | 'offline' | 'warning'
  position: { x: number, y: number }
  floor: string
  ip?: string
  mac?: string
  model?: string
  location?: string
  channel?: string
  bandwidth?: string
  power?: string
  clients?: number
  signal?: string
}

interface WiFiDeviceDetailsProps {
  device: WiFiDevice
  isOpen: boolean
  onClose: () => void
  onSave: (device: WiFiDevice) => void
  onDelete?: (deviceId: string) => void
}

export default function WiFiDeviceDetails({
  device,
  isOpen,
  onClose,
  onSave,
  onDelete
}: WiFiDeviceDetailsProps) {
  const [editedDevice, setEditedDevice] = useState<WiFiDevice>({ ...device })
  const [activeTab, setActiveTab] = useState(0)
  const { t } = useTranslation()
  const toast = useToast()
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure()
  const cancelRef = React.useRef<HTMLButtonElement>(null)
  
  // Reset form when device changes
  useEffect(() => {
    setEditedDevice({ ...device })
  }, [device])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setEditedDevice(prev => ({ ...prev, [name]: value }))
  }

  const handleNameChange = (newName: string) => {
    setEditedDevice(prev => ({ ...prev, name: newName }))
  }

  const handleSave = () => {
    if (!editedDevice.name.trim()) {
      toast({
        title: t('warning'),
        description: t('deviceNameRequired'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }
    
    try {
      onSave(editedDevice)
      toast({
        title: t('success'),
        description: t('deviceRefreshed'),
        status: 'success',
        duration: 2000,
        isClosable: true,
      })
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorRefreshingDevice'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
    }
  }

  const handleDelete = () => {
    if (onDelete) {
      try {
        onDelete(device.id)
        onDeleteClose()
        onClose()
        toast({
          title: t('success'),
          description: t('deviceDeleted'),
          status: 'success',
          duration: 2000,
          isClosable: true,
        })
      } catch (error) {
        toast({
          title: t('error'),
          description: t('errorDeletingDevice'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        })
      }
    }
  }

  const getBadgeColor = (status: string) => {
    switch (status) {
      case 'online': return 'green'
      case 'warning': return 'yellow'
      case 'offline': return 'gray'
      default: return 'gray'
    }
  }
  
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'online': return t('ap.online')
      case 'warning': return t('ap.warning')
      case 'offline': return t('ap.offline')
      default: return status
    }
  }
  
  const infoSectionBg = useColorModeValue('gray.50', 'gray.700')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  return (
    <>
      <Modal 
        isOpen={isOpen} 
        onClose={onClose} 
        size="xl" 
        scrollBehavior="inside"
        motionPreset="slideInBottom"
      >
        <ModalOverlay backgroundColor="rgba(0, 0, 0, 0.3)" backdropFilter="blur(5px)" />
        <ModalContent 
          borderRadius="md" 
          shadow="xl" 
          bg={useColorModeValue('rgba(255, 255, 255, 0.85)', 'rgba(26, 32, 44, 0.85)')}
          backdropFilter="blur(10px)"
          borderWidth="1px"
          borderColor={useColorModeValue('gray.200', 'gray.700')}
        >
          <ModalHeader px={6} pt={4} pb={0} bg="transparent">
            <Flex justify="space-between" align="center" w="100%">
              <Flex align="center" gap={2}>
                <Icon as={WifiHigh} boxSize={5} color="blue.400" />
                <Text fontWeight="bold">{t('deviceDetails')}</Text>
              </Flex>
              <Flex align="center" gap={2}>
                <Badge 
                  colorScheme={getBadgeColor(editedDevice.status)} 
                  fontSize="sm" 
                  px={2} 
                  py={1} 
                  borderRadius="md"
                  variant="solid"
                >
                  {getStatusLabel(editedDevice.status)}
                </Badge>
                <Tooltip label={t('deleteDevice')} placement="top" hasArrow>
                  <IconButton
                    icon={<Trash size={18} />}
                    aria-label={t('deleteDevice')}
                    colorScheme="red"
                    variant="ghost"
                    size="sm"
                    onClick={onDeleteOpen}
                  />
                </Tooltip>
              </Flex>
            </Flex>
            <ModalCloseButton size="sm" top="16px" />
          </ModalHeader>
          
          <Box px={6} mt={4}>
            <Editable
              value={editedDevice.name}
              onChange={handleNameChange}
              fontSize="xl"
              fontWeight="bold"
              w="100%"
              isPreviewFocusable={true}
            >
              <Flex align="center">
                <EditablePreview 
                  mr={2} 
                  _hover={{ bg: useColorModeValue('rgba(237, 242, 247, 0.6)', 'rgba(45, 55, 72, 0.6)') }} 
                  px={2} 
                  borderRadius="md"
                />
                <EditableInput borderColor={borderColor} bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')} />
                <Tooltip label={t('editName')} placement="top" hasArrow>
                  <Box color="blue.500" ml={2}>
                    <PencilSimple size={16} />
                  </Box>
                </Tooltip>
              </Flex>
            </Editable>
          </Box>
          
          <Divider mt={3} />
          
          <ModalBody p={0} bg="transparent">
            <Tabs 
              index={activeTab} 
              onChange={setActiveTab} 
              colorScheme="blue" 
              variant="enclosed"
              isLazy
            >
              <TabList px={6} bg="transparent">
                <Tab fontWeight="medium" _selected={{ color: 'blue.500', borderBottomColor: 'blue.500' }}>
                  <Flex align="center" gap={2}>
                    <Icon as={Cpu} weight="regular" />
                    <Text>{t('overview')}</Text>
                  </Flex>
                </Tab>
                <Tab fontWeight="medium" _selected={{ color: 'blue.500', borderBottomColor: 'blue.500' }}>
                  <Flex align="center" gap={2}>
                    <Icon as={Gear} weight="regular" />
                    <Text>{t('settings')}</Text>
                  </Flex>
                </Tab>
                <Tab fontWeight="medium" _selected={{ color: 'blue.500', borderBottomColor: 'blue.500' }}>
                  <Flex align="center" gap={2}>
                    <Icon as={ArrowsOutLineVertical} weight="regular" />
                    <Text>{t('status')}</Text>
                  </Flex>
                </Tab>
              </TabList>

              <TabPanels>
                {/* Overview Tab */}
                <TabPanel p={0} bg="transparent">
                  <Box p={6}>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                      {/* Device Info */}
                      <Box 
                        borderWidth="1px" 
                        borderColor={borderColor} 
                        borderRadius="md" 
                        overflow="hidden"
                        bg={useColorModeValue('rgba(255, 255, 255, 0.6)', 'rgba(45, 55, 72, 0.6)')}
                        backdropFilter="blur(3px)"
                        boxShadow="sm"
                      >
                        <Flex 
                          bg={useColorModeValue('rgba(247, 250, 252, 0.7)', 'rgba(26, 32, 44, 0.7)')}
                          p={3} 
                          align="center" 
                          borderBottomWidth="1px"
                          borderColor={borderColor}
                        >
                          <Icon as={Cpu} color="blue.500" boxSize={5} mr={2} />
                          <Text fontWeight="bold">{t('deviceInfo')}</Text>
                        </Flex>
                        <VStack align="stretch" p={4} spacing={3}>
                          <InfoRow label={t('ap.model')} value={editedDevice.model || '-'} />
                          <InfoRow label={t('ap.ip')} value={editedDevice.ip || '-'} />
                          <InfoRow label={t('ap.mac')} value={editedDevice.mac || '-'} />
                          <InfoRow label={t('ap.location')} value={editedDevice.location || '-'} />
                          <InfoRow label={t('ap.floor')} value={editedDevice.floor} />
                        </VStack>
                      </Box>
                      
                      {/* Radio Info */}
                      <Box 
                        borderWidth="1px" 
                        borderColor={borderColor} 
                        borderRadius="md" 
                        overflow="hidden"
                        bg={useColorModeValue('rgba(255, 255, 255, 0.6)', 'rgba(45, 55, 72, 0.6)')}
                        backdropFilter="blur(3px)"
                        boxShadow="sm"
                      >
                        <Flex 
                          bg={useColorModeValue('rgba(247, 250, 252, 0.7)', 'rgba(26, 32, 44, 0.7)')}
                          p={3} 
                          align="center" 
                          borderBottomWidth="1px"
                          borderColor={borderColor}
                        >
                          <Icon as={Broadcast} color="blue.500" boxSize={5} mr={2} />
                          <Text fontWeight="bold">{t('radioInfo')}</Text>
                        </Flex>
                        <VStack align="stretch" p={4} spacing={3}>
                          <InfoRow label={t('ap.channel')} value={editedDevice.channel || '-'} />
                          <InfoRow label={t('ap.bandwidth')} value={editedDevice.bandwidth || '-'} />
                          <InfoRow label={t('ap.power')} value={editedDevice.power ? `${editedDevice.power} dBm` : '-'} />
                          <InfoRow label={t('ap.clients')} value={editedDevice.clients?.toString() || '0'} />
                          <InfoRow label={t('ap.signal')} value={editedDevice.signal || '-'} />
                        </VStack>
                      </Box>
                    </SimpleGrid>
                  </Box>
                </TabPanel>

                {/* Settings Tab */}
                <TabPanel p={6} bg="transparent">
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    <Box>
                      <Text fontSize="lg" fontWeight="semibold" mb={4}>
                        {t('basicSettings')}
                      </Text>
                      
                      <VStack spacing={4} align="stretch">
                        <FormControl id="name" isRequired>
                          <FormLabel fontWeight="medium">{t('ap.name')}</FormLabel>
                          <InputGroup>
                            <InputLeftAddon children={<Icon as={WifiHigh} />} />
                            <Input 
                              name="name" 
                              value={editedDevice.name} 
                              onChange={handleChange}
                              placeholder={t('enterName')}
                              bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')} 
                            />
                          </InputGroup>
                        </FormControl>
                        
                        <FormControl id="status">
                          <FormLabel fontWeight="medium">{t('ap.status')}</FormLabel>
                          <Select 
                            name="status" 
                            value={editedDevice.status} 
                            onChange={handleChange}
                            variant="filled"
                            bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                          >
                            <option value="online">{t('ap.online')}</option>
                            <option value="offline">{t('ap.offline')}</option>
                            <option value="warning">{t('ap.warning')}</option>
                          </Select>
                        </FormControl>
                        
                        <FormControl id="model">
                          <FormLabel fontWeight="medium">{t('ap.model')}</FormLabel>
                          <InputGroup>
                            <InputLeftAddon children={<Icon as={Cpu} />} />
                            <Input 
                              name="model" 
                              value={editedDevice.model || ''} 
                              onChange={handleChange}
                              placeholder="AP-4000"
                              bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                            />
                          </InputGroup>
                        </FormControl>
                      </VStack>
                    </Box>
                    
                    <Box>
                      <Text fontSize="lg" fontWeight="semibold" mb={4}>
                        {t('networkSettings')}
                      </Text>
                      
                      <VStack spacing={4} align="stretch">
                        <FormControl id="ip">
                          <FormLabel fontWeight="medium">{t('ap.ip')}</FormLabel>
                          <Input 
                            name="ip" 
                            value={editedDevice.ip || ''} 
                            onChange={handleChange}
                            placeholder="*************" 
                            bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                          />
                          <FormHelperText>{t('deviceIPHelp')}</FormHelperText>
                        </FormControl>
                        
                        <FormControl id="mac">
                          <FormLabel fontWeight="medium">{t('ap.mac')}</FormLabel>
                          <Input 
                            name="mac" 
                            value={editedDevice.mac || ''} 
                            onChange={handleChange}
                            placeholder="00:11:22:33:44:55" 
                            bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                          />
                        </FormControl>
                        
                        <FormControl id="location">
                          <FormLabel fontWeight="medium">{t('ap.location')}</FormLabel>
                          <InputGroup>
                            <InputLeftAddon children={<Icon as={Storefront} />} />
                            <Input 
                              name="location" 
                              value={editedDevice.location || ''} 
                              onChange={handleChange}
                              placeholder={t('locationPlaceholder')}
                              bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                            />
                          </InputGroup>
                        </FormControl>
                      </VStack>
                    </Box>
                  </SimpleGrid>
                  
                  <Divider my={6} />
                  
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    <Box>
                      <Text fontSize="lg" fontWeight="semibold" mb={4}>
                        {t('radioSettings')}
                      </Text>
                      
                      <VStack spacing={4} align="stretch">
                        <FormControl id="channel">
                          <FormLabel fontWeight="medium">{t('ap.channel')}</FormLabel>
                          <Select 
                            name="channel" 
                            value={editedDevice.channel || ''} 
                            onChange={handleChange}
                            variant="filled"
                            bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                          >
                            <option value="">Auto</option>
                            <option value="1">1</option>
                            <option value="6">6</option>
                            <option value="11">11</option>
                            <option value="36">36</option>
                            <option value="40">40</option>
                          </Select>
                        </FormControl>
                        
                        <FormControl id="bandwidth">
                          <FormLabel fontWeight="medium">{t('ap.bandwidth')}</FormLabel>
                          <Select 
                            name="bandwidth" 
                            value={editedDevice.bandwidth || ''} 
                            onChange={handleChange}
                            variant="filled"
                            bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                          >
                            <option value="20MHz">20 MHz</option>
                            <option value="40MHz">40 MHz</option>
                            <option value="80MHz">80 MHz</option>
                            <option value="160MHz">160 MHz</option>
                          </Select>
                        </FormControl>
                        
                        <FormControl id="power">
                          <FormLabel fontWeight="medium">{t('ap.power')}</FormLabel>
                          <InputGroup>
                            <Input 
                              name="power" 
                              value={editedDevice.power || ''} 
                              onChange={handleChange}
                              placeholder="20" 
                              bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                            />
                            <InputLeftAddon>dBm</InputLeftAddon>
                          </InputGroup>
                        </FormControl>
                      </VStack>
                    </Box>
                    
                    <Box>
                      <Text fontSize="lg" fontWeight="semibold" mb={4}>
                        {t('otherSettings')}
                      </Text>
                      
                      <VStack spacing={4} align="stretch">
                        <FormControl id="floor">
                          <FormLabel fontWeight="medium">{t('ap.floor')}</FormLabel>
                          <Select 
                            name="floor" 
                            value={editedDevice.floor} 
                            onChange={handleChange}
                            variant="filled"
                            bg={useColorModeValue('rgba(255, 255, 255, 0.5)', 'rgba(45, 55, 72, 0.5)')}
                          >
                            <option value="1F">1F</option>
                            <option value="2F">2F</option>
                            <option value="3F">3F</option>
                            <option value="4F">4F</option>
                            <option value="5F">5F</option>
                            <option value="6F">6F</option>
                            <option value="7F">7F</option>
                            <option value="8F">8F</option>
                          </Select>
                        </FormControl>
                        
                        <FormControl display="flex" alignItems="center" mt={2}>
                          <FormLabel htmlFor="enable-wps" mb="0" fontWeight="medium">
                            {t('enableWPS')}
                          </FormLabel>
                          <Switch id="enable-wps" colorScheme="blue" />
                        </FormControl>
                        
                        <FormControl display="flex" alignItems="center" mt={2}>
                          <FormLabel htmlFor="guest-network" mb="0" fontWeight="medium">
                            {t('enableGuestNetwork')}
                          </FormLabel>
                          <Switch id="guest-network" colorScheme="blue" />
                        </FormControl>
                      </VStack>
                    </Box>
                  </SimpleGrid>
                </TabPanel>

                {/* Status Tab */}
                <TabPanel p={6} bg="transparent">
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                    {/* Status Overview */}
                    <Box 
                      borderWidth="1px" 
                      borderColor={borderColor} 
                      borderRadius="md" 
                      overflow="hidden"
                      bg={useColorModeValue('rgba(255, 255, 255, 0.6)', 'rgba(45, 55, 72, 0.6)')}
                      backdropFilter="blur(3px)"
                      boxShadow="sm"
                    >
                      <Flex 
                        bg={useColorModeValue('rgba(247, 250, 252, 0.7)', 'rgba(26, 32, 44, 0.7)')}
                        p={3} 
                        align="center" 
                        borderBottomWidth="1px"
                        borderColor={borderColor}
                      >
                        <Icon as={ChartLineUp} color="blue.500" boxSize={5} mr={2} />
                        <Text fontWeight="bold">{t('statusOverview')}</Text>
                      </Flex>
                      <VStack align="stretch" p={4} spacing={3}>
                        <InfoRow 
                          label={t('ap.status')} 
                          value={
                            <Badge 
                              colorScheme={getBadgeColor(editedDevice.status)} 
                              fontSize="sm" 
                              px={2} 
                              py={0.5}
                            >
                              {getStatusLabel(editedDevice.status)}
                            </Badge>
                          } 
                        />
                        <InfoRow label={t('uptime')} value="23h 45m" />
                        <InfoRow label={t('firmwareVersion')} value="v2.5.7" />
                        <InfoRow label={t('cpuUsage')} value="15%" />
                        <InfoRow label={t('memoryUsage')} value="32%" />
                      </VStack>
                    </Box>
                    
                    {/* Client Info */}
                    <Box 
                      borderWidth="1px" 
                      borderColor={borderColor} 
                      borderRadius="md" 
                      overflow="hidden"
                      bg={useColorModeValue('rgba(255, 255, 255, 0.6)', 'rgba(45, 55, 72, 0.6)')}
                      backdropFilter="blur(3px)"
                      boxShadow="sm"
                    >
                      <Flex 
                        bg={useColorModeValue('rgba(247, 250, 252, 0.7)', 'rgba(26, 32, 44, 0.7)')}
                        p={3} 
                        align="center" 
                        borderBottomWidth="1px"
                        borderColor={borderColor}
                      >
                        <Icon as={Users} color="blue.500" boxSize={5} mr={2} />
                        <Text fontWeight="bold">{t('clientInfo')}</Text>
                      </Flex>
                      <VStack align="stretch" p={4} spacing={3}>
                        <InfoRow label={t('connectedClients')} value={editedDevice.clients?.toString() || '0'} />
                        <InfoRow label={t('2.4GHzClients')} value="3" />
                        <InfoRow label={t('5GHzClients')} value="5" />
                        <InfoRow label={t('dataTransferred')} value="2.3 GB" />
                        <InfoRow label={t('signalQuality')} value="Excellent" />
                      </VStack>
                    </Box>
                  </SimpleGrid>
                  
                  <Box 
                    mt={6} 
                    p={4} 
                    bg={useColorModeValue('rgba(247, 250, 252, 0.7)', 'rgba(26, 32, 44, 0.7)')}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor={borderColor}
                    backdropFilter="blur(3px)"
                    boxShadow="sm"
                  >
                    <Text mb={2} fontWeight="bold">{t('snmpData')}</Text>
                    <Text color="gray.500" fontSize="sm">
                      {t('snmpDataDescription')}
                    </Text>
                  </Box>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </ModalBody>

          <Divider />
          
          <ModalFooter px={6} py={3} bg="transparent">
            {activeTab === 1 && (
              <Button 
                leftIcon={<Icon as={Gear} />}
                colorScheme="blue" 
                mr={3} 
                onClick={handleSave}
                size="md"
                px={6}
              >
                {t('saveChanges')}
              </Button>
            )}
            <Button 
              leftIcon={<Icon as={Trash} />}
              colorScheme="red" 
              variant="outline"
              mr={3}
              onClick={onDeleteOpen}
            >
              {t('delete')}
            </Button>
            <Button variant="ghost" onClick={onClose}>{t('close')}</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
        isCentered
        size="sm"
      >
        <AlertDialogOverlay backdropFilter="blur(8px)" backgroundColor="rgba(0, 0, 0, 0.4)">
          <AlertDialogContent
            borderRadius="md" 
            shadow="xl" 
            bg={useColorModeValue('rgba(255, 255, 255, 0.95)', 'rgba(26, 32, 44, 0.95)')}
            backdropFilter="blur(12px)"
            borderWidth="1px"
            borderColor={useColorModeValue('gray.200', 'gray.700')}
            maxW="320px"
          >
            <AlertDialogHeader fontSize="md" fontWeight="bold" pb={2}>
              {t('deleteDevice')}
            </AlertDialogHeader>

            <AlertDialogBody pb={4}>
              <Flex direction="column" align="center" gap={2}>
                <Icon as={WifiHigh} color="red.500" boxSize={8} mb={1} />
                <Text fontSize="sm">{t('deleteDeviceConfirmation')}</Text>
                <Text fontWeight="bold" fontSize="md" color={useColorModeValue('blue.600', 'blue.300')}>
                  {editedDevice.name}
                </Text>
              </Flex>
            </AlertDialogBody>

            <AlertDialogFooter pt={2}>
              <Button size="sm" ref={cancelRef} onClick={onDeleteClose}>
                {t('cancel')}
              </Button>
              <Button size="sm" colorScheme="red" onClick={handleDelete} ml={3}>
                {t('delete')}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  )
}

// Helper component for displaying info rows consistently
const InfoRow = ({ label, value }: { label: string, value: React.ReactNode }) => (
  <Flex justify="space-between" align="center">
    <Text fontWeight="medium" color="gray.600">{label}</Text>
    {typeof value === 'string' ? <Text>{value}</Text> : value}
  </Flex>
) 