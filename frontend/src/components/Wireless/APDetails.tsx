'use client'

import React from 'react'
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  useColorModeValue,
  Grid,
  GridItem,
  Divider,
} from '@chakra-ui/react'
import { useTranslation } from '@/contexts/LanguageContext'

interface AP {
  id: string
  name: string
  number: string
  status: 'online' | 'offline' | 'warning'
  location: {
    x: number
    y: number
    floor: string
  }
  model: string
  ip: string
  mac: string
  signal: number
  clients: number
  channel: number
  bandwidth: string
  power: number
}

interface APDetailsProps {
  ap: AP | null
}

export default function APDetails({ ap }: APDetailsProps) {
  const { t } = useTranslation()
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  if (!ap) return null

  // Get status color
  const getStatusColor = (status: AP['status']) => {
    switch (status) {
      case 'online':
        return 'green'
      case 'offline':
        return 'red'
      case 'warning':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  // Get status text
  const getStatusText = (status: AP['status']) => {
    return t(`ap.${status}`)
  }

  return (
    <Box
      borderWidth="1px"
      borderRadius="lg"
      p={4}
      bg={bgColor}
      borderColor={borderColor}
    >
      <VStack align="stretch" spacing={4}>
        {/* Header */}
        <HStack justify="space-between">
          <VStack align="start" spacing={1}>
            <Text fontSize="lg" fontWeight="bold">
              {ap.name}
            </Text>
            <Badge colorScheme={getStatusColor(ap.status)}>
              {getStatusText(ap.status)}
            </Badge>
          </VStack>
        </HStack>

        <Divider />

        {/* Details Grid */}
        <Grid templateColumns="repeat(2, 1fr)" gap={4}>
          <GridItem>
            <Text fontWeight="medium">{t('ap.model')}</Text>
            <Text>{ap.model}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.location')}</Text>
            <Text>{ap.location.floor}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.ip')}</Text>
            <Text>{ap.ip}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.mac')}</Text>
            <Text>{ap.mac}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.signal')}</Text>
            <Text>{ap.signal}%</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.clients')}</Text>
            <Text>{ap.clients}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.channel')}</Text>
            <Text>{ap.channel}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.bandwidth')}</Text>
            <Text>{ap.bandwidth}</Text>
          </GridItem>
          <GridItem>
            <Text fontWeight="medium">{t('ap.power')}</Text>
            <Text>{ap.power} dBm</Text>
          </GridItem>
        </Grid>
      </VStack>
    </Box>
  )
} 