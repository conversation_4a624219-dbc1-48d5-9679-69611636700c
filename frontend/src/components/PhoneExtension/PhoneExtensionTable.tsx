import React, { useState, useEffect } from 'react';
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Input,
  Button,
  Box,
  useToast,
  HStack,
  Select,
  Spinner,
  Center,
  useColorMode,
  TableContainer,
  Text,
} from '@chakra-ui/react';
import { PhoneExtension, PhoneExtensionUpdateData } from '../../types/phoneExtension';
import { phoneExtensionService } from '../../services/phoneExtensionService';
import { useTranslation } from '@/contexts/LanguageContext';

interface PhoneExtensionTableProps {
  initialCabinetNumber?: number;
}

export const PhoneExtensionTable: React.FC<PhoneExtensionTableProps> = ({ 
  initialCabinetNumber = 1 
}) => {
  const { colorMode } = useColorMode();
  const [extensions, setExtensions] = useState<PhoneExtension[]>([]);
  const [selectedCabinet, setSelectedCabinet] = useState(initialCabinetNumber);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editData, setEditData] = useState<Record<number, PhoneExtensionUpdateData>>({});
  const toast = useToast();
  const { t } = useTranslation();

  const cabinets = Array.from({ length: 10 }, (_, i) => i + 1);
  const positions = Array.from({ length: 30 }, (_, i) => i + 1);

  useEffect(() => {
    loadExtensions();
  }, [selectedCabinet]);

  const loadExtensions = async () => {
    setLoading(true);
    try {
      const data = await phoneExtensionService.getExtensionsByCabinet(selectedCabinet);
      setExtensions(data);
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载分机数据',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
    setLoading(false);
  };

  const handleInputChange = (id: number, field: keyof PhoneExtensionUpdateData, value: string) => {
    setEditData((prev) => ({
      ...prev,
      [id]: {
        ...prev[id],
        [field]: value
      }
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const updates = Object.entries(editData).map(([id, data]) => ({
        id: Number(id),
        ...data
      }));
      
      await Promise.all(
        updates.map(({ id, ...data }) => 
          phoneExtensionService.updateExtension(id, data)
        )
      );
      
      toast({
        title: '保存成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      await loadExtensions();
      setEditData({});
      setEditMode(false);
    } catch (error) {
      toast({
        title: '保存失败',
        description: '无法保存更改',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
    setSaving(false);
  };

  if (loading) {
    return (
      <Center h="400px">
        <Spinner size="xl" />
      </Center>
    );
  }

  return (
    <Box>
      <HStack spacing={4} mb={4}>
        <Select
          value={selectedCabinet}
          onChange={(e) => setSelectedCabinet(Number(e.target.value))}
          w="200px"
          isDisabled={editMode}
        >
          {cabinets.map((cabinet) => (
            <option key={cabinet} value={cabinet}>
              {t('phone.cabinet')} {cabinet}
            </option>
          ))}
        </Select>
        <Button
          colorScheme={editMode ? "green" : "blue"}
          onClick={editMode ? handleSave : () => setEditMode(true)}
          isLoading={saving}
        >
          {editMode ? t('save') : t('edit')}
        </Button>
        {editMode && (
          <Button
            variant="outline"
            onClick={() => {
              setEditMode(false);
              setEditData({});
            }}
          >
            {t('cancel')}
          </Button>
        )}
      </HStack>

      <TableContainer
        borderWidth="1px"
        borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
        borderRadius="lg"
        overflowX="auto"
      >
        <Box p={4} borderBottomWidth="1px" borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}>
          <Text fontSize="lg" fontWeight="medium">{t('phone.cabinet')} {selectedCabinet}</Text>
        </Box>
        <Table variant="simple" size="sm">
          <Thead bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}>
            <Tr>
              <Th width="80px" textAlign="center">序号</Th>
              <Th width="120px">{t('phone.extension')}</Th>
              <Th width="120px">{t('phone.socket')}</Th>
              <Th>{t('phone.user')}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {positions.map((position) => {
              const extension = extensions.find(
                (e) => e.cabinetNumber === selectedCabinet && Number(e.socketNumber) === position
              );

              return (
                <Tr key={position}>
                  <Td textAlign="center">{position}</Td>
                  <Td>
                    {editMode ? (
                      <Input
                        size="sm"
                        value={extension ? (editData[extension.id]?.extensionNumber ?? extension.extensionNumber) : ''}
                        onChange={(e) => extension && handleInputChange(extension.id, 'extensionNumber', e.target.value)}
                        placeholder={t('phone.extension')}
                      />
                    ) : (
                      extension?.extensionNumber || '-'
                    )}
                  </Td>
                  <Td>
                    <Input
                      size="sm"
                      value={position.toString()}
                      isReadOnly
                      bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                      border="none"
                      _hover={{ bg: colorMode === 'dark' ? 'gray.700' : 'gray.50' }}
                      _focus={{ bg: colorMode === 'dark' ? 'gray.700' : 'gray.50' }}
                    />
                  </Td>
                  <Td>
                    {editMode ? (
                      <Input
                        size="sm"
                        value={extension ? (editData[extension.id]?.user ?? extension.user ?? '') : ''}
                        onChange={(e) => extension && handleInputChange(extension.id, 'user', e.target.value || '')}
                        placeholder={t('phone.user')}
                      />
                    ) : (
                      extension?.user || '-'
                    )}
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );
};