'use client'

import {
  Box,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  HStack,
  Icon,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useColorMode,
  Avatar,
  Text,
  IconButton,
  Tooltip,
} from '@chakra-ui/react'
import { MagnifyingGlass, Bell, User, Sun, Moon } from '@phosphor-icons/react'
import { useTranslation } from '@/contexts/LanguageContext'

const Header = () => {
  const { t } = useTranslation()
  const { colorMode, toggleColorMode } = useColorMode()

  return (
    <Box
      as="header"
      bg={colorMode === 'dark' ? 'komodo.darkGray' : 'white'}
      borderBottom="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
      py={3}
      px={6}
      width="100%"
      boxShadow="sm"
      position="sticky"
      top={0}
      zIndex={1000}
    >
      <Flex justify="space-between" align="center" maxW="1600px" mx="auto">
        <Box>
          {/* Left side content if needed */}
        </Box>

        <HStack spacing={4}>
          <InputGroup maxW={{ base: "200px", md: "300px" }}>
            <InputLeftElement pointerEvents="none" h="32px" pl={3}>
              <Icon as={MagnifyingGlass} color={colorMode === 'dark' ? 'gray.400' : 'gray.500'} boxSize={4} />
            </InputLeftElement>
            <Input
              placeholder={t('search')}
              variant="filled"
              bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
              _hover={{ bg: colorMode === 'dark' ? 'gray.600' : 'gray.100' }}
              _focus={{
                bg: colorMode === 'dark' ? 'gray.600' : 'white',
                borderColor: 'komodo.green',
                boxShadow: '0 0 0 1px var(--chakra-colors-komodo-green)'
              }}
              size="sm"
              height="32px"
              pl={10}
              borderRadius="md"
              border="1px"
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
            />
          </InputGroup>



          <Tooltip label={t('notification')}>
            <IconButton
              aria-label={t('notification')}
              icon={<Icon as={Bell} />}
              variant="ghost"
              size="sm"
              position="relative"
              color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}
              _hover={{
                bg: colorMode === 'dark' ? 'whiteAlpha.200' : 'gray.100'
              }}
              transition="all 0.2s"
            >
              <Box
                position="absolute"
                top={1}
                right={1}
                boxSize={2}
                bg="red.500"
                borderRadius="full"
              />
            </IconButton>
          </Tooltip>

          <Menu>
            <MenuButton
              as={Button}
              variant="ghost"
              size="sm"
              leftIcon={<Avatar size="xs" name="User" bg="komodo.green" />}
              rightIcon={<></>}
              _hover={{
                bg: colorMode === 'dark' ? 'whiteAlpha.200' : 'gray.100'
              }}
              borderRadius="md"
              px={2}
              transition="all 0.2s"
            >
              <Text fontSize="sm" display={{ base: 'none', md: 'block' }}>
                {t('user')}
              </Text>
            </MenuButton>
            <MenuList
              bg={colorMode === 'dark' ? 'gray.800' : 'white'}
              borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
              boxShadow="lg"
            >
              <MenuItem
                _hover={{
                  bg: colorMode === 'dark' ? 'gray.700' : 'gray.100'
                }}
              >
                {t('profile')}
              </MenuItem>
              <MenuItem
                _hover={{
                  bg: colorMode === 'dark' ? 'gray.700' : 'gray.100'
                }}
              >
                {t('settings')}
              </MenuItem>
              <MenuItem
                color={colorMode === 'dark' ? 'red.300' : 'red.500'}
                _hover={{
                  bg: colorMode === 'dark' ? 'gray.700' : 'gray.100'
                }}
              >
                {t('logout')}
              </MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </Flex>
    </Box>
  )
}

export default Header