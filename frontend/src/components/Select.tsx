'use client'

import React from 'react'
import {
  Select as ShadcnSelect,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { Label } from "./ui/label"

// 兼容NextUI和Chakra UI的Select属性
export interface SelectProps {
  // 通用属性
  id?: string
  name?: string
  value?: string
  defaultValue?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  className?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  
  // 标签相关
  label?: React.ReactNode
  labelPlacement?: 'top' | 'left'
  
  // 选项相关
  children?: React.ReactNode
  options?: { value: string; label: string; disabled?: boolean }[]
  
  // 大小相关
  size?: 'sm' | 'md' | 'lg'
  
  // 状态相关
  isInvalid?: boolean
  errorMessage?: string
  
  // 其他属性
  variant?: 'outline' | 'filled' | 'flushed' | 'unstyled'
  isFullWidth?: boolean
}

/**
 * Select组件 - 兼容NextUI和Chakra UI的API
 */
export function Select({
  id,
  name,
  value,
  defaultValue,
  placeholder = "请选择...",
  disabled = false,
  required = false,
  className = '',
  onChange,
  onBlur,
  label,
  labelPlacement = 'top',
  children,
  options,
  size = 'md',
  isInvalid = false,
  errorMessage,
  variant = 'outline',
  isFullWidth = true,
}: SelectProps) {
  // 处理大小
  const sizeClasses = {
    sm: 'h-8 text-xs',
    md: '',
    lg: 'h-12 text-base',
  }
  
  // 处理变体
  const variantClasses = {
    outline: '',
    filled: 'bg-muted/50',
    flushed: 'border-0 border-b rounded-none px-0',
    unstyled: 'border-0 px-0 shadow-none',
  }
  
  // 处理错误状态
  const errorClasses = isInvalid ? 'border-destructive focus:ring-destructive' : ''
  
  // 处理宽度
  const widthClass = isFullWidth ? 'w-full' : ''
  
  // 处理值变化
  const handleValueChange = (value: string) => {
    if (onChange) {
      onChange(value)
    }
  }
  
  // 渲染标签
  const renderLabel = () => {
    if (!label) return null
    
    return (
      <Label 
        htmlFor={id} 
        className={`mb-2 ${labelPlacement === 'left' ? 'mr-2' : 'block'}`}
      >
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
    )
  }
  
  // 渲染选择器
  const renderSelect = () => (
    <ShadcnSelect
      defaultValue={defaultValue}
      value={value}
      onValueChange={handleValueChange}
      disabled={disabled}
      name={name}
    >
      <SelectTrigger 
        id={id}
        className={`
          ${sizeClasses[size]} 
          ${variantClasses[variant]} 
          ${errorClasses} 
          ${widthClass} 
          ${className}
        `}
        onBlur={onBlur}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {children || (
          <SelectGroup>
            {options?.map((option) => (
              <SelectItem 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectGroup>
        )}
      </SelectContent>
    </ShadcnSelect>
  )
  
  // 渲染错误信息
  const renderError = () => {
    if (!isInvalid || !errorMessage) return null
    
    return (
      <p className="text-destructive text-sm mt-1">
        {errorMessage}
      </p>
    )
  }
  
  return (
    <div className={`${labelPlacement === 'left' ? 'flex items-center' : ''}`}>
      {renderLabel()}
      <div className={`${isFullWidth ? 'w-full' : ''}`}>
        {renderSelect()}
        {renderError()}
      </div>
    </div>
  )
}

/**
 * SelectItem组件 - 用于直接使用时的子项
 */
export function SelectOption({ 
  value, 
  children, 
  disabled 
}: { 
  value: string; 
  children: React.ReactNode; 
  disabled?: boolean 
}) {
  return (
    <SelectItem value={value} disabled={disabled}>
      {children}
    </SelectItem>
  )
}

/**
 * 用法示例:
 * 
 * // 使用options属性
 * <Select
 *   label="选择国家"
 *   options={[
 *     { value: "cn", label: "中国" },
 *     { value: "us", label: "美国" },
 *     { value: "jp", label: "日本" },
 *   ]}
 *   onChange={(value) => console.log(value)}
 * />
 * 
 * // 使用子组件
 * <Select label="选择国家">
 *   <SelectOption value="cn">中国</SelectOption>
 *   <SelectOption value="us">美国</SelectOption>
 *   <SelectOption value="jp">日本</SelectOption>
 * </Select>
 */
