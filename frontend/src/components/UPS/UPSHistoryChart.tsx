import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  ButtonGroup,
  useColorModeValue,
  Spinner,
  Center,
  Select,
  HStack,
  Card,
  CardBody,
  CardHeader,
  Heading,
} from '@chakra-ui/react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';

// 注册 ChartJS 组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// 时间范围类型
type TimeRange = '24h' | '7d' | '30d';

// 数据类型
interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill?: boolean;
    tension?: number;
  }[];
}

// 组件属性
interface UPSHistoryChartProps {
  upsId: string;
  metricType: 'battery' | 'load' | 'voltage' | 'temperature';
  title: string;
  color: string;
  unit: string;
  showAreaFill?: boolean;
}

// 生成模拟数据
const generateMockData = (
  timeRange: TimeRange,
  metricType: string,
  baseValue: number,
  variance: number
): { labels: string[]; values: number[] } => {
  let dataPoints: number;
  let labels: string[] = [];
  let values: number[] = [];
  const now = new Date();

  switch (timeRange) {
    case '24h':
      dataPoints = 24;
      for (let i = 0; i < dataPoints; i++) {
        const time = new Date(now);
        time.setHours(now.getHours() - (dataPoints - i));
        labels.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
        
        // 根据指标类型生成不同的模拟数据模式
        let value: number;
        if (metricType === 'battery') {
          // 电池电量通常会缓慢下降
          value = baseValue - (variance * (dataPoints - i) / dataPoints) + (Math.random() * variance * 0.1);
        } else if (metricType === 'load') {
          // 负载可能有较大波动
          value = baseValue + (Math.random() * variance * 2 - variance);
        } else if (metricType === 'voltage') {
          // 电压通常波动较小
          value = baseValue + (Math.random() * variance * 0.5 - variance * 0.25);
        } else if (metricType === 'temperature') {
          // 温度通常有日间模式
          const hourOfDay = time.getHours();
          const dayFactor = hourOfDay > 8 && hourOfDay < 18 ? 1 : 0.7;
          value = baseValue * dayFactor + (Math.random() * variance - variance / 2);
        } else {
          value = baseValue + (Math.random() * variance * 2 - variance);
        }
        
        // 确保值在合理范围内
        value = Math.max(0, value);
        if (metricType === 'battery') value = Math.min(100, value);
        
        values.push(Number(value.toFixed(1)));
      }
      break;
    case '7d':
      dataPoints = 7;
      for (let i = 0; i < dataPoints; i++) {
        const date = new Date(now);
        date.setDate(now.getDate() - (dataPoints - i - 1));
        labels.push(date.toLocaleDateString([], { month: 'short', day: 'numeric' }));
        
        // 生成每日平均值
        let value: number;
        if (metricType === 'battery') {
          value = baseValue - (variance * i / dataPoints) + (Math.random() * variance * 0.1);
        } else if (metricType === 'load') {
          value = baseValue + (Math.random() * variance - variance / 2);
        } else {
          value = baseValue + (Math.random() * variance - variance / 2);
        }
        
        value = Math.max(0, value);
        if (metricType === 'battery') value = Math.min(100, value);
        
        values.push(Number(value.toFixed(1)));
      }
      break;
    case '30d':
      dataPoints = 30;
      for (let i = 0; i < dataPoints; i++) {
        const date = new Date(now);
        date.setDate(now.getDate() - (dataPoints - i - 1));
        labels.push(date.toLocaleDateString([], { month: 'short', day: 'numeric' }));
        
        // 生成每日平均值
        let value: number;
        if (metricType === 'battery') {
          // 电池电量可能有长期下降趋势
          value = baseValue - (variance * i / dataPoints) + (Math.random() * variance * 0.1);
        } else {
          value = baseValue + (Math.random() * variance - variance / 2);
        }
        
        value = Math.max(0, value);
        if (metricType === 'battery') value = Math.min(100, value);
        
        values.push(Number(value.toFixed(1)));
      }
      break;
    default:
      dataPoints = 24;
      for (let i = 0; i < dataPoints; i++) {
        labels.push(`${i}:00`);
        values.push(baseValue + (Math.random() * variance * 2 - variance));
      }
  }

  return { labels, values };
};

// 获取基准值和波动范围
const getBaseValueAndVariance = (metricType: string, upsId: string): { baseValue: number; variance: number } => {
  switch (metricType) {
    case 'battery':
      return { baseValue: 95, variance: 10 };
    case 'load':
      return { baseValue: 45, variance: 15 };
    case 'voltage':
      return { baseValue: 220, variance: 5 };
    case 'temperature':
      return { baseValue: 28, variance: 4 };
    default:
      return { baseValue: 50, variance: 10 };
  }
};

const UPSHistoryChart: React.FC<UPSHistoryChartProps> = ({
  upsId,
  metricType,
  title,
  color,
  unit,
  showAreaFill = false,
}) => {
  const [timeRange, setTimeRange] = useState<TimeRange>('24h');
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isClient, setIsClient] = useState<boolean>(false);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const lineColor = color || 'blue.500';
  
  // 客户端检测
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 获取图表数据
  useEffect(() => {
    setIsLoading(true);
    
    // 模拟API调用延迟
    setTimeout(() => {
      const { baseValue, variance } = getBaseValueAndVariance(metricType, upsId);
      const { labels, values } = generateMockData(timeRange, metricType, baseValue, variance);
      
      const data: ChartData = {
        labels,
        datasets: [
          {
            label: title,
            data: values,
            borderColor: lineColor,
            backgroundColor: `${lineColor}33`, // 添加透明度
            fill: showAreaFill,
            tension: 0.4,
          },
        ],
      };
      
      setChartData(data);
      setIsLoading(false);
    }, 800);
  }, [timeRange, metricType, upsId, title, lineColor, showAreaFill]);
  
  // 图表选项
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.raw}${unit}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: metricType === 'battery' || metricType === 'load',
        ticks: {
          callback: function(value: any) {
            return `${value}${unit}`;
          }
        }
      }
    },
  };
  
  return (
    <Card borderRadius="lg" boxShadow="md" bg={bgColor} borderWidth="1px" borderColor={borderColor}>
      <CardHeader pb={0}>
        <Flex justify="space-between" align="center" wrap="wrap">
          <Heading size="md">{title} 历史趋势</Heading>
          <ButtonGroup size="sm" isAttached variant="outline" mb={{ base: 4, md: 0 }}>
            <Button 
              onClick={() => setTimeRange('24h')} 
              colorScheme={timeRange === '24h' ? 'blue' : undefined}
            >
              24小时
            </Button>
            <Button 
              onClick={() => setTimeRange('7d')} 
              colorScheme={timeRange === '7d' ? 'blue' : undefined}
            >
              7天
            </Button>
            <Button 
              onClick={() => setTimeRange('30d')} 
              colorScheme={timeRange === '30d' ? 'blue' : undefined}
            >
              30天
            </Button>
          </ButtonGroup>
        </Flex>
      </CardHeader>
      <CardBody>
        <Box height="300px" position="relative" suppressHydrationWarning>
          {isLoading ? (
            <Center h="100%">
              <Spinner color="blue.500" />
            </Center>
          ) : chartData && isClient ? (
            <Line data={chartData} options={chartOptions} />
          ) : (
            <Center h="100%">
              <Text color={textColor}>无数据</Text>
            </Center>
          )}
        </Box>
      </CardBody>
    </Card>
  );
};

export default UPSHistoryChart;
