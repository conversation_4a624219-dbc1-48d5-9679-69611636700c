'use client'

import React, { useState } from 'react'
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Flex, 
  useColorMode,
  Button
} from '@chakra-ui/react'
import { FaPlus } from 'react-icons/fa'
import RackDevice, { RackDeviceProps } from './RackDevice'
import RackDetail from './RackDetail'
import { useTranslation } from '@/contexts/LanguageContext'
import { EditIcon } from '@chakra-ui/icons'

export interface RackViewProps {
  id: string
  name: string
  location: string
  description?: string
  totalU: number
  devices: RackDeviceProps[]
  monitoring?: {
    temperature?: {
      current: number
      capacity: number
    }
    power?: {
      current: number
      capacity: number
    }
  }
  onAddDevice?: () => void
  onEditRack?: () => void
  onSelectDevice?: (deviceId: string) => void
}

export const RackView: React.FC<RackViewProps> = ({
  id,
  name,
  location,
  description,
  totalU,
  devices,
  monitoring,
  onAddDevice,
  onEditRack,
  onSelectDevice
}) => {
  const { colorMode } = useColorMode()
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null)
  const { t } = useTranslation()
  
  // 计算已使用的U数
  const usedU = devices.reduce((total, device) => {
    return total + (device.positionEnd - device.positionStart + 1)
  }, 0)
  
  // 创建U位图，确定每个U位是否被占用
  const rackUnits = Array(totalU).fill(null).map((_, index) => {
    const position = totalU - index // 从上到下计算U位，从1开始
    const occupyingDevice = devices.find(
      device => position >= device.positionStart && position <= device.positionEnd
    )
    
    return {
      position,
      isOccupied: !!occupyingDevice,
      deviceId: occupyingDevice?.id
    }
  })

  // 获取设备在机柜中的位置
  const getDevicePosition = (device: RackDeviceProps) => {
    // 从底部开始计算位置
    const bottomPosition = totalU - device.positionEnd
    return bottomPosition * 40 // 每个U位高度为40px
  }

  // 处理设备点击事件
  const handleDeviceClick = (deviceId: string) => {
    setSelectedDeviceId(deviceId === selectedDeviceId ? null : deviceId)
    if (onSelectDevice) {
      onSelectDevice(deviceId)
    }
  }

  return (
    <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
      {/* 机柜视图 */}
      <Box 
        flex="2"
        borderWidth="1px" 
        borderRadius="lg" 
        overflow="hidden"
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
        boxShadow="md"
      >
        <Flex 
          p={3} 
          borderBottomWidth="1px" 
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'} 
          align="center"
          justify="space-between"
        >
          <Text fontWeight="bold">{/* 机柜视图 */}</Text>
          {onAddDevice && (
            <Button 
              size="sm" 
              colorScheme="blue" 
              leftIcon={<FaPlus />}
              onClick={onAddDevice}
            >
              添加设备
            </Button>
          )}
        </Flex>
        
        <Flex p={4} height="800px" position="relative">
          {/* U位标记 */}
          <VStack 
            mr={2} 
            spacing={0}
            height="100%" 
            justify="space-between" 
            align="flex-end" 
            minW="30px"
          >
            {rackUnits.map(unit => (
              <Box 
                key={unit.position} 
                height="40px" 
                width="100%" 
                display="flex" 
                alignItems="center" 
                justifyContent="flex-end"
              >
                <Text 
                  fontSize="xs" 
                  fontWeight={unit.isOccupied ? "bold" : "normal"}
                  color={unit.isOccupied ? 
                    (colorMode === 'dark' ? 'blue.300' : 'blue.600') : 
                    (colorMode === 'dark' ? 'gray.500' : 'gray.400')
                  }
                >
                  {unit.position}U
                </Text>
              </Box>
            ))}
          </VStack>
          
          {/* 机柜主体 */}
          <Box 
            flex="1" 
            borderWidth="2px" 
            borderRadius="md" 
            bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'} 
            position="relative"
            borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
          >
            {/* 设备放置 */}
            {devices.map(device => (
              <Box
                key={device.id}
                position="absolute"
                left={0}
                right={0}
                top={`${getDevicePosition(device)}px`}
                px={2}
              >
                <RackDevice
                  {...device}
                  onClick={() => handleDeviceClick(device.id)}
                />
              </Box>
            ))}
            
            {/* 空U位 */}
            {rackUnits.filter(unit => !unit.isOccupied).map(unit => (
              <Box
                key={`empty-${unit.position}`}
                position="absolute"
                left={0}
                right={0}
                top={`${(totalU - unit.position) * 40}px`}
                height="40px"
                px={2}
              >
                <Box
                  height="100%"
                  width="100%"
                  borderWidth="1px"
                  borderRadius="sm"
                  borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
                  borderStyle="dashed"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <Text fontSize="xs" color="gray.500">空闲</Text>
                </Box>
              </Box>
            ))}
          </Box>
        </Flex>
      </Box>
      
      {/* 机柜详情 */}
      <Box flex="1">
        <RackDetail
          id={id}
          name={name}
          location={location}
          description={description}
          totalU={totalU}
          usedU={usedU}
          devices={devices}
          monitoring={monitoring}
          onAddDevice={onAddDevice}
          onEdit={onEditRack}
        />
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={2}>
            {t('rack.usage')}
          </Text>
          <Text>
            {t('rack.usedU')}: {usedU} / {totalU}
          </Text>
          <Text>
            {t('rack.usagePercentage')}: {((usedU / totalU) * 100).toFixed(1)}%
          </Text>
        </Box>
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={2}>
            {t('rack.monitoring')}
          </Text>
          <Text>
            {t('rack.temperature')}: {monitoring?.temperature?.current.toString()}°C / {monitoring?.temperature?.capacity.toString()}°C
          </Text>
          <Text>
            {t('rack.power')}: {monitoring?.power?.current.toString()}W / {monitoring?.power?.capacity.toString()}W
          </Text>
        </Box>
        <Box>
          <Text fontSize="xl" fontWeight="bold">
            {t('rack.details')}
          </Text>
          <Text>
            {t('rack.location')}: {location}
          </Text>
          <Text>
            {t('rack.description')}: {description}
          </Text>
          <Button
            leftIcon={<EditIcon />}
            size="sm"
            onClick={onEditRack}
          >
            {t('common.edit')}
          </Button>
        </Box>
        <Box>
          <Text fontSize="lg" fontWeight="bold" mb={2}>
            {t('rack.devices')}
          </Text>
          {devices.map((device) => (
            <Box
              key={device.id}
              p={2}
              borderWidth={1}
              borderRadius="md"
              mb={2}
              cursor="pointer"
              onClick={() => handleDeviceClick(device.id)}
              bg={selectedDeviceId === device.id ? 'gray.100' : 'white'}
            >
              <Text fontWeight="bold">{device.name}</Text>
              <Text>{t('device.position')}: U{device.positionStart} - U{device.positionEnd}</Text>
              <Text>{t('device.status')}: {device.status}</Text>
            </Box>
          ))}
        </Box>
      </Box>
    </Flex>
  )
}

export default RackView 