import { Box, Grid, Text, Flex, useColorMode, Center } from '@chakra-ui/react'
import { useState } from 'react'
import { useTranslation } from '@/contexts/LanguageContext'

interface RackData {
  id: string
  name: string
  totalU: number
  usedU: number
  location: string
}

interface DataCenterLayoutProps {
  racks: RackData[]
  onSelectRack?: (rackId: string) => void
}

// 计算机柜空间状态
const getRackSpaceStatus = (usedU: number, totalU: number) => {
  const utilization = (usedU / totalU) * 100
  if (utilization < 70) {
    return 'sufficient' // 空间充足
  } else if (utilization < 90) {
    return 'limited' // 空间有限
  } else {
    return 'tight' // 空间紧张
  }
}

// 获取状态对应的颜色
const getStatusColor = (status: string, colorMode: string) => {
  switch (status) {
    case 'sufficient':
      return colorMode === 'dark' ? 'green.700' : 'green.100'
    case 'limited':
      return colorMode === 'dark' ? 'yellow.700' : 'yellow.100'
    case 'tight':
      return colorMode === 'dark' ? 'red.700' : 'red.100'
    case 'aircondition':
      return colorMode === 'dark' ? 'blue.700' : 'blue.100'
    case 'pdu':
      return colorMode === 'dark' ? 'red.500' : 'red.300'
    case 'network':
      return colorMode === 'dark' ? 'blue.500' : 'blue.300'
    default:
      return colorMode === 'dark' ? 'gray.700' : 'gray.100'
  }
}

// 获取状态文字
const getStatusText = (status: string, t: (key: string) => string) => {
  switch (status) {
    case 'sufficient':
      return t('space.sufficient')
    case 'limited':
      return t('space.limited')
    case 'tight':
      return t('space.critical')
    case 'aircondition':
      return t('列间空调')
    case 'pdu':
      return t('配电柜')
    case 'network':
      return t('cold.aisle')
    default:
      return ''
  }
}

interface RackItemProps {
  rack: RackData
  onSelect?: (rackId: string) => void
}

// 新增垂直文字组件
const VerticalText = ({ text }: { text: string }) => (
  <Box
    height="100%"
    bg="blue.500"
    color="white"
    display="flex"
    alignItems="center"
    justifyContent="center"
  >
    <Text
      style={{
        writingMode: 'vertical-rl',
        textOrientation: 'mixed', 
        transform: 'rotate(180deg)'
      }}
      fontWeight="bold"
      letterSpacing="1px"
    >
      {text}
    </Text>
  </Box>
);

// 简化的机柜项组件
const RackItem = ({ rack, onSelect }: RackItemProps) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const [isHovered, setIsHovered] = useState(false)
  
  const status = getRackSpaceStatus(rack.usedU, rack.totalU)
  const handleClick = () => {
    if (onSelect) {
      onSelect(rack.id)
    }
  }
  
  // 计算剩余空间和百分比
  const remainingU = rack.totalU - rack.usedU
  const remainingPercentage = Math.round((remainingU / rack.totalU) * 100)
  
  return (
    <Box
      w="100%"
      h="60px"
      bg="white"
      border="1px"
      borderColor="gray.200"
      borderRadius="md"
      textAlign="center"
      position="relative"
      transition="all 0.2s"
      transform={isHovered ? 'scale(1.02)' : 'scale(1)'}
      boxShadow={isHovered ? 'md' : 'none'}
      zIndex={isHovered ? 1 : 0}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      cursor={onSelect ? 'pointer' : 'default'}
      _active={{
        transform: onSelect ? 'scale(0.98)' : 'scale(1)'
      }}
      py={2}
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      mx={1}
      my={1}
    >
      <Text fontWeight="bold" fontSize="sm">{rack.name}</Text>
      <Text fontSize="xs" mt={0}>{rack.usedU}U</Text>
      {/* 添加空间信息 */}
      <Text fontSize="xs" mt={0} color={
        status === 'sufficient' ? 'green.500' : 
        status === 'limited' ? 'yellow.500' : 'red.500'
      }>
        {t('空间')}: {remainingU}U ({remainingPercentage}%)
      </Text>
      
      {isHovered && (
        <Box
          position="absolute"
          top="-80px"
          left="50%"
          transform="translateX(-50%)"
          bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          p={3}
          borderRadius="md"
          boxShadow="md"
          zIndex={2}
          width="150px"
          border="1px"
          borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
        >
          <Text fontWeight="bold" mb={1}>{rack.name}</Text>
          <Flex justify="space-between" mb={1}>
            <Text fontSize="xs">{t('总容量')}:</Text>
            <Text fontSize="xs" fontWeight="medium">{rack.totalU}U</Text>
          </Flex>
          <Flex justify="space-between" mb={1}>
            <Text fontSize="xs">{t('已使用')}:</Text>
            <Text fontSize="xs" fontWeight="medium">{rack.usedU}U</Text>
          </Flex>
          <Flex justify="space-between" mb={1}>
            <Text fontSize="xs">{t('剩余')}:</Text>
            <Text 
              fontSize="xs" 
              fontWeight="medium"
              color={
                status === 'sufficient' ? 'green.500' : 
                status === 'limited' ? 'yellow.500' : 'red.500'
              }
            >
              {rack.totalU - rack.usedU}U ({((rack.totalU - rack.usedU) / rack.totalU * 100).toFixed(0)}%)
            </Text>
          </Flex>
          <Text 
            fontSize="xs" 
            fontWeight="bold"
            color={
              status === 'sufficient' ? 'green.500' : 
              status === 'limited' ? 'yellow.500' : 'red.500'
            }
          >
            {getStatusText(status, t)}
          </Text>
        </Box>
      )}
    </Box>
  )
}

// 特殊设备组件 (空调、配电柜)
const SpecialItem = ({ type }: { type: 'aircondition' | 'pdu' }) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const [isHovered, setIsHovered] = useState(false)
  
  const bgColor = type === 'aircondition' ? 'blue.100' : 'red.100'
  const displayText = type === 'aircondition' ? t('列间空调') : t('配电柜')
  
  return (
    <Box
      w="100%"
      h="60px"
      bg={bgColor}
      border="1px"
      borderColor="gray.200"
      borderRadius="md"
      textAlign="center"
      position="relative"
      transition="all 0.2s"
      transform={isHovered ? 'scale(1.02)' : 'scale(1)'}
      boxShadow={isHovered ? 'md' : 'none'}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      display="flex"
      justifyContent="center"
      alignItems="center"
      mx={1}
      my={1}
    >
      <Text fontWeight="bold" fontSize="sm">{displayText}</Text>
    </Box>
  )
}

const DataCenterLayout = ({ racks, onSelectRack }: DataCenterLayoutProps) => {
  const { colorMode } = useColorMode()
  
  // 通过ID查找机柜
  const findRackById = (id: string) => {
    return racks.find(rack => rack.id === id)
  }
  
  return (
    <Box>
      <Box
        bg={colorMode === 'dark' ? 'gray.800' : 'blue.50'}
        p={3}
        borderRadius="lg"
        border="1px"
        borderColor={colorMode === 'dark' ? 'gray.700' : 'blue.200'}
        mb={4}
      >
        <Flex justify="space-between" mb={4}>
          <Box flex="1">
            <Grid templateColumns="1fr" gap={2}>
              {/* 左侧机柜 */}
              <RackItem 
                rack={findRackById('A08') || { id: 'A08', name: 'A08', totalU: 42, usedU: 20, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A09') || { id: 'A09', name: 'A09', totalU: 42, usedU: 3, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A10') || { id: 'A10', name: 'A10', totalU: 42, usedU: 12, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <SpecialItem type="aircondition" />
              <RackItem 
                rack={findRackById('A11') || { id: 'A11', name: 'A11', totalU: 42, usedU: 8, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A12') || { id: 'A12', name: 'A12', totalU: 42, usedU: 5, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A13') || { id: 'A13', name: 'A13', totalU: 42, usedU: 22, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <SpecialItem type="aircondition" />
              <RackItem 
                rack={findRackById('A14') || { id: 'A14', name: 'A14', totalU: 42, usedU: 27, location: '左侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A15') || { id: 'A15', name: 'A15', totalU: 42, usedU: 1, location: '左侧' }} 
                onSelect={onSelectRack}
              />
            </Grid>
          </Box>
          
          {/* 中间冷通道 */}
          <Box width="40px" mx={2} bg="blue.500" borderRadius="md" display="flex" alignItems="center" justifyContent="center">
            <Text
              style={{
                writingMode: 'vertical-rl',
                textOrientation: 'mixed'
              }}
              fontWeight="bold"
              letterSpacing="1px"
              color="white"
              fontSize="sm"
            >
              冷通道01 - 运维组
            </Text>
          </Box>
          
          <Box flex="1">
            <Grid templateColumns="1fr" gap={2}>
              {/* 右侧机柜 */}
              <RackItem 
                rack={findRackById('A07') || { id: 'A07', name: 'A07', totalU: 42, usedU: 24, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A06') || { id: 'A06', name: 'A06', totalU: 42, usedU: 11, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A05') || { id: 'A05', name: 'A05', totalU: 42, usedU: 17, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <SpecialItem type="aircondition" />
              <RackItem 
                rack={findRackById('A04') || { id: 'A04', name: 'A04', totalU: 42, usedU: 4, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A03') || { id: 'A03', name: 'A03', totalU: 42, usedU: 13, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <RackItem 
                rack={findRackById('A02') || { id: 'A02', name: 'A02', totalU: 42, usedU: 7, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <SpecialItem type="aircondition" />
              <RackItem 
                rack={findRackById('A01') || { id: 'A01', name: 'A01', totalU: 42, usedU: 14, location: '右侧' }} 
                onSelect={onSelectRack}
              />
              <SpecialItem type="pdu" />
            </Grid>
          </Box>
        </Flex>
        
        {/* 图例 */}
        <Flex justify="center" mt={2} gap={6}>
          <Flex align="center">
            <Box w="8px" h="8px" borderRadius="full" bg="green.500" mr={1}></Box>
            <Text fontSize="xs" color="gray.600">空间充足</Text>
          </Flex>
          <Flex align="center">
            <Box w="8px" h="8px" borderRadius="full" bg="yellow.500" mr={1}></Box>
            <Text fontSize="xs" color="gray.600">空间有限</Text>
          </Flex>
          <Flex align="center">
            <Box w="8px" h="8px" borderRadius="full" bg="red.500" mr={1}></Box>
            <Text fontSize="xs" color="gray.600">空间紧张</Text>
          </Flex>
        </Flex>
      </Box>
    </Box>
  )
}

export default DataCenterLayout 