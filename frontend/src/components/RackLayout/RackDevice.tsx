'use client'

import { 
  Box, 
  Text, 
  HStack, 
  Badge, 
  useColorMode, 
  Tooltip,
  Flex,
  Icon
} from '@chakra-ui/react'
import { FaServer, FaExclamationTriangle, FaCheckCircle, FaCog } from 'react-icons/fa'
import { ReactNode } from 'react'
import { useTranslation } from '@/contexts/LanguageContext'

export interface RackDeviceProps {
  id: string
  name: string
  model: string
  status: 'online' | 'offline' | 'maintenance' | 'warning'
  positionStart: number
  positionEnd: number
  temperature?: number
  powerUsage?: number
  ipAddress?: string
  onClick?: () => void
  children?: ReactNode
}

// 获取设备状态信息
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'online':
      return {
        text: 'status.online',
        color: 'green',
        icon: FaCheckCircle
      }
    case 'warning':
      return {
        text: 'status.warning',
        color: 'yellow',
        icon: FaExclamationTriangle
      }
    case 'offline':
      return {
        text: 'status.offline',
        color: 'red',
        icon: FaExclamationTriangle
      }
    case 'maintenance':
      return {
        text: 'status.maintenance',
        color: 'blue',
        icon: FaCog
      }
    default:
      return {
        text: 'status.unknown',
        color: 'gray',
        icon: FaServer
      }
  }
}

const RackDevice = ({
  id,
  name,
  model,
  status,
  positionStart,
  positionEnd,
  temperature,
  powerUsage,
  ipAddress,
  onClick,
  children
}: RackDeviceProps) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const uHeight = positionEnd - positionStart + 1
  const statusInfo = getStatusInfo(status)
  
  return (
    <Tooltip
      label={
        <Box p={2}>
          <Text fontWeight="bold">{name}</Text>
          <Text fontSize="sm">{t('型号')}: {model}</Text>
          <Text fontSize="sm">{t('位置')}: {positionStart}U - {positionEnd}U ({uHeight}U)</Text>
          {ipAddress && <Text fontSize="sm">{t('IP地址')}: {ipAddress}</Text>}
          {temperature && <Text fontSize="sm">{t('温度')}: {temperature}°C</Text>}
          {powerUsage && <Text fontSize="sm">{t('功率')}: {powerUsage}W</Text>}
          <Badge mt={1} colorScheme={statusInfo.color}>{t(statusInfo.text)}</Badge>
        </Box>
      }
      placement="right"
      hasArrow
    >
      <Box
        borderWidth="1px"
        borderRadius="md"
        p={2}
        bg={colorMode === 'dark' ? `${statusInfo.color}.900` : `${statusInfo.color}.50`}
        borderColor={`${statusInfo.color}.400`}
        height={`${uHeight * 40}px`}
        minHeight="40px"
        width="100%"
        position="relative"
        overflow="hidden"
        cursor="pointer"
        transition="all 0.2s"
        _hover={{
          boxShadow: 'md',
          bg: colorMode === 'dark' ? `${statusInfo.color}.800` : `${statusInfo.color}.100`
        }}
        onClick={onClick}
      >
        <Flex 
          position="absolute" 
          top={0} 
          left={0} 
          height="100%" 
          width="4px" 
          bg={`${statusInfo.color}.500`} 
        />
        
        <Flex 
          height="100%" 
          direction="column" 
          justifyContent="space-between" 
          ml={2}
        >
          <Box>
            <HStack>
              <Icon as={statusInfo.icon} color={`${statusInfo.color}.500`} />
              <Text 
                fontWeight="medium" 
                fontSize="sm" 
                noOfLines={1}
              >
                {name}
              </Text>
            </HStack>
            <Text 
              fontSize="xs" 
              color={colorMode === 'dark' ? 'gray.300' : 'gray.600'} 
              noOfLines={1}
            >
              {model}
            </Text>
          </Box>
          
          {children}
          
          <HStack justify="space-between">
            <Badge size="sm" colorScheme={statusInfo.color} variant="subtle">
              {t(statusInfo.text)}
            </Badge>
            <Text 
              fontSize="xs" 
              color={colorMode === 'dark' ? 'gray.300' : 'gray.600'}
            >
              {uHeight}U
            </Text>
          </HStack>
        </Flex>
      </Box>
    </Tooltip>
  )
}

export default RackDevice 