'use client'

import React from 'react'
import {
  Box,
  Flex,
  Text,
  Grid,
  GridItem,
  Badge,
  useColorMode,
  Tooltip,
  HStack,
  SimpleGrid,
  Icon,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress
} from '@chakra-ui/react'
import { 
  FaEthernet, 
  FaWifi, 
  FaServer, 
  FaCircle, 
  FaBolt, 
  FaExclamationTriangle,
  FaSignal,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa'
import { useTranslation } from '@/contexts/LanguageContext'

// 端口状态类型
export type PortStatus = 'up' | 'down' | 'error' | 'disabled' | 'warning'

// 端口类型
export type PortType = 'ethernet' | 'fiber' | 'console' | 'management' | 'sfp' | 'sfpplus' | 'qsfp'

// 端口接口
export interface NetworkPort {
  id: string
  name: string
  type: PortType
  status: PortStatus
  speed?: string
  duplex?: 'full' | 'half'
  connection?: string
  macAddress?: string
  ipAddress?: string
  vlan?: number
  traffic?: {
    inbound: number
    outbound: number
    unit: 'bps' | 'Kbps' | 'Mbps' | 'Gbps'
  }
  errors?: number
  utilization?: number
}

// 网络设备接口
export interface NetworkDevice {
  id: string
  name: string
  type: string
  ports: NetworkPort[]
  status: string
  model?: string
  manufacturer?: string
}

interface NetworkPortStatusProps {
  device: NetworkDevice
}

const NetworkPortStatus: React.FC<NetworkPortStatusProps> = ({ device }) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  
  // 获取端口状态颜色
  const getStatusColor = (status: PortStatus) => {
    switch(status) {
      case 'up':
        return 'green.500'
      case 'down':
        return 'gray.400'
      case 'error':
        return 'red.500'
      case 'warning':
        return 'yellow.500'
      case 'disabled':
        return 'gray.300'
      default:
        return 'gray.400'
    }
  }
  
  // 获取端口类型图标
  const getTypeIcon = (type: PortType) => {
    switch(type) {
      case 'ethernet':
        return FaEthernet
      case 'fiber':
        return FaSignal
      case 'sfp':
      case 'sfpplus':
      case 'qsfp':
        return FaBolt
      default:
        return FaEthernet
    }
  }
  
  // 端口分组（按照类型）
  const groupedPorts: Record<string, NetworkPort[]> = {}
  device.ports.forEach(port => {
    const group = port.type
    if (!groupedPorts[group]) {
      groupedPorts[group] = []
    }
    groupedPorts[group].push(port)
  })
  
  // 计算端口统计数据
  const portStats = {
    total: device.ports.length,
    up: device.ports.filter(p => p.status === 'up').length,
    down: device.ports.filter(p => p.status === 'down').length,
    error: device.ports.filter(p => p.status === 'error').length,
    warning: device.ports.filter(p => p.status === 'warning').length,
    disabled: device.ports.filter(p => p.status === 'disabled').length,
  }
  
  // 渲染端口图标
  const renderPort = (port: NetworkPort) => {
    const IconComponent = getTypeIcon(port.type)
    const statusColor = getStatusColor(port.status)
    
    return (
      <Tooltip
        key={port.id}
        label={
          <Box p={2}>
            <Text fontWeight="bold">{port.name}</Text>
            <Text fontSize="sm">状态: {
              port.status === 'up' ? '已连接' :
              port.status === 'down' ? '未连接' :
              port.status === 'error' ? '错误' :
              port.status === 'warning' ? '警告' : '已禁用'
            }</Text>
            {port.speed && <Text fontSize="sm">速率: {port.speed}</Text>}
            {port.duplex && <Text fontSize="sm">双工: {port.duplex === 'full' ? '全双工' : '半双工'}</Text>}
            {port.vlan && <Text fontSize="sm">VLAN: {port.vlan}</Text>}
            {port.connection && <Text fontSize="sm">连接到: {port.connection}</Text>}
            {port.traffic && (
              <>
                <Text fontSize="sm">流入: {port.traffic.inbound} {port.traffic.unit}</Text>
                <Text fontSize="sm">流出: {port.traffic.outbound} {port.traffic.unit}</Text>
              </>
            )}
            {port.errors !== undefined && <Text fontSize="sm">错误: {port.errors}</Text>}
          </Box>
        }
        placement="top"
        hasArrow
      >
        <Box
          position="relative"
          borderWidth="1px"
          borderRadius="md"
          p={1}
          m={1}
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
          borderColor={port.status === 'up' ? 'green.400' : 'gray.300'}
          _hover={{
            transform: 'scale(1.05)',
            zIndex: 2,
            boxShadow: 'md'
          }}
          transition="all 0.2s"
          cursor="pointer"
          width="60px"
          height="60px"
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
        >
          <Icon 
            as={IconComponent} 
            boxSize="24px" 
            color={statusColor}
            mb={1}
          />
          <Text 
            fontSize="xs" 
            fontWeight="medium"
            isTruncated
            maxWidth="100%"
          >
            {port.name}
          </Text>
          {port.traffic && (
            <Flex w="100%" mt={1} h="2px">
              <Box 
                flex={1} 
                bg="green.400" 
                height="100%" 
                opacity={0.3 + (port.traffic.inbound / 1000) * 0.7} 
              />
              <Box 
                flex={1} 
                bg="blue.400" 
                height="100%" 
                opacity={0.3 + (port.traffic.outbound / 1000) * 0.7} 
              />
            </Flex>
          )}
        </Box>
      </Tooltip>
    )
  }
  
  // 渲染端口组
  const renderPortGroup = (type: string, ports: NetworkPort[]) => {
    const typeName = 
      type === 'ethernet' ? '以太网端口' :
      type === 'fiber' ? '光纤端口' :
      type === 'console' ? '控制台端口' :
      type === 'management' ? '管理端口' :
      type === 'sfp' ? 'SFP端口' :
      type === 'sfpplus' ? 'SFP+端口' :
      type === 'qsfp' ? 'QSFP端口' : type
    
    return (
      <Box 
        key={type}
        borderWidth="1px"
        borderRadius="md"
        overflow="hidden"
        mb={4}
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      >
        <Flex 
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'} 
          p={2} 
          borderBottomWidth="1px"
          justify="space-between"
          align="center"
        >
          <Flex align="center">
            <Icon as={getTypeIcon(type as PortType)} mr={2} />
            <Text fontWeight="medium">{typeName}</Text>
          </Flex>
          <Badge colorScheme={ports.some(p => p.status === 'up') ? 'green' : 'gray'}>
            {ports.filter(p => p.status === 'up').length}/{ports.length} 已连接
          </Badge>
        </Flex>
        <Flex p={2} flexWrap="wrap" justify="flex-start">
          {ports.map(renderPort)}
        </Flex>
      </Box>
    )
  }

  return (
    <Box mb={6}>
      <Card 
        variant="outline" 
        mb={4}
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
        borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
      >
        <CardHeader bg={colorMode === 'dark' ? 'blue.900' : 'blue.50'} py={3}>
          <Heading size="md" color={colorMode === 'dark' ? 'blue.200' : 'blue.600'}>
            端口状态概览
          </Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 2, md: 3, lg: 5 }} spacing={4}>
            <Stat>
              <StatLabel>总端口数</StatLabel>
              <StatNumber>{portStats.total}</StatNumber>
            </Stat>
            <Stat>
              <StatLabel>已连接</StatLabel>
              <StatNumber color="green.500">{portStats.up}</StatNumber>
              <StatHelpText>
                {portStats.total > 0 && (
                  <>{Math.round((portStats.up / portStats.total) * 100)}%</>
                )}
              </StatHelpText>
            </Stat>
            <Stat>
              <StatLabel>未连接</StatLabel>
              <StatNumber color="gray.500">{portStats.down}</StatNumber>
              <StatHelpText>
                {portStats.total > 0 && (
                  <>{Math.round((portStats.down / portStats.total) * 100)}%</>
                )}
              </StatHelpText>
            </Stat>
            <Stat>
              <StatLabel>故障</StatLabel>
              <StatNumber color="red.500">{portStats.error}</StatNumber>
              {portStats.error > 0 && (
                <StatHelpText color="red.500">
                  <StatArrow type="increase" />
                  需要处理
                </StatHelpText>
              )}
            </Stat>
            <Stat>
              <StatLabel>警告</StatLabel>
              <StatNumber color="yellow.500">{portStats.warning}</StatNumber>
              {portStats.warning > 0 && (
                <StatHelpText color="yellow.500">
                  <StatArrow type="increase" />
                  需要检查
                </StatHelpText>
              )}
            </Stat>
          </SimpleGrid>
          
          <Box mt={4}>
            <Text fontWeight="medium" mb={2}>端口利用率</Text>
            <Progress 
              value={(portStats.up / (portStats.total || 1)) * 100} 
              size="sm" 
              colorScheme="blue" 
              borderRadius="full" 
            />
          </Box>
        </CardBody>
      </Card>
      
      <Divider my={4} />
      
      <Box mb={4}>
        <Heading size="md" mb={4}>端口详情</Heading>
        {Object.entries(groupedPorts).map(([type, ports]) => renderPortGroup(type, ports))}
      </Box>
    </Box>
  )
}

export default NetworkPortStatus 