'use client'

import { Box, Text, Tooltip, useColorMode } from '@chakra-ui/react'
import { ReactNode } from 'react'

export interface RackUnitProps {
  position: number
  status: 'occupied' | 'free'
  name?: string
  deviceName?: string
  remainingUnits?: number
  spaceStatus?: 'sufficient' | 'limited' | 'tight'
  isStart?: boolean
  isEnd?: boolean
  onClick?: () => void
  children?: ReactNode
}

// 根据空间状态获取对应的颜色
const getSpaceStatusColor = (status: string, colorMode: string) => {
  switch (status) {
    case 'sufficient':
      return colorMode === 'dark' ? 'green.600' : 'green.100'
    case 'limited':
      return colorMode === 'dark' ? 'yellow.600' : 'yellow.100'
    case 'tight':
      return colorMode === 'dark' ? 'red.600' : 'red.100'
    default:
      return colorMode === 'dark' ? 'gray.700' : 'gray.50'
  }
}

// 获取空间状态对应的文字
const getSpaceStatusText = (status: string) => {
  switch (status) {
    case 'sufficient':
      return '空间充足'
    case 'limited':
      return '空间有限'
    case 'tight':
      return '空间紧张'
    default:
      return ''
  }
}

const RackUnit = ({
  position,
  status,
  name,
  deviceName,
  remainingUnits,
  spaceStatus,
  isStart,
  isEnd,
  onClick,
  children,
}: RackUnitProps) => {
  const { colorMode } = useColorMode()

  // 计算空间状态 - 如果传入spaceStatus则使用，否则根据remainingUnits计算
  const calculatedStatus = spaceStatus || (
    remainingUnits 
      ? (remainingUnits > 10 
          ? 'sufficient' 
          : remainingUnits > 4 
            ? 'limited' 
            : 'tight')
      : 'sufficient'
  )

  // 设置是否显示上下边框
  const showTopBorder = isStart
  const showBottomBorder = isEnd

  // 设置设备单元的背景
  const bgColor = status === 'occupied' 
    ? (colorMode === 'dark' ? 'blue.700' : 'blue.100')
    : getSpaceStatusColor(calculatedStatus, colorMode)

  // 设置设备单元的悬浮背景
  const hoverBgColor = status === 'occupied'
    ? (colorMode === 'dark' ? 'blue.600' : 'blue.200')
    : colorMode === 'dark' 
      ? calculatedStatus === 'sufficient' ? 'green.500' : calculatedStatus === 'limited' ? 'yellow.500' : 'red.500'
      : calculatedStatus === 'sufficient' ? 'green.200' : calculatedStatus === 'limited' ? 'yellow.200' : 'red.200'

  // 设置悬浮提示文字
  const tooltipLabel = status === 'occupied'
    ? `位置: ${position}U ${deviceName ? `\n设备: ${deviceName}` : ''}`
    : remainingUnits
      ? `空闲位置: ${position}U\n连续空闲: ${remainingUnits}U\n${getSpaceStatusText(calculatedStatus)}`
      : `空闲位置: ${position}U`

  return (
    <Tooltip 
      label={tooltipLabel} 
      placement="right" 
      hasArrow 
      bg={colorMode === 'dark' ? 'gray.700' : 'white'}
      color={colorMode === 'dark' ? 'white' : 'black'}
      boxShadow="md"
      p={2}
      borderRadius="md"
      fontSize="sm"
    >
      <Box
        height="28px"
        width="100%"
        bg={bgColor}
        borderTop={showTopBorder ? '2px solid' : '1px solid'}
        borderBottom={showBottomBorder ? '2px solid' : '1px solid'}
        borderLeft="1px solid"
        borderRight="1px solid"
        borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
        display="flex"
        alignItems="center"
        px={2}
        cursor={onClick ? 'pointer' : 'default'}
        transition="all 0.2s ease"
        position="relative"
        _hover={{
          bg: hoverBgColor,
          borderColor: colorMode === 'dark' ? 'gray.500' : 'gray.400',
          transform: 'scale(1.02)',
          boxShadow: 'sm',
          zIndex: 1
        }}
        onClick={onClick}
      >
        <Text 
          fontSize="xs" 
          fontWeight={status === 'occupied' ? 'bold' : 'normal'}
          color={colorMode === 'dark' ? 'white' : status === 'occupied' ? 'gray.700' : 'gray.600'}
          mr={2}
        >
          {position}U
        </Text>
        
        {status === 'occupied' && (
          <Box 
            flex="1" 
            overflow="hidden" 
            textOverflow="ellipsis" 
            whiteSpace="nowrap"
          >
            <Text 
              fontSize="xs" 
              fontWeight="medium"
              color={colorMode === 'dark' ? 'white' : 'gray.700'}
            >
              {name || deviceName || '-'}
            </Text>
          </Box>
        )}
        
        {status === 'free' && remainingUnits && remainingUnits > 1 && (
          <Box 
            bg={colorMode === 'dark' ? 'gray.700' : 'white'} 
            px={1.5} 
            py={0.5} 
            borderRadius="md"
            ml="auto"
          >
            <Text 
              fontSize="xs" 
              fontWeight="medium"
              color={
                calculatedStatus === 'sufficient' ? 'green.500' : 
                calculatedStatus === 'limited' ? 'yellow.500' : 'red.500'
              }
            >
              {remainingUnits}U
            </Text>
          </Box>
        )}
        
        {children}
      </Box>
    </Tooltip>
  )
}

export default RackUnit 