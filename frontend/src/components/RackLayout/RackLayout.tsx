import { Box, Text, Grid, useColorMode } from '@chakra-ui/react'
import RackUnit from './RackUnit'

interface Device {
  id: number
  name: string
  position_start: number
  position_end: number
}

interface RackLayoutProps {
  totalU: number
  devices: Device[]
}

const RackLayout = ({ totalU, devices }: RackLayoutProps) => {
  const { colorMode } = useColorMode()

  // Create an array of U positions from bottom to top
  const positions = Array.from({ length: totalU }, (_, i) => totalU - i)

  // Find continuous empty spaces
  const findEmptySpaces = () => {
    const occupied = new Set(
      devices.flatMap(d => 
        Array.from(
          { length: d.position_end - d.position_start + 1 }, 
          (_, i) => d.position_start + i
        )
      )
    )

    const emptySpaces: { start: number; end: number }[] = []
    let currentStart: number | null = null

    positions.forEach(pos => {
      if (!occupied.has(pos)) {
        if (currentStart === null) {
          currentStart = pos
        }
      } else if (currentStart !== null) {
        emptySpaces.push({ start: currentStart, end: pos - 1 })
        currentStart = null
      }
    })

    if (currentStart !== null) {
      emptySpaces.push({ start: currentStart, end: positions[positions.length - 1] })
    }

    return emptySpaces
  }

  const emptySpaces = findEmptySpaces()

  const getDeviceAtPosition = (position: number) => {
    return devices.find(d => 
      position >= d.position_start && position <= d.position_end
    )
  }

  const getEmptySpaceSize = (position: number) => {
    const space = emptySpaces.find(s => 
      position >= s.start && position <= s.end
    )
    return space ? space.end - space.start + 1 : 0
  }

  const getEmptySpaceStartEnd = (position: number) => {
    const space = emptySpaces.find(s => 
      position >= s.start && position <= s.end
    )
    return space || { start: position, end: position }
  }

  return (
    <Box
      border="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
      borderRadius="lg"
      overflow="hidden"
    >
      <Grid templateColumns="1fr" gap={0}>
        {positions.map(position => {
          const device = getDeviceAtPosition(position)
          const emptySpaceSize = getEmptySpaceSize(position)
          const space = getEmptySpaceStartEnd(position)
          const isStart = device 
            ? position === device.position_end
            : emptySpaceSize > 1 && position === space.start
          const isEnd = device
            ? position === device.position_start
            : emptySpaceSize > 1 && position === space.end

          return (
            <RackUnit
              key={position}
              position={position}
              status={device ? 'occupied' : 'free'}
              deviceName={device?.name}
              remainingUnits={emptySpaceSize > 1 ? emptySpaceSize : undefined}
              isStart={isStart}
              isEnd={isEnd}
            />
          )
        })}
      </Grid>
    </Box>
  )
}

export default RackLayout 