'use client'

import React from 'react'
import {
  Box,
  VStack,
  Flex,
  Text,
  Badge,
  Progress,
  Button,
  Divider,
  useColorMode,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Tooltip,
  Icon
} from '@chakra-ui/react'
import { FaEdit, FaThermometerHalf, FaBolt } from 'react-icons/fa'
import { RackDeviceProps } from './RackDevice'

export interface RackDetailProps {
  id: string
  name: string
  location: string
  description?: string
  totalU: number
  usedU: number
  devices: RackDeviceProps[]
  monitoring?: {
    temperature?: {
      current: number
      capacity: number
    }
    power?: {
      current: number
      capacity: number
    }
  }
  onAddDevice?: () => void
  onEdit?: () => void
}

const RackDetail: React.FC<RackDetailProps> = ({
  id,
  name,
  location,
  description,
  totalU,
  usedU,
  devices,
  monitoring,
  onAddDevice,
  onEdit
}) => {
  const { colorMode } = useColorMode()
  const usagePercent = Math.round((usedU / totalU) * 100)
  
  // 获取电源使用百分比
  const getPowerUsagePercent = () => {
    if (!monitoring?.power) return 0
    return Math.round((monitoring.power.current / monitoring.power.capacity) * 100)
  }
  
  // 确定电源使用颜色
  const getPowerColor = () => {
    const percent = getPowerUsagePercent()
    if (percent > 80) return 'red'
    if (percent > 60) return 'orange'
    return 'green'
  }

  // 按状态筛选设备
  const deviceStatusCount = {
    online: devices.filter(d => d.status === 'online').length,
    offline: devices.filter(d => d.status === 'offline').length,
    warning: devices.filter(d => d.status === 'warning').length,
    maintenance: devices.filter(d => d.status === 'maintenance').length
  }

  return (
    <Box 
      borderWidth="1px" 
      borderRadius="lg" 
      overflow="hidden"
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      boxShadow="md"
      height="100%"
    >
      <Flex 
        p={3} 
        borderBottomWidth="1px" 
        bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'} 
        align="center"
        justify="space-between"
      >
        <Text fontWeight="bold">机柜详情</Text>
        {onEdit && (
          <Button 
            size="sm" 
            variant="ghost" 
            colorScheme="blue"
            leftIcon={<FaEdit />}
            onClick={onEdit}
          >
            编辑
          </Button>
        )}
      </Flex>
      
      <VStack p={4} align="stretch" spacing={4}>
        {/* 基本信息 */}
        <Box>
          <Text color={colorMode === 'dark' ? 'gray.300' : 'gray.600'}>{location}</Text>
          {description && (
            <Text mt={2} fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.500'}>
              {description}
            </Text>
          )}
        </Box>
        
        <Divider />
        
        {/* U位使用情况 */}
        <Box>
          <Flex justify="space-between" align="center" mb={2}>
            <Text fontWeight="medium">U位使用</Text>
            <Text fontSize="sm">
              {usedU} / {totalU} U ({usagePercent}%)
            </Text>
          </Flex>
          <Progress 
            value={usagePercent} 
            colorScheme={usagePercent > 80 ? 'red' : usagePercent > 60 ? 'orange' : 'green'} 
            size="sm" 
            borderRadius="full"
          />
        </Box>
        
        {/* 设备状态统计 */}
        <Box>
          <Text fontWeight="medium" mb={2}>设备状态</Text>
          <Flex wrap="wrap" gap={2}>
            <Badge colorScheme="green">在线: {deviceStatusCount.online}</Badge>
            <Badge colorScheme="red">离线: {deviceStatusCount.offline}</Badge>
            <Badge colorScheme="orange">警告: {deviceStatusCount.warning}</Badge>
            <Badge colorScheme="purple">维护: {deviceStatusCount.maintenance}</Badge>
          </Flex>
        </Box>
        
        {/* 监控数据 */}
        <StatGroup>
          {monitoring?.temperature && (
            <Stat>
              <StatLabel>
                <Flex align="center">
                  <Icon as={FaThermometerHalf} mr={1} />
                  <Text>温度</Text>
                </Flex>
              </StatLabel>
              <Tooltip label="机柜环境温度" placement="top">
                <StatNumber>{monitoring.temperature.current}°C</StatNumber>
              </Tooltip>
            </Stat>
          )}
          
          {monitoring?.power && (
            <Stat>
              <StatLabel>
                <Flex align="center">
                  <Icon as={FaBolt} mr={1} />
                  <Text>电源</Text>
                </Flex>
              </StatLabel>
              <Tooltip label={`${monitoring.power.current}W / ${monitoring.power.capacity}W`} placement="top">
                <Box>
                  <StatNumber>{monitoring.power.current}W</StatNumber>
                  <Progress 
                    value={getPowerUsagePercent()} 
                    colorScheme={getPowerColor()} 
                    size="xs" 
                    mt={1}
                    borderRadius="full"
                  />
                </Box>
              </Tooltip>
            </Stat>
          )}
        </StatGroup>
        
        {/* 设备列表统计 */}
        <Box>
          <Flex justify="space-between" align="center" mb={2}>
            <Text fontWeight="medium">设备总数</Text>
            <Text>{devices.length}</Text>
          </Flex>
        </Box>
      </VStack>
    </Box>
  )
}

export default RackDetail 