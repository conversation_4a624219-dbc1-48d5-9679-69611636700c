'use client'

import React from 'react'
import { Box, Heading, Text, Flex } from '@chakra-ui/react'

interface PageHeaderProps {
  title: string
  subtitle?: string
  children?: React.ReactNode
  extra?: React.ReactNode[]
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, subtitle, children, extra }) => {
  return (
    <Box mb={6}>
      <Flex justify="space-between" align="center">
        <Box>
          <Heading size="lg">{title}</Heading>
          {subtitle && (
            <Text mt={1} color="gray.500">
              {subtitle}
            </Text>
          )}
        </Box>
        {extra && (
          <Flex gap={2}>
            {extra}
          </Flex>
        )}
        {children}
      </Flex>
    </Box>
  )
}

export default PageHeader 