'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon, Package, AlertTriangle, Truck, BarChart3 } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from '@/components/ui/tabs'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'

interface AssetCategory {
  id: number
  name: string
  code: string
  description?: string
  level: number
  parent_id?: number
}

interface ConsumableAssetFormData {
  name: string
  code: string
  category_id: number
  consumable_type: string
  brand: string
  model?: string
  specification: string
  unit: string
  unit_price: number
  currency: string
  current_stock: number
  min_stock_level: number
  max_stock_level: number
  reorder_point: number
  reorder_quantity: number
  supplier: string
  supplier_contact?: string
  supplier_phone?: string
  lead_time_days: number
  shelf_life_days?: number
  storage_location: string
  storage_conditions?: string
  purchase_date?: Date
  expiration_date?: Date
  batch_number?: string
  quality_standard?: string
  usage_department: string
  responsible_person: string
  auto_reorder: boolean
  track_expiration: boolean
  requires_approval: boolean
  status: string
  description?: string
}

const consumableAssetSchema = z.object({
  name: z.string().min(1, '消耗品名称不能为空'),
  code: z.string().min(1, '消耗品编码不能为空'),
  category_id: z.number().min(1, '请选择消耗品分类'),
  consumable_type: z.string().min(1, '请选择消耗品类型'),
  brand: z.string().min(1, '品牌不能为空'),
  model: z.string().optional(),
  specification: z.string().min(1, '规格型号不能为空'),
  unit: z.string().min(1, '计量单位不能为空'),
  unit_price: z.number().min(0, '单价不能为负数'),
  currency: z.string(),
  current_stock: z.number().min(0, '当前库存不能为负数'),
  min_stock_level: z.number().min(0, '最低库存不能为负数'),
  max_stock_level: z.number().min(0, '最高库存不能为负数'),
  reorder_point: z.number().min(0, '补货点不能为负数'),
  reorder_quantity: z.number().min(1, '补货数量必须大于0'),
  supplier: z.string().min(1, '供应商不能为空'),
  supplier_contact: z.string().optional(),
  supplier_phone: z.string().optional(),
  lead_time_days: z.number().min(0, '采购周期不能为负数'),
  shelf_life_days: z.number().optional(),
  storage_location: z.string().min(1, '存储位置不能为空'),
  storage_conditions: z.string().optional(),
  purchase_date: z.date().optional(),
  expiration_date: z.date().optional(),
  batch_number: z.string().optional(),
  quality_standard: z.string().optional(),
  usage_department: z.string().min(1, '使用部门不能为空'),
  responsible_person: z.string().min(1, '负责人不能为空'),
  auto_reorder: z.boolean(),
  track_expiration: z.boolean(),
  requires_approval: z.boolean(),
  status: z.string(),
  description: z.string().optional(),
}).refine((data) => data.max_stock_level >= data.min_stock_level, {
  message: "最高库存必须大于等于最低库存",
  path: ["max_stock_level"],
}).refine((data) => data.reorder_point >= data.min_stock_level, {
  message: "补货点应该大于等于最低库存",
  path: ["reorder_point"],
})

interface ConsumableAssetFormProps {
  onSubmit: (data: ConsumableAssetFormData) => Promise<void>
  initialData?: Partial<ConsumableAssetFormData>
  isLoading?: boolean
}

export function ConsumableAssetForm({ onSubmit, initialData, isLoading = false }: ConsumableAssetFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [autoGenerateCode, setAutoGenerateCode] = useState(true)

  const form = useForm<ConsumableAssetFormData>({
    resolver: zodResolver(consumableAssetSchema),
    defaultValues: {
      name: '',
      code: '',
      category_id: 0,
      consumable_type: '',
      brand: '',
      specification: '',
      unit: '',
      unit_price: 0,
      currency: 'CNY',
      current_stock: 0,
      min_stock_level: 10,
      max_stock_level: 100,
      reorder_point: 20,
      reorder_quantity: 50,
      supplier: '',
      lead_time_days: 7,
      storage_location: '',
      usage_department: '',
      responsible_person: '',
      auto_reorder: false,
      track_expiration: false,
      requires_approval: false,
      status: 'active',
      ...initialData
    }
  })

  // 获取消耗品分类
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/asset-categories')
        if (response.ok) {
          const data = await response.json()
          // 过滤消耗品相关分类
          const consumableCategories = data.filter((cat: AssetCategory) =>
            cat.name.includes('消耗品') || cat.code.includes('CONS')
          )
          setCategories(consumableCategories)
        }
      } catch (error) {
        console.error('获取资产分类失败:', error)
        toast({
          title: '错误',
          description: '获取资产分类失败',
          variant: 'destructive'
        })
      }
    }
    fetchCategories()
  }, [])

  // 构建层级化的分类选项
  const buildHierarchicalCategories = (categories: AssetCategory[], parentId: number | null = null, level: number = 0): any[] => {
    const result: any[] = []
    const children = categories.filter(cat => cat.parent_id === parentId)
    
    children.forEach(category => {
      const indent = '　'.repeat(level) // 使用全角空格缩进
      result.push({
        ...category,
        displayName: `${indent}${category.name} (${category.code})`,
        level
      })
      
      // 递归添加子分类
      const subCategories = buildHierarchicalCategories(categories, category.id, level + 1)
      result.push(...subCategories)
    })
    
    return result
  }

  const hierarchicalCategories = buildHierarchicalCategories(categories)

  // 自动生成消耗品编码
  const generateConsumableCode = (categoryId: number, consumableType: string, brand: string) => {
    if (!autoGenerateCode) return
    
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      const prefix = category.code || 'CS'
      const year = new Date().getFullYear().toString().slice(-2)
      const month = (new Date().getMonth() + 1).toString().padStart(2, '0')
      const typePrefix = consumableType.substring(0, 2).toUpperCase()
      const brandPrefix = brand.substring(0, 2).toUpperCase()
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const code = `${prefix}-${year}${month}${typePrefix}${brandPrefix}${random}`
      form.setValue('code', code)
    }
  }

  // 监听字段变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name: fieldName }) => {
      if (fieldName === 'category_id' || fieldName === 'consumable_type' || fieldName === 'brand') {
        const categoryId = value.category_id
        const consumableType = value.consumable_type
        const brand = value.brand
        if (categoryId && consumableType && brand && autoGenerateCode) {
          generateConsumableCode(categoryId, consumableType, brand)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, categories, autoGenerateCode])

  // 计算库存状态
  const getStockStatus = (current: number, min: number, reorderPoint: number) => {
    if (current <= 0) return { status: 'out_of_stock', color: 'destructive', text: '缺货' }
    if (current <= min) return { status: 'low_stock', color: 'destructive', text: '库存不足' }
    if (current <= reorderPoint) return { status: 'reorder', color: 'secondary', text: '需要补货' }
    return { status: 'normal', color: 'default', text: '库存正常' }
  }

  const handleSubmit = async (data: ConsumableAssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      toast({
        title: '成功',
        description: '消耗品资产登记成功',
      })
      form.reset()
    } catch (error) {
      console.error('消耗品资产登记失败:', error)
      toast({
        title: '错误',
        description: '消耗品资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>消耗品名称 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入消耗品名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                消耗品编码 *
                <Switch
                  checked={autoGenerateCode}
                  onCheckedChange={setAutoGenerateCode}
                  size="sm"
                />
                <span className="text-xs text-muted-foreground">自动生成</span>
              </FormLabel>
              <FormControl>
                <Input 
                  placeholder="请输入消耗品编码" 
                  {...field} 
                  disabled={autoGenerateCode}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>消耗品分类 *</FormLabel>
              <Select onValueChange={(value) => field.onChange(Number(value))} value={field.value?.toString()}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择消耗品分类" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hierarchicalCategories.map((category) => (
                    <SelectItem 
                      key={category.id} 
                      value={category.id.toString()}
                      className={category.level > 0 ? 'text-sm' : 'font-medium'}
                    >
                      {category.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="consumable_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>消耗品类型 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择消耗品类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="print_supplies">打印耗材</SelectItem>
                  <SelectItem value="network_supplies">网络耗材</SelectItem>
                  <SelectItem value="office_supplies">办公用品</SelectItem>
                  <SelectItem value="cleaning_supplies">清洁用品</SelectItem>
                  <SelectItem value="safety_supplies">安全用品</SelectItem>
                  <SelectItem value="maintenance_supplies">维护用品</SelectItem>
                  <SelectItem value="electronic_components">电子元件</SelectItem>
                  <SelectItem value="cables_connectors">线缆连接器</SelectItem>
                  <SelectItem value="storage_media">存储介质</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="brand"
          render={({ field }) => (
            <FormItem>
              <FormLabel>品牌 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入品牌" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="model"
          render={({ field }) => (
            <FormItem>
              <FormLabel>型号</FormLabel>
              <FormControl>
                <Input placeholder="请输入型号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="specification"
          render={({ field }) => (
            <FormItem>
              <FormLabel>规格型号 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入规格型号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 位置信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">存放位置信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="storage_location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>存放位置 *</FormLabel>
                <FormControl>
                  <Input placeholder="请输入存放位置" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="building"
            render={({ field }) => (
              <FormItem>
                <FormLabel>建筑物</FormLabel>
                <FormControl>
                  <Input placeholder="请输入建筑物" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="floor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>楼层</FormLabel>
                <FormControl>
                  <Input placeholder="请输入楼层" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="room"
            render={({ field }) => (
              <FormItem>
                <FormLabel>房间</FormLabel>
                <FormControl>
                  <Input placeholder="请输入房间" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="storage_cabinet"
            render={({ field }) => (
              <FormItem>
                <FormLabel>存储柜</FormLabel>
                <FormControl>
                  <Input placeholder="请输入存储柜编号" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="unit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>计量单位 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择计量单位" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="piece">个</SelectItem>
                  <SelectItem value="box">盒</SelectItem>
                  <SelectItem value="pack">包</SelectItem>
                  <SelectItem value="set">套</SelectItem>
                  <SelectItem value="roll">卷</SelectItem>
                  <SelectItem value="meter">米</SelectItem>
                  <SelectItem value="kilogram">千克</SelectItem>
                  <SelectItem value="liter">升</SelectItem>
                  <SelectItem value="bottle">瓶</SelectItem>
                  <SelectItem value="bag">袋</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="unit_price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>单价</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  step="0.01"
                  placeholder="请输入单价" 
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="currency"
          render={({ field }) => (
            <FormItem>
              <FormLabel>货币类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择货币类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                  <SelectItem value="USD">美元 (USD)</SelectItem>
                  <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                  <SelectItem value="JPY">日元 (JPY)</SelectItem>
                  <SelectItem value="HKD">港币 (HKD)</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="active">正常</SelectItem>
                  <SelectItem value="discontinued">停产</SelectItem>
                  <SelectItem value="obsolete">淘汰</SelectItem>
                  <SelectItem value="restricted">限制使用</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )

  const renderInventoryInfo = () => {
    const currentStock = form.watch('current_stock')
    const minStock = form.watch('min_stock_level')
    const reorderPoint = form.watch('reorder_point')
    const stockStatus = getStockStatus(currentStock, minStock, reorderPoint)
    
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <FormField
            control={form.control}
            name="current_stock"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  当前库存
                  <Badge variant={stockStatus.color as any}>
                    {stockStatus.text}
                  </Badge>
                </FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="当前库存" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="min_stock_level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>最低库存</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="最低库存" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="max_stock_level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>最高库存</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="最高库存" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="reorder_point"
            render={({ field }) => (
              <FormItem>
                <FormLabel>补货点</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="补货点" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="reorder_quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>补货数量</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="补货数量" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="lead_time_days"
            render={({ field }) => (
              <FormItem>
                <FormLabel>采购周期（天）</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="采购周期" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="storage_location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>存储位置 *</FormLabel>
                <FormControl>
                  <Input placeholder="请输入存储位置" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="shelf_life_days"
            render={({ field }) => (
              <FormItem>
                <FormLabel>保质期（天）</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="保质期天数" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="storage_conditions"
          render={({ field }) => (
            <FormItem>
              <FormLabel>存储条件</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入存储条件要求" 
                  className="min-h-[80px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <FormField
            control={form.control}
            name="auto_reorder"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">自动补货</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    库存低于补货点时自动下单
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="track_expiration"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">跟踪过期</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    监控消耗品过期时间
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="requires_approval"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">需要审批</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    领用时需要审批
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    )
  }

  const renderSupplierInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="supplier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>供应商 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入供应商名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="supplier_contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系人</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="supplier_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系电话</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系电话" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="usage_department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>使用部门 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入使用部门" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>负责人 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入负责人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="purchase_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>采购日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择采购日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="expiration_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>过期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择过期日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="batch_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>批次号</FormLabel>
              <FormControl>
                <Input placeholder="请输入批次号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="quality_standard"
        render={({ field }) => (
          <FormItem>
            <FormLabel>质量标准</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请输入质量标准和要求" 
                className="min-h-[80px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>备注说明</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请输入备注说明" 
                className="min-h-[80px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="inventory" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              库存管理
            </TabsTrigger>
            <TabsTrigger value="supplier" className="flex items-center gap-2">
              <Truck className="h-4 w-4" />
              供应商信息
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="mt-6">
            {renderBasicInfo()}
          </TabsContent>
          
          <TabsContent value="inventory" className="mt-6">
            {renderInventoryInfo()}
          </TabsContent>
          
          <TabsContent value="supplier" className="mt-6">
            {renderSupplierInfo()}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button type="button" variant="outline" onClick={() => form.reset()}>
            重置
          </Button>
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting ? '登记中...' : '确认登记'}
          </Button>
        </div>
      </form>
    </Form>
  )
}