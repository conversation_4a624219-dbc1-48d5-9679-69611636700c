'use client'

import React from 'react'
import { Checkbox as ShadcnCheckbox } from "./ui/checkbox"
import { Label } from "./ui/label"

// 兼容NextUI和Chakra UI的Checkbox属性
export interface CheckboxProps {
  // 通用属性
  id?: string
  name?: string
  value?: string
  checked?: boolean
  defaultChecked?: boolean
  disabled?: boolean
  required?: boolean
  className?: string
  onChange?: (checked: boolean) => void
  onBlur?: () => void
  
  // 标签相关
  label?: React.ReactNode
  labelPlacement?: 'start' | 'end' | 'top' | 'bottom'
  
  // 大小相关
  size?: 'sm' | 'md' | 'lg'
  
  // 状态相关
  isInvalid?: boolean
  errorMessage?: string
  
  // 其他属性
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  indeterminate?: boolean
  isIndeterminate?: boolean
}

/**
 * Checkbox组件 - 兼容NextUI和Chakra UI的API
 */
export function Checkbox({
  id,
  name,
  value,
  checked,
  defaultChecked,
  disabled = false,
  required = false,
  className = '',
  onChange,
  onBlur,
  label,
  labelPlacement = 'end',
  size = 'md',
  isInvalid = false,
  errorMessage,
  color = 'primary',
  indeterminate,
  isIndeterminate,
}: CheckboxProps) {
  // 处理大小
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }
  
  // 处理颜色
  const colorClasses = {
    default: 'border-gray-400 data-[state=checked]:bg-gray-500',
    primary: 'border-primary data-[state=checked]:bg-primary',
    secondary: 'border-secondary data-[state=checked]:bg-secondary',
    success: 'border-green-500 data-[state=checked]:bg-green-500',
    warning: 'border-yellow-500 data-[state=checked]:bg-yellow-500',
    danger: 'border-destructive data-[state=checked]:bg-destructive',
  }
  
  // 处理错误状态
  const errorClasses = isInvalid ? 'border-destructive' : ''
  
  // 处理值变化
  const handleCheckedChange = (checked: boolean) => {
    if (onChange) {
      onChange(checked)
    }
  }
  
  // 处理不确定状态
  const isIndeterminateState = indeterminate || isIndeterminate
  
  // 渲染标签
  const renderLabel = () => {
    if (!label) return null
    
    return (
      <Label 
        htmlFor={id} 
        className={`
          ${labelPlacement === 'start' ? 'mr-2 order-first' : ''}
          ${labelPlacement === 'end' ? 'ml-2' : ''}
          ${labelPlacement === 'top' ? 'mb-2 block' : ''}
          ${labelPlacement === 'bottom' ? 'mt-2 block' : ''}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
    )
  }
  
  // 渲染错误信息
  const renderError = () => {
    if (!isInvalid || !errorMessage) return null
    
    return (
      <p className="text-destructive text-sm mt-1">
        {errorMessage}
      </p>
    )
  }
  
  return (
    <div className={`
      ${labelPlacement === 'top' || labelPlacement === 'bottom' ? 'flex flex-col' : 'flex items-center'}
    `}>
      <div className="flex items-center">
        <ShadcnCheckbox
          id={id}
          name={name}
          value={value}
          checked={checked}
          defaultChecked={defaultChecked}
          disabled={disabled}
          required={required}
          className={`
            ${sizeClasses[size]} 
            ${colorClasses[color]} 
            ${errorClasses} 
            ${className}
          `}
          onCheckedChange={handleCheckedChange}
          onBlur={onBlur}
          data-indeterminate={isIndeterminateState}
        />
        {renderLabel()}
      </div>
      {renderError()}
    </div>
  )
}

/**
 * CheckboxGroup组件 - 用于管理一组复选框
 */
export interface CheckboxGroupProps {
  children: React.ReactNode
  value?: string[]
  defaultValue?: string[]
  onChange?: (values: string[]) => void
  orientation?: 'horizontal' | 'vertical'
  className?: string
}

export function CheckboxGroup({
  children,
  value,
  defaultValue,
  onChange,
  orientation = 'vertical',
  className = '',
}: CheckboxGroupProps) {
  const [selectedValues, setSelectedValues] = React.useState<string[]>(value || defaultValue || [])
  
  React.useEffect(() => {
    if (value !== undefined) {
      setSelectedValues(value)
    }
  }, [value])
  
  const handleCheckboxChange = (checkboxValue: string, checked: boolean) => {
    let newValues: string[]
    
    if (checked) {
      newValues = [...selectedValues, checkboxValue]
    } else {
      newValues = selectedValues.filter(v => v !== checkboxValue)
    }
    
    if (value === undefined) {
      setSelectedValues(newValues)
    }
    
    if (onChange) {
      onChange(newValues)
    }
  }
  
  // 克隆子元素并注入属性
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        checked: selectedValues.includes(child.props.value),
        onChange: (checked: boolean) => handleCheckboxChange(child.props.value, checked),
      })
    }
    return child
  })
  
  return (
    <div className={`
      ${orientation === 'horizontal' ? 'flex flex-row gap-4' : 'flex flex-col gap-2'}
      ${className}
    `}>
      {childrenWithProps}
    </div>
  )
}

/**
 * 用法示例:
 * 
 * // 单个复选框
 * <Checkbox label="同意条款" onChange={(checked) => console.log(checked)} />
 * 
 * // 复选框组
 * <CheckboxGroup onChange={(values) => console.log(values)}>
 *   <Checkbox value="apple" label="苹果" />
 *   <Checkbox value="banana" label="香蕉" />
 *   <Checkbox value="orange" label="橙子" />
 * </CheckboxGroup>
 */
