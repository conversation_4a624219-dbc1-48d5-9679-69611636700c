'use client'

import React from 'react';
import dynamic from 'next/dynamic';

// 动态导入 Chakra UI 组件
const ChakraComponents = dynamic(
  () => import('@/components/Dashboard/ChakraComponents'),
  { ssr: false }
);
import {
  ArrowUp,
  ArrowDown,
  Clock,
  Lightning,
  Warning,
  CheckCircle,
  X,
  Gauge,
  Wifi,
  Database,
} from '@phosphor-icons/react';

interface KPICardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ElementType;
  iconColor: string;
  helpText?: string;
  isGood?: boolean;
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  change,
  icon,
  iconColor,
  helpText,
  isGood = true,
}) => {
  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading...</div>
  }

  // 解构 Chakra 组件
  const { Box, Stat, StatLabel, StatNumber, StatHelpText, StatArrow, Flex, Icon, Text, useColorModeValue } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      p={5}
      borderRadius="xl"
      bg={bgColor}
      boxShadow="md"
      borderWidth="1px"
      borderColor={borderColor}
      transition="all 0.3s"
      _hover={{ transform: 'translateY(-5px)', boxShadow: 'lg' }}
    >
      <Flex justify="space-between" mb={4}>
        <Stat>
          <StatLabel fontWeight="medium" fontSize="sm" color="gray.500">
            {title}
          </StatLabel>
          <StatNumber fontSize="2xl" fontWeight="bold" my={2}>
            {value}
          </StatNumber>
          {change !== undefined && (
            <StatHelpText mb={0}>
              <Flex align="center">
                <StatArrow type={change >= 0 ? 'increase' : 'decrease'} />
                <Text color={isGood ? (change >= 0 ? 'green.500' : 'red.500') : (change >= 0 ? 'red.500' : 'green.500')}>
                  {Math.abs(change)}%
                </Text>
              </Flex>
            </StatHelpText>
          )}
          {helpText && (
            <Text fontSize="xs" color="gray.500" mt={1}>
              {helpText}
            </Text>
          )}
        </Stat>
        <Flex
          w="48px"
          h="48px"
          borderRadius="full"
          bg={`${iconColor}20`}
          color={iconColor}
          align="center"
          justify="center"
        >
          <Icon as={icon} boxSize={6} />
        </Flex>
      </Flex>
    </Box>
  );
};

const KPIMetrics: React.FC = () => {
  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading KPI metrics...</div>
  }

  // 解构 Chakra 组件
  const { SimpleGrid, Tooltip } = ChakraComponents;

  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
      <KPICard
        title="网络可用性"
        value="99.98%"
        change={0.02}
        icon={Wifi}
        iconColor="green.500"
        helpText="过去30天平均"
        isGood={true}
      />
      <KPICard
        title="平均响应时间"
        value="1.2ms"
        change={-15}
        icon={Lightning}
        iconColor="blue.500"
        helpText="比上周快15%"
        isGood={true}
      />
      <KPICard
        title="活跃告警"
        value="3"
        change={-40}
        icon={Warning}
        iconColor="orange.500"
        helpText="比上周减少40%"
        isGood={false}
      />
      <KPICard
        title="系统健康度"
        value="94%"
        change={2}
        icon={Gauge}
        iconColor="teal.500"
        helpText="比上月提高2%"
        isGood={true}
      />
    </SimpleGrid>
  );
};

export default KPIMetrics;
