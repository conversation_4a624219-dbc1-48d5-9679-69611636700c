'use client'

import React, { useState, useEffect } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Box,
  Button,
  Flex,
  Text,
  Icon,
  useToken,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  ButtonGroup
} from '@chakra-ui/react'
import { X } from '@phosphor-icons/react'
import ReactECharts from 'echarts-for-react'
import * as echarts from 'echarts'

type TimeRange = '1h' | '24h' | '7d' | '30d'
type ViewMode = 'single' | 'compare' | 'correlation'

interface HistoricalDataModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  metricType: 'cpu' | 'memory' | 'disk' | 'network' | 'temperature' | 'humidity' | 'power' | 'smoke'
  currentValue: string | number
  unit?: string
  color: string
  showAreaFill?: boolean
  relatedMetrics?: Array<{
    type: string
    name: string
    color: string
  }>
}

const HistoricalDataModal: React.FC<HistoricalDataModalProps> = ({
  isOpen,
  onClose,
  title,
  metricType,
  currentValue,
  unit = '',
  color,
  showAreaFill = false,
  relatedMetrics = []
}) => {
  const [colorValue] = useToken('colors', [color])
  const [timeRange, setTimeRange] = useState<TimeRange>('24h')
  const [activePoints, setActivePoints] = useState<Array<[string, number]>>([])
  const [isClient, setIsClient] = useState(false)

  const generateMockData = () => {
    const now = new Date()
    const dataPoints = timeRange === '1h' ? 60 : 
                      timeRange === '24h' ? 24 : 
                      timeRange === '7d' ? 7 : 30

    const timestamps: string[] = []
    const values: number[] = []

    for (let i = 0; i < dataPoints; i++) {
      const timePoint = new Date(now.getTime() - (dataPoints - i - 1) * 60 * 60 * 1000)
      timestamps.push(timePoint.toISOString())
      values.push(Math.random() * 80 + 20)
    }

    console.log('Generated mock data:', { timestamps, values })
    return { timestamps, values }
  }

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    const { timestamps, values } = generateMockData()
    const points = timestamps.map((ts, i) => [ts, values[i]] as [string, number])
    setActivePoints(points)
  }, [timeRange, isOpen])

  const getChartOption = (viewMode: ViewMode = 'single') => {
    const option = {
      animation: true,
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const date = new Date(params[0].value[0])
          return `${date.toLocaleString()}<br/>${params[0].marker} ${params[0].seriesName}: ${params[0].value[1]}${unit}`
        }
      },
      xAxis: {
        type: 'time',
        axisLabel: {
          formatter: (value: number) => new Date(value).toLocaleTimeString()
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: `{value}${unit}`
        }
      },
      series: [{
        name: title,
        type: 'line',
        data: activePoints,
        smooth: true,
        lineStyle: {
          width: 3,
          color: colorValue
        },
        areaStyle: showAreaFill ? {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: `${colorValue}80` },
            { offset: 1, color: `${colorValue}10` }
          ])
        } : undefined
      }]
    }

    console.log('ECharts option:', option)
    return option
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full" motionPreset="slideInBottom">
      <ModalOverlay bg="blackAlpha.700" backdropFilter="blur(3px)" />
      <ModalContent bg="white" maxW="100%" h="100vh" m={0}>
        <ModalHeader>
          <Flex justify="space-between" align="center">
            <Text>{title}</Text>
            <Button onClick={onClose} variant="ghost">
              <Icon as={X} />
            </Button>
          </Flex>
        </ModalHeader>
        <Flex px={4} pt={2} pb={4} gap={2}>
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button 
              isActive={timeRange === '1h'}
              onClick={() => setTimeRange('1h')}
            >
              1小时
            </Button>
            <Button 
              isActive={timeRange === '24h'}
              onClick={() => setTimeRange('24h')}
            >
              24小时
            </Button>
            <Button 
              isActive={timeRange === '7d'}
              onClick={() => setTimeRange('7d')}
            >
              7天
            </Button>
            <Button 
              isActive={timeRange === '30d'}
              onClick={() => setTimeRange('30d')}
            >
              30天
            </Button>
          </ButtonGroup>
        </Flex>
        <ModalCloseButton />
        <ModalBody p={4}>
          <Tabs>
            <TabList>
              <Tab>单一指标</Tab>
              <Tab>指标对比</Tab>
            </TabList>
            <TabPanels>
              <TabPanel>
                <Box height="400px" width="100%" suppressHydrationWarning>
                  {isClient && (
                    <ReactECharts
                      option={getChartOption('single')}
                      style={{ height: '100%', width: '100%' }}
                      notMerge={true}
                      lazyUpdate={true}
                    />
                  )}
                </Box>
              </TabPanel>
              <TabPanel>
                <Box height="400px" width="100%" suppressHydrationWarning>
                  {isClient && (
                    <ReactECharts
                      option={getChartOption('compare')}
                      style={{ height: '100%', width: '100%' }}
                      notMerge={true}
                      lazyUpdate={true}
                    />
                  )}
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

export default HistoricalDataModal
