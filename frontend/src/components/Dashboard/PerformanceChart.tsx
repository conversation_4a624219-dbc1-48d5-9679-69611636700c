'use client'

import React, { useState, useEffect } from 'react'
import ReactECharts from 'echarts-for-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2 } from 'lucide-react'

// 模拟数据生成函数
const generateMockData = (days: number = 30) => {
  const now = new Date()
  const data = []
  
  // CPU数据
  const cpuData = []
  // 内存数据
  const memoryData = []
  // 网络数据
  const networkData = []
  // 日期标签
  const dateLabels = []
  
  for (let i = days; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    
    // 格式化日期为 MM-DD 格式
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const dateStr = `${month}-${day}`
    
    dateLabels.push(dateStr)
    
    // 生成随机数据，但保持一定的连续性
    const cpuBase = i === days ? Math.random() * 30 + 20 : cpuData[cpuData.length - 1]
    const cpuValue = Math.max(10, Math.min(90, cpuBase + (Math.random() * 10 - 5)))
    cpuData.push(parseFloat(cpuValue.toFixed(1)))
    
    const memoryBase = i === days ? Math.random() * 30 + 40 : memoryData[memoryData.length - 1]
    const memoryValue = Math.max(30, Math.min(95, memoryBase + (Math.random() * 8 - 4)))
    memoryData.push(parseFloat(memoryValue.toFixed(1)))
    
    const networkBase = i === days ? Math.random() * 20 + 70 : networkData[networkData.length - 1]
    const networkValue = Math.max(50, Math.min(100, networkBase + (Math.random() * 6 - 3)))
    networkData.push(parseFloat(networkValue.toFixed(1)))
  }
  
  return {
    dateLabels,
    cpuData,
    memoryData,
    networkData
  }
}

interface PerformanceChartProps {
  title?: string
  description?: string
  className?: string
}

export default function PerformanceChart({ 
  title = "系统性能", 
  description = "过去30天的系统性能指标",
  className 
}: PerformanceChartProps) {
  const [chartData, setChartData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('30')
  const [refreshing, setRefreshing] = useState(false)
  const [isClient, setIsClient] = useState(false)
  
  // 加载图表数据
  const loadChartData = async (days: number = 30) => {
    try {
      setLoading(true)
      setError(null)
      
      // 在实际应用中，这里应该是API调用
      // 现在我们使用模拟数据
      const mockData = generateMockData(days)
      
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 800))
      
      setChartData(mockData)
    } catch (err) {
      console.error('Failed to load chart data:', err)
      setError('加载数据失败，请稍后重试')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }
  
  // 刷新数据
  const handleRefresh = () => {
    setRefreshing(true)
    loadChartData(parseInt(timeRange))
  }
  
  // 时间范围变化
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value)
    loadChartData(parseInt(value))
  }
  
  // 客户端检测
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 初始加载
  useEffect(() => {
    loadChartData(parseInt(timeRange))
  }, [])
  
  // 图表配置
  const getChartOption = () => {
    if (!chartData) return {}
    
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['CPU使用率', '内存使用率', '网络性能']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.dateLabels
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: 'CPU使用率',
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#10b981'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(16, 185, 129, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(16, 185, 129, 0.05)'
                }
              ]
            }
          },
          emphasis: {
            focus: 'series'
          },
          data: chartData.cpuData
        },
        {
          name: '内存使用率',
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#6366f1'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(99, 102, 241, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(99, 102, 241, 0.05)'
                }
              ]
            }
          },
          emphasis: {
            focus: 'series'
          },
          data: chartData.memoryData
        },
        {
          name: '网络性能',
          type: 'line',
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#f59e0b'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(245, 158, 11, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(245, 158, 11, 0.05)'
                }
              ]
            }
          },
          emphasis: {
            focus: 'series'
          },
          data: chartData.networkData
        }
      ]
    }
  }
  
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">过去7天</SelectItem>
              <SelectItem value="14">过去14天</SelectItem>
              <SelectItem value="30">过去30天</SelectItem>
              <SelectItem value="90">过去90天</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={loading || refreshing}
          >
            {refreshing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            刷新
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading && !refreshing ? (
          <div className="flex h-[300px] items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        ) : error ? (
          <div className="flex h-[300px] items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="mb-2">{error}</p>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                重试
              </Button>
            </div>
          </div>
        ) : (
          <div className={refreshing ? "opacity-60 transition-opacity" : ""} suppressHydrationWarning>
            {isClient && (
              <ReactECharts 
                option={getChartOption()} 
                style={{ height: '300px', width: '100%' }}
                notMerge={true}
                lazyUpdate={true}
              />
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}