'use client'

import React from 'react';
import dynamic from 'next/dynamic';

// 动态导入 Chakra UI 组件
const ChakraComponents = dynamic(
  () => import('@/components/Dashboard/ChakraComponents'),
  { ssr: false }
);
import { useRouter } from 'next/navigation';
import {
  Clock,
  CheckCircle,
  Warning,
  X,
  CloudArrowUp,
  Gear,
  User,
  ArrowRight,
} from '@phosphor-icons/react';

// 活动类型定义
type ActivityType = 'success' | 'warning' | 'error' | 'info';

// 活动项接口
interface ActivityItem {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  time: string;
  user?: string;
  userAvatar?: string;
}

// 模拟活动数据
const mockActivities: ActivityItem[] = [
  {
    id: '1',
    type: 'success',
    title: '巡检完成',
    description: '核心交换机巡检任务已成功完成',
    time: '10分钟前',
    user: '系统',
  },
  {
    id: '2',
    type: 'warning',
    title: 'CPU使用率警告',
    description: 'Router-01 CPU使用率超过80%',
    time: '30分钟前',
    user: '系统',
  },
  {
    id: '3',
    type: 'info',
    title: '配置备份',
    description: '已完成5台设备的配置备份',
    time: '1小时前',
    user: '张工',
    userAvatar: 'https://i.pravatar.cc/150?img=3',
  },
  {
    id: '4',
    type: 'error',
    title: '连接失败',
    description: '无法连接到Access-Switch-02',
    time: '2小时前',
    user: '系统',
  },
  {
    id: '5',
    type: 'info',
    title: '配置更新',
    description: '已更新Core-Switch-01的VLAN配置',
    time: '3小时前',
    user: '李工',
    userAvatar: 'https://i.pravatar.cc/150?img=4',
  },
];

// 获取活动类型对应的图标和颜色
const getActivityTypeProps = (type: ActivityType) => {
  switch (type) {
    case 'success':
      return { icon: CheckCircle, color: 'green.500' };
    case 'warning':
      return { icon: Warning, color: 'orange.500' };
    case 'error':
      return { icon: X, color: 'red.500' };
    case 'info':
    default:
      return { icon: CloudArrowUp, color: 'blue.500' };
  }
};

// 活动项组件
const ActivityItem: React.FC<{ activity: ActivityItem }> = ({ activity }) => {
  const { icon, color } = getActivityTypeProps(activity.type);

  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading...</div>
  }

  // 解构 Chakra 组件
  const { Box, Flex, Text, Icon, Badge, Avatar, useColorModeValue } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.100', 'gray.700');

  return (
    <Box
      p={4}
      borderRadius="md"
      bg={bgColor}
      borderWidth="1px"
      borderColor={borderColor}
      mb={3}
      transition="all 0.2s"
      _hover={{
        transform: 'translateX(5px)',
        borderColor: color,
        boxShadow: 'sm'
      }}
    >
      <Flex align="center" mb={2}>
        <Flex
          w="36px"
          h="36px"
          borderRadius="full"
          bg={`${color}20`}
          color={color}
          align="center"
          justify="center"
          mr={3}
        >
          <Icon as={icon} boxSize={5} />
        </Flex>
        <Box flex="1">
          <Flex justify="space-between" align="center">
            <Text fontWeight="bold">{activity.title}</Text>
            <Badge colorScheme={color.split('.')[0]} variant="subtle" borderRadius="full">
              {activity.type === 'success' ? '成功' :
               activity.type === 'warning' ? '警告' :
               activity.type === 'error' ? '错误' : '信息'}
            </Badge>
          </Flex>
          <Text fontSize="sm" color="gray.500" mt={1}>
            {activity.description}
          </Text>
        </Box>
      </Flex>
      <Flex justify="space-between" align="center" mt={2}>
        <Flex align="center">
          <Icon as={Clock} boxSize={4} color="gray.400" mr={1} />
          <Text fontSize="xs" color="gray.500">
            {activity.time}
          </Text>
        </Flex>
        <Flex align="center">
          {activity.userAvatar ? (
            <Avatar size="xs" src={activity.userAvatar} mr={1} />
          ) : (
            <Icon as={User} boxSize={4} color="gray.400" mr={1} />
          )}
          <Text fontSize="xs" color="gray.500">
            {activity.user}
          </Text>
        </Flex>
      </Flex>
    </Box>
  );
};

// 活动摘要组件
const ActivityFeed: React.FC = () => {
  const router = useRouter();

  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading activity feed...</div>
  }

  // 解构 Chakra 组件
  const {
    Box, Flex, Text, Icon, Badge, Avatar, useColorModeValue,
    Button, Divider, VStack, HStack
  } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box
      p={5}
      borderRadius="xl"
      bg={bgColor}
      boxShadow="md"
      borderWidth="1px"
      borderColor={borderColor}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="lg" fontWeight="medium">
          最近活动
        </Text>
        <Button
          rightIcon={<ArrowRight />}
          variant="ghost"
          size="sm"
          onClick={() => router.push('/notifications')}
        >
          查看全部
        </Button>
      </Flex>
      <Divider mb={4} />
      <VStack spacing={0} align="stretch">
        {mockActivities.map((activity) => (
          <ActivityItem key={activity.id} activity={activity} />
        ))}
      </VStack>
    </Box>
  );
};

export default ActivityFeed;
