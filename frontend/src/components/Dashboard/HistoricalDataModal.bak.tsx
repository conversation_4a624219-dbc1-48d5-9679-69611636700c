'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  Box,
  Button,
  Flex,
  Text,
  useColorMode,
  ButtonGroup,
  Icon,
  useToken,
  HStack,
  VStack,
} from '@chakra-ui/react'
import { Calendar, CaretLeft, CaretRight, Download, Export, X } from '@phosphor-icons/react'

type TimeRange = '1h' | '24h' | '7d' | '30d' | 'custom'

interface HistoricalDataModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  metricType: 'cpu' | 'memory' | 'disk' | 'network' | 'temperature' | 'humidity' | 'power' | 'smoke'
  currentValue: string | number
  unit?: string
  color: string
  showAreaFill?: boolean
}

// 简单的线图点接口
interface Point {
  x: number;
  y: number;
  value: number;
  label: string;
  timestamp: string;
}

const HistoricalDataModal: React.FC<HistoricalDataModalProps> = ({
  isOpen,
  onClose,
  title,
  metricType,
  currentValue,
  unit = '',
  color,
  showAreaFill = false
}) => {
  const { colorMode } = useColorMode()
  const [timeRange, setTimeRange] = useState<TimeRange>('24h')
  const [startDate, setStartDate] = useState<Date>(new Date(Date.now() - 24 * 60 * 60 * 1000))
  const [endDate, setEndDate] = useState<Date>(new Date())
  const [hoverPoint, setHoverPoint] = useState<Point | null>(null)
  const [activePoints, setActivePoints] = useState<Point[]>([])
  const [secondaryPoints, setSecondaryPoints] = useState<Point[]>([])
  const chartRef = useRef<HTMLDivElement>(null)
  
  // 获取颜色的真实值
  const [colorValue] = useToken('colors', [color])
  const bgColor = 'white'
  const borderColor = '#EAEAEA'
  const gridColor = '#F5F5F5'
  const chartBgColor = 'white'
  const textColor = '#707070'
  const lineColor = '#0066B3'
  const fillColor = 'rgba(0, 102, 179, 0.1)' // 淡蓝色填充区域
  
  // 计算最小值和最大值，确保Y轴有适当的范围
  const calculateYAxisRange = (values: number[]) => {
    const max = Math.max(...values);
    const min = Math.min(...values);
    const range = max - min;
    
    // 确保图表有足够的上下边距，使数据点不会太靠近边缘
    const paddedMin = Math.max(0, min - range * 0.1);
    const paddedMax = max + range * 0.1;
    
    return { min: paddedMin, max: paddedMax };
  };
  
  // 格式化日期范围显示
  const formatDateRange = () => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: '2-digit', day: '2-digit' };
    return `${startDate.toLocaleDateString('zh-CN', options)} - ${endDate.toLocaleDateString('zh-CN', options)}`
  }

  // 基于选择的时间范围生成模拟数据
  const generateMockData = () => {
    let dataPoints = 24
    let labels: string[] = []
    let timestamps: string[] = []
    const now = new Date()
    
    switch (timeRange) {
      case '1h':
        dataPoints = 60
        for (let i = 0; i < dataPoints; i++) {
          const timePoint = new Date(now.getTime() - (dataPoints - i - 1) * 60 * 1000)
          labels.push(`${timePoint.getHours().toString().padStart(2, '0')}:${timePoint.getMinutes().toString().padStart(2, '0')}`)
          timestamps.push(timePoint.toISOString())
        }
        break
      case '24h':
        dataPoints = 24
        for (let i = 0; i < dataPoints; i++) {
          const timePoint = new Date(now.getTime() - (dataPoints - i - 1) * 60 * 60 * 1000)
          labels.push(`${timePoint.getHours().toString().padStart(2, '0')}:00`)
          timestamps.push(timePoint.toISOString())
        }
        break
      case '7d':
        dataPoints = 7
        for (let i = 0; i < dataPoints; i++) {
          const timePoint = new Date(now.getTime() - (dataPoints - i - 1) * 24 * 60 * 60 * 1000)
          labels.push(`${(timePoint.getMonth() + 1).toString().padStart(2, '0')}/${timePoint.getDate().toString().padStart(2, '0')}`)
          timestamps.push(timePoint.toISOString())
        }
        break
      case '30d':
        dataPoints = 30
        for (let i = 0; i < dataPoints; i++) {
          const timePoint = new Date(now.getTime() - (dataPoints - i - 1) * 24 * 60 * 60 * 1000)
          labels.push(`${(timePoint.getMonth() + 1).toString().padStart(2, '0')}/${timePoint.getDate().toString().padStart(2, '0')}`)
          timestamps.push(timePoint.toISOString())
        }
        break
      default:
        dataPoints = 24
        for (let i = 0; i < dataPoints; i++) {
          const timePoint = new Date(now.getTime() - (dataPoints - i - 1) * 60 * 60 * 1000)
          labels.push(`${timePoint.getHours().toString().padStart(2, '0')}:00`)
          timestamps.push(timePoint.toISOString())
        }
    }
    
    // 根据指标类型生成不同范围的模拟数据
    let baseValue = 0
    let variance = 0
    let values: number[] = []
    let secondaryValues: number[] = [] // 添加第二个数据集
    
    switch (metricType) {
      case 'cpu':
        baseValue = 65
        variance = 15
        break
      case 'memory':
        baseValue = 45
        variance = 10
        break
      case 'disk':
        baseValue = 72
        variance = 5
        break
      case 'network':
        baseValue = 75
        variance = 40
        // 为网络流量添加出站流量（负值）
        secondaryValues = Array.from({ length: dataPoints }, () => 
          -Math.abs(baseValue * 0.3 + (Math.random() - 0.5) * variance * 0.6)
        )
        break
      case 'temperature':
        baseValue = 24
        variance = 2
        break
      case 'humidity':
        baseValue = 65
        variance = 10
        break
      case 'power':
        baseValue = 3.2
        variance = 0.5
        break
      case 'smoke':
        baseValue = 0
        variance = 0.5
        // 偶尔有烟雾报警
        values = Array.from({ length: dataPoints }, () => 0)
        const alarmIndex = Math.floor(Math.random() * dataPoints)
        values[alarmIndex] = 1
        return { labels, values, timestamps, secondaryValues: [] }
    }

    // 使用更真实的数据模式
    if (timeRange === '24h') {
      // 模拟一天中的使用模式：早上起床，工作时间繁忙，晚上下降
      values = Array.from({ length: dataPoints }, (_, i) => {
        const hour = i;
        if (hour >= 8 && hour <= 18) {
          // 工作时间 - 较高负载
          return baseValue + 10 + Math.sin(((hour - 8) / 10) * Math.PI) * 8 + (Math.random() - 0.5) * variance;
        } else if (hour >= 22 || hour <= 5) {
          // 深夜至清晨 - 低负载
          return baseValue - 15 + (Math.random() * variance / 2);
        } else {
          // 其他时间 - 中等负载
          return baseValue + (Math.random() - 0.3) * variance;
        }
      });
    } else if (timeRange === '7d' || timeRange === '30d') {
      // 模拟工作日和周末模式
      values = Array.from({ length: dataPoints }, (_, i) => {
        const date = new Date(timestamps[i]);
        const dayOfWeek = date.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        if (isWeekend) {
          // 周末负载较低
          return baseValue - 10 + (Math.random() * variance);
        } else {
          // 工作日负载正常到较高
          return baseValue + 5 + (Math.random() - 0.3) * variance;
        }
      });
    } else {
      // 一小时内的数据 - 可能有短暂的峰值
      values = Array.from({ length: dataPoints }, (_, i) => {
        // 添加一些随机峰值
        if (i % 15 === 0 && Math.random() > 0.7) {
          return baseValue + variance * 1.5;
        }
        return baseValue + (Math.random() - 0.5) * variance;
      });
    }
    
    // 确保最后一个值与当前显示值一致
    const currentNumValue = typeof currentValue === 'string' 
      ? parseFloat(currentValue.replace(/[^\d.-]/g, ''))
      : Number(currentValue)
    
    if (!isNaN(currentNumValue)) {
      values[values.length - 1] = currentNumValue
    }
    
    return { labels, values, timestamps, secondaryValues }
  }
  
  const { labels, values, timestamps, secondaryValues } = generateMockData()
  const yAxisRange = calculateYAxisRange(values);
  
  // 创建SVG线图点
  const createPoints = (): Point[] => {
    const points: Point[] = []
    const range = yAxisRange.max - yAxisRange.min || 1
    
    for (let i = 0; i < values.length; i++) {
      const x = (i / (values.length - 1)) * 100
      // 将数据点值映射到Y轴坐标（反转，因为SVG坐标是从上到下的）
      const y = 100 - ((values[i] - yAxisRange.min) / range) * 90
      points.push({ 
        x, 
        y, 
        value: values[i],
        label: labels[i],
        timestamp: timestamps[i]
      })
    }
    
    return points
  }

  // 添加生成第二个数据集的点
  const createSecondaryPoints = (): Point[] => {
    const points: Point[] = []
    const range = yAxisRange.max - yAxisRange.min || 1
    
    for (let i = 0; i < secondaryValues.length; i++) {
      const x = (i / (secondaryValues.length - 1)) * 100
      // 将数据点值映射到Y轴坐标（反转，因为SVG坐标是从上到下的）
      const y = 100 - ((secondaryValues[i] - yAxisRange.min) / range) * 90
      points.push({ 
        x, 
        y, 
        value: secondaryValues[i],
        label: labels[i],
        timestamp: timestamps[i]
      })
    }
    
    return points
  }

  useEffect(() => {
    // 当时间范围改变或模态框打开时重新生成数据点
    setActivePoints(createPoints())
    setSecondaryPoints(createSecondaryPoints())
  }, [timeRange, isOpen])
  
  // 创建SVG路径数据
  const createPathData = (): string => {
    if (activePoints.length === 0) return ''
    
    let path = `M ${activePoints[0].x} ${activePoints[0].y}`
    
    for (let i = 1; i < activePoints.length; i++) {
      path += ` L ${activePoints[i].x} ${activePoints[i].y}`
    }
    
    return path
  }

  // 创建区域填充SVG路径数据
  const createAreaPathData = (): string => {
    if (activePoints.length === 0) return ''
    
    // 起始点
    let path = `M ${activePoints[0].x} 100 L ${activePoints[0].x} ${activePoints[0].y}`
    
    // 添加所有点
    for (let i = 1; i < activePoints.length; i++) {
      path += ` L ${activePoints[i].x} ${activePoints[i].y}`
    }
    
    // 闭合路径到底部
    path += ` L ${activePoints[activePoints.length - 1].x} 100 Z`
    
    return path
  }

  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range)
    
    // 根据时间范围更新起止日期
    const now = new Date()
    
    switch (range) {
      case '1h':
        setStartDate(new Date(now.getTime() - 60 * 60 * 1000))
        break
      case '24h':
        setStartDate(new Date(now.getTime() - 24 * 60 * 60 * 1000))
        break
      case '7d':
        setStartDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000))
        break
      case '30d':
        setStartDate(new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000))
        break
    }
    
    setEndDate(now)
  }

  // 生成Y轴标签
  const getYAxisLabels = () => {
    const { min, max } = yAxisRange;
    const range = max - min;
    const step = range / 4;

    return [
      { value: max, y: 10 },
      { value: min + step * 3, y: 32.5 },
      { value: min + step * 2, y: 55 },
      { value: min + step, y: 77.5 },
      { value: min, y: 100 }
    ];
  }

  const yAxisLabels = getYAxisLabels();
  
  // 格式化Y轴标签值
  const formatYAxisValue = (value: number) => {
    if (metricType === 'temperature') {
      return `${value.toFixed(0)}°C`;
    } else if (metricType === 'power') {
      return `${value.toFixed(1)}kW`;
    } else if (metricType === 'cpu' || metricType === 'memory' || metricType === 'disk' || metricType === 'network') {
      return `${value.toFixed(0)}%`;
    } else if (metricType === 'humidity') {
      return `${value.toFixed(0)}%`;
    } else {
      return `${value.toFixed(0)}${unit}`;
    }
  };
  
  // 鼠标移动处理函数，用于显示悬停数据点
  const handleMouseMove = (e: React.MouseEvent<SVGSVGElement>) => {
    if (!chartRef.current) return;
    
    const svgRect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - svgRect.left) / svgRect.width) * 100;
    
    // 找到最近的点
    let closestPoint = activePoints[0];
    let minDistance = Math.abs(closestPoint.x - x);
    
    for (let i = 1; i < activePoints.length; i++) {
      const distance = Math.abs(activePoints[i].x - x);
      if (distance < minDistance) {
        minDistance = distance;
        closestPoint = activePoints[i];
      }
    }
    
    setHoverPoint(closestPoint);
  };
  
  const handleMouseLeave = () => {
    setHoverPoint(null);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full" motionPreset="slideInBottom">
      <ModalOverlay bg="blackAlpha.700" backdropFilter="blur(3px)" />
      <ModalContent 
        bg={bgColor}
        color="gray.700"
        borderRadius="none"
        maxW="100%"
        h="100vh"
        m={0}
        overflow="hidden"
        position="relative"
      >
        {/* 顶部导航栏 */}
        <Flex 
          w="100%" 
          h="54px" 
          px={6} 
          alignItems="center" 
          justifyContent="space-between" 
          borderBottomWidth="1px" 
          borderColor={borderColor}
          bg={bgColor}
        >
          <Flex alignItems="center" gap={8}>
            <Text fontSize="16px" fontWeight="500" color="#333333">
              {title}
            </Text>
            <HStack spacing={1}>
              <Button 
                size="sm" 
                bg={timeRange === '1h' ? '#F0F0F0' : 'transparent'}
                _hover={{ bg: '#F5F5F5' }}
                color="#333333"
                borderRadius="sm"
                height="30px"
                minW="40px"
                fontWeight="normal"
                onClick={() => handleTimeRangeChange('1h')}
              >
                1h
              </Button>
              <Button 
                size="sm" 
                bg={timeRange === '24h' ? '#F0F0F0' : 'transparent'}
                _hover={{ bg: '#F5F5F5' }}
                color="#333333"
                borderRadius="sm"
                height="30px"
                minW="40px"
                fontWeight={timeRange === '24h' ? "medium" : "normal"}
                onClick={() => handleTimeRangeChange('24h')}
              >
                24h
              </Button>
              <Button 
                size="sm" 
                bg={timeRange === '7d' ? '#F0F0F0' : 'transparent'}
                _hover={{ bg: '#F5F5F5' }}
                color="#333333"
                borderRadius="sm"
                height="30px"
                minW="40px"
                fontWeight="normal"
                onClick={() => handleTimeRangeChange('7d')}
              >
                7d
              </Button>
            </HStack>
          </Flex>
          <Button
            variant="unstyled"
            display="flex"
            alignItems="center"
            justifyContent="center"
            w="32px"
            h="32px"
            onClick={onClose}
            _hover={{ bg: '#F5F5F5' }}
            borderRadius="full"
          >
            <Icon as={X} weight="bold" boxSize={4} color="#333333" />
          </Button>
        </Flex>

        {/* 简洁卡片风格的图表视图 */}
        <Box p={8} height="calc(100vh - 104px)" overflowY="auto">
          <Flex direction="column" maxW="1200px" mx="auto">
            {/* 卡片容器 */}
            <Box 
              bg="white" 
              borderRadius="md" 
              boxShadow="sm" 
              borderWidth="1px" 
              borderColor="#EAEAEA"
              p={6}
              mb={4}
            >
              {/* 卡片标题和当前值 */}
              <Flex direction="column" mb={3}>
                <Text fontSize="sm" color="#6A6A6A" mb={1}>
                  {title}
                </Text>
                <Text fontSize="4xl" fontWeight="bold" lineHeight="1">
                  {typeof currentValue === 'string' 
                    ? currentValue
                    : `${currentValue.toFixed(0)}`}
                </Text>
              </Flex>

              {/* 简洁图表 */}
              <Box 
                ref={chartRef} 
                h="60px" 
                position="relative"
              >
                <svg 
                  width="100%" 
                  height="100%" 
                  viewBox="0 0 100 100" 
                  preserveAspectRatio="none"
                  style={{ overflow: 'visible' }}
                >
                  {/* 区域填充 */}
                  {showAreaFill && (
                    <path
                      d={createAreaPathData()}
                      fill={fillColor}
                      opacity="1"
                    />
                  )}
                  
                  {/* 折线 */}
                  <path
                    d={createPathData()}
                    fill="none"
                    stroke={lineColor}
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Box>
            </Box>
          </Flex>
        </Box>

        {/* 当前模态窗口视图中的详细图表 */}
        <Box display="none">
          {/* 主图表区域 */}
          <Box ref={chartRef} flex="1" bg={bgColor} position="relative" overflow="hidden" p={0}>
            {/* Y轴标签区 */}
            <Box 
              position="absolute" 
              left={0} 
              top={0} 
              bottom={0} 
              width="50px" 
              paddingTop="20px"
              paddingBottom="35px"
              bg={bgColor}
              zIndex={2}
            >
              <Flex 
                height="100%" 
                direction="column" 
                justifyContent="space-between"
                alignItems="flex-end"
                pr={3}
              >
                {yAxisLabels.map((label, i) => (
                  <Text 
                    key={`y-label-${i}`} 
                    fontSize="12px" 
                    color={textColor}
                    pr={1}
                    fontFamily="system-ui, sans-serif"
                  >
                    {label.value.toFixed(0)}%
                  </Text>
                ))}
              </Flex>
            </Box>

            {/* 图表主体 */}
            <Box 
              position="absolute"
              left="50px"
              right={0}
              top={0}
              bottom={0}
              paddingTop="20px"
              paddingBottom="35px"
            >
              <svg 
                width="100%" 
                height="100%" 
                viewBox="0 0 100 100" 
                preserveAspectRatio="none"
                style={{ backgroundColor: bgColor }}
                onMouseMove={handleMouseMove}
                onMouseLeave={handleMouseLeave}
              >
                {/* 垂直网格线 */}
                {labels.map((_, i) => {
                  const x = (i / (labels.length - 1)) * 100;
                  return (
                    <line 
                      key={`grid-v-${i}`}
                      x1={x} 
                      y1="0" 
                      x2={x} 
                      y2="100" 
                      stroke={gridColor} 
                      strokeWidth="1"
                    />
                  );
                })}
                
                {/* 水平网格线 */}
                {yAxisLabels.map((label, i) => (
                  <line 
                    key={`grid-h-${i}`}
                    x1="0" 
                    y1={label.y} 
                    x2="100" 
                    y2={label.y} 
                    stroke={gridColor} 
                    strokeWidth="1"
                  />
                ))}
                
                {/* 当前时间线（垂直虚线） */}
                <line 
                  x1="85" 
                  y1="0" 
                  x2="85" 
                  y2="100" 
                  stroke="rgba(100, 100, 100, 0.3)" 
                  strokeWidth="1"
                  strokeDasharray="3,3"
                />

                {/* 区域填充 */}
                {showAreaFill && (
                  <path
                    d={createAreaPathData()}
                    fill={fillColor}
                    opacity="0.7"
                  />
                )}

                {/* 数据线 */}
                <path
                  d={createPathData()}
                  fill="none"
                  stroke={lineColor}
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                
                {/* 数据点 */}
                {activePoints.map((point, i) => (
                  <circle
                    key={`point-${i}`}
                    cx={point.x}
                    cy={point.y}
                    r="5"
                    fill={lineColor}
                    stroke="white"
                    strokeWidth="2"
                  />
                ))}
                
                {/* 悬停高亮点和信息提示框 */}
                {hoverPoint && (
                  <>
                    {/* 垂直辅助线 */}
                    <line 
                      x1={hoverPoint.x} 
                      y1="0" 
                      x2={hoverPoint.x} 
                      y2="100" 
                      stroke="rgba(100, 100, 100, 0.2)"
                      strokeWidth="1"
                      strokeDasharray="3,2"
                    />
                    
                    {/* 当前点高亮 */}
                    <circle
                      cx={hoverPoint.x}
                      cy={hoverPoint.y}
                      r="7"
                      fill={lineColor}
                      stroke="white"
                      strokeWidth="2"
                    />
                    
                    {/* 悬停信息框 */}
                    <foreignObject
                      x={hoverPoint.x - 40}
                      y={hoverPoint.y - 50}
                      width="80"
                      height="40"
                    >
                      <Box
                        bg="white"
                        p={2}
                        borderRadius="sm"
                        boxShadow="0 2px 8px rgba(0,0,0,0.15)"
                        textAlign="center"
                        border="1px solid"
                        borderColor="#E0E0E0"
                      >
                        <Text fontSize="16px" color="#333333" fontWeight="bold">
                          {hoverPoint.value.toFixed(0)}%
                        </Text>
                        <Text fontSize="12px" color={textColor}>
                          {hoverPoint.label}
                        </Text>
                      </Box>
                    </foreignObject>
                  </>
                )}
              </svg>
            </Box>
            
            {/* 时间轴标签区 */}
            <Box
              position="absolute"
              left="50px"
              right={0}
              bottom={0}
              height="35px"
              paddingX={4}
              borderTopWidth="1px"
              borderColor={borderColor}
              bg={bgColor}
            >
              <Flex justify="space-between" align="center" h="100%" px={2}>
                {labels.filter((_, i) => i % 4 === 0 || i === labels.length - 1).map((label, i) => (
                  <Text key={`time-label-${i}`} fontSize="12px" color={textColor} fontFamily="system-ui, sans-serif">
                    {label}
                  </Text>
                ))}
              </Flex>
            </Box>
          </Box>

          {/* 底部控制栏 */}
          <Flex 
            w="100%" 
            h="50px" 
            px={6} 
            alignItems="center" 
            justifyContent="space-between" 
            borderTopWidth="1px" 
            borderColor={borderColor}
            bg="white"
          >
            <Text color="#333333" fontSize="md" fontWeight="medium">
              当前值: <Text as="span" color={lineColor} fontWeight="bold">{metricType === 'humidity' ? '65%' : formatYAxisValue(values[values.length - 1] || 0)}</Text>
            </Text>
            
            <HStack spacing={6}>
              <HStack spacing={1}>
                <Button 
                  leftIcon={<Icon as={CaretLeft} weight="bold" boxSize="14px" />} 
                  variant="ghost" 
                  size="sm"
                  color="#505050"
                  onClick={() => {
                    const now = new Date();
                    if (timeRange === '1h') {
                      setStartDate(new Date(startDate.getTime() - 60 * 60 * 1000));
                      setEndDate(new Date(endDate.getTime() - 60 * 60 * 1000));
                    } else if (timeRange === '24h') {
                      setStartDate(new Date(startDate.getTime() - 24 * 60 * 60 * 1000));
                      setEndDate(new Date(endDate.getTime() - 24 * 60 * 60 * 1000));
                    } else if (timeRange === '7d') {
                      setStartDate(new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000));
                    } else if (timeRange === '30d') {
                      setStartDate(new Date(startDate.getTime() - 30 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000));
                    }
                  }}
                >
                  上一个
                </Button>
                <Button 
                  rightIcon={<Icon as={CaretRight} weight="bold" boxSize="14px" />} 
                  variant="ghost" 
                  size="sm"
                  color="#505050"
                  isDisabled={timeRange === 'custom' || endDate.getTime() >= new Date().getTime()}
                  onClick={() => {
                    const now = new Date();
                    if (timeRange === '1h') {
                      setStartDate(new Date(startDate.getTime() + 60 * 60 * 1000));
                      setEndDate(new Date(Math.min(endDate.getTime() + 60 * 60 * 1000, now.getTime())));
                    } else if (timeRange === '24h') {
                      setStartDate(new Date(startDate.getTime() + 24 * 60 * 60 * 1000));
                      setEndDate(new Date(Math.min(endDate.getTime() + 24 * 60 * 60 * 1000, now.getTime())));
                    } else if (timeRange === '7d') {
                      setStartDate(new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date(Math.min(endDate.getTime() + 7 * 24 * 60 * 60 * 1000, now.getTime())));
                    } else if (timeRange === '30d') {
                      setStartDate(new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000));
                      setEndDate(new Date(Math.min(endDate.getTime() + 30 * 24 * 60 * 60 * 1000, now.getTime())));
                    }
                  }}
                >
                  下一个
                </Button>
              </HStack>
              
              <Button 
                rightIcon={<Icon as={Export} boxSize="14px" />} 
                size="sm" 
                variant="ghost"
                color="#505050"
              >
                导出数据
              </Button>
            </HStack>
          </Flex>
        </Box>
      </ModalContent>
    </Modal>
  )
}

export default HistoricalDataModal 