'use client'

import {
  Box,
  Container,
  Grid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Heading,
  Text,
  Flex,
  Icon,
  Progress,
  SimpleGrid,
  CircularProgress,
  CircularProgressLabel,
  useColorMode,
  Button,
  HStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue,
  Divider,
  Center,
} from '@chakra-ui/react'

// 导出所有 Chakra UI 组件
const ChakraComponents = {
  Box,
  Container,
  Grid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Heading,
  Text,
  Flex,
  Icon,
  Progress,
  SimpleGrid,
  CircularProgress,
  CircularProgressLabel,
  useColorMode,
  Button,
  HStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue,
  Divider,
  Center,
}

export default ChakraComponents
