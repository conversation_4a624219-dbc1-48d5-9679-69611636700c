'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Activity,
  AlertTriangle,
  BarChart,
  Clock,
  CpuIcon,
  Database,
  HardDrive,
  Loader2,
  RefreshCw,
  Server,
  Wifi
} from 'lucide-react'
import PerformanceChart from './PerformanceChart'

// 仪表盘数据类型
interface DashboardData {
  serverStatus: {
    status: string;
    description: string;
  };
  cpuUsage: {
    current: number;
    trend: number;
  };
  memoryUsage: {
    current: number;
    trend: number;
  };
  networkStatus: {
    uptime: number;
    bandwidth: number;
  };
  devices: {
    name: string;
    ip: string;
    status: string;
    lastCheck: string;
  }[];
  recentActivities: {
    title: string;
    description: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
    icon: React.ElementType;
  }[];
}

// 生成模拟数据
const generateMockData = (): DashboardData => {
  return {
    serverStatus: {
      status: '正常',
      description: '所有系统运行正常'
    },
    cpuUsage: {
      current: Math.floor(Math.random() * 40) + 20,
      trend: Math.floor(Math.random() * 10) - 5
    },
    memoryUsage: {
      current: Math.floor(Math.random() * 30) + 40,
      trend: Math.floor(Math.random() * 8) - 4
    },
    networkStatus: {
      uptime: 99.98,
      bandwidth: Math.floor(Math.random() * 500) + 500
    },
    devices: [
      { name: '核心交换机', ip: '***********', status: '正常', lastCheck: '10分钟前' },
      { name: '边缘路由器', ip: '***********', status: '正常', lastCheck: '15分钟前' },
      { name: '主数据库服务器', ip: '************', status: '正常', lastCheck: '5分钟前' },
      { name: '备份服务器', ip: '************', status: '正常', lastCheck: '20分钟前' },
      { name: '应用服务器', ip: '************', status: '正常', lastCheck: '8分钟前' }
    ],
    recentActivities: [
      {
        title: '系统备份完成',
        description: '每日自动备份已成功完成',
        timestamp: '30分钟前',
        status: 'success',
        icon: Database
      },
      {
        title: '网络带宽峰值',
        description: '检测到网络带宽使用率超过80%',
        timestamp: '2小时前',
        status: 'warning',
        icon: Activity
      },
      {
        title: '服务器更新',
        description: '应用服务器已完成安全更新',
        timestamp: '昨天',
        status: 'success',
        icon: Server
      }
    ]
  };
};

export default function ModernDashboard() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // 加载数据
  const loadData = async () => {
    try {
      setError(null);
      // 在实际应用中，这里应该是API调用
      // 现在我们使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 800));
      const mockData = generateMockData();
      setData(mockData);
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError('加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
  };

  // 初始加载
  useEffect(() => {
    loadData();
  }, []);

  if (loading && !data) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-9 w-24" />
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 rounded-lg" />
          ))}
        </div>
        <Skeleton className="h-[400px] rounded-lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh]">
        <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
        <h2 className="text-2xl font-bold mb-2">加载失败</h2>
        <p className="text-muted-foreground mb-4">{error}</p>
        <Button onClick={loadData}>重试</Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">系统仪表盘</h1>
          <p className="text-muted-foreground text-sm">
            实时监控系统状态和关键性能指标
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          {refreshing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              刷新中...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              刷新数据
            </>
          )}
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-white dark:bg-gray-800 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">服务器状态</CardTitle>
            <div className="h-8 w-8 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
              <Server className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
              {data?.serverStatus.status}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {data?.serverStatus.description}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">CPU 使用率</CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
              <CpuIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{data?.cpuUsage.current}%</div>
            <div className="mt-2 h-1.5 w-full rounded-full bg-blue-100 dark:bg-blue-900/30">
              <div
                className="h-full rounded-full bg-blue-600 dark:bg-blue-400"
                style={{ width: `${data?.cpuUsage.current}%` }}
              />
            </div>
            <p className="mt-1 text-xs text-muted-foreground">
              较昨日 {data?.cpuUsage.trend && data.cpuUsage.trend > 0 ? '+' : ''}{data?.cpuUsage.trend}%
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">内存使用率</CardTitle>
            <div className="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
              <HardDrive className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{data?.memoryUsage.current}%</div>
            <div className="mt-2 h-1.5 w-full rounded-full bg-purple-100 dark:bg-purple-900/30">
              <div
                className="h-full rounded-full bg-purple-600 dark:bg-purple-400"
                style={{ width: `${data?.memoryUsage.current}%` }}
              />
            </div>
            <p className="mt-1 text-xs text-muted-foreground">
              较昨日 {data?.memoryUsage.trend && data.memoryUsage.trend > 0 ? '+' : ''}{data?.memoryUsage.trend}%
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white dark:bg-gray-800 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">网络状态</CardTitle>
            <div className="h-8 w-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center">
              <Wifi className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">{data?.networkStatus.uptime.toFixed(2)}%</div>
            <p className="text-xs text-muted-foreground mt-1">
              正常运行时间
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-7">
        <Card className="md:col-span-4 bg-white dark:bg-gray-800 border-0 shadow-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">系统性能趋势</CardTitle>
                <CardDescription className="text-xs">
                  CPU、内存和网络性能历史数据
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-2">
            <PerformanceChart className="border-0" />
          </CardContent>
        </Card>

        <Card className="md:col-span-3 bg-white dark:bg-gray-800 border-0 shadow-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">最近活动</CardTitle>
                <CardDescription className="text-xs">
                  系统最近的活动记录
                </CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="h-8 text-xs">
                查看全部
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data?.recentActivities.map((item, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`mt-0.5 rounded-full p-1.5
                    ${item.status === 'success' ? 'bg-emerald-100 dark:bg-emerald-900/30' :
                      item.status === 'warning' ? 'bg-amber-100 dark:bg-amber-900/30' :
                      'bg-rose-100 dark:bg-rose-900/30'}`}>
                    <item.icon className={`h-3.5 w-3.5
                      ${item.status === 'success' ? 'text-emerald-600 dark:text-emerald-400' :
                        item.status === 'warning' ? 'text-amber-600 dark:text-amber-400' :
                        'text-rose-600 dark:text-rose-400'}`} />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium leading-none">
                        {item.title}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {item.timestamp}
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-white dark:bg-gray-800 border-0 shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">设备状态</CardTitle>
              <CardDescription className="text-xs">
                所有网络设备的当前状态
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" className="h-8 text-xs">
              查看全部设备
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="font-medium">设备名称</TableHead>
                  <TableHead className="font-medium">IP地址</TableHead>
                  <TableHead className="font-medium">状态</TableHead>
                  <TableHead className="font-medium">上次检查</TableHead>
                  <TableHead className="font-medium text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.devices.map((device, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{device.name}</TableCell>
                    <TableCell>{device.ip}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:border-emerald-800">
                        {device.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{device.lastCheck}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" className="h-7 text-xs">
                        查看详情
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
