'use client'

import React from 'react';
import dynamic from 'next/dynamic';

// 动态导入 Chakra UI 组件
const ChakraComponents = dynamic(
  () => import('@/components/Dashboard/ChakraComponents'),
  { ssr: false }
);
import {
  Gauge,
  Lightning,
  ArrowsClockwise,
  Wifi,
  Cloud,
  Globe,
  Database,
  HardDrives,
} from '@phosphor-icons/react';

// 健康指标接口
interface HealthMetric {
  id: string;
  name: string;
  value: number;
  icon: React.ElementType;
  color: string;
  description: string;
}

// 模拟健康指标数据
const healthMetrics: HealthMetric[] = [
  {
    id: 'availability',
    name: '可用性',
    value: 99.8,
    icon: Wifi,
    color: 'green.500',
    description: '网络设备可用性百分比',
  },
  {
    id: 'performance',
    name: '性能',
    value: 87,
    icon: Lightning,
    color: 'blue.500',
    description: '网络性能评分',
  },
  {
    id: 'reliability',
    name: '可靠性',
    value: 92,
    icon: ArrowsClockwise,
    color: 'purple.500',
    description: '网络可靠性评分',
  },
  {
    id: 'security',
    name: '安全性',
    value: 85,
    icon: Database,
    color: 'red.500',
    description: '网络安全评分',
  },
];

// 网络区域健康状态接口
interface NetworkArea {
  id: string;
  name: string;
  health: number;
  devices: number;
  issues: number;
  icon: React.ElementType;
  color: string;
}

// 模拟网络区域数据
const networkAreas: NetworkArea[] = [
  {
    id: 'core',
    name: '核心网络',
    health: 95,
    devices: 8,
    issues: 0,
    icon: HardDrives,
    color: 'blue.500',
  },
  {
    id: 'access',
    name: '接入网络',
    health: 88,
    devices: 24,
    issues: 2,
    icon: Wifi,
    color: 'green.500',
  },
  {
    id: 'wan',
    name: '广域网',
    health: 92,
    devices: 4,
    issues: 1,
    icon: Globe,
    color: 'purple.500',
  },
  {
    id: 'datacenter',
    name: '数据中心',
    health: 97,
    devices: 16,
    issues: 0,
    icon: Database,
    color: 'teal.500',
  },
];

// 获取健康状态颜色
const getHealthColor = (value: number) => {
  if (value >= 90) return 'green.500';
  if (value >= 80) return 'yellow.500';
  if (value >= 70) return 'orange.500';
  return 'red.500';
};

// 健康指标组件
const HealthMetricCard: React.FC<{ metric: HealthMetric }> = ({ metric }) => {
  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading...</div>
  }

  // 解构 Chakra 组件
  const {
    Box, Flex, Text, Icon, useColorModeValue, Tooltip,
    CircularProgress, CircularProgressLabel, Progress
  } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const healthColor = getHealthColor(metric.value);

  return (
    <Tooltip label={metric.description} placement="top" hasArrow>
      <Box
        p={4}
        borderRadius="lg"
        bg={bgColor}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        transition="all 0.2s"
        _hover={{ transform: 'scale(1.02)', boxShadow: 'md' }}
      >
        <Flex align="center" mb={3}>
          <Flex
            w="36px"
            h="36px"
            borderRadius="full"
            bg={`${metric.color}20`}
            color={metric.color}
            align="center"
            justify="center"
            mr={3}
          >
            <Icon as={metric.icon} boxSize={5} />
          </Flex>
          <Text fontWeight="medium">{metric.name}</Text>
        </Flex>
        <Flex align="center" justify="space-between">
          <CircularProgress
            value={metric.value}
            color={healthColor}
            size="60px"
            thickness="8px"
          >
            <CircularProgressLabel fontWeight="bold">
              {metric.value}%
            </CircularProgressLabel>
          </CircularProgress>
          <Box flex="1" ml={4}>
            <Progress
              value={metric.value}
              colorScheme={healthColor.split('.')[0]}
              borderRadius="full"
              size="sm"
              hasStripe
            />
          </Box>
        </Flex>
      </Box>
    </Tooltip>
  );
};

// 网络区域组件
const NetworkAreaCard: React.FC<{ area: NetworkArea }> = ({ area }) => {
  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading...</div>
  }

  // 解构 Chakra 组件
  const {
    Box, Flex, Text, Icon, useColorModeValue, Progress
  } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const healthColor = getHealthColor(area.health);

  return (
    <Box
      p={4}
      borderRadius="lg"
      bg={bgColor}
      boxShadow="sm"
      borderWidth="1px"
      borderColor={borderColor}
      transition="all 0.2s"
      _hover={{ transform: 'scale(1.02)', boxShadow: 'md' }}
    >
      <Flex align="center" mb={3}>
        <Flex
          w="36px"
          h="36px"
          borderRadius="full"
          bg={`${area.color}20`}
          color={area.color}
          align="center"
          justify="center"
          mr={3}
        >
          <Icon as={area.icon} boxSize={5} />
        </Flex>
        <Box>
          <Text fontWeight="medium">{area.name}</Text>
          <Text fontSize="xs" color="gray.500">
            {area.devices} 设备 | {area.issues} 问题
          </Text>
        </Box>
      </Flex>
      <Progress
        value={area.health}
        colorScheme={healthColor.split('.')[0]}
        borderRadius="full"
        size="sm"
        hasStripe
      />
      <Flex justify="space-between" mt={2}>
        <Text fontSize="xs" color="gray.500">健康度</Text>
        <Text fontSize="xs" fontWeight="bold" color={healthColor}>
          {area.health}%
        </Text>
      </Flex>
    </Box>
  );
};

// 网络健康状态组件
const NetworkHealth: React.FC = () => {
  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading network health...</div>
  }

  // 解构 Chakra 组件
  const {
    Box, Flex, Text, Icon, useColorModeValue, SimpleGrid,
    CircularProgress, CircularProgressLabel, Progress, Divider
  } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 计算总体健康度
  const overallHealth = Math.round(
    healthMetrics.reduce((sum, metric) => sum + metric.value, 0) / healthMetrics.length
  );

  return (
    <Box
      p={5}
      borderRadius="xl"
      bg={bgColor}
      boxShadow="md"
      borderWidth="1px"
      borderColor={borderColor}
      mb={8}
    >
      <Flex align="center" mb={4}>
        <Icon as={Gauge} color="blue.500" boxSize={6} mr={2} />
        <Text fontSize="lg" fontWeight="medium">网络健康状态</Text>
      </Flex>

      <Flex
        direction={{ base: 'column', md: 'row' }}
        align="center"
        justify="space-between"
        mb={6}
        p={4}
        bg={useColorModeValue('gray.50', 'gray.700')}
        borderRadius="lg"
      >
        <Flex align="center">
          <CircularProgress
            value={overallHealth}
            color={getHealthColor(overallHealth)}
            size="100px"
            thickness="8px"
            mr={4}
          >
            <CircularProgressLabel fontWeight="bold" fontSize="xl">
              {overallHealth}%
            </CircularProgressLabel>
          </CircularProgress>
          <Box>
            <Text fontSize="lg" fontWeight="bold">总体健康度</Text>
            <Text fontSize="sm" color="gray.500">基于所有网络指标的综合评分</Text>
          </Box>
        </Flex>
        <Divider orientation="vertical" height="80px" mx={6} display={{ base: 'none', md: 'block' }} />
        <Box mt={{ base: 4, md: 0 }}>
          <Text fontSize="sm" fontWeight="medium" mb={2}>健康状态分布</Text>
          <Flex gap={2}>
            <Flex align="center">
              <Box w="10px" h="10px" borderRadius="full" bg="green.500" mr={1} />
              <Text fontSize="xs">优 (90-100%)</Text>
            </Flex>
            <Flex align="center">
              <Box w="10px" h="10px" borderRadius="full" bg="yellow.500" mr={1} />
              <Text fontSize="xs">良 (80-89%)</Text>
            </Flex>
            <Flex align="center">
              <Box w="10px" h="10px" borderRadius="full" bg="orange.500" mr={1} />
              <Text fontSize="xs">中 (70-79%)</Text>
            </Flex>
            <Flex align="center">
              <Box w="10px" h="10px" borderRadius="full" bg="red.500" mr={1} />
              <Text fontSize="xs">差 (&lt;70%)</Text>
            </Flex>
          </Flex>
        </Box>
      </Flex>

      <Text fontSize="md" fontWeight="medium" mb={3}>健康指标</Text>
      <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={4} mb={6}>
        {healthMetrics.map(metric => (
          <HealthMetricCard key={metric.id} metric={metric} />
        ))}
      </SimpleGrid>

      <Text fontSize="md" fontWeight="medium" mb={3}>网络区域</Text>
      <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={4}>
        {networkAreas.map(area => (
          <NetworkAreaCard key={area.id} area={area} />
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default NetworkHealth;
