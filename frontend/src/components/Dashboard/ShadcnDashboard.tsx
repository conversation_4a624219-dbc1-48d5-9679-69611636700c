'use client'

import * as React from 'react'
import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  <PERSON><PERSON>,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Activity,
  CpuIcon,
  Database,
  HardDrive,
  Loader2,
  RefreshCw,
  Server,
  Wifi
} from 'lucide-react'
import PerformanceChart from './PerformanceChart'

interface DashboardData {
  serverStatus: {
    status: string;
    description: string;
  };
  cpuUsage: {
    current: number;
    trend: number;
  };
  memoryUsage: {
    current: number;
    trend: number;
  };
  networkStatus: {
    uptime: number;
  };
  recentActivities: Array<{
    icon: any;
    title: string;
    description: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
  }>;
  devices: Array<{
    name: string;
    ip: string;
    status: string;
    lastCheck: string;
  }>;
}

// 模拟数据生成函数
const generateMockData = (): DashboardData => ({
  serverStatus: {
    status: '正常',
    description: '所有系统运行正常',
  },
  cpuUsage: {
    current: Math.floor(Math.random() * 30) + 30, // 30-60%
    trend: Math.floor(Math.random() * 20) - 10, // -10 to +10
  },
  memoryUsage: {
    current: Math.floor(Math.random() * 30) + 50, // 50-80%
    trend: Math.floor(Math.random() * 20) - 10, // -10 to +10
  },
  networkStatus: {
    uptime: 98.2 + (Math.random() * 1.5 - 0.75), // 97.45-99.7%
  },
  recentActivities: [
    {
      icon: Database,
      title: "数据库备份完成",
      description: "自动备份已完成",
      timestamp: "10分钟前",
      status: "success"
    },
    {
      icon: Server,
      title: "服务器更新",
      description: "系统更新已安装",
      timestamp: "2小时前",
      status: "success"
    },
    {
      icon: Activity,
      title: "网络波动",
      description: "检测到短暂网络波动",
      timestamp: "昨天",
      status: "warning"
    }
  ],
  devices: [
    {
      name: "核心交换机",
      ip: "***********",
      status: "运行中",
      lastCheck: "5分钟前"
    },
    {
      name: "防火墙",
      ip: "***********",
      status: "运行中",
      lastCheck: "5分钟前"
    },
    {
      name: "无线AP-01",
      ip: "***********00",
      status: "运行中",
      lastCheck: "10分钟前"
    },
    {
      name: "存储服务器",
      ip: "************",
      status: "运行中",
      lastCheck: "15分钟前"
    },
    {
      name: "备份服务器",
      ip: "************",
      status: "运行中",
      lastCheck: "15分钟前"
    }
  ]
});

export default function ShadcnDashboard() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // 加载数据
  const loadData = async () => {
    try {
      setError(null);
      // 在实际应用中，这里应该是API调用
      // 现在我们使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 800));
      const mockData = generateMockData();
      setData(mockData);
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError('加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
  };

  // 初始加载
  useEffect(() => {
    loadData();
    // 设置自动刷新（每5分钟）
    const interval = setInterval(() => {
      handleRefresh();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  if (loading && !data) {
    return (
      <div className="flex h-[500px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-[500px] items-center justify-center">
        <div className="text-center">
          <p className="mb-4 text-muted-foreground">{error}</p>
          <Button onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                重试中
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                重试
              </>
            )}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">仪表盘</h1>
          <p className="text-muted-foreground">
            欢迎使用燃石医学智能运维平台，查看系统状态和关键指标。
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing}
          variant="outline"
          className="gap-2"
        >
          {refreshing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              刷新中...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              刷新数据
            </>
          )}
        </Button>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="analytics">分析</TabsTrigger>
          <TabsTrigger value="reports">报告</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card
              variant="success"
              hover="lift"
              className={refreshing ? "opacity-70 transition-opacity" : ""}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium" variant="success">
                  服务器状态
                </CardTitle>
                <div className="rounded-full bg-success-100 dark:bg-success-900/30 p-2">
                  <Server className="h-4 w-4 text-success-600 dark:text-success-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-success-600 dark:text-success-400">
                  {data?.serverStatus.status}
                </div>
                <p className="text-xs text-success-600/70 dark:text-success-400/70">
                  {data?.serverStatus.description}
                </p>
              </CardContent>
            </Card>
            <Card
              variant="primary"
              hover="lift"
              className={refreshing ? "opacity-70 transition-opacity" : ""}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium" variant="primary">
                  CPU 使用率
                </CardTitle>
                <div className="rounded-full bg-primary-100 dark:bg-primary-900/30 p-2">
                  <CpuIcon className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary-700 dark:text-primary-300">{data?.cpuUsage.current}%</div>
                <div className="mt-2 h-2 w-full rounded-full bg-primary-100 dark:bg-primary-900/30">
                  <div
                    className="h-full rounded-full bg-gradient-to-r from-primary-400 to-primary-600"
                    style={{ width: `${data?.cpuUsage.current}%` }}
                  />
                </div>
                <p className="mt-2 text-xs text-primary-600/70 dark:text-primary-400/70">
                  较昨日 {data?.cpuUsage.trend && data.cpuUsage.trend > 0 ? '+' : ''}{data?.cpuUsage.trend}%
                </p>
              </CardContent>
            </Card>
            <Card
              variant="secondary"
              hover="lift"
              className={refreshing ? "opacity-70 transition-opacity" : ""}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium" variant="secondary">
                  内存使用率
                </CardTitle>
                <div className="rounded-full bg-secondary-100 dark:bg-secondary-900/30 p-2">
                  <HardDrive className="h-4 w-4 text-secondary-600 dark:text-secondary-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-secondary-700 dark:text-secondary-300">{data?.memoryUsage.current}%</div>
                <div className="mt-2 h-2 w-full rounded-full bg-secondary-100 dark:bg-secondary-900/30">
                  <div
                    className="h-full rounded-full bg-gradient-to-r from-secondary-400 to-secondary-600"
                    style={{ width: `${data?.memoryUsage.current}%` }}
                  />
                </div>
                <p className="mt-2 text-xs text-secondary-600/70 dark:text-secondary-400/70">
                  较昨日 {data?.memoryUsage.trend && data.memoryUsage.trend > 0 ? '+' : ''}{data?.memoryUsage.trend}%
                </p>
              </CardContent>
            </Card>
            <Card
              variant="info"
              hover="lift"
              className={refreshing ? "opacity-70 transition-opacity" : ""}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium" variant="info">
                  网络状态
                </CardTitle>
                <div className="rounded-full bg-info-100 dark:bg-info-900/30 p-2">
                  <Wifi className="h-4 w-4 text-info-600 dark:text-info-400" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-info-700 dark:text-info-300">{data?.networkStatus.uptime.toFixed(1)}%</div>
                <p className="text-xs text-info-600/70 dark:text-info-400/70">
                  正常运行时间
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>系统性能</CardTitle>
                <CardDescription>
                  过去30天的系统性能指标
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PerformanceChart />
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
                <CardDescription>
                  系统最近的活动记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data?.recentActivities.map((item, index) => (
                    <div key={index} className="flex items-center">
                      <div className="mr-4 rounded-full bg-primary/10 p-2">
                        <item.icon className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {item.title}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {item.description}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            item.status === "success" ? "default" :
                            item.status === "warning" ? "secondary" :
                            "destructive"
                          }
                        >
                          {item.status === "success" ? "成功" :
                           item.status === "warning" ? "警告" : "错误"}
                        </Badge>
                        <p className="text-xs text-muted-foreground">
                          {item.timestamp}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>设备状态</CardTitle>
              <CardDescription>
                所有网络设备的当前状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>设备名称</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>上次检查</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.devices.map((device, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{device.name}</TableCell>
                      <TableCell>{device.ip}</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          {device.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{device.lastCheck}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">导出报告</Button>
              <Button onClick={handleRefresh} disabled={refreshing}>
                {refreshing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    刷新中...
                  </>
                ) : (
                  "刷新数据"
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>分析数据</CardTitle>
              <CardDescription>
                查看系统性能和使用情况的详细分析
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <p className="text-muted-foreground">分析数据将在此显示</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统报告</CardTitle>
              <CardDescription>
                查看和下载系统生成的报告
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <p className="text-muted-foreground">报告列表将在此显示</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}