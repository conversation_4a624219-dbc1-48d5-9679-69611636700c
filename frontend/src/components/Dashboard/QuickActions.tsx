'use client'

import React from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';

// 动态导入 Chakra UI 组件
const ChakraComponents = dynamic(
  () => import('@/components/Dashboard/ChakraComponents'),
  { ssr: false }
);
import {
  MagnifyingGlass,
  CloudArrowUp,
  Gear,
  ClipboardText,
  Bell,
  Download,
  Plus,
  ArrowsClockwise,
} from '@phosphor-icons/react';

interface QuickActionProps {
  title: string;
  icon: React.ElementType;
  color: string;
  path: string;
  description: string;
}

const QuickAction: React.FC<QuickActionProps> = ({
  title,
  icon,
  color,
  path,
  description,
}) => {
  const router = useRouter();

  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading...</div>
  }

  // 解构 Chakra 组件
  const { Box, Flex, Icon, Text, useColorModeValue, Tooltip } = ChakraComponents;

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const hoverBgColor = useColorModeValue(`${color}10`, `${color}20`);

  return (
    <Tooltip label={description} placement="top" hasArrow>
      <Box
        p={4}
        borderRadius="xl"
        bg={bgColor}
        boxShadow="md"
        borderWidth="1px"
        borderColor={borderColor}
        cursor="pointer"
        onClick={() => router.push(path)}
        transition="all 0.3s"
        _hover={{
          transform: 'translateY(-5px)',
          boxShadow: 'lg',
          bg: hoverBgColor,
          borderColor: color
        }}
      >
        <Flex direction="column" align="center" justify="center">
          <Flex
            w="50px"
            h="50px"
            borderRadius="full"
            bg={`${color}20`}
            color={color}
            align="center"
            justify="center"
            mb={3}
          >
            <Icon as={icon} boxSize={6} />
          </Flex>
          <Text fontWeight="medium" textAlign="center">
            {title}
          </Text>
        </Flex>
      </Box>
    </Tooltip>
  );
};

const QuickActions: React.FC = () => {
  // 如果 ChakraComponents 还没有加载，显示加载状态
  if (!ChakraComponents) {
    return <div>Loading quick actions...</div>
  }

  // 解构 Chakra 组件
  const { Box, Text, SimpleGrid } = ChakraComponents;

  return (
    <Box mb={8}>
      <Text fontSize="lg" fontWeight="medium" mb={4}>
        快速操作
      </Text>
      <SimpleGrid columns={{ base: 2, sm: 3, md: 4, lg: 8 }} spacing={4}>
        <QuickAction
          title="网络巡检"
          icon={MagnifyingGlass}
          color="blue.500"
          path="/smart-ops/one-click-inspection"
          description="执行一键网络巡检"
        />
        <QuickAction
          title="配置备份"
          icon={CloudArrowUp}
          color="teal.500"
          path="/network/config-backup"
          description="备份网络设备配置"
        />
        <QuickAction
          title="配置管理"
          icon={Gear}
          color="purple.500"
          path="/network/config-management"
          description="管理网络设备配置"
        />
        <QuickAction
          title="巡检报告"
          icon={ClipboardText}
          color="orange.500"
          path="/smart-ops/inspection-report"
          description="查看巡检报告"
        />
        <QuickAction
          title="告警中心"
          icon={Bell}
          color="red.500"
          path="/notifications"
          description="查看系统告警"
        />
        <QuickAction
          title="资产管理"
          icon={Plus}
          color="green.500"
          path="/assets"
          description="管理网络资产"
        />
        <QuickAction
          title="报表导出"
          icon={Download}
          color="yellow.500"
          path="/reports"
          description="导出系统报表"
        />
        <QuickAction
          title="系统更新"
          icon={ArrowsClockwise}
          color="cyan.500"
          path="/settings"
          description="检查系统更新"
        />
      </SimpleGrid>
    </Box>
  );
};

export default QuickActions;
