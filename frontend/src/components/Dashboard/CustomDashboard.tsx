import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  SimpleGrid,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Heading,
  Text,
  Flex,
  IconButton,
  useColorModeValue,
  Select,
  VStack,
  HStack,
  Divider
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon, SettingsIcon } from '@chakra-ui/icons';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

// 可用的小部件类型
export const AVAILABLE_WIDGETS = [
  { id: 'device-status', name: '设备状态', defaultSize: 'md', description: '显示所有设备的当前状态概览' },
  { id: 'recent-inspections', name: '最近巡检', defaultSize: 'lg', description: '显示最近执行的巡检任务及结果' },
  { id: 'alerts', name: '告警信息', defaultSize: 'md', description: '显示当前活跃的告警信息' },
  { id: 'inspection-stats', name: '巡检统计', defaultSize: 'sm', description: '显示巡检执行统计数据' },
  { id: 'device-health', name: '设备健康', defaultSize: 'md', description: '显示设备健康评分' },
  { id: 'maintenance-calendar', name: '维护日历', defaultSize: 'lg', description: '显示即将到来的维护任务' },
];

// 小部件配置类型
interface WidgetConfig {
  id: string;
  position: number;
  size: 'sm' | 'md' | 'lg';
  settings?: Record<string, any>;
}

// 小部件选择模态框
interface WidgetSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddWidget: (widgetType: string, size: 'sm' | 'md' | 'lg') => void;
}

const WidgetSelectionModal: React.FC<WidgetSelectionModalProps> = ({ isOpen, onClose, onAddWidget }) => {
  const [selectedWidget, setSelectedWidget] = useState<string>('');
  const [selectedSize, setSelectedSize] = useState<'sm' | 'md' | 'lg'>('md');
  
  const handleAdd = () => {
    if (selectedWidget) {
      onAddWidget(selectedWidget, selectedSize);
      onClose();
      setSelectedWidget('');
      setSelectedSize('md');
    }
  };
  
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>添加小部件</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Box>
              <Text fontWeight="bold" mb={2}>选择小部件类型</Text>
              <Select 
                placeholder="选择小部件" 
                value={selectedWidget}
                onChange={(e) => setSelectedWidget(e.target.value)}
              >
                {AVAILABLE_WIDGETS.map(widget => (
                  <option key={widget.id} value={widget.id}>{widget.name}</option>
                ))}
              </Select>
            </Box>
            
            {selectedWidget && (
              <Box>
                <Text fontWeight="bold" mb={2}>小部件描述</Text>
                <Text>{AVAILABLE_WIDGETS.find(w => w.id === selectedWidget)?.description}</Text>
              </Box>
            )}
            
            <Box>
              <Text fontWeight="bold" mb={2}>选择大小</Text>
              <Select 
                value={selectedSize}
                onChange={(e) => setSelectedSize(e.target.value as 'sm' | 'md' | 'lg')}
              >
                <option value="sm">小</option>
                <option value="md">中</option>
                <option value="lg">大</option>
              </Select>
            </Box>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>取消</Button>
          <Button colorScheme="teal" onClick={handleAdd} isDisabled={!selectedWidget}>添加</Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// 主仪表盘组件
const CustomDashboard: React.FC = () => {
  const [widgets, setWidgets] = useState<WidgetConfig[]>([]);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  // 从本地存储加载用户的仪表盘配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('dashboardConfig');
    if (savedConfig) {
      setWidgets(JSON.parse(savedConfig));
    } else {
      // 默认配置
      setWidgets([
        { id: 'device-status', position: 0, size: 'md' },
        { id: 'recent-inspections', position: 1, size: 'lg' },
      ]);
    }
  }, []);
  
  // 保存配置到本地存储
  const saveConfig = (newWidgets: WidgetConfig[]) => {
    setWidgets(newWidgets);
    localStorage.setItem('dashboardConfig', JSON.stringify(newWidgets));
  };
  
  // 处理拖放重新排序
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const items = Array.from(widgets);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    // 更新位置索引
    const updatedItems = items.map((item, index) => ({
      ...item,
      position: index
    }));
    
    saveConfig(updatedItems);
  };
  
  // 添加新小部件
  const handleAddWidget = (widgetType: string, size: 'sm' | 'md' | 'lg') => {
    const newWidget = {
      id: widgetType,
      position: widgets.length,
      size: size
    };
    saveConfig([...widgets, newWidget]);
  };
  
  // 删除小部件
  const handleRemoveWidget = (index: number) => {
    const newWidgets = widgets.filter((_, i) => i !== index);
    // 更新位置索引
    const updatedWidgets = newWidgets.map((item, index) => ({
      ...item,
      position: index
    }));
    saveConfig(updatedWidgets);
  };
  
  return (
    <Box>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">仪表盘</Heading>
        <Button leftIcon={<AddIcon />} colorScheme="teal" onClick={onOpen}>添加小部件</Button>
      </Flex>
      
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="dashboard-widgets">
          {(provided) => (
            <SimpleGrid 
              columns={{ base: 1, md: 2, lg: 3 }} 
              spacing={4}
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {widgets.sort((a, b) => a.position - b.position).map((widget, index) => (
                <Draggable key={widget.id + index} draggableId={widget.id + index} index={index}>
                  {(provided, snapshot) => (
                    <Box
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      bg={snapshot.isDragging ? "teal.50" : bgColor}
                      p={4}
                      borderRadius="md"
                      boxShadow="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      height={widget.size === 'sm' ? "200px" : widget.size === 'md' ? "300px" : "400px"}
                      gridColumn={widget.size === 'lg' ? { base: "auto", lg: "span 2" } : "auto"}
                    >
                      <Flex justify="space-between" align="center" mb={3} {...provided.dragHandleProps}>
                        <Heading size="sm">{AVAILABLE_WIDGETS.find(w => w.id === widget.id)?.name || widget.id}</Heading>
                        <HStack>
                          <IconButton 
                            aria-label="设置" 
                            icon={<SettingsIcon />} 
                            size="sm" 
                            variant="ghost" 
                          />
                          <IconButton 
                            aria-label="删除" 
                            icon={<DeleteIcon />} 
                            size="sm" 
                            colorScheme="red" 
                            variant="ghost" 
                            onClick={() => handleRemoveWidget(index)}
                          />
                        </HStack>
                      </Flex>
                      <Divider mb={3} />
                      
                      {/* 根据widget.id渲染相应的小部件内容 */}
                      <Box overflow="auto" h="calc(100% - 50px)">
                        {widget.id === 'device-status' && <Text>设备状态小部件内容</Text>}
                        {widget.id === 'recent-inspections' && <Text>最近巡检小部件内容</Text>}
                        {widget.id === 'alerts' && <Text>告警信息小部件内容</Text>}
                        {widget.id === 'inspection-stats' && <Text>巡检统计小部件内容</Text>}
                        {widget.id === 'device-health' && <Text>设备健康小部件内容</Text>}
                        {widget.id === 'maintenance-calendar' && <Text>维护日历小部件内容</Text>}
                      </Box>
                    </Box>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </SimpleGrid>
          )}
        </Droppable>
      </DragDropContext>
      
      <WidgetSelectionModal 
        isOpen={isOpen} 
        onClose={onClose} 
        onAddWidget={handleAddWidget} 
      />
    </Box>
  );
};

export default CustomDashboard;
