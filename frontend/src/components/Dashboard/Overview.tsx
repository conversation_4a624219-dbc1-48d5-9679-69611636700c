import { Box, Grid, Text, Flex, Icon, useColorMode, Table, Thead, Tbody, Tr, Td, Link } from '@chakra-ui/react'
import { CircularProgress, CircularProgressLabel } from '@chakra-ui/react'
import { useTranslation } from '@/contexts/LanguageContext'
import { CheckCircle, Warning, ClipboardText, CaretRight } from '@phosphor-icons/react'

interface StatCardProps {
  title: string
  value: number
  icon: React.ElementType
  iconBgColor: string
  iconColor?: string
}

const StatCard = ({ title, value, icon, iconBgColor, iconColor = 'white' }: StatCardProps) => {
  const { colorMode } = useColorMode()
  
  return (
    <Box
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="lg"
      p={8}
      boxShadow="sm"
      border="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
    >
      <Flex justify="space-between" align="center">
        <Box>
          <Text color={colorMode === 'dark' ? 'gray.400' : 'gray.600'} fontSize="sm" mb={3}>
            {title}
          </Text>
          <Text fontSize="2xl" fontWeight="bold">
            {value}
          </Text>
        </Box>
        <Box
          bg={iconBgColor}
          p={4}
          borderRadius="lg"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <Icon as={icon} fontSize="24px" color={iconColor} />
        </Box>
      </Flex>
    </Box>
  )
}

interface ServiceStatusProps {
  title: string
  total: number
  running: number
  status: 'normal' | 'warning'
}

const ServiceStatus = ({ title, total, running, status }: ServiceStatusProps) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const percentage = Math.round((running / total) * 100)
  
  return (
    <Box
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="lg"
      p={8}
      boxShadow="sm"
      border="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
    >
      <Text color={colorMode === 'dark' ? 'gray.400' : 'gray.600'} fontSize="sm" mb={5}>
        {title}
      </Text>
      <Flex justify="center" align="center">
        <CircularProgress
          value={percentage}
          size="120px"
          thickness="8px"
          color={status === 'normal' ? 'green.400' : 'orange.400'}
          trackColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
        >
          <CircularProgressLabel fontSize="xl" fontWeight="bold">
            {percentage}%
          </CircularProgressLabel>
        </CircularProgress>
      </Flex>
      <Text
        mt={5}
        textAlign="center"
        color={status === 'normal' ? 'green.400' : 'orange.400'}
        fontSize="sm"
      >
        {running} {status === 'normal' ? t('status.healthy') : t('status.running')} / {total} {t('dashboard.total')}
      </Text>
    </Box>
  )
}

interface ActivityTableProps {
  title: string
  data: Array<{
    name: string
    status: string
    tags: string[]
  }>
  viewAllLink: string
}

const ActivityTable = ({ title, data, viewAllLink }: ActivityTableProps) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  
  return (
    <Box
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="lg"
      border="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
      overflow="hidden"
    >
      <Flex 
        justify="space-between" 
        align="center" 
        px={8} 
        py={5} 
        borderBottom="1px" 
        borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
      >
        <Text fontSize="sm" fontWeight="medium">{title}</Text>
        <Link 
          href={viewAllLink}
          fontSize="sm"
          color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}
          _hover={{ color: 'komodo.green' }}
          display="flex"
          alignItems="center"
        >
          {t('dashboard.view.all')}
          <Icon as={CaretRight} ml={2} />
        </Link>
      </Flex>
      <Box>
        <Table variant="simple" size="sm">
          <Thead display="none">
            <Tr>
              <Td>{t('table.name')}</Td>
              <Td>{t('table.status')}</Td>
              <Td>{t('table.tags')}</Td>
            </Tr>
          </Thead>
          <Tbody>
            {data.map((item, index) => (
              <Tr key={index}>
                <Td py={4} px={6}>{item.name}</Td>
                <Td py={4}>
                  <Text
                    color={item.status === t('status.running') ? 'green.500' : 
                          item.status === t('status.failed') ? 'red.500' : 'orange.500'}
                    fontSize="sm"
                  >
                    {item.status}
                  </Text>
                </Td>
                <Td py={4}>
                  <Flex gap={3}>
                    {item.tags.map((tag, tagIndex) => (
                      <Box
                        key={tagIndex}
                        px={3}
                        py={1}
                        borderRadius="full"
                        bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                        fontSize="xs"
                      >
                        {tag}
                      </Box>
                    ))}
                  </Flex>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    </Box>
  )
}

// 1. 添加分机数据
const extensionData = [
  {
    cabinet: '分机柜1',
    extensions: [107, 108, 114, 116, 118, 124, 125, 126, 134, 138, 140, 141, 142, 143, 144, 148, 154, 156, 157, 159, 160, 161, 169, 170, 172, 173, 174, 175, 176, 185]
  },
  {
    cabinet: '分机柜2',
    extensions: [186, 187, 188, 189, 190, 191, 192, 201, 202, 203, 204, 205, 206, 207, 209, 210, 211, 212, 213, 214, 215, 216, 217, 219, 221, 267, 268, 269, 279, 280]
  },
  {
    cabinet: '分机柜3',
    extensions: [281, 289, 290, 291, 292, 303, 304, 305, 306, 307, 308, 311, 316, 317, 318, 319, 320, 321, 323, 325, 327, 329, 330, 334, 335, 336, 337, 338, 339, 341]
  },
  {
    cabinet: '分机柜4',
    extensions: [342, 343, 344, 347, 348, 349, 350, 351, 352, 355, 357, 359, 361, 364, 368, 369, 371, 373, 374, 375, 376, 377, 378, 379, 380, 383, 388, 389, 390, 391]
  },
  {
    cabinet: '分机柜5',
    extensions: [392, 398, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648]
  },
  {
    cabinet: '分机柜6',
    extensions: [649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678]
  },
  {
    cabinet: '分机柜7',
    extensions: [679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 803, 804, 810, 814, 821, 827, 828, 847, 848]
  },
  {
    cabinet: '分机柜8',
    extensions: [853, 879, 880, 881, 882, 884, 888, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722]
  },
  {
    cabinet: '分机柜9',
    extensions: [722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 746, 747, 748, 749, 750, 751]
  }
]

// 2. 分机表格组件
const ExtensionTable = () => (
  <Box overflowX="auto" mt={10}>
    <Text fontSize="xl" fontWeight="bold" mb={4}>电话分机管理</Text>
    <Table variant="striped" size="sm">
      <Thead>
        <Tr>
          <Td>分机柜</Td>
          {Array.from({ length: 30 }, (_, i) => (
            <Td key={i}>分机{i + 1}</Td>
          ))}
        </Tr>
      </Thead>
      <Tbody>
        {extensionData.map((row, idx) => (
          <Tr key={row.cabinet}>
            <Td>{row.cabinet}</Td>
            {row.extensions.map((ext, i) => (
              <Td key={i}>{ext}</Td>
            ))}
            {/* 若不足30个分机号，补空单元格 */}
            {row.extensions.length < 30 && Array.from({ length: 30 - row.extensions.length }).map((_, i) => <Td key={`empty-${i}`}></Td>)}
          </Tr>
        ))}
      </Tbody>
    </Table>
  </Box>
)

export const Overview = () => {
  const { t } = useTranslation()
  const { colorMode } = useColorMode()
  
  const recentServers = [
    { name: 'demo-server', status: t('status.running'), tags: [t('tag.demo'), 'ranshi'] },
  ]
  
  const recentDeployments = [
    { name: 'hello-world', status: t('status.failed'), tags: ['hello world'] },
    { name: 'grafana-ui', status: t('status.running'), tags: [t('tag.logging'), 'ui'] },
  ]
  
  return (
    <Box p={10}>
      <Grid 
        templateColumns={{ 
          base: '1fr', 
          sm: 'repeat(2, 1fr)', 
          lg: 'repeat(4, 1fr)' 
        }} 
        gap={10} 
        mb={12}
      >
        <StatCard
          title={t('dashboard.total.devices')}
          value={128}
          icon={ClipboardText}
          iconBgColor="blue.50"
          iconColor="blue.500"
        />
        <StatCard
          title={t('dashboard.normal.running')}
          value={98}
          icon={CheckCircle}
          iconBgColor="green.50"
          iconColor="green.500"
        />
        <StatCard
          title={t('dashboard.fault.devices')}
          value={12}
          icon={Warning}
          iconBgColor="red.50"
          iconColor="red.500"
        />
        <StatCard
          title={t('dashboard.pending.tickets')}
          value={18}
          icon={ClipboardText}
          iconBgColor="orange.50"
          iconColor="orange.500"
        />
      </Grid>

      <Grid 
        templateColumns={{ 
          base: '1fr', 
          md: 'repeat(2, 1fr)', 
          xl: 'repeat(3, 1fr)' 
        }} 
        gap={10} 
        mb={12}
      >
        <ServiceStatus title={t('service.servers')} total={1} running={1} status="normal" />
        <ServiceStatus title={t('service.stacks')} total={4} running={4} status="normal" />
        <ServiceStatus title={t('service.deployments')} total={3} running={2} status="warning" />
      </Grid>

      <Text
        fontSize="sm"
        fontWeight="medium"
        color="gray.500"
        textTransform="uppercase"
        mb={8}
      >
        {t('dashboard.recent.activities')}
      </Text>

      <Grid 
        templateColumns={{ 
          base: '1fr', 
          lg: 'repeat(2, 1fr)' 
        }} 
        gap={10}
      >
        <ActivityTable
          title={t('recent.servers')}
          data={recentServers}
          viewAllLink="/servers"
        />
        <ActivityTable
          title={t('recent.deployments')}
          data={recentDeployments}
          viewAllLink="/deployments"
        />
      </Grid>

      {/* 在页面底部插入分机表格 */}
      <ExtensionTable />
    </Box>
  )
}

export default Overview 