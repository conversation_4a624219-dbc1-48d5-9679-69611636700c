import React, { useState, useEffect } from 'react'
import {
  Box,
  <PERSON>ing,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Grid,
  GridItem,
  Text,
  Divider,
  Badge,
  useColorModeValue,
  Card,
  CardBody,
  Flex
} from '@chakra-ui/react'
import { useTranslation } from 'react-i18next'
import PortList, { Port } from './PortList'

export interface Device {
  id: string
  name: string
  model: string
  serialNumber: string
  ipAddress: string
  location: string
  status: 'online' | 'offline' | 'maintenance'
  lastSeen: string
  firmware: string
  ports: Port[]
}

interface DeviceDetailProps {
  device: Device | null
  isLoading: boolean
}

const DeviceDetail: React.FC<DeviceDetailProps> = ({ device, isLoading }) => {
  const { t } = useTranslation()
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const bgColor = useColorModeValue('white', 'gray.800')
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green'
      case 'offline':
        return 'red'
      case 'maintenance':
        return 'orange'
      default:
        return 'gray'
    }
  }

  if (isLoading) {
    return (
      <Box p={4}>
        <Text>{t('loading')}</Text>
      </Box>
    )
  }

  if (!device) {
    return (
      <Box p={4} textAlign="center">
        <Text>{t('no_device_selected')}</Text>
      </Box>
    )
  }

  return (
    <Box borderWidth="1px" borderRadius="lg" borderColor={borderColor} bg={bgColor} p={4}>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="lg">{device.name}</Heading>
        <Badge colorScheme={getStatusColor(device.status)} fontSize="0.8em" p={1}>
          {t(device.status)}
        </Badge>
      </Flex>

      <Divider mb={6} />

      <Tabs variant="soft-rounded" colorScheme="blue" isLazy>
        <TabList mb={4}>
          <Tab>{t('overview')}</Tab>
          <Tab>{t('ports')}</Tab>
        </TabList>
        
        <TabPanels>
          <TabPanel>
            <Card mb={4}>
              <CardBody>
                <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={4}>
                  <GridItem>
                    <Box mb={3}>
                      <Text fontWeight="bold">{t('model')}</Text>
                      <Text>{device.model}</Text>
                    </Box>
                    <Box mb={3}>
                      <Text fontWeight="bold">{t('serial_number')}</Text>
                      <Text>{device.serialNumber}</Text>
                    </Box>
                    <Box mb={3}>
                      <Text fontWeight="bold">{t('ip_address')}</Text>
                      <Text>{device.ipAddress}</Text>
                    </Box>
                  </GridItem>
                  <GridItem>
                    <Box mb={3}>
                      <Text fontWeight="bold">{t('location')}</Text>
                      <Text>{device.location}</Text>
                    </Box>
                    <Box mb={3}>
                      <Text fontWeight="bold">{t('last_seen')}</Text>
                      <Text>{device.lastSeen}</Text>
                    </Box>
                    <Box mb={3}>
                      <Text fontWeight="bold">{t('firmware')}</Text>
                      <Text>{device.firmware}</Text>
                    </Box>
                  </GridItem>
                </Grid>
              </CardBody>
            </Card>
          </TabPanel>
          
          <TabPanel>
            <PortList ports={device.ports} isLoading={isLoading} />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  )
}

export default DeviceDetail 