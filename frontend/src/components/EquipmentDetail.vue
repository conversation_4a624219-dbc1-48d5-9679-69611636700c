<template>
  <div class="equipment-detail">
    <div class="header flex items-center justify-between mb-6">
      <div class="flex items-center">
        <h3 class="text-xl font-semibold mr-4">{{ equipment.name }}</h3>
        <el-tag :type="getStatusTagType(equipment.status)" size="medium">
          {{ equipment.status }}
        </el-tag>
      </div>
      <div>
        <el-button size="small" type="success" @click="$emit('showQRCode')">
          <i class="fas fa-qrcode mr-1"></i> 查看二维码
        </el-button>
      </div>
    </div>
    
    <el-tabs v-model="activeTab" class="detail-tabs">
      <el-tab-pane label="基本信息" name="basic">
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center">
              <i class="fas fa-info-circle mr-2 text-primary"></i>
              <span>设备基本信息</span>
            </div>
          </template>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="info-section">
              <div class="info-row">
                <span class="label">设备编号：</span>
                <span class="value">{{ equipment.code || '暂无' }}</span>
              </div>
              <div class="info-row">
                <span class="label">设备类型：</span>
                <span class="value">{{ equipment.type || '暂无' }}</span>
              </div>
              <div class="info-row">
                <span class="label">规格型号：</span>
                <span class="value">{{ equipment.model || '暂无' }}</span>
              </div>
              <div class="info-row">
                <span class="label">所属部门：</span>
                <span class="value">{{ equipment.department || '暂无' }}</span>
              </div>
            </div>
            
            <div class="info-section">
              <div class="info-row">
                <span class="label">负责人：</span>
                <span class="value">{{ equipment.owner || '暂无' }}</span>
              </div>
              <div class="info-row">
                <span class="label">启用时间：</span>
                <span class="value">{{ equipment.startDate || '暂无' }}</span>
              </div>
              <div class="info-row">
                <span class="label">设备状态：</span>
                <span class="value">
                  <el-tag :type="getStatusTagType(equipment.status)" size="small">
                    {{ equipment.status || '暂无' }}
                  </el-tag>
                </span>
              </div>
              <div class="info-row">
                <span class="label">最后检修：</span>
                <span class="value">{{ equipment.lastMaintenance || '暂无记录' }}</span>
              </div>
            </div>
          </div>
          
          <div class="mt-6">
            <div class="label mb-2">设备描述：</div>
            <div class="bg-gray-50 p-3 rounded">
              {{ equipment.description || '暂无描述' }}
            </div>
          </div>
        </el-card>
        
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center">
              <i class="fas fa-cogs mr-2 text-primary"></i>
              <span>设备参数</span>
            </div>
          </template>
          
          <el-table :data="equipment.parameters || []" border style="width: 100%" stripe>
            <el-table-column prop="name" label="参数名称" width="180" />
            <el-table-column prop="value" label="参数值" />
            <el-table-column prop="unit" label="单位" width="100" />
          </el-table>
          
          <div v-if="!equipment.parameters || equipment.parameters.length === 0" class="text-center py-6 text-gray-500">
            暂无参数数据
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="3D模型" name="model">
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center">
              <i class="fas fa-cube mr-2 text-primary"></i>
              <span>设备3D模型</span>
            </div>
          </template>
          
          <div class="model-container">
            <div id="model-viewer" ref="modelViewer" class="h-96 bg-gray-100 flex items-center justify-center">
              <div v-if="!hasModel" class="text-center text-gray-500">
                <i class="fas fa-cube text-5xl mb-4"></i>
                <div>暂无3D模型</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="运行数据" name="data">
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <i class="fas fa-chart-line mr-2 text-primary"></i>
                <span>设备运行数据</span>
              </div>
              <el-radio-group v-model="dataTimeRange" size="small">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          
          <div class="chart-container">
            <div id="data-chart" ref="dataChartRef" style="width: 100%; height: 400px;"></div>
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="维修记录" name="maintenance">
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <i class="fas fa-tools mr-2 text-primary"></i>
                <span>维修记录</span>
              </div>
              <el-button size="small" type="primary">
                <i class="fas fa-plus mr-1"></i> 新增记录
              </el-button>
            </div>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in maintenanceRecords"
              :key="index"
              :timestamp="activity.time"
              :type="activity.type"
            >
              <el-card class="mb-2">
                <h4>{{ activity.title }}</h4>
                <p>{{ activity.content }}</p>
                <div class="text-gray-500 text-sm mt-2">
                  处理人: {{ activity.operator }}
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          
          <div v-if="maintenanceRecords.length === 0" class="text-center py-6 text-gray-500">
            暂无维修记录
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

export default {
  props: {
    equipment: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'basic',
      dataTimeRange: 'day',
      hasModel: false,
      maintenanceRecords: [
        {
          time: '2023-12-15 14:30',
          title: '定期维护保养',
          content: '更换滤芯，清洁设备表面，检查各连接部件',
          operator: '张工',
          type: 'success'
        },
        {
          time: '2023-11-02 09:15',
          title: '故障维修',
          content: '电机异响，更换轴承并重新调整',
          operator: '李工',
          type: 'danger'
        },
        {
          time: '2023-09-20 16:45',
          title: '软件升级',
          content: '升级控制系统固件至v2.3.5版本',
          operator: '王工',
          type: 'primary'
        }
      ],
      chart: null,
      renderer: null,
      scene: null,
      camera: null,
      controls: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.activeTab === 'data') {
        this.initChart();
      } else if (this.activeTab === 'model') {
        this.init3DModel();
      }
    });
  },
  methods: {
    getStatusTagType(status) {
      const map = {
        '正常': 'success',
        '故障': 'danger',
        '待检': 'warning',
        '处理中': 'primary'
      }
      return map[status] || ''
    },
    initChart() {
      if (this.chart) {
        this.chart.dispose();
      }
      
      this.chart = echarts.init(this.$refs.dataChartRef);
      
      // 模拟数据
      const generateData = (days, baseValue = 100, variance = 20) => {
        const data = [];
        let now = new Date();
        let value = baseValue;
        
        for (let i = 0; i < days; i++) {
          const date = new Date(now.getTime() - (days - i) * 24 * 3600 * 1000);
          value = value + Math.random() * variance * 2 - variance;
          data.push([date.toLocaleDateString(), Math.round(value)]);
        }
        
        return data;
      };
      
      let days = 7;
      if (this.dataTimeRange === 'day') {
        days = 1;
      } else if (this.dataTimeRange === 'month') {
        days = 30;
      }
      
      const temperatureData = generateData(days, 65, 5);
      const pressureData = generateData(days, 120, 10);
      const speedData = generateData(days, 1800, 200);
      
      const option = {
        title: {
          text: '设备运行参数趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['温度(°C)', '压力(kPa)', '转速(rpm)'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: temperatureData.map(item => item[0])
        },
        yAxis: [
          {
            type: 'value',
            name: '温度/压力',
            position: 'left'
          },
          {
            type: 'value',
            name: '转速',
            position: 'right'
          }
        ],
        series: [
          {
            name: '温度(°C)',
            type: 'line',
            data: temperatureData.map(item => item[1]),
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#FF9800'
            }
          },
          {
            name: '压力(kPa)',
            type: 'line',
            data: pressureData.map(item => item[1]),
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#2196F3'
            }
          },
          {
            name: '转速(rpm)',
            type: 'line',
            yAxisIndex: 1,
            data: speedData.map(item => item[1]),
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#4CAF50'
            }
          }
        ]
      };
      
      this.chart.setOption(option);
      window.addEventListener('resize', this.resizeChart);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    init3DModel() {
      // 检查是否有模型数据
      if (!this.equipment.modelUrl) {
        this.hasModel = false;
        return;
      }
      
      this.hasModel = true;
      const container = this.$refs.modelViewer;
      
      // 创建场景
      this.scene = new THREE.Scene();
      this.scene.background = new THREE.Color(0xf0f0f0);
      
      // 创建相机
      this.camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
      this.camera.position.z = 5;
      
      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true });
      this.renderer.setSize(container.clientWidth, container.clientHeight);
      container.innerHTML = '';
      container.appendChild(this.renderer.domElement);
      
      // 添加控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      
      // 添加灯光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      this.scene.add(ambientLight);
      
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(1, 1, 1);
      this.scene.add(directionalLight);
      
      // 添加示例几何体（实际项目中应该加载模型文件）
      const geometry = new THREE.BoxGeometry(2, 2, 2);
      const material = new THREE.MeshStandardMaterial({ color: 0x3080e8 });
      const cube = new THREE.Mesh(geometry, material);
      this.scene.add(cube);
      
      // 动画循环
      const animate = () => {
        requestAnimationFrame(animate);
        cube.rotation.x += 0.01;
        cube.rotation.y += 0.01;
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
      };
      
      animate();
      
      // 响应窗口大小变化
      window.addEventListener('resize', this.resize3DView);
    },
    resize3DView() {
      if (!this.renderer || !this.camera) return;
      
      const container = this.$refs.modelViewer;
      this.camera.aspect = container.clientWidth / container.clientHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(container.clientWidth, container.clientHeight);
    }
  },
  watch: {
    activeTab(newVal) {
      this.$nextTick(() => {
        if (newVal === 'data') {
          this.initChart();
        } else if (newVal === 'model') {
          this.init3DModel();
        }
      });
    },
    dataTimeRange() {
      if (this.activeTab === 'data') {
        this.initChart();
      }
    }
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizeChart);
    window.removeEventListener('resize', this.resize3DView);
    
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    
    if (this.renderer) {
      this.renderer.dispose();
      this.scene = null;
      this.camera = null;
      this.controls = null;
      this.renderer = null;
    }
  }
}
</script>

<style scoped>
.equipment-detail {
  padding: 20px;
}
.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.header h3 {
  margin-right: 20px;
  margin-bottom: 0;
}
.info-section {
  margin-bottom: 30px;
}
.info-row {
  margin-bottom: 15px;
  font-size: 14px;
}
.label {
  display: inline-block;
  width: 100px;
  color: #666;
}
.value {
  color: #333;
}
.parameters {
  margin-top: 30px;
}
.model-container {
  width: 100%;
  height: 500px;
  background-color: #f5f5f5;
}
</style>