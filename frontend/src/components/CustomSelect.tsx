import React from 'react';
import { Box, SelectProps, useColorModeValue, StyleProps } from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';

/**
 * 自定义 Select 组件，解决下拉图标重复问题
 *
 * 这个组件使用原生 select 元素和自定义样式，完全避免 Chakra UI Select 组件
 * 可能出现的图标重复问题。
 */
const CustomSelect: React.FC<SelectProps & { containerStyle?: StyleProps }> = (props) => {
  const {
    children,
    placeholder,
    value,
    onChange,
    maxW,
    size = "md",
    isDisabled,
    containerStyle,
    sx,
    ...rest
  } = props;

  // 根据尺寸设置高度
  const heights = {
    sm: "32px",
    md: "40px",
    lg: "48px"
  };

  // 根据尺寸设置字体大小
  const fontSizes = {
    sm: "sm",
    md: "md",
    lg: "lg"
  };

  // 根据尺寸设置内边距
  const paddings = {
    sm: "0 28px 0 12px",
    md: "0 36px 0 16px",
    lg: "0 44px 0 20px"
  };

  // 处理选项内边距
  const optionPadding = sx && sx['& > option'] ? sx['& > option'] : {};

  return (
    <Box
      position="relative"
      maxW={maxW}
      width="100%"
      {...containerStyle}
    >
      <Box
        as="select"
        width="100%"
        height={heights[size]}
        padding={paddings[size]}
        fontSize={fontSizes[size]}
        borderRadius="md"
        borderWidth="1px"
        borderColor={useColorModeValue('gray.200', 'gray.600')}
        bg={useColorModeValue('white', 'gray.700')}
        color={useColorModeValue('gray.800', 'white')}
        outline="none"
        transition="all 0.2s"
        opacity={isDisabled ? 0.6 : 1}
        cursor={isDisabled ? "not-allowed" : "pointer"}
        _hover={{
          borderColor: useColorModeValue('gray.300', 'gray.500')
        }}
        _focus={{
          borderColor: 'blue.500',
          boxShadow: '0 0 0 1px var(--chakra-colors-blue-500)'
        }}
        value={value}
        onChange={onChange}
        disabled={isDisabled}
        css={{
          // 完全隐藏浏览器默认下拉图标
          WebkitAppearance: 'none',
          MozAppearance: 'none',
          appearance: 'none',
          '&::-ms-expand': {
            display: 'none'
          }
        }}
        {...rest}
      >
        {placeholder && <option value="">{placeholder}</option>}
        {children}
      </Box>
      <Box
        position="absolute"
        right={size === "sm" ? "8px" : size === "md" ? "12px" : "16px"}
        top="50%"
        transform="translateY(-50%)"
        pointerEvents="none"
        color={useColorModeValue('gray.500', 'gray.400')}
        zIndex={1}
      >
        <ChevronDownIcon boxSize={size === "sm" ? "16px" : size === "md" ? "20px" : "24px"} />
      </Box>
    </Box>
  );
};

export default CustomSelect;
