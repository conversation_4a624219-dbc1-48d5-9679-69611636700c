import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Spinner,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  List,
  ListItem,
  ListIcon,
  Badge,
  Flex,
  Button,
  useColorModeValue,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Progress,
  Icon,
  Tooltip
} from '@chakra-ui/react';
import { CheckCircleIcon, WarningIcon, InfoIcon, RepeatIcon } from '@chakra-ui/icons';
import { getEnhancedAnalysis, EnhancedAnalysisResult } from '../../services/aiService';

interface AIAnalysisPanelProps {
  inspectionResults: any;
  basicAnalysis?: string;
}

const AIAnalysisPanel: React.FC<AIAnalysisPanelProps> = ({ inspectionResults, basicAnalysis }) => {
  const [loading, setLoading] = useState(false);
  const [analysis, setAnalysis] = useState<EnhancedAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const accentColor = useColorModeValue('teal.500', 'teal.300');
  
  // 计算巡检通过率
  const calculatePassRate = () => {
    if (!inspectionResults?.results?.length) return 0;
    
    const passedItems = inspectionResults.results.filter((item: any) => item.passed).length;
    return Math.round((passedItems / inspectionResults.results.length) * 100);
  };
  
  const passRate = calculatePassRate();
  
  // 获取增强分析
  const fetchEnhancedAnalysis = async () => {
    if (!inspectionResults?.results?.length) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await getEnhancedAnalysis(inspectionResults);
      setAnalysis(result);
    } catch (err) {
      setError('获取AI增强分析失败');
      console.error('AI分析错误:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // 组件加载时获取分析
  useEffect(() => {
    if (inspectionResults?.results?.length) {
      fetchEnhancedAnalysis();
    }
  }, [inspectionResults]);
  
  // 如果没有巡检结果，显示空状态
  if (!inspectionResults?.results?.length) {
    return (
      <Box p={5} bg={bgColor} borderRadius="md" boxShadow="sm" borderWidth="1px" borderColor={borderColor}>
        <Heading size="md" mb={4}>AI分析</Heading>
        <Text color="gray.500">没有可用的巡检结果进行分析</Text>
      </Box>
    );
  }
  
  return (
    <Box p={5} bg={bgColor} borderRadius="md" boxShadow="sm" borderWidth="1px" borderColor={borderColor}>
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">AI增强分析</Heading>
        <Button
          leftIcon={<RepeatIcon />}
          size="sm"
          colorScheme="teal"
          variant="outline"
          onClick={fetchEnhancedAnalysis}
          isLoading={loading}
        >
          刷新分析
        </Button>
      </Flex>
      
      {/* 基础统计信息 */}
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} mb={6}>
        <Stat bg="gray.50" p={3} borderRadius="md" boxShadow="sm">
          <StatLabel>通过率</StatLabel>
          <StatNumber>{passRate}%</StatNumber>
          <Progress 
            value={passRate} 
            colorScheme={passRate > 80 ? "green" : passRate > 60 ? "yellow" : "red"} 
            size="sm" 
            mt={2} 
          />
        </Stat>
        
        <Stat bg="gray.50" p={3} borderRadius="md" boxShadow="sm">
          <StatLabel>检查项数</StatLabel>
          <StatNumber>{inspectionResults.results.length}</StatNumber>
          <StatHelpText>
            通过: {inspectionResults.results.filter((item: any) => item.passed).length} | 
            失败: {inspectionResults.results.filter((item: any) => !item.passed).length}
          </StatHelpText>
        </Stat>
        
        <Stat bg="gray.50" p={3} borderRadius="md" boxShadow="sm">
          <StatLabel>自动修复</StatLabel>
          <StatNumber>
            {inspectionResults.results.filter((item: any) => item.fixed).length}
          </StatNumber>
          <StatHelpText>
            已修复 {Math.round((inspectionResults.results.filter((item: any) => item.fixed).length / 
              inspectionResults.results.filter((item: any) => !item.passed).length) * 100) || 0}% 的问题
          </StatHelpText>
        </Stat>
      </SimpleGrid>
      
      {/* 加载状态 */}
      {loading && (
        <Flex direction="column" align="center" justify="center" py={10}>
          <Spinner size="xl" color={accentColor} mb={4} />
          <Text>正在进行AI增强分析...</Text>
        </Flex>
      )}
      
      {/* 错误状态 */}
      {error && !loading && (
        <Box p={4} bg="red.50" borderRadius="md" mb={4}>
          <Flex align="center">
            <WarningIcon color="red.500" mr={2} />
            <Text color="red.500">{error}</Text>
          </Flex>
          <Text mt={2} fontSize="sm">已显示基础分析结果</Text>
        </Box>
      )}
      
      {/* 基础分析 */}
      {basicAnalysis && !analysis && !loading && (
        <Box p={4} bg="blue.50" borderRadius="md" mb={4}>
          <Heading size="sm" mb={2}>基础分析</Heading>
          <Text>{basicAnalysis}</Text>
        </Box>
      )}
      
      {/* 增强分析结果 */}
      {analysis && !loading && (
        <>
          {/* 摘要 */}
          <Box p={4} bg="blue.50" borderRadius="md" mb={4}>
            <Heading size="sm" mb={2}>分析摘要</Heading>
            <Text>{analysis.summary}</Text>
          </Box>
          
          <Divider my={4} />
          
          {/* 根本原因 */}
          <Accordion allowMultiple defaultIndex={[0]} mb={4}>
            <AccordionItem border="none" bg="gray.50" borderRadius="md" mb={2}>
              <h2>
                <AccordionButton>
                  <Box flex="1" textAlign="left" fontWeight="bold">
                    <Flex align="center">
                      <Icon as={InfoIcon} mr={2} color="orange.500" />
                      根本原因分析
                    </Flex>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel pb={4}>
                <List spacing={2}>
                  {analysis.rootCauses.map((cause, index) => (
                    <ListItem key={index}>
                      <ListIcon as={WarningIcon} color="orange.500" />
                      {cause}
                    </ListItem>
                  ))}
                </List>
              </AccordionPanel>
            </AccordionItem>
            
            {/* 建议 */}
            <AccordionItem border="none" bg="gray.50" borderRadius="md" mb={2}>
              <h2>
                <AccordionButton>
                  <Box flex="1" textAlign="left" fontWeight="bold">
                    <Flex align="center">
                      <Icon as={CheckCircleIcon} mr={2} color="green.500" />
                      修复建议
                    </Flex>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel pb={4}>
                <List spacing={2}>
                  {analysis.recommendations.map((rec, index) => (
                    <ListItem key={index}>
                      <ListIcon as={CheckCircleIcon} color="green.500" />
                      {rec}
                    </ListItem>
                  ))}
                </List>
              </AccordionPanel>
            </AccordionItem>
            
            {/* 相关问题 */}
            {analysis.relatedIssues.length > 0 && (
              <AccordionItem border="none" bg="gray.50" borderRadius="md">
                <h2>
                  <AccordionButton>
                    <Box flex="1" textAlign="left" fontWeight="bold">
                      <Flex align="center">
                        <Icon as={InfoIcon} mr={2} color="blue.500" />
                        相关历史问题
                      </Flex>
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                </h2>
                <AccordionPanel pb={4}>
                  <List spacing={3}>
                    {analysis.relatedIssues.map((issue) => (
                      <ListItem key={issue.id} p={2} bg="white" borderRadius="md" boxShadow="sm">
                        <Flex justify="space-between" align="center" mb={1}>
                          <Text fontWeight="bold">{issue.id}</Text>
                          <Tooltip label={`相似度: ${Math.round(issue.similarity * 100)}%`}>
                            <Badge colorScheme={issue.similarity > 0.8 ? "green" : "blue"}>
                              {Math.round(issue.similarity * 100)}% 相似
                            </Badge>
                          </Tooltip>
                        </Flex>
                        <Text fontSize="sm">{issue.description}</Text>
                      </ListItem>
                    ))}
                  </List>
                </AccordionPanel>
              </AccordionItem>
            )}
          </Accordion>
        </>
      )}
    </Box>
  );
};

export default AIAnalysisPanel;
