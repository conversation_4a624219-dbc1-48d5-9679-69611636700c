import React from 'react';
import { 
  Box, 
  Text, 
  Flex,
  useColorModeValue
} from '@chakra-ui/react';
import { useTranslation } from '@/contexts/LanguageContext';
import { MetricData } from '@/types/monitoring';

interface MetricsChartProps {
  metricData: MetricData;
  height?: number;
}

const MetricsChart: React.FC<MetricsChartProps> = ({
  metricData,
  height = 150
}) => {
  const { t } = useTranslation();
  const barColor = useColorModeValue('blue.500', 'blue.300');
  const barBgColor = useColorModeValue('gray.100', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  
  // Get min and max values for scaling
  const values = metricData.data_points.map(point => point.value);
  const maxValue = Math.max(...values);
  const minValue = Math.min(...values);
  
  // Take only the last 12 data points if more exist
  const displayPoints = metricData.data_points.slice(-12);
  
  return (
    <Box>
      <Text mb={1} fontWeight="medium">
        {t(`metrics.${metricData.metric_name}`) || metricData.metric_name}
      </Text>
      
      <Flex 
        height={`${height}px`} 
        align="flex-end" 
        justify="space-between"
        position="relative"
        borderBottom="1px solid"
        borderColor="gray.200"
        p={2}
      >
        {/* Y-axis label */}
        <Text 
          position="absolute" 
          top={0} 
          left={-8} 
          fontSize="xs" 
          color={textColor}
        >
          {maxValue}{metricData.unit}
        </Text>
        
        {displayPoints.map((point, index) => {
          // Calculate height percentage based on min and max values
          const heightPercent = ((point.value - minValue) / (maxValue - minValue || 1)) * 100;
          
          // Format timestamp for tooltip and x-axis
          const date = new Date(point.timestamp);
          const timeStr = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          
          return (
            <Flex 
              key={index} 
              direction="column" 
              align="center" 
              width={`${100 / displayPoints.length}%`}
            >
              <Box 
                width="80%" 
                height={`${heightPercent}%`}
                minHeight="5px"
                bg={barColor}
                borderRadius="sm"
                title={`${timeStr}: ${point.value}${metricData.unit}`}
                _hover={{ 
                  bg: 'blue.600',
                  boxShadow: 'md',
                  transform: 'translateY(-2px)'
                }}
                transition="all 0.2s"
              />
              
              {/* Only show some of the time labels to avoid crowding */}
              {index % 3 === 0 && (
                <Text mt={1} fontSize="xs" color={textColor} textAlign="center">
                  {timeStr}
                </Text>
              )}
            </Flex>
          );
        })}
      </Flex>
      
      <Flex justify="space-between" mt={1}>
        <Text fontSize="xs" color={textColor}>
          {t('metrics.min') || 'Min'}: {minValue}{metricData.unit}
        </Text>
        <Text fontSize="xs" color={textColor}>
          {t('metrics.max') || 'Max'}: {maxValue}{metricData.unit}
        </Text>
      </Flex>
    </Box>
  );
};

export default MetricsChart; 