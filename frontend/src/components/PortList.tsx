import React, { useState } from 'react'
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Box,
  Text,
  Input,
  InputGroup,
  InputLeftElement,
  Flex,
  Select,
  Stack
} from '@chakra-ui/react'
import { SearchIcon } from '@chakra-ui/icons'
import { useTranslation } from 'react-i18next'
import PortStatus from './PortStatus'

export interface Port {
  id: string
  name: string
  type: string
  status: 'connected' | 'disconnected' | 'error' | 'disabled'
  speed?: string
  connectedTo?: string
}

interface PortListProps {
  ports: Port[]
  isLoading: boolean
}

const PortList: React.FC<PortListProps> = ({ ports, isLoading }) => {
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  // 过滤端口
  const filteredPorts = ports.filter(port => {
    const matchesSearch = port.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (port.connectedTo && port.connectedTo.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesStatus = statusFilter === 'all' || port.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  if (isLoading) {
    return (
      <Box p={4}>
        <Text>{t('loading')}</Text>
      </Box>
    )
  }

  return (
    <Box>
      <Flex mb={4} gap={4} direction={{ base: 'column', md: 'row' }}>
        <InputGroup size="md">
          <InputLeftElement pointerEvents="none">
            <SearchIcon color="gray.400" />
          </InputLeftElement>
          <Input
            placeholder={t('search_ports')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </InputGroup>
        
        <Select 
          width={{ base: '100%', md: '200px' }}
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="all">{t('all_statuses')}</option>
          <option value="connected">{t('connected')}</option>
          <option value="disconnected">{t('disconnected')}</option>
          <option value="error">{t('error')}</option>
          <option value="disabled">{t('disabled')}</option>
        </Select>
      </Flex>

      {filteredPorts.length > 0 ? (
        <Table variant="simple" size="sm">
          <Thead>
            <Tr>
              <Th>{t('port_name')}</Th>
              <Th>{t('port_type')}</Th>
              <Th>{t('status')}</Th>
              <Th>{t('speed')}</Th>
              <Th>{t('connected_to')}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredPorts.map(port => (
              <Tr key={port.id}>
                <Td>{port.name}</Td>
                <Td>{port.type}</Td>
                <Td>
                  <Flex align="center" gap={2}>
                    <PortStatus status={port.status} />
                    <Text fontSize="sm">{t(port.status)}</Text>
                  </Flex>
                </Td>
                <Td>{port.speed || '-'}</Td>
                <Td>{port.connectedTo || '-'}</Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      ) : (
        <Box p={4} textAlign="center">
          <Text color="gray.500">{t('no_ports_found')}</Text>
        </Box>
      )}
    </Box>
  )
}

export default PortList 