import React, { useState } from 'react'
import {
  Box,
  Flex,
  Text,
  Badge,
  useColorMode,
  Tooltip,
  Icon,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Divider,
  Progress,
  SimpleGrid,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  useColorModeValue,
  Stack
} from '@chakra-ui/react'
import { 
  FaEthernet, 
  FaSignal, 
  FaBolt, 
  FaNetworkWired,
  FaSyncAlt,
  FaCog
} from 'react-icons/fa'
import { useTranslation } from '@/contexts/LanguageContext'

// 端口状态类型
export type PortStatus = 'up' | 'down' | 'error' | 'disabled' | 'warning'

// 端口类型
export type PortType = 'ethernet' | 'fiber' | 'console' | 'management' | 'sfp' | 'sfpplus' | 'qsfp'

// 端口接口
export interface NetworkPort {
  id: string
  name: string
  type: PortType
  status: PortStatus
  speed?: string
  duplex?: 'full' | 'half'
  connection?: string
  macAddress?: string
  ipAddress?: string
  vlan?: number
  utilization?: number
}

// 网络设备接口
export interface NetworkDevice {
  id: string
  name: string
  type: string
  ports: NetworkPort[]
  status: string
}

interface NetworkPortStatusProps {
  device: NetworkDevice
  onCustomize?: (options: { 
    count: number, 
    prefix: string, 
    startIndex: number 
  }) => void
}

const NetworkPortStatus: React.FC<NetworkPortStatusProps> = ({ device, onCustomize }) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const [isCustomModalOpen, setIsCustomModalOpen] = useState(false)
  const [portCount, setPortCount] = useState<number>(24)
  const [portPrefix, setPortPrefix] = useState<string>('GI')
  const [startIndex, setStartIndex] = useState<number>(1)
  
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  const headerBgColor = useColorModeValue('blue.50', 'blue.900')
  const headerColor = useColorModeValue('blue.600', 'blue.200')
  
  // 获取端口状态颜色
  const getStatusColor = (status: PortStatus) => {
    switch(status) {
      case 'up':
        return 'green.500'
      case 'down':
        return 'gray.400'
      case 'error':
        return 'red.500'
      case 'warning':
        return 'yellow.500'
      case 'disabled':
        return 'gray.300'
      default:
        return 'gray.400'
    }
  }
  
  // 获取端口类型图标
  const getTypeIcon = (type: PortType) => {
    switch(type) {
      case 'ethernet':
        return FaEthernet
      case 'fiber':
        return FaSignal
      case 'sfp':
      case 'sfpplus':
      case 'qsfp':
        return FaBolt
      default:
        return FaEthernet
    }
  }
  
  // 端口分组（按照类型）
  const groupedPorts: Record<string, NetworkPort[]> = {}
  device.ports.forEach(port => {
    const group = port.type
    if (!groupedPorts[group]) {
      groupedPorts[group] = []
    }
    groupedPorts[group].push(port)
  })
  
  // 计算端口统计数据
  const portStats = {
    total: device.ports.length,
    up: device.ports.filter(p => p.status === 'up').length,
    down: device.ports.filter(p => p.status === 'down').length,
    error: device.ports.filter(p => p.status === 'error').length,
    warning: device.ports.filter(p => p.status === 'warning').length,
    disabled: device.ports.filter(p => p.status === 'disabled').length,
  }
  
  // 应用自定义端口设置
  const handleApplyCustomization = () => {
    if (onCustomize) {
      onCustomize({
        count: portCount,
        prefix: portPrefix,
        startIndex: startIndex
      })
    }
    setIsCustomModalOpen(false)
  }
  
  // 渲染端口图标
  const renderPort = (port: NetworkPort) => {
    const IconComponent = getTypeIcon(port.type)
    const statusColor = getStatusColor(port.status)
    
    return (
      <Tooltip
        key={port.id}
        label={
          <Box p={2}>
            <Text fontWeight="bold">{port.name}</Text>
            <Text fontSize="sm">状态: {
              port.status === 'up' ? '已连接' :
              port.status === 'down' ? '未连接' :
              port.status === 'error' ? '错误' :
              port.status === 'warning' ? '警告' : '已禁用'
            }</Text>
            {port.speed && <Text fontSize="sm">速率: {port.speed}</Text>}
            {port.duplex && <Text fontSize="sm">双工: {port.duplex === 'full' ? '全双工' : '半双工'}</Text>}
            {port.vlan && <Text fontSize="sm">VLAN: {port.vlan}</Text>}
            {port.connection && <Text fontSize="sm">连接到: {port.connection}</Text>}
          </Box>
        }
        placement="top"
        hasArrow
      >
        <Box
          position="relative"
          borderWidth="1px"
          borderRadius="md"
          p={1}
          m={1}
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
          borderColor={port.status === 'up' ? 'green.400' : 'gray.300'}
          _hover={{
            transform: 'scale(1.05)',
            zIndex: 2,
            boxShadow: 'md'
          }}
          transition="all 0.2s"
          cursor="pointer"
          width="60px"
          height="60px"
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
        >
          <Icon 
            as={IconComponent} 
            boxSize="24px" 
            color={statusColor}
            mb={1}
          />
          <Text 
            fontSize="xs" 
            fontWeight="medium"
            isTruncated
            maxWidth="100%"
          >
            {port.name}
          </Text>
        </Box>
      </Tooltip>
    )
  }
  
  // 渲染端口组
  const renderPortGroup = (type: string, ports: NetworkPort[]) => {
    const typeName = 
      type === 'ethernet' ? '以太网端口' :
      type === 'fiber' ? '光纤端口' :
      type === 'console' ? '控制台端口' :
      type === 'management' ? '管理端口' :
      type === 'sfp' ? 'SFP端口' :
      type === 'sfpplus' ? 'SFP+端口' :
      type === 'qsfp' ? 'QSFP端口' : type
    
    // 计算已连接端口数量和百分比
    const connectedCount = ports.filter(p => p.status === 'up').length
    const connectedPercentage = connectedCount > 0 ? 
      `${Math.round((connectedCount / ports.length) * 100)}%` : '0%'
    
    return (
      <Box 
        key={type}
        borderWidth="1px"
        borderRadius="md"
        overflow="hidden"
        mb={4}
        bg={bgColor}
      >
        <Flex 
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'} 
          p={2} 
          borderBottomWidth="1px"
          justify="space-between"
          align="center"
        >
          <Flex align="center">
            <Icon as={getTypeIcon(type as PortType)} mr={2} />
            <Text fontWeight="medium">{typeName}</Text>
          </Flex>
          <Badge colorScheme={connectedCount > 0 ? 'green' : 'gray'}>
            {connectedCount}/{ports.length} 已连接 ({connectedPercentage})
          </Badge>
        </Flex>
        <Flex p={2} flexWrap="wrap" justify="flex-start">
          {ports.map(renderPort)}
        </Flex>
      </Box>
    )
  }

  return (
    <Box mb={6}>
      <Card 
        variant="outline" 
        mb={4}
        bg={bgColor}
        borderColor={borderColor}
      >
        <CardHeader bg={headerBgColor} py={3} display="flex" justifyContent="space-between" alignItems="center">
          <Heading size="md" color={headerColor}>
            网络端口状态
          </Heading>
          {onCustomize && (
            <Button 
              size="sm" 
              leftIcon={<Icon as={FaCog} />}
              onClick={() => setIsCustomModalOpen(true)}
              colorScheme="blue"
              variant="outline"
            >
              自定义端口
            </Button>
          )}
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 2, md: 3, lg: 5 }} spacing={4} mb={4}>
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Text fontSize="sm" color="gray.500">总端口数</Text>
              <Text fontSize="2xl" fontWeight="bold">{portStats.total}</Text>
            </Box>
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Text fontSize="sm" color="gray.500">已连接</Text>
              <Text fontSize="2xl" fontWeight="bold" color="green.500">{portStats.up}</Text>
              <Text fontSize="xs" color="green.500">
                {portStats.total > 0 && (
                  `${Math.round((portStats.up / portStats.total) * 100)}%`
                )}
              </Text>
            </Box>
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Text fontSize="sm" color="gray.500">未连接</Text>
              <Text fontSize="2xl" fontWeight="bold" color="gray.500">{portStats.down}</Text>
              <Text fontSize="xs" color="gray.500">
                {portStats.total > 0 && (
                  `${Math.round((portStats.down / portStats.total) * 100)}%`
                )}
              </Text>
            </Box>
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Text fontSize="sm" color="gray.500">故障</Text>
              <Text fontSize="2xl" fontWeight="bold" color="red.500">{portStats.error}</Text>
              {portStats.error > 0 && (
                <Text fontSize="xs" color="red.500">
                  需要处理
                </Text>
              )}
            </Box>
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Text fontSize="sm" color="gray.500">警告</Text>
              <Text fontSize="2xl" fontWeight="bold" color="yellow.500">{portStats.warning}</Text>
              {portStats.warning > 0 && (
                <Text fontSize="xs" color="yellow.500">
                  需要检查
                </Text>
              )}
            </Box>
          </SimpleGrid>
          
          <Box mt={4} mb={6}>
            <Text fontWeight="medium" mb={2}>端口利用率</Text>
            <Progress 
              value={(portStats.up / (portStats.total || 1)) * 100} 
              size="sm" 
              colorScheme="blue" 
              borderRadius="full" 
            />
          </Box>
          
          <Divider mb={4} />
          
          <Text fontWeight="bold" fontSize="lg" mb={4}>端口详情</Text>
          {Object.entries(groupedPorts).map(([type, ports]) => renderPortGroup(type, ports))}
        </CardBody>
      </Card>
      
      {/* 自定义端口模态框 */}
      <Modal isOpen={isCustomModalOpen} onClose={() => setIsCustomModalOpen(false)}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>自定义端口配置</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Stack spacing={4}>
              <FormControl>
                <FormLabel>端口数量</FormLabel>
                <NumberInput
                  value={portCount}
                  onChange={(valueString) => setPortCount(parseInt(valueString))}
                  min={1}
                  max={48}
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
              </FormControl>
              
              <FormControl>
                <FormLabel>端口前缀</FormLabel>
                <Input 
                  value={portPrefix} 
                  onChange={(e) => setPortPrefix(e.target.value)} 
                  placeholder="GI" 
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>起始索引</FormLabel>
                <NumberInput
                  value={startIndex}
                  onChange={(valueString) => setStartIndex(parseInt(valueString))}
                  min={1}
                  max={48}
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
              </FormControl>
            </Stack>
          </ModalBody>
          <ModalFooter>
            <Button 
              colorScheme="blue" 
              mr={3} 
              leftIcon={<Icon as={FaSyncAlt} />}
              onClick={handleApplyCustomization}
            >
              应用
            </Button>
            <Button variant="ghost" onClick={() => setIsCustomModalOpen(false)}>
              取消
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}

export default NetworkPortStatus 