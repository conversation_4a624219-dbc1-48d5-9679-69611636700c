'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { NotificationTemplate, ALERT_TEMPLATE_VARIABLES } from '@/types/notificationTemplate'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'

const templateSchema = z.object({
  name: z.string().min(2, '名称至少需要2个字符'),
  type: z.enum(['email', 'webhook', 'slack', 'sms']),
  subject: z.string().optional(),
  content: z.string().min(1, '模板内容不能为空'),
  isDefault: z.boolean()
})

export function NotificationTemplates() {
  const [templates, setTemplates] = useState<NotificationTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedType, setSelectedType] = useState<'email' | 'webhook' | 'slack' | 'sms'>('email')

  const form = useForm<z.infer<typeof templateSchema>>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: '',
      type: 'email',
      subject: '',
      content: '',
      isDefault: false
    }
  })

  const loadTemplates = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/notification-templates')
      const data = await response.json()
      setTemplates(data)
    } catch (err) {
      console.error('加载模板失败:', err)
      toast({
        title: "加载失败",
        description: "无法加载通知模板",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTemplates()
  }, [])

  const onSubmit = async (values: z.infer<typeof templateSchema>) => {
    try {
      const response = await fetch('/api/notification-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          variables: Object.keys(ALERT_TEMPLATE_VARIABLES)
        })
      })
      
      if (response.ok) {
        toast({
          title: "操作成功",
          description: "通知模板已创建",
        })
        loadTemplates()
        form.reset()
      }
    } catch (err) {
      toast({
        title: "操作失败",
        description: err instanceof Error ? err.message : "创建通知模板失败",
        variant: "destructive",
      })
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'email':
        return <Badge variant="default">邮件</Badge>
      case 'webhook':
        return <Badge variant="secondary">Webhook</Badge>
      case 'slack':
        return <Badge className="bg-purple-500">Slack</Badge>
      case 'sms':
        return <Badge className="bg-green-500">短信</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-4 border rounded-lg">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>模板名称</FormLabel>
                  <FormControl>
                    <Input placeholder="默认邮件模板" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>模板类型</FormLabel>
                  <Select 
                    onValueChange={(value: any) => {
                      setSelectedType(value)
                      field.onChange(value)
                    }} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择模板类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="email">邮件</SelectItem>
                      <SelectItem value="webhook">Webhook</SelectItem>
                      <SelectItem value="slack">Slack</SelectItem>
                      <SelectItem value="sms">短信</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedType === 'email' && (
              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>邮件主题</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="报警通知: {alert.name}" 
                        {...field} 
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="isDefault"
              render={({ field }) => (
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel>设为默认</FormLabel>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>模板内容</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={`例如: 设备 {device.name}({device.ip}) 触发报警 {alert.name}\n当前值: {alert.value}\n阈值: {alert.threshold}`}
                    className="min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="p-4 border rounded-lg bg-muted/50">
            <h4 className="text-sm font-medium mb-2">可用变量:</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {Object.entries(ALERT_TEMPLATE_VARIABLES).map(([varName, desc]) => (
                <div key={varName} className="text-sm">
                  <code className="bg-muted px-2 py-1 rounded">{`{${varName}}`}</code>
                  <span className="ml-2 text-muted-foreground">{desc}</span>
                </div>
              ))}
            </div>
          </div>

          <Button type="submit">保存模板</Button>
        </form>
      </Form>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>名称</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {templates.map((template) => (
              <TableRow key={template.id}>
                <TableCell className="font-medium">{template.name}</TableCell>
                <TableCell>{getTypeBadge(template.type)}</TableCell>
                <TableCell>
                  {template.isDefault ? (
                    <Badge>默认模板</Badge>
                  ) : (
                    <Badge variant="outline">备用模板</Badge>
                  )}
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm">
                    编辑
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}