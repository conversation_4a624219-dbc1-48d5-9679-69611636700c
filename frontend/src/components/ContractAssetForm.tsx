'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon, FileText, Users, DollarSign, AlertTriangle } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bsTrigger,
} from '@/components/ui/tabs'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'

interface AssetCategory {
  id: number
  name: string
  code: string
  description?: string
  level: number
  parent_id?: number
}

interface ContractAssetFormData {
  name: string
  code: string
  category_id: number
  contract_type: string
  contract_number: string
  vendor: string
  vendor_contact: string
  vendor_phone?: string
  vendor_email?: string
  service_type: string
  service_description: string
  contract_amount: number
  currency: string
  payment_method: string
  payment_schedule: string
  start_date: Date
  end_date: Date
  renewal_notice_days: number
  auto_renewal: boolean
  renewal_terms?: string
  sla_requirements?: string
  penalty_terms?: string
  termination_terms?: string
  department: string
  responsible_person: string
  backup_contact?: string
  status: string
  is_critical: boolean
  requires_approval: boolean
  approval_workflow?: string
  description?: string
}

const contractAssetSchema = z.object({
  name: z.string().min(1, '合同名称不能为空'),
  code: z.string().min(1, '合同编码不能为空'),
  category_id: z.number().min(1, '请选择合同分类'),
  contract_type: z.string().min(1, '请选择合同类型'),
  contract_number: z.string().min(1, '合同编号不能为空'),
  vendor: z.string().min(1, '供应商不能为空'),
  vendor_contact: z.string().min(1, '供应商联系人不能为空'),
  vendor_phone: z.string().optional(),
  vendor_email: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
  service_type: z.string().min(1, '请选择服务类型'),
  service_description: z.string().min(1, '服务描述不能为空'),
  contract_amount: z.number().min(0, '合同金额不能为负数'),
  currency: z.string(),
  payment_method: z.string(),
  payment_schedule: z.string(),
  start_date: z.date(),
  end_date: z.date(),
  renewal_notice_days: z.number().min(0, '续约提醒天数不能为负数'),
  auto_renewal: z.boolean(),
  renewal_terms: z.string().optional(),
  sla_requirements: z.string().optional(),
  penalty_terms: z.string().optional(),
  termination_terms: z.string().optional(),
  department: z.string().min(1, '使用部门不能为空'),
  responsible_person: z.string().min(1, '负责人不能为空'),
  backup_contact: z.string().optional(),
  status: z.string(),
  is_critical: z.boolean(),
  requires_approval: z.boolean(),
  approval_workflow: z.string().optional(),
  description: z.string().optional(),
}).refine((data) => data.end_date > data.start_date, {
  message: "结束日期必须晚于开始日期",
  path: ["end_date"],
})

interface ContractAssetFormProps {
  onSubmit: (data: ContractAssetFormData) => Promise<void>
  initialData?: Partial<ContractAssetFormData>
  isLoading?: boolean
}

export function ContractAssetForm({ onSubmit, initialData, isLoading = false }: ContractAssetFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [autoGenerateCode, setAutoGenerateCode] = useState(true)

  const form = useForm<ContractAssetFormData>({
    resolver: zodResolver(contractAssetSchema),
    defaultValues: {
      name: '',
      code: '',
      category_id: 0,
      contract_type: '',
      contract_number: '',
      vendor: '',
      vendor_contact: '',
      service_type: '',
      service_description: '',
      contract_amount: 0,
      currency: 'CNY',
      payment_method: '',
      payment_schedule: '',
      start_date: new Date(),
      end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 默认一年后
      renewal_notice_days: 30,
      auto_renewal: false,
      status: 'active',
      is_critical: false,
      requires_approval: false,
      department: '',
      responsible_person: '',
      ...initialData
    }
  })

  // 获取合同分类
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/asset-categories')
        if (response.ok) {
          const data = await response.json()
          // 过滤合同相关分类
          const contractCategories = data.filter((cat: AssetCategory) =>
            cat.name.includes('合同') || cat.code.includes('CONTRACT')
          )
          setCategories(contractCategories)
        }
      } catch (error) {
        console.error('获取资产分类失败:', error)
        toast({
          title: '错误',
          description: '获取资产分类失败',
          variant: 'destructive'
        })
      }
    }
    fetchCategories()
  }, [])

  // 构建层级化的分类选项
  const buildHierarchicalCategories = (categories: AssetCategory[], parentId: number | null = null, level: number = 0): any[] => {
    const result: any[] = []
    const children = categories.filter(cat => cat.parent_id === parentId)
    
    children.forEach(category => {
      const indent = '　'.repeat(level) // 使用全角空格缩进
      result.push({
        ...category,
        displayName: `${indent}${category.name} (${category.code})`,
        level
      })
      
      // 递归添加子分类
      const subCategories = buildHierarchicalCategories(categories, category.id, level + 1)
      result.push(...subCategories)
    })
    
    return result
  }

  const hierarchicalCategories = buildHierarchicalCategories(categories)

  // 自动生成合同编码
  const generateContractCode = (categoryId: number, contractType: string, vendor: string) => {
    if (!autoGenerateCode) return
    
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      const prefix = category.code || 'CT'
      const year = new Date().getFullYear().toString().slice(-2)
      const month = (new Date().getMonth() + 1).toString().padStart(2, '0')
      const typePrefix = contractType.substring(0, 2).toUpperCase()
      const vendorPrefix = vendor.substring(0, 2).toUpperCase()
      const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
      const code = `${prefix}-${year}${month}${typePrefix}${vendorPrefix}${random}`
      form.setValue('code', code)
    }
  }

  // 监听字段变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name: fieldName }) => {
      if (fieldName === 'category_id' || fieldName === 'contract_type' || fieldName === 'vendor') {
        const categoryId = value.category_id
        const contractType = value.contract_type
        const vendor = value.vendor
        if (categoryId && contractType && vendor && autoGenerateCode) {
          generateContractCode(categoryId, contractType, vendor)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, categories, autoGenerateCode])

  // 计算合同剩余天数
  const calculateRemainingDays = (endDate: Date) => {
    const today = new Date()
    const diffTime = endDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const handleSubmit = async (data: ContractAssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      toast({
        title: '成功',
        description: '合同资产登记成功',
      })
      form.reset()
    } catch (error) {
      console.error('合同资产登记失败:', error)
      toast({
        title: '错误',
        description: '合同资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同名称 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入合同名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                合同编码 *
                <Switch
                  checked={autoGenerateCode}
                  onCheckedChange={setAutoGenerateCode}
                  size="sm"
                />
                <span className="text-xs text-muted-foreground">自动生成</span>
              </FormLabel>
              <FormControl>
                <Input 
                  placeholder="请输入合同编码" 
                  {...field} 
                  disabled={autoGenerateCode}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同分类 *</FormLabel>
              <Select onValueChange={(value) => field.onChange(Number(value))} value={field.value?.toString()}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择合同分类" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hierarchicalCategories.map((category) => (
                    <SelectItem 
                      key={category.id} 
                      value={category.id.toString()}
                      className={category.level === 1 ? "font-bold" : "text-sm"}
                    >
                      {category.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="contract_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同类型 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择合同类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="service">服务合同</SelectItem>
                  <SelectItem value="maintenance">维保合同</SelectItem>
                  <SelectItem value="license">软件许可</SelectItem>
                  <SelectItem value="support">技术支持</SelectItem>
                  <SelectItem value="consulting">咨询服务</SelectItem>
                  <SelectItem value="outsourcing">外包服务</SelectItem>
                  <SelectItem value="lease">租赁合同</SelectItem>
                  <SelectItem value="subscription">订阅服务</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="contract_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同编号 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入合同编号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择合同状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="draft">草稿</SelectItem>
                  <SelectItem value="pending">待审批</SelectItem>
                  <SelectItem value="active">生效中</SelectItem>
                  <SelectItem value="expired">已过期</SelectItem>
                  <SelectItem value="terminated">已终止</SelectItem>
                  <SelectItem value="renewed">已续约</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="service_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>服务类型 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择服务类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="it_service">IT服务</SelectItem>
                  <SelectItem value="cloud_service">云服务</SelectItem>
                  <SelectItem value="security_service">安全服务</SelectItem>
                  <SelectItem value="network_service">网络服务</SelectItem>
                  <SelectItem value="data_service">数据服务</SelectItem>
                  <SelectItem value="backup_service">备份服务</SelectItem>
                  <SelectItem value="monitoring_service">监控服务</SelectItem>
                  <SelectItem value="consulting_service">咨询服务</SelectItem>
                  <SelectItem value="training_service">培训服务</SelectItem>
                  <SelectItem value="other">其他服务</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="currency"
          render={({ field }) => (
            <FormItem>
              <FormLabel>货币类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择货币类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                  <SelectItem value="USD">美元 (USD)</SelectItem>
                  <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                  <SelectItem value="JPY">日元 (JPY)</SelectItem>
                  <SelectItem value="HKD">港币 (HKD)</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* 位置信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">服务位置信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="service_location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>服务位置</FormLabel>
                <FormControl>
                  <Input placeholder="请输入服务位置" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="building"
            render={({ field }) => (
              <FormItem>
                <FormLabel>建筑物</FormLabel>
                <FormControl>
                  <Input placeholder="请输入建筑物" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="floor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>楼层</FormLabel>
                <FormControl>
                  <Input placeholder="请输入楼层" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="room"
            render={({ field }) => (
              <FormItem>
                <FormLabel>房间</FormLabel>
                <FormControl>
                  <Input placeholder="请输入房间" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="department"
            render={({ field }) => (
              <FormItem>
                <FormLabel>服务部门</FormLabel>
                <FormControl>
                  <Input placeholder="请输入服务部门" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <FormField
        control={form.control}
        name="service_description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>服务描述 *</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="请详细描述服务内容和范围" 
                className="min-h-[100px]"
                {...field} 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )

  const renderVendorInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="vendor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>供应商 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入供应商名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="vendor_contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系人 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系人姓名" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="vendor_phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系电话</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系电话" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="vendor_email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>联系邮箱</FormLabel>
              <FormControl>
                <Input placeholder="请输入联系邮箱" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>使用部门 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入使用部门" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>负责人 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入负责人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="backup_contact"
          render={({ field }) => (
            <FormItem>
              <FormLabel>备用联系人</FormLabel>
              <FormControl>
                <Input placeholder="请输入备用联系人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )

  const renderContractTerms = () => {
    const endDate = form.watch('end_date')
    const remainingDays = endDate ? calculateRemainingDays(endDate) : 0
    
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="contract_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>合同金额 *</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入合同金额" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="payment_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>付款方式</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择付款方式" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="bank_transfer">银行转账</SelectItem>
                    <SelectItem value="check">支票</SelectItem>
                    <SelectItem value="cash">现金</SelectItem>
                    <SelectItem value="credit_card">信用卡</SelectItem>
                    <SelectItem value="online_payment">在线支付</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="payment_schedule"
          render={({ field }) => (
            <FormItem>
              <FormLabel>付款计划</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择付款计划" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="monthly">按月付款</SelectItem>
                  <SelectItem value="quarterly">按季度付款</SelectItem>
                  <SelectItem value="semi_annual">半年付款</SelectItem>
                  <SelectItem value="annual">年度付款</SelectItem>
                  <SelectItem value="one_time">一次性付款</SelectItem>
                  <SelectItem value="milestone">里程碑付款</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>开始日期 *</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择开始日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel className="flex items-center gap-2">
                  结束日期 *
                  {remainingDays > 0 && remainingDays <= 30 && (
                    <Badge variant="destructive" className="text-xs">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      {remainingDays}天后到期
                    </Badge>
                  )}
                  {remainingDays <= 0 && (
                    <Badge variant="destructive" className="text-xs">
                      已过期
                    </Badge>
                  )}
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择结束日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date()}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="renewal_notice_days"
            render={({ field }) => (
              <FormItem>
                <FormLabel>续约提醒天数</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="提前多少天提醒续约" 
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex items-center space-x-4 pt-8">
            <FormField
              control={form.control}
              name="auto_renewal"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="text-sm font-normal">
                    自动续约
                  </FormLabel>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="is_critical"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="text-sm font-normal">
                    关键合同
                  </FormLabel>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="requires_approval"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="text-sm font-normal">
                    需要审批
                  </FormLabel>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="space-y-4">
          <FormField
            control={form.control}
            name="sla_requirements"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SLA要求</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="请输入服务级别协议要求" 
                    className="min-h-[80px]"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="penalty_terms"
            render={({ field }) => (
              <FormItem>
                <FormLabel>违约条款</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="请输入违约责任和处罚条款" 
                    className="min-h-[80px]"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="termination_terms"
            render={({ field }) => (
              <FormItem>
                <FormLabel>终止条款</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="请输入合同终止条件和程序" 
                    className="min-h-[80px]"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>备注说明</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="请输入其他备注信息" 
                    className="min-h-[80px]"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="vendor" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              供应商信息
            </TabsTrigger>
            <TabsTrigger value="terms" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              合同条款
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="mt-6">
            {renderBasicInfo()}
          </TabsContent>
          
          <TabsContent value="vendor" className="mt-6">
            {renderVendorInfo()}
          </TabsContent>
          
          <TabsContent value="terms" className="mt-6">
            {renderContractTerms()}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button type="button" variant="outline" onClick={() => form.reset()}>
            重置
          </Button>
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting ? '登记中...' : '确认登记'}
          </Button>
        </div>
      </form>
    </Form>
  )
}