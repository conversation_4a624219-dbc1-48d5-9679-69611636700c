'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Asset, MonitoringConfig, SNMPConfig, SSHConfig } from '@/types/asset'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { snmpService, sshService } from '@/services'
import { toast } from '@/components/ui/use-toast'

interface AssetFormProps {
  initialData?: Asset
  onSubmit: (data: Asset) => Promise<void>
}

export function AssetForm({ initialData, onSubmit }: AssetFormProps) {
  const [monitoringType, setMonitoringType] = useState<'snmp' | 'ssh' | 'none'>(
    initialData?.monitoringConfig?.type || 'none'
  )
  const [isMonitoringEnabled, setIsMonitoringEnabled] = useState(
    initialData?.monitoringConfig?.enabled || false
  )

  const form = useForm<Asset>({
    defaultValues: initialData || {
      name: '',
      type: 'server',
      ipAddress: '',
      monitoringConfig: {
        enabled: false,
        type: 'none',
        config: null
      }
    },
    resolver: zodResolver(z.object({
      name: z.string().min(2, '名称至少需要2个字符'),
      type: z.enum(['server', 'network', 'printer', 'other']),
      ipAddress: z.string().min(7, '请输入有效的IP地址').regex(
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
        '请输入有效的IP地址'
      ),
      macAddress: z.string().optional().refine(val =>
        !val || /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(val),
        '请输入有效的MAC地址'
      ),
      monitoringConfig: z.object({
        enabled: z.boolean(),
        type: z.enum(['snmp', 'ssh', 'none']),
        config: z.any().optional()
      }).superRefine((val, ctx) => {
        if (val.enabled && val.type !== 'none') {
          if (val.type === 'snmp') {
            const config = val.config as SNMPConfig
            if (!config?.community) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['monitoringConfig', 'config', 'community'],
                message: 'SNMP Community不能为空'
              })
            }
            if (!config?.oids || config.oids.length === 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['monitoringConfig', 'config', 'oids'],
                message: '至少需要一个OID'
              })
            }
          } else {
            const config = val.config as SSHConfig
            if (!config?.username) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['monitoringConfig', 'config', 'username'],
                message: 'SSH用户名不能为空'
              })
            }
            if (!config?.commands || config.commands.length === 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['monitoringConfig', 'config', 'commands'],
                message: '至少需要一个命令'
              })
            }
          }
        }
      })
    }))
  })

  const handleSubmit = async (data: Asset) => {
    try {
      // 处理监控配置
      if (isMonitoringEnabled && monitoringType !== 'none') {
        const config = monitoringType === 'snmp'
          ? {
              community: form.getValues('monitoringConfig.config.community'),
              oids: form.getValues('monitoringConfig.config.oids')?.split('\n') || [],
              interval: Number(form.getValues('monitoringConfig.config.interval'))
            }
          : {
              username: form.getValues('monitoringConfig.config.username'),
              password: form.getValues('monitoringConfig.config.password'),
              commands: form.getValues('monitoringConfig.config.commands')?.split('\n') || [],
              interval: Number(form.getValues('monitoringConfig.config.interval')),
              port: Number(form.getValues('monitoringConfig.config.port')) || 22
            };

        data.monitoringConfig = {
          enabled: true,
          type: monitoringType,
          config
        };

        // 添加到监控服务
        if (monitoringType === 'snmp') {
          await snmpService.addDevice({
            host: data.ipAddress,
            ...config as SNMPConfig
          });
        } else {
          await sshService.addDevice({
            host: data.ipAddress,
            ...config as SSHConfig
          });
        }
      } else {
        data.monitoringConfig = {
          enabled: false,
          type: 'none',
          config: null
        }
      }

      await onSubmit(data)

      toast({
        title: "操作成功",
        description: "资产信息已保存",
      })
    } catch (err) {
      console.error('保存资产失败:', err)
      toast({
        title: "操作失败",
        description: err instanceof Error ? err.message : "保存资产时发生错误",
        variant: "destructive",
      })
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>设备名称</FormLabel>
              <FormControl>
                <Input placeholder="设备名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>设备类型</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择设备类型" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="network">网络设备</SelectItem>
                  <SelectItem value="printer">打印机</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="ipAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel>IP地址</FormLabel>
              <FormControl>
                <Input placeholder="***********" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="monitoring-enabled"
              checked={isMonitoringEnabled}
              onCheckedChange={setIsMonitoringEnabled}
            />
            <label htmlFor="monitoring-enabled" className="text-sm font-medium">
              启用监控
            </label>
          </div>

          {isMonitoringEnabled && (
            <div className="pl-6 space-y-4 border-l-2">
              <FormField
                control={form.control}
                name="monitoringConfig.type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>监控类型</FormLabel>
                    <Select
                      value={monitoringType}
                      onValueChange={(value: 'snmp' | 'ssh' | 'none') => {
                        setMonitoringType(value)
                        field.onChange(value)
                      }}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择监控类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="snmp">SNMP</SelectItem>
                        <SelectItem value="ssh">SSH</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {monitoringType === 'snmp' && (
                <>
                  <FormField
                    control={form.control}
                    name="monitoringConfig.config.community"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SNMP Community</FormLabel>
                        <FormControl>
                          <Input placeholder="public" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="monitoringConfig.config.oids"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>监控OIDs (每行一个)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="*******.*******.0\n*******.2.*******"
                            {...field}
                            rows={4}
                          />
                        </FormControl>
                        <FormDescription>
                          输入要监控的SNMP OID列表
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {monitoringType === 'ssh' && (
                <>
                  <FormField
                    control={form.control}
                    name="monitoringConfig.config.username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SSH用户名</FormLabel>
                        <FormControl>
                          <Input placeholder="admin" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="monitoringConfig.config.password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SSH密码</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="密码" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="monitoringConfig.config.port"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SSH端口</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="22" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="monitoringConfig.config.commands"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>监控命令 (每行一个)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="uptime\nfree -m"
                            {...field}
                            rows={4}
                          />
                        </FormControl>
                        <FormDescription>
                          输入要执行的SSH命令列表
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              <FormField
                control={form.control}
                name="monitoringConfig.config.interval"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>采集间隔 (秒)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="60" {...field} />
                    </FormControl>
                    <FormDescription>
                      数据采集的时间间隔
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>

        <Button type="submit">保存</Button>
      </form>
    </Form>
  )
}