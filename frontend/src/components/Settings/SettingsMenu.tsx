'use client'

import React from 'react'
import {
  Box,
  Stack,
  FormControl,
  FormLabel,
  Switch,
  Select,
  useColorMode,
  Card,
  CardBody,
  Heading,
  Container,
} from '@chakra-ui/react'
import { useTranslation } from '@/contexts/LanguageContext'

const SettingsMenu = () => {
  const { colorMode, toggleColorMode } = useColorMode()
  const { t, setLanguage, language } = useTranslation()

  return (
    <Container maxW="container.md" p={4}>
      <Card>
        <CardBody>
          <Stack spacing={6}>
            <Heading size="lg" mb={4}>
              {t('settings')}
            </Heading>

            <FormControl display="flex" alignItems="center">
              <FormLabel htmlFor="dark-mode" mb="0" flex="1">
                {t('dark.mode')}
              </FormLabel>
              <Switch
                id="dark-mode"
                isChecked={colorMode === 'dark'}
                onChange={toggleColorMode}
                colorScheme="blue"
              />
            </FormControl>

            <FormControl>
              <FormLabel htmlFor="language">
                {t('language')}
              </FormLabel>
              <Select
                id="language"
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                variant="filled"
              >
                <option value="zh-CN">中文</option>
                <option value="en-US">English</option>
              </Select>
            </FormControl>
          </Stack>
        </CardBody>
      </Card>
    </Container>
  )
}

export default SettingsMenu 