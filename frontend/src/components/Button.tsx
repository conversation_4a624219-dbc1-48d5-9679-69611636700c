'use client'

import React from 'react'
import { Button as ShadcnButton, ButtonProps as ShadcnButtonProps } from "@/components/ui/button"

// 扩展Shadcn按钮属性，添加与现有NextUI/Chakra UI按钮兼容的属性
export interface ButtonProps extends Omit<ShadcnButtonProps, 'variant'> {
  // NextUI兼容属性
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  // Chakra UI兼容属性
  colorScheme?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  isLoading?: boolean
  loadingText?: string
  // 通用属性
  variant?: ShadcnButtonProps['variant'] | 'solid' | 'flat' | 'light' | 'bordered' | 'faded'
}

/**
 * 兼容层按钮组件
 * 
 * 这个组件包装了Shadcn/UI的Button组件，提供与NextUI和Chakra UI按钮兼容的API
 */
export function Button({
  children,
  color,
  colorScheme,
  leftIcon,
  rightIcon,
  isLoading,
  loadingText,
  variant,
  ...props
}: ButtonProps) {
  // 将NextUI/Chakra UI的属性映射到Shadcn/UI的属性
  let mappedVariant: ShadcnButtonProps['variant'] = 'default'
  
  // 处理variant映射
  if (variant) {
    switch (variant) {
      case 'solid':
        mappedVariant = 'default'
        break
      case 'flat':
      case 'light':
        mappedVariant = 'secondary'
        break
      case 'bordered':
        mappedVariant = 'outline'
        break
      case 'faded':
        mappedVariant = 'ghost'
        break
      default:
        // 如果是Shadcn原生variant，直接使用
        if (['default', 'destructive', 'outline', 'secondary', 'ghost', 'link', 'komodo'].includes(variant as string)) {
          mappedVariant = variant as ShadcnButtonProps['variant']
        }
    }
  }
  
  // 处理color/colorScheme映射
  if (color || colorScheme) {
    const colorValue = color || colorScheme
    switch (colorValue) {
      case 'primary':
      case 'blue':
        mappedVariant = 'default'
        break
      case 'secondary':
      case 'gray':
        mappedVariant = 'secondary'
        break
      case 'success':
      case 'green':
        mappedVariant = 'komodo' // 使用我们自定义的komodo变体
        break
      case 'danger':
      case 'red':
        mappedVariant = 'destructive'
        break
    }
  }
  
  // 处理加载状态
  const content = isLoading ? (
    <>
      <svg
        className="mr-2 h-4 w-4 animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      {loadingText || children}
    </>
  ) : (
    <>
      {leftIcon && <span className="mr-2">{leftIcon}</span>}
      {children}
      {rightIcon && <span className="ml-2">{rightIcon}</span>}
    </>
  )
  
  return (
    <ShadcnButton
      {...props}
      variant={mappedVariant}
      disabled={props.disabled || isLoading}
    >
      {content}
    </ShadcnButton>
  )
}
