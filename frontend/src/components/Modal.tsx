'use client'

import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/Button"

// 兼容NextUI和Chakra UI的Modal属性
export interface ModalProps {
  // 通用属性
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
  onClose?: () => void
  children?: React.ReactNode
  
  // 标题相关
  title?: React.ReactNode
  header?: React.ReactNode
  
  // 底部相关
  footer?: React.ReactNode
  
  // 大小相关
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full'
  
  // 其他属性
  isDismissable?: boolean
  hideCloseButton?: boolean
  className?: string
  contentClassName?: string
  bodyClassName?: string
}

// 子组件类型
export interface ModalBodyProps {
  children?: React.ReactNode
  className?: string
}

export interface ModalHeaderProps {
  children?: React.ReactNode
  className?: string
}

export interface ModalFooterProps {
  children?: React.ReactNode
  className?: string
}

/**
 * Modal组件 - 兼容NextUI和Chakra UI的API
 */
export function Modal({
  isOpen = false,
  onOpenChange,
  onClose,
  children,
  title,
  header,
  footer,
  size = 'md',
  isDismissable = true,
  hideCloseButton = false,
  className = '',
  contentClassName = '',
}: ModalProps) {
  // 处理大小映射
  const sizeClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    full: 'max-w-full',
  }
  
  // 处理关闭事件
  const handleOpenChange = (open: boolean) => {
    if (onOpenChange) {
      onOpenChange(open)
    } else if (!open && onClose) {
      onClose()
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent 
        className={`${sizeClasses[size]} ${contentClassName} ${className}`}
        onInteractOutside={isDismissable ? undefined : (e) => e.preventDefault()}
        hideCloseButton={hideCloseButton}
      >
        {(title || header) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {header}
          </DialogHeader>
        )}
        
        {children}
        
        {footer && <DialogFooter>{footer}</DialogFooter>}
      </DialogContent>
    </Dialog>
  )
}

/**
 * ModalBody组件
 */
export function ModalBody({ children, className = '' }: ModalBodyProps) {
  return (
    <div className={`py-4 ${className}`}>
      {children}
    </div>
  )
}

/**
 * ModalHeader组件
 */
export function ModalHeader({ children, className = '' }: ModalHeaderProps) {
  return (
    <DialogHeader className={className}>
      {children}
    </DialogHeader>
  )
}

/**
 * ModalFooter组件
 */
export function ModalFooter({ children, className = '' }: ModalFooterProps) {
  return (
    <DialogFooter className={className}>
      {children}
    </DialogFooter>
  )
}

/**
 * 用法示例:
 * 
 * <Modal 
 *   isOpen={isOpen} 
 *   onOpenChange={setIsOpen}
 *   title="Modal Title"
 * >
 *   <ModalBody>
 *     Content goes here
 *   </ModalBody>
 *   <ModalFooter>
 *     <Button variant="outline" onClick={() => setIsOpen(false)}>
 *       Cancel
 *     </Button>
 *     <Button onClick={handleConfirm}>Confirm</Button>
 *   </ModalFooter>
 * </Modal>
 */
