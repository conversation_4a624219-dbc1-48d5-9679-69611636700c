import { useColorMode } from '@chakra-ui/react'
import { useState, useEffect } from 'react'

export const CustomAccordionIcon = ({ isOpen }: { isOpen: boolean }) => {
  const { colorMode } = useColorMode()
  const [isClient, setIsClient] = useState(false)
  
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  const color = isOpen
    ? 'var(--chakra-colors-komodo-green, #2ecc71)'
    : (isClient && colorMode === 'dark' ? '#888' : '#bbb')

  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      style={{
        transition: 'transform 0.2s cubic-bezier(.4,0,.2,1)',
        transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)',
        display: 'block',
      }}
      fill="none"
      stroke={color}
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
      suppressHydrationWarning
    >
      <polyline points="7 8 10 11 13 8" />
    </svg>
  )
}