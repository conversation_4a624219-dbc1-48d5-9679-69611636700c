'use client';

import { NextUIProvider as NextUIProviderOriginal } from '@nextui-org/react';
import { useEffect, useState } from 'react';
import nextUITheme from '../theme/nextui-theme';

export function NextUIProvider({ children }: { children: React.ReactNode }) {
  // 从localStorage获取存储的主题或默认为'light'
  const [themeMode, setThemeMode] = useState<'light' | 'dark'>('light');
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 标记为客户端渲染
    setIsClient(true);
    
    // 获取存储的主题
    const storedTheme = localStorage.getItem('theme-mode') as 'light' | 'dark' | null;

    // 如果有存储的主题，使用它
    if (storedTheme) {
      setThemeMode(storedTheme);
      document.documentElement.classList.toggle('dark', storedTheme === 'dark');
    }
    // 否则，检查系统偏好
    else {
      const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeMode(isDarkMode ? 'dark' : 'light');
      document.documentElement.classList.toggle('dark', isDarkMode);
    }

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      if (!localStorage.getItem('theme-mode')) {
        const newTheme = e.matches ? 'dark' : 'light';
        setThemeMode(newTheme);
        document.documentElement.classList.toggle('dark', e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return (
    <NextUIProviderOriginal>
      {children}
    </NextUIProviderOriginal>
  );
}
