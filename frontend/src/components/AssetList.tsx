'use client'

import { useState, useEffect } from 'react'
import { query } from '@/lib/db'
import { Asset } from '@/types/asset'
import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Monitor, MonitorOff, Settings } from 'lucide-react'
import { AssetForm } from './AssetForm'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'

export function AssetList() {
  const [assets, setAssets] = useState<Asset[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  useEffect(() => {
    loadAssets()
  }, [])

  const loadAssets = async () => {
    try {
      setLoading(true)
      const data = await query('SELECT * FROM assets ORDER BY created_at DESC')
      setAssets(data)
    } catch (err) {
      console.error('加载资产失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleEditMonitoring = (asset: Asset) => {
    setSelectedAsset(asset)
    setIsDialogOpen(true)
  }

  const handleSubmitSuccess = () => {
    setIsDialogOpen(false)
    loadAssets()
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">IT资产管理</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>添加资产</Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <AssetForm 
              initialData={selectedAsset} 
              onSubmit={handleSubmitSuccess}
            />
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <p>加载中...</p>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>名称</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>IP地址</TableHead>
              <TableHead>监控状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assets.map((asset) => (
              <TableRow key={asset.id}>
                <TableCell className="font-medium">{asset.name}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {asset.type === 'server' && '服务器'}
                    {asset.type === 'network' && '网络设备'}
                    {asset.type === 'printer' && '打印机'}
                    {asset.type === 'other' && '其他'}
                  </Badge>
                </TableCell>
                <TableCell>{asset.ipAddress}</TableCell>
                <TableCell>
                  {asset.monitoringConfig?.enabled ? (
                    <Badge className="gap-1">
                      <Monitor className="h-3 w-3" />
                      {asset.monitoringConfig.type.toUpperCase()}
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="gap-1">
                      <MonitorOff className="h-3 w-3" />
                      未监控
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleEditMonitoring(asset)}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    配置监控
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  )
}