'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Heading,
  Text,
  Flex,
  Button,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useDisclosure,
  Spinner,
  Center,
  Select,
  Input,
  FormControl,
  FormLabel,
  Stack,
  HStack,
  Divider,
  useToast,
} from '@chakra-ui/react'
import {
  Plus,
  PencilSimple,
  Trash,
  MagnifyingGlass,
  ArrowsClockwise,
  Warning,
} from '@phosphor-icons/react'
import {
  getSNMPItems,
  createSNMPItem,
  updateSNMPItem,
  deleteSNMPItem,
  SNMPItem,
  SNMPItemCreateRequest,
  SNMPItemUpdateRequest,
  createSNMPItemsBatch,
} from '@/services/snmpItemsService'
import { getMonitoringDevices } from '@/services/monitoringService'
import SNMPItemAddModal from './SNMPItemAddModal'
import SNMPItemEditModal from './SNMPItemEditModal'
import SNMPItemDeleteModal from './SNMPItemDeleteModal'

// SNMP监控配置管理页面
const SNMPConfigPage: React.FC = () => {
  // 状态
  const [items, setItems] = useState<SNMPItem[]>([]);
  const [devices, setDevices] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>('all');
  const [selectedItemType, setSelectedItemType] = useState<string>('all');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // 模态框状态
  const { isOpen: isAddModalOpen, onOpen: onAddModalOpen, onClose: onAddModalClose } = useDisclosure();
  const { isOpen: isEditModalOpen, onOpen: onEditModalOpen, onClose: onEditModalClose } = useDisclosure();
  const { isOpen: isDeleteModalOpen, onOpen: onDeleteModalOpen, onClose: onDeleteModalClose } = useDisclosure();

  // 当前选中的项
  const [currentItem, setCurrentItem] = useState<SNMPItem | null>(null);

  // Toast
  const toast = useToast();

  // 颜色
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 加载数据
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 获取SNMP监控项
      const itemsData = await getSNMPItems();
      setItems(itemsData);

      // 获取监控设备
      const devicesData = await getMonitoringDevices();
      setDevices(devicesData);

    } catch (err) {
      console.error('加载SNMP监控配置失败:', err);
      setError('加载数据失败，请稍后重试');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // 刷新数据
  const refreshData = () => {
    setIsRefreshing(true);
    loadData();
  };

  // 初始加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 过滤SNMP监控项
  const filteredItems = items.filter(item => {
    // 设备过滤
    if (selectedDeviceId !== 'all' && item.device_id !== selectedDeviceId) {
      return false;
    }

    // 类型过滤
    if (selectedItemType !== 'all' && item.item_type !== selectedItemType) {
      return false;
    }

    // 位置过滤
    if (selectedLocation !== 'all' && item.location !== selectedLocation) {
      return false;
    }

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        item.name.toLowerCase().includes(query) ||
        item.key.toLowerCase().includes(query) ||
        item.oid.toLowerCase().includes(query) ||
        (item.description && item.description.toLowerCase().includes(query))
      );
    }

    return true;
  });

  // 获取唯一的设备ID列表
  const deviceIds = ['all', ...new Set(items.map(item => item.device_id))];

  // 获取唯一的监控项类型列表
  const itemTypes = ['all', ...new Set(items.map(item => item.item_type))];

  // 获取唯一的位置列表
  const locations = ['all', ...new Set(items.map(item => item.location))];

  // 处理添加SNMP监控项
  const handleAddItem = async (newItem: SNMPItemCreateRequest) => {
    try {
      const createdItem = await createSNMPItem(newItem);
      setItems([...items, createdItem]);
      toast({
        title: '添加成功',
        description: `SNMP监控项 "${newItem.name}" 已成功添加`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      onAddModalClose();
    } catch (err) {
      console.error('添加SNMP监控项失败:', err);
      toast({
        title: '添加失败',
        description: '无法添加SNMP监控项，请稍后重试',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 处理编辑SNMP监控项
  const handleEditItem = async (id: number, updatedItem: SNMPItemUpdateRequest) => {
    try {
      const updated = await updateSNMPItem(id, updatedItem);
      setItems(items.map(item => item.id === id ? updated : item));
      toast({
        title: '更新成功',
        description: `SNMP监控项 "${updated.name}" 已成功更新`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      onEditModalClose();
    } catch (err) {
      console.error('更新SNMP监控项失败:', err);
      toast({
        title: '更新失败',
        description: '无法更新SNMP监控项，请稍后重试',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 处理删除SNMP监控项
  const handleDeleteItem = async (id: number) => {
    try {
      await deleteSNMPItem(id);
      setItems(items.filter(item => item.id !== id));
      toast({
        title: '删除成功',
        description: 'SNMP监控项已成功删除',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      onDeleteModalClose();
    } catch (err) {
      console.error('删除SNMP监控项失败:', err);
      toast({
        title: '删除失败',
        description: '无法删除SNMP监控项，请稍后重试',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 打开编辑模态框
  const openEditModal = (item: SNMPItem) => {
    setCurrentItem(item);
    onEditModalOpen();
  };

  // 打开删除模态框
  const openDeleteModal = (item: SNMPItem) => {
    setCurrentItem(item);
    onDeleteModalOpen();
  };

  return (
    <Box maxW="container.xl" mx="auto" py={8} px={4}>
      {/* 页面标题区域 */}
      <Box
        mb={8}
        p={6}
        borderRadius="xl"
        bg={bgColor}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          height="6px"
          bgGradient="linear(to-r, blue.400, teal.500)"
        />

        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>SNMP监控配置管理</Heading>
            <Text color="gray.500" fontSize="md">管理数据中心和弱电间的SNMP监控项配置</Text>
          </Box>
          <Flex mt={{ base: 4, md: 0 }} align="center" gap={4}>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="blue"
              size="md"
              onClick={onAddModalOpen}
            >
              添加监控项
            </Button>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="teal"
              size="md"
              isLoading={isRefreshing}
              loadingText="刷新中"
              onClick={refreshData}
            >
              刷新
            </Button>
          </Flex>
        </Flex>
      </Box>

      {/* 主要内容区域 */}
      {isLoading ? (
        <Center py={10}>
          <Spinner size="xl" color="teal.500" thickness="4px" />
        </Center>
      ) : error ? (
        <Box
          p={6}
          borderRadius="lg"
          bg="red.50"
          color="red.500"
          borderWidth="1px"
          borderColor="red.200"
          textAlign="center"
        >
          <Warning size={32} weight="bold" style={{ margin: '0 auto 16px' }} />
          <Heading size="md" mb={2}>{error}</Heading>
          <Button onClick={refreshData} colorScheme="red" size="sm" mt={4}>
            重试
          </Button>
        </Box>
      ) : (
        <Box>
          {/* 过滤器 */}
          <Flex
            mb={6}
            direction={{ base: 'column', md: 'row' }}
            gap={4}
            p={4}
            bg={bgColor}
            borderRadius="lg"
            boxShadow="sm"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <FormControl maxW={{ base: 'full', md: '200px' }}>
              <FormLabel fontSize="sm">设备</FormLabel>
              <Select
                value={selectedDeviceId}
                onChange={(e) => setSelectedDeviceId(e.target.value)}
                size="md"
              >
                <option value="all">所有设备</option>
                {deviceIds.filter(id => id !== 'all').map(id => (
                  <option key={id} value={id}>{id}</option>
                ))}
              </Select>
            </FormControl>

            <FormControl maxW={{ base: 'full', md: '200px' }}>
              <FormLabel fontSize="sm">监控项类型</FormLabel>
              <Select
                value={selectedItemType}
                onChange={(e) => setSelectedItemType(e.target.value)}
                size="md"
              >
                <option value="all">所有类型</option>
                {itemTypes.filter(type => type !== 'all').map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </Select>
            </FormControl>

            <FormControl maxW={{ base: 'full', md: '200px' }}>
              <FormLabel fontSize="sm">位置</FormLabel>
              <Select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                size="md"
              >
                <option value="all">所有位置</option>
                {locations.filter(loc => loc !== 'all').map(loc => (
                  <option key={loc} value={loc}>{loc}</option>
                ))}
              </Select>
            </FormControl>

            <FormControl maxW={{ base: 'full', md: '300px' }}>
              <FormLabel fontSize="sm">搜索</FormLabel>
              <Input
                placeholder="搜索监控项..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                size="md"
              />
            </FormControl>
          </Flex>

          {/* 监控项表格 */}
          <Box
            overflowX="auto"
            bg={bgColor}
            borderRadius="lg"
            boxShadow="sm"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>名称</Th>
                  <Th>设备ID</Th>
                  <Th>键值</Th>
                  <Th>OID</Th>
                  <Th>单位</Th>
                  <Th>类型</Th>
                  <Th>位置</Th>
                  <Th>状态</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredItems.length === 0 ? (
                  <Tr>
                    <Td colSpan={9} textAlign="center" py={6}>
                      <Text color="gray.500">暂无监控项数据</Text>
                    </Td>
                  </Tr>
                ) : (
                  filteredItems.map(item => (
                    <Tr key={item.id}>
                      <Td fontWeight="medium">{item.name}</Td>
                      <Td>{item.device_id}</Td>
                      <Td><code>{item.key}</code></Td>
                      <Td><code>{item.oid}</code></Td>
                      <Td>{item.unit || '-'}</Td>
                      <Td>{item.item_type}</Td>
                      <Td>
                        {item.location}
                        {item.position && <Text as="span" fontSize="sm" color="gray.500"> ({item.position})</Text>}
                      </Td>
                      <Td>
                        <Badge
                          colorScheme={item.status === 'active' ? 'green' : 'red'}
                          fontSize="0.8em"
                          px={2}
                          py={1}
                          borderRadius="full"
                        >
                          {item.status === 'active' ? '启用' : '禁用'}
                        </Badge>
                      </Td>
                      <Td>
                        <HStack spacing={2}>
                          <IconButton
                            aria-label="编辑"
                            icon={<PencilSimple weight="bold" />}
                            size="sm"
                            colorScheme="blue"
                            variant="ghost"
                            onClick={() => openEditModal(item)}
                          />
                          <IconButton
                            aria-label="删除"
                            icon={<Trash weight="bold" />}
                            size="sm"
                            colorScheme="red"
                            variant="ghost"
                            onClick={() => openDeleteModal(item)}
                          />
                        </HStack>
                      </Td>
                    </Tr>
                  ))
                )}
              </Tbody>
            </Table>
          </Box>
        </Box>
      )}

      {/* 添加模态框 */}
      <SNMPItemAddModal
        isOpen={isAddModalOpen}
        onClose={onAddModalClose}
        onAdd={handleAddItem}
        devices={devices}
      />

      {/* 编辑模态框 */}
      <SNMPItemEditModal
        isOpen={isEditModalOpen}
        onClose={onEditModalClose}
        onEdit={handleEditItem}
        item={currentItem}
      />

      {/* 删除确认模态框 */}
      <SNMPItemDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={onDeleteModalClose}
        onDelete={handleDeleteItem}
        item={currentItem}
      />
    </Box>
  );
};

export default SNMPConfigPage;
