'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Flex,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Card,
  CardHeader,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Select,
  Button,
  useColorModeValue,
  Divider,
  Spinner,
  Center,
} from '@chakra-ui/react'
import {
  Thermometer,
  Drop,
  Fire,
  Warning,
  ArrowsClockwise,
  Buildings,
  CaretDown,
  CaretUp,
} from '@phosphor-icons/react'
import {
  getEnvironmentData,
  getUPSData,
  EnvironmentData as ApiEnvironmentData,
  UPSData as ApiUPSData
} from '@/services/monitoringService'
import DatacenterTemperature from './DatacenterTemperature'

// 前端使用的环境数据类型
interface EnvironmentData {
  id: string;
  location: string;
  floor: string;
  temperature: number;
  humidity: number;
  smoke: boolean;
  water: boolean;
  lastUpdated: string;
  status: string;
  // 冷通道温度数据（可选）
  cold_aisle1_temp1?: number;
  cold_aisle1_temp2?: number;
  cold_aisle2_temp1?: number;
  cold_aisle2_temp2?: number;
}

// 前端使用的UPS数据类型
interface UPSData {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'warning';
  load: number;
  battery: number;
  inputVoltage: number;
  outputVoltage: number;
  temperature: number;
  lastUpdated: string;
}

// 将API数据转换为前端数据格式
const convertApiEnvironmentData = (apiData: ApiEnvironmentData[]): EnvironmentData[] => {
  return apiData.map(data => ({
    id: data.id.toString(),
    location: data.location,
    floor: data.floor,
    temperature: data.temperature,
    humidity: data.humidity,
    smoke: data.smoke,
    water: data.water,
    lastUpdated: new Date(data.timestamp).toLocaleString(),
    status: data.status,
    // 冷通道温度数据
    cold_aisle1_temp1: data.cold_aisle1_temp1,
    cold_aisle1_temp2: data.cold_aisle1_temp2,
    cold_aisle2_temp1: data.cold_aisle2_temp1,
    cold_aisle2_temp2: data.cold_aisle2_temp2
  }));
};

// 将API数据转换为前端UPS数据格式
const convertApiUPSData = (apiData: ApiUPSData[]): UPSData[] => {
  return apiData.map(data => ({
    id: data.id.toString(),
    name: data.name,
    status: data.status as 'online' | 'offline' | 'warning',
    load: data.load,
    battery: data.battery_level,
    inputVoltage: data.input_voltage,
    outputVoltage: data.output_voltage,
    temperature: data.temperature,
    lastUpdated: new Date(data.timestamp).toLocaleString()
  }));
};

const EnvironmentMonitoringPage = () => {
  const [environmentData, setEnvironmentData] = useState<EnvironmentData[]>([]);
  const [upsData, setUPSData] = useState<UPSData[]>([]);
  const [selectedFloor, setSelectedFloor] = useState<string>('all');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 加载数据
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 获取环境监控数据
      const envData = await getEnvironmentData();
      if (envData.length > 0) {
        setEnvironmentData(convertApiEnvironmentData(envData));
      }

      // 获取UPS监控数据
      const upsApiData = await getUPSData();
      if (upsApiData.length > 0) {
        setUPSData(convertApiUPSData(upsApiData));
      }

      setLastRefresh(new Date());
    } catch (err) {
      console.error('加载监控数据失败:', err);
      setError('加载数据失败，请稍后重试');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // 刷新数据
  const refreshData = () => {
    setIsRefreshing(true);
    loadData();
  };

  // 初始加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 5 * 60 * 1000); // 每5分钟刷新一次

    return () => clearInterval(interval);
  }, []);

  // 过滤环境数据
  const filteredEnvironmentData = selectedFloor === 'all'
    ? environmentData
    : environmentData.filter(data => data.floor === selectedFloor);

  return (
    <Box maxW="container.xl" mx="auto" py={8} px={4}>
      {/* 页面标题区域 */}
      <Box
        mb={8}
        p={6}
        borderRadius="xl"
        bg={bgColor}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          height="6px"
          bgGradient="linear(to-r, blue.400, teal.500)"
        />

        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>基础设施监控</Heading>
            <Text color="gray.500" fontSize="md">监控数据中心和弱电间的环境状态和UPS设备</Text>
          </Box>
          <Flex mt={{ base: 4, md: 0 }} align="center" gap={4}>
            <Text fontSize="sm" color="gray.500">
              最后更新: {lastRefresh.toLocaleTimeString()}
            </Text>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="teal"
              size="md"
              isLoading={isRefreshing}
              loadingText="刷新中"
              onClick={refreshData}
            >
              刷新
            </Button>
          </Flex>
        </Flex>
      </Box>

      {/* 主要内容区域 */}
      {isLoading ? (
        <Center py={10}>
          <Spinner size="xl" color="teal.500" thickness="4px" />
        </Center>
      ) : error ? (
        <Box
          p={6}
          borderRadius="lg"
          bg="red.50"
          color="red.500"
          borderWidth="1px"
          borderColor="red.200"
          textAlign="center"
        >
          <Icon as={Warning} boxSize={8} mb={4} />
          <Heading size="md" mb={2}>{error}</Heading>
          <Button onClick={refreshData} colorScheme="red" size="sm" mt={4}>
            重试
          </Button>
        </Box>
      ) : (
        <Tabs variant="soft-rounded" colorScheme="teal" mb={8}>
          <TabList mb={4}>
            <Tab>环境监控</Tab>
            <Tab>UPS监控</Tab>
          </TabList>

          <TabPanels>
            {/* 环境监控标签页 */}
            <TabPanel px={0}>
              <Flex justify="flex-end" mb={4}>
                <Select
                  value={selectedFloor}
                  onChange={(e) => setSelectedFloor(e.target.value)}
                  width="200px"
                  size="md"
                  borderRadius="md"
                >
                  <option value="all">所有楼层</option>
                  <option value="2F">2F 弱电间</option>
                  <option value="3F">3F 数据中心</option>
                  <option value="4F">4F 弱电间</option>
                  <option value="7F">7F 弱电间</option>
                </Select>
              </Flex>

              {filteredEnvironmentData.length === 0 ? (
                <Box p={6} textAlign="center" color="gray.500">
                  <Text mb={4}>暂无环境监控数据</Text>
                  <Button onClick={refreshData} colorScheme="teal" size="sm">
                    刷新
                  </Button>
                </Box>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  {filteredEnvironmentData.map((data) => (
                    data.location === "数据中心" && data.cold_aisle1_temp1 ? (
                      // 使用数据中心温度组件显示
                      <DatacenterTemperature key={data.id} data={data} />
                    ) : (
                      // 使用普通环境监控卡片显示
                      <Card key={data.id} borderRadius="lg" boxShadow="md" overflow="hidden">
                        <CardHeader bg={useColorModeValue('gray.50', 'gray.700')} py={4} px={6}>
                          <Flex justify="space-between" align="center">
                            <Flex align="center">
                              <Icon as={Buildings} mr={2} boxSize={5} color="teal.500" />
                              <Heading size="md">{data.floor} {data.location}</Heading>
                            </Flex>
                            <Badge
                              colorScheme={data.status === 'normal' ? 'green' : data.status === 'warning' ? 'yellow' : 'red'}
                              fontSize="0.8em"
                              px={2}
                              py={1}
                              borderRadius="full"
                            >
                              {data.status === 'normal' ? '正常' : data.status === 'warning' ? '警告' : '异常'}
                            </Badge>
                          </Flex>
                        </CardHeader>
                        <CardBody p={6}>
                          <SimpleGrid columns={2} spacing={6} mb={4}>
                            {/* 温度 */}
                            <Stat>
                              <Flex align="center" mb={2}>
                                <Icon as={Thermometer} color="orange.500" mr={2} />
                                <StatLabel>温度</StatLabel>
                              </Flex>
                              <StatNumber>{data.temperature}°C</StatNumber>
                              <StatHelpText>
                                <Flex align="center">
                                  <Icon as={data.temperature > 23 ? CaretUp : CaretDown} color={data.temperature > 23 ? "red.500" : "green.500"} mr={1} />
                                  <Text color={data.temperature > 23 ? "red.500" : "green.500"}>
                                    {Math.abs(data.temperature - 23).toFixed(1)}°C {data.temperature > 23 ? "高于" : "低于"} 标准
                                  </Text>
                                </Flex>
                              </StatHelpText>
                            </Stat>

                            {/* 湿度 */}
                            <Stat>
                              <Flex align="center" mb={2}>
                                <Icon as={Drop} color="blue.500" mr={2} />
                                <StatLabel>湿度</StatLabel>
                              </Flex>
                              <StatNumber>{data.humidity}%</StatNumber>
                              <StatHelpText>
                                <Flex align="center">
                                  <Icon as={data.humidity > 50 ? CaretUp : CaretDown} color={data.humidity > 60 || data.humidity < 40 ? "red.500" : "green.500"} mr={1} />
                                  <Text color={data.humidity > 60 || data.humidity < 40 ? "red.500" : "green.500"}>
                                    {data.humidity > 50 ? "偏高" : "正常"} 范围
                                  </Text>
                                </Flex>
                              </StatHelpText>
                            </Stat>
                          </SimpleGrid>

                          <Divider my={4} />

                          <SimpleGrid columns={2} spacing={6}>
                            {/* 烟感 */}
                            <Stat>
                              <Flex align="center" mb={2}>
                                <Icon as={Fire} color="red.500" mr={2} />
                                <StatLabel>烟感</StatLabel>
                              </Flex>
                              <StatNumber color={data.smoke ? "red.500" : "green.500"}>
                                {data.smoke ? "告警" : "正常"}
                              </StatNumber>
                              <StatHelpText>
                                最后检测: {data.lastUpdated.split(' ')[1]}
                              </StatHelpText>
                            </Stat>

                            {/* 水浸 */}
                            <Stat>
                              <Flex align="center" mb={2}>
                                <Icon as={Drop} color="blue.500" mr={2} />
                                <StatLabel>水浸</StatLabel>
                              </Flex>
                              <StatNumber color={data.water ? "red.500" : "green.500"}>
                                {data.water ? "告警" : "正常"}
                              </StatNumber>
                              <StatHelpText>
                                最后检测: {data.lastUpdated.split(' ')[1]}
                              </StatHelpText>
                            </Stat>
                          </SimpleGrid>
                        </CardBody>
                      </Card>
                    )
                  ))}
                </SimpleGrid>
              )}
            </TabPanel>

            {/* UPS监控标签页 */}
            <TabPanel px={0}>
              {upsData.length === 0 ? (
                <Box p={6} textAlign="center" color="gray.500">
                  <Text mb={4}>暂无UPS监控数据</Text>
                  <Button onClick={refreshData} colorScheme="teal" size="sm">
                    刷新
                  </Button>
                </Box>
              ) : (
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  {upsData.map((ups) => (
                    <Card key={ups.id} borderRadius="lg" boxShadow="md" overflow="hidden">
                      <CardHeader bg={useColorModeValue('gray.50', 'gray.700')} py={4} px={6}>
                        <Flex justify="space-between" align="center">
                          <Heading size="md">{ups.name}</Heading>
                          <Badge
                            colorScheme={ups.status === 'online' ? 'green' : ups.status === 'warning' ? 'yellow' : 'red'}
                            fontSize="0.8em"
                            px={2}
                            py={1}
                            borderRadius="full"
                          >
                            {ups.status === 'online' ? '在线' : ups.status === 'warning' ? '警告' : '离线'}
                          </Badge>
                        </Flex>
                      </CardHeader>
                      <CardBody p={6}>
                        <SimpleGrid columns={2} spacing={6} mb={4}>
                          {/* 负载 */}
                          <Stat>
                            <StatLabel>负载</StatLabel>
                            <StatNumber>{ups.load}%</StatNumber>
                            <StatHelpText color={ups.load > 70 ? "orange.500" : "green.500"}>
                              {ups.load > 70 ? "负载较高" : "正常范围"}
                            </StatHelpText>
                          </Stat>

                          {/* 电池 */}
                          <Stat>
                            <StatLabel>电池电量</StatLabel>
                            <StatNumber>{ups.battery}%</StatNumber>
                            <StatHelpText color={ups.battery < 90 ? "orange.500" : "green.500"}>
                              {ups.battery < 90 ? "需要关注" : "正常范围"}
                            </StatHelpText>
                          </Stat>
                        </SimpleGrid>

                        <Divider my={4} />

                        <SimpleGrid columns={2} spacing={6} mb={4}>
                          {/* 输入电压 */}
                          <Stat>
                            <StatLabel>输入电压</StatLabel>
                            <StatNumber>{ups.inputVoltage}V</StatNumber>
                            <StatHelpText color={ups.inputVoltage < 210 || ups.inputVoltage > 230 ? "orange.500" : "green.500"}>
                              {ups.inputVoltage < 210 || ups.inputVoltage > 230 ? "波动" : "稳定"}
                            </StatHelpText>
                          </Stat>

                          {/* 输出电压 */}
                          <Stat>
                            <StatLabel>输出电压</StatLabel>
                            <StatNumber>{ups.outputVoltage}V</StatNumber>
                            <StatHelpText color="green.500">
                              稳定
                            </StatHelpText>
                          </Stat>
                        </SimpleGrid>

                        <Divider my={4} />

                        <Flex justify="space-between" align="center">
                          <Stat>
                            <StatLabel>温度</StatLabel>
                            <StatNumber>{ups.temperature}°C</StatNumber>
                            <StatHelpText color={ups.temperature > 40 ? "red.500" : "green.500"}>
                              {ups.temperature > 40 ? "温度过高" : "正常范围"}
                            </StatHelpText>
                          </Stat>

                          <Button colorScheme="blue" size="sm">
                            详细信息
                          </Button>
                        </Flex>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              )}
            </TabPanel>
          </TabPanels>
        </Tabs>
      )}
    </Box>
  );
};

export default EnvironmentMonitoringPage;
