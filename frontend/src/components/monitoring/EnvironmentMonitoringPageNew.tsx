'use client'

import React, { useState, useEffect } from 'react'
import {
  Thermometer,
  Drop,
  Fire,
  Warning,
  ArrowClockwise,
  Buildings,
  Lightning,
  Plugs,
  Power,
  Gauge
} from '@phosphor-icons/react'
import { <PERSON>Container, PageHeader, RefreshButton } from '@/components/ui/page-container'
import { StatCard } from '@/components/ui/stat-card'
import { DashboardCard } from '@/components/ui/dashboard-card'
import { MetricProgress } from '@/components/ui/metric-progress'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  getEnvironmentData,
  getUPSData,
  EnvironmentData as ApiEnvironmentData,
  UPSData as ApiUPSData
} from '@/services/monitoringService'

// 前端使用的环境数据类型
interface EnvironmentData {
  id: string;
  location: string;
  floor: string;
  temperature: number;
  humidity: number;
  smoke: boolean;
  water: boolean;
  lastUpdated: string;
  status: string;
  // 冷通道温度数据（可选）
  cold_aisle1_temp1?: number;
  cold_aisle1_temp2?: number;
  cold_aisle2_temp1?: number;
  cold_aisle2_temp2?: number;
}

// 前端使用的UPS数据类型
interface UPSData {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'warning';
  load: number;
  battery: number;
  inputVoltage: number;
  outputVoltage: number;
  temperature: number;
  lastUpdated: string;
}

// 将API数据转换为前端数据格式
const convertApiEnvironmentData = (apiData: ApiEnvironmentData[]): EnvironmentData[] => {
  return apiData.map(data => ({
    id: data.id.toString(),
    location: data.location,
    floor: data.floor,
    temperature: data.temperature,
    humidity: data.humidity,
    smoke: data.smoke,
    water: data.water,
    lastUpdated: new Date(data.timestamp).toLocaleString(),
    status: data.status,
    // 冷通道温度数据
    cold_aisle1_temp1: data.cold_aisle1_temp1,
    cold_aisle1_temp2: data.cold_aisle1_temp2,
    cold_aisle2_temp1: data.cold_aisle2_temp1,
    cold_aisle2_temp2: data.cold_aisle2_temp2
  }));
};

// 将API数据转换为前端UPS数据格式
const convertApiUPSData = (apiData: ApiUPSData[]): UPSData[] => {
  return apiData.map(data => ({
    id: data.id.toString(),
    name: data.name,
    status: data.status as 'online' | 'offline' | 'warning',
    load: data.load,
    battery: data.battery_level,
    inputVoltage: data.input_voltage,
    outputVoltage: data.output_voltage,
    temperature: data.temperature,
    lastUpdated: new Date(data.timestamp).toLocaleString()
  }));
};

// 模拟环境数据
const mockEnvironmentData: EnvironmentData[] = [
  {
    id: '1',
    location: '数据中心',
    floor: '3F',
    temperature: 22.5,
    humidity: 45,
    smoke: false,
    water: false,
    lastUpdated: '2023-06-15 14:30:00',
    status: 'normal',
    cold_aisle1_temp1: 19.2,
    cold_aisle1_temp2: 19.8,
    cold_aisle2_temp1: 20.1,
    cold_aisle2_temp2: 20.5
  },
  {
    id: '2',
    location: '弱电间',
    floor: '2F',
    temperature: 24.8,
    humidity: 52,
    smoke: false,
    water: false,
    lastUpdated: '2023-06-15 14:30:00',
    status: 'normal'
  },
  {
    id: '3',
    location: '弱电间',
    floor: '4F',
    temperature: 26.2,
    humidity: 58,
    smoke: false,
    water: false,
    lastUpdated: '2023-06-15 14:30:00',
    status: 'warning'
  },
  {
    id: '4',
    location: '弱电间',
    floor: '7F',
    temperature: 23.5,
    humidity: 48,
    smoke: false,
    water: false,
    lastUpdated: '2023-06-15 14:30:00',
    status: 'normal'
  }
];

// 模拟UPS数据
const mockUPSData: UPSData[] = [
  {
    id: '1',
    name: 'UPS-01 (数据中心)',
    status: 'online',
    load: 65,
    battery: 95,
    inputVoltage: 220,
    outputVoltage: 220,
    temperature: 35,
    lastUpdated: '2023-06-15 14:30:00'
  },
  {
    id: '2',
    name: 'UPS-02 (数据中心)',
    status: 'online',
    load: 72,
    battery: 92,
    inputVoltage: 218,
    outputVoltage: 220,
    temperature: 38,
    lastUpdated: '2023-06-15 14:30:00'
  },
  {
    id: '3',
    name: 'UPS-03 (2F弱电间)',
    status: 'warning',
    load: 85,
    battery: 78,
    inputVoltage: 215,
    outputVoltage: 220,
    temperature: 42,
    lastUpdated: '2023-06-15 14:30:00'
  },
  {
    id: '4',
    name: 'UPS-04 (4F弱电间)',
    status: 'online',
    load: 45,
    battery: 98,
    inputVoltage: 220,
    outputVoltage: 220,
    temperature: 32,
    lastUpdated: '2023-06-15 14:30:00'
  }
];

const EnvironmentMonitoringPageNew = () => {
  const [environmentData, setEnvironmentData] = useState<EnvironmentData[]>(mockEnvironmentData);
  const [upsData, setUPSData] = useState<UPSData[]>(mockUPSData);
  const [selectedFloor, setSelectedFloor] = useState<string>('all');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('environment');

  // 加载数据
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 在实际应用中，这里应该调用API获取数据
      // const envData = await getEnvironmentData();
      // if (envData.length > 0) {
      //   setEnvironmentData(convertApiEnvironmentData(envData));
      // }

      // const upsApiData = await getUPSData();
      // if (upsApiData.length > 0) {
      //   setUPSData(convertApiUPSData(upsApiData));
      // }

      // 模拟API调用延迟
      setTimeout(() => {
        setEnvironmentData(mockEnvironmentData);
        setUPSData(mockUPSData);
        setLastRefresh(new Date());
        setIsLoading(false);
        setIsRefreshing(false);
      }, 1000);
    } catch (err) {
      console.error('加载监控数据失败:', err);
      setError('加载数据失败，请稍后重试');
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // 刷新数据
  const refreshData = () => {
    setIsRefreshing(true);
    loadData();
  };

  // 初始加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 过滤环境数据
  const filteredEnvironmentData = selectedFloor === 'all'
    ? environmentData
    : environmentData.filter(data => data.floor === selectedFloor);

  // 获取环境状态统计
  const getEnvironmentStats = () => {
    const total = environmentData.length;
    const normal = environmentData.filter(data => data.status === 'normal').length;
    const warning = environmentData.filter(data => data.status === 'warning').length;
    const critical = environmentData.filter(data => data.status === 'critical').length;

    return { total, normal, warning, critical };
  };

  // 获取UPS状态统计
  const getUPSStats = () => {
    const total = upsData.length;
    const online = upsData.filter(data => data.status === 'online').length;
    const warning = upsData.filter(data => data.status === 'warning').length;
    const offline = upsData.filter(data => data.status === 'offline').length;

    return { total, online, warning, offline };
  };

  const stats = getEnvironmentStats();
  const upsStats = getUPSStats();

  // 判断温度是否在正常范围内
  const isTemperatureNormal = (temp: number) => {
    return temp >= 18 && temp <= 27
  }

  // 获取温度状态文本
  const getTemperatureStatus = (temp: number) => {
    if (temp < 18) return "偏低"
    if (temp > 27) return "偏高"
    return "正常"
  }

  return (
    <PageContainer>
      <PageHeader
        title="基础设施监控"
        description="监控数据中心和弱电间的环境状态和UPS设备"
      >
        <div className="flex items-center gap-3">
          <div className="text-sm text-muted-foreground">
            最后更新: {lastRefresh.toLocaleTimeString()}
          </div>
          <RefreshButton
            onClick={refreshData}
            isLoading={isRefreshing}
          />
        </div>
      </PageHeader>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="环境监控点"
          value={stats.total}
          icon={<Thermometer size={20} weight="fill" />}
          helpText={`${stats.normal} 正常`}
          colorScheme="blue"
        />

        <StatCard
          title="UPS设备"
          value={upsStats.total}
          icon={<Power size={20} weight="fill" />}
          helpText={`${upsStats.online} 在线`}
          colorScheme="green"
        />

        <StatCard
          title="平均温度"
          value={`${(environmentData.reduce((sum, data) => sum + data.temperature, 0) / environmentData.length).toFixed(1)}°C`}
          icon={<Thermometer size={20} weight="fill" />}
          colorScheme="amber"
        />

        <StatCard
          title="告警数量"
          value={stats.warning + stats.critical + upsStats.warning + upsStats.offline}
          icon={<Warning size={20} weight="fill" />}
          colorScheme="red"
        />
      </div>

      {/* 主要内容区域 */}
      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <DashboardCard
          title="加载错误"
          icon={<Warning size={20} weight="fill" />}
          className="bg-red-50 dark:bg-red-900/20 border-red-100 dark:border-red-800/30"
        >
          <div className="text-center py-8">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mb-4">
              <Warning size={32} weight="fill" />
            </div>
            <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 mb-2">{error}</h3>
            <Button onClick={refreshData} variant="outline" className="mt-2">
              重试
            </Button>
          </div>
        </DashboardCard>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="mb-6">
            <TabsTrigger value="environment">环境监控</TabsTrigger>
            <TabsTrigger value="ups">UPS监控</TabsTrigger>
          </TabsList>

          <TabsContent value="environment">
            <div className="flex justify-end mb-6">
              <Select value={selectedFloor} onValueChange={setSelectedFloor}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="选择楼层" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有楼层</SelectItem>
                  <SelectItem value="2F">2F 弱电间</SelectItem>
                  <SelectItem value="3F">3F 数据中心</SelectItem>
                  <SelectItem value="4F">4F 弱电间</SelectItem>
                  <SelectItem value="7F">7F 弱电间</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {filteredEnvironmentData.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <p className="mb-4">暂无环境监控数据</p>
                <Button onClick={refreshData} variant="outline">
                  刷新
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredEnvironmentData.map((data) => (
                  data.location === "数据中心" && data.cold_aisle1_temp1 ? (
                    // 数据中心温度卡片
                    <DashboardCard
                      key={data.id}
                      title={`${data.floor} ${data.location}`}
                      icon={<Buildings size={20} weight="fill" />}
                      headerClassName="border-b pb-4"
                    >
                      <div className="space-y-6">
                        {/* 主温度显示 */}
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="flex items-center text-muted-foreground mb-1">
                              <Thermometer size={16} className="mr-1" />
                              <span>平均温度</span>
                            </div>
                            <div className="text-3xl font-bold">{data.temperature}°C</div>
                          </div>
                          <Badge className={
                            isTemperatureNormal(data.temperature)
                              ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                              : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                          }>
                            {getTemperatureStatus(data.temperature)}
                          </Badge>
                        </div>

                        <div className="border-t pt-4">
                          <h3 className="font-medium mb-3">冷通道温度详情</h3>
                          <div className="grid grid-cols-2 gap-4">
                            {/* 冷通道1-温度01 */}
                            {data.cold_aisle1_temp1 !== undefined && (
                              <div className="bg-slate-50 dark:bg-slate-900 p-3 rounded-lg">
                                <div className="flex items-center mb-1 text-sm text-muted-foreground">
                                  <Thermometer size={14} className="mr-1" />
                                  <span>冷通道1-温度01</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xl font-semibold">{data.cold_aisle1_temp1}°C</span>
                                  <Badge className={
                                    isTemperatureNormal(data.cold_aisle1_temp1)
                                      ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                      : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                                  }>
                                    {getTemperatureStatus(data.cold_aisle1_temp1)}
                                  </Badge>
                                </div>
                              </div>
                            )}

                            {/* 冷通道1-温度02 */}
                            {data.cold_aisle1_temp2 !== undefined && (
                              <div className="bg-slate-50 dark:bg-slate-900 p-3 rounded-lg">
                                <div className="flex items-center mb-1 text-sm text-muted-foreground">
                                  <Thermometer size={14} className="mr-1" />
                                  <span>冷通道1-温度02</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xl font-semibold">{data.cold_aisle1_temp2}°C</span>
                                  <Badge className={
                                    isTemperatureNormal(data.cold_aisle1_temp2)
                                      ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                      : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                                  }>
                                    {getTemperatureStatus(data.cold_aisle1_temp2)}
                                  </Badge>
                                </div>
                              </div>
                            )}

                            {/* 冷通道2-温度01 */}
                            {data.cold_aisle2_temp1 !== undefined && (
                              <div className="bg-slate-50 dark:bg-slate-900 p-3 rounded-lg">
                                <div className="flex items-center mb-1 text-sm text-muted-foreground">
                                  <Thermometer size={14} className="mr-1" />
                                  <span>冷通道2-温度01</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xl font-semibold">{data.cold_aisle2_temp1}°C</span>
                                  <Badge className={
                                    isTemperatureNormal(data.cold_aisle2_temp1)
                                      ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                      : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                                  }>
                                    {getTemperatureStatus(data.cold_aisle2_temp1)}
                                  </Badge>
                                </div>
                              </div>
                            )}

                            {/* 冷通道2-温度02 */}
                            {data.cold_aisle2_temp2 !== undefined && (
                              <div className="bg-slate-50 dark:bg-slate-900 p-3 rounded-lg">
                                <div className="flex items-center mb-1 text-sm text-muted-foreground">
                                  <Thermometer size={14} className="mr-1" />
                                  <span>冷通道2-温度02</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xl font-semibold">{data.cold_aisle2_temp2}°C</span>
                                  <Badge className={
                                    isTemperatureNormal(data.cold_aisle2_temp2)
                                      ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                      : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"
                                  }>
                                    {getTemperatureStatus(data.cold_aisle2_temp2)}
                                  </Badge>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="text-xs text-muted-foreground mt-4">
                          最后更新: {data.lastUpdated}
                        </div>
                      </div>
                    </DashboardCard>
                  ) : (
                    // 普通环境监控卡片
                    <DashboardCard
                      key={data.id}
                      title={`${data.floor} ${data.location}`}
                      icon={<Buildings size={20} weight="fill" />}
                      headerClassName="border-b pb-4"
                    >
                      <div className="space-y-6">
                        <div className="grid grid-cols-2 gap-6">
                          {/* 温度 */}
                          <div>
                            <div className="flex items-center text-muted-foreground mb-2">
                              <Thermometer size={16} className="mr-1 text-orange-500" />
                              <span>温度</span>
                            </div>
                            <div className="text-3xl font-bold">{data.temperature}°C</div>
                            <div className={
                              isTemperatureNormal(data.temperature)
                                ? "text-green-600 dark:text-green-400 text-sm mt-1"
                                : "text-red-600 dark:text-red-400 text-sm mt-1"
                            }>
                              {getTemperatureStatus(data.temperature)}
                            </div>
                          </div>

                          {/* 湿度 */}
                          <div>
                            <div className="flex items-center text-muted-foreground mb-2">
                              <Drop size={16} className="mr-1 text-blue-500" />
                              <span>湿度</span>
                            </div>
                            <div className="text-3xl font-bold">{data.humidity}%</div>
                            <div className={
                              data.humidity > 60 || data.humidity < 40
                                ? "text-amber-600 dark:text-amber-400 text-sm mt-1"
                                : "text-green-600 dark:text-green-400 text-sm mt-1"
                            }>
                              {data.humidity > 60 ? "偏高" : data.humidity < 40 ? "偏低" : "正常"}
                            </div>
                          </div>
                        </div>

                        <div className="border-t pt-4">
                          <div className="grid grid-cols-2 gap-6">
                            {/* 烟感 */}
                            <div>
                              <div className="flex items-center text-muted-foreground mb-2">
                                <Fire size={16} className="mr-1 text-red-500" />
                                <span>烟感</span>
                              </div>
                              <div className={
                                data.smoke
                                  ? "text-xl font-bold text-red-600 dark:text-red-400"
                                  : "text-xl font-bold text-green-600 dark:text-green-400"
                              }>
                                {data.smoke ? "告警" : "正常"}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                最后检测: {data.lastUpdated.split(' ')[1]}
                              </div>
                            </div>

                            {/* 水浸 */}
                            <div>
                              <div className="flex items-center text-muted-foreground mb-2">
                                <Drop size={16} className="mr-1 text-blue-500" />
                                <span>水浸</span>
                              </div>
                              <div className={
                                data.water
                                  ? "text-xl font-bold text-red-600 dark:text-red-400"
                                  : "text-xl font-bold text-green-600 dark:text-green-400"
                              }>
                                {data.water ? "告警" : "正常"}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                最后检测: {data.lastUpdated.split(' ')[1]}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="text-xs text-muted-foreground mt-4">
                          最后更新: {data.lastUpdated}
                        </div>
                      </div>
                    </DashboardCard>
                  )
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="ups">
            {upsData.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <p className="mb-4">暂无UPS监控数据</p>
                <Button onClick={refreshData} variant="outline">
                  刷新
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {upsData.map((ups) => (
                  <DashboardCard
                    key={ups.id}
                    title={ups.name}
                    icon={<Battery size={20} weight="fill" />}
                    headerClassName="border-b pb-4"
                  >
                    <div className="space-y-6">
                      <div className="grid grid-cols-2 gap-6">
                        {/* 负载 */}
                        <div>
                          <div className="text-muted-foreground mb-2">负载</div>
                          <div className="text-3xl font-bold">{ups.load}%</div>
                          <div className={
                            ups.load > 70
                              ? "text-amber-600 dark:text-amber-400 text-sm mt-1"
                              : "text-green-600 dark:text-green-400 text-sm mt-1"
                          }>
                            {ups.load > 70 ? "负载较高" : "正常范围"}
                          </div>
                        </div>

                        {/* 电池 */}
                        <div>
                          <div className="text-muted-foreground mb-2">电池电量</div>
                          <div className="text-3xl font-bold">{ups.battery}%</div>
                          <div className={
                            ups.battery < 90
                              ? "text-amber-600 dark:text-amber-400 text-sm mt-1"
                              : "text-green-600 dark:text-green-400 text-sm mt-1"
                          }>
                            {ups.battery < 90 ? "需要关注" : "正常范围"}
                          </div>
                        </div>
                      </div>

                      <div className="border-t pt-4">
                        <div className="grid grid-cols-2 gap-6">
                          {/* 输入电压 */}
                          <div>
                            <div className="text-muted-foreground mb-2">输入电压</div>
                            <div className="text-3xl font-bold">{ups.inputVoltage}V</div>
                            <div className={
                              ups.inputVoltage < 210 || ups.inputVoltage > 230
                                ? "text-amber-600 dark:text-amber-400 text-sm mt-1"
                                : "text-green-600 dark:text-green-400 text-sm mt-1"
                            }>
                              {ups.inputVoltage < 210 || ups.inputVoltage > 230 ? "波动" : "稳定"}
                            </div>
                          </div>

                          {/* 输出电压 */}
                          <div>
                            <div className="text-muted-foreground mb-2">输出电压</div>
                            <div className="text-3xl font-bold">{ups.outputVoltage}V</div>
                            <div className="text-green-600 dark:text-green-400 text-sm mt-1">
                              稳定
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="border-t pt-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="text-muted-foreground mb-2">温度</div>
                            <div className="text-3xl font-bold">{ups.temperature}°C</div>
                            <div className={
                              ups.temperature > 40
                                ? "text-red-600 dark:text-red-400 text-sm mt-1"
                                : "text-green-600 dark:text-green-400 text-sm mt-1"
                            }>
                              {ups.temperature > 40 ? "温度过高" : "正常范围"}
                            </div>
                          </div>

                          <Button variant="outline">
                            详细信息
                          </Button>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground mt-4">
                        最后更新: {ups.lastUpdated}
                      </div>
                    </div>
                  </DashboardCard>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </PageContainer>
  );
};

export default EnvironmentMonitoringPageNew;
