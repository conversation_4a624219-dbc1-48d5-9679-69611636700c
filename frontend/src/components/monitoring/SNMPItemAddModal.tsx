'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>dalOverlay,
  ModalContent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  VStack,
  HStack,
  Divider,
  Text,
  useToast,
} from '@chakra-ui/react'
import { SNMPItemCreateRequest } from '@/services/snmpItemsService'

interface SNMPItemAddModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: SNMPItemCreateRequest) => void;
  devices: any[];
}

const SNMPItemAddModal: React.FC<SNMPItemAddModalProps> = ({ isOpen, onClose, onAdd, devices }) => {
  // 初始状态
  const initialState: SNMPItemCreateRequest = {
    device_id: '',
    name: '',
    key: '',
    oid: '',
    unit: '',
    item_type: 'temperature',
    location: 'cold_aisle1',
    position: '',
    description: '',
    status: 'active',
  };
  
  // 状态
  const [formData, setFormData] = useState<SNMPItemCreateRequest>(initialState);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // Toast
  const toast = useToast();
  
  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // 处理提交
  const handleSubmit = async () => {
    // 验证必填字段
    if (!formData.device_id || !formData.name || !formData.key || !formData.oid || !formData.item_type || !formData.location) {
      toast({
        title: '表单不完整',
        description: '请填写所有必填字段',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      await onAdd(formData);
      // 重置表单
      setFormData(initialState);
    } catch (error) {
      console.error('添加SNMP监控项失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 处理关闭
  const handleClose = () => {
    setFormData(initialState);
    onClose();
  };
  
  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>添加SNMP监控项</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text fontWeight="medium" color="gray.600">基本信息</Text>
            
            <FormControl isRequired>
              <FormLabel>设备ID</FormLabel>
              <Select
                name="device_id"
                value={formData.device_id}
                onChange={handleChange}
                placeholder="选择设备"
              >
                {devices.map(device => (
                  <option key={device.device_id} value={device.device_id}>
                    {device.name} ({device.device_id})
                  </option>
                ))}
              </Select>
            </FormControl>
            
            <HStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>监控项名称</FormLabel>
                <Input
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="例如：冷通道1-温度01"
                />
              </FormControl>
              
              <FormControl isRequired>
                <FormLabel>键值</FormLabel>
                <Input
                  name="key"
                  value={formData.key}
                  onChange={handleChange}
                  placeholder="例如：cold_aisle1_temp1"
                />
              </FormControl>
            </HStack>
            
            <FormControl isRequired>
              <FormLabel>SNMP OID</FormLabel>
              <Input
                name="oid"
                value={formData.oid}
                onChange={handleChange}
                placeholder="例如：1.3.6.1.4.1.1.268435795.2.0"
              />
            </FormControl>
            
            <HStack spacing={4}>
              <FormControl>
                <FormLabel>单位</FormLabel>
                <Input
                  name="unit"
                  value={formData.unit || ''}
                  onChange={handleChange}
                  placeholder="例如：℃"
                />
              </FormControl>
              
              <FormControl isRequired>
                <FormLabel>监控项类型</FormLabel>
                <Select
                  name="item_type"
                  value={formData.item_type}
                  onChange={handleChange}
                >
                  <option value="temperature">温度</option>
                  <option value="humidity">湿度</option>
                  <option value="smoke">烟感</option>
                  <option value="water">水浸</option>
                </Select>
              </FormControl>
            </HStack>
            
            <Divider />
            
            <Text fontWeight="medium" color="gray.600">位置信息</Text>
            
            <HStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>位置</FormLabel>
                <Select
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                >
                  <option value="cold_aisle1">冷通道1</option>
                  <option value="cold_aisle2">冷通道2</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel>具体位置</FormLabel>
                <Input
                  name="position"
                  value={formData.position || ''}
                  onChange={handleChange}
                  placeholder="例如：01"
                />
              </FormControl>
            </HStack>
            
            <FormControl>
              <FormLabel>描述</FormLabel>
              <Textarea
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                placeholder="监控项描述..."
                rows={3}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel>状态</FormLabel>
              <Select
                name="status"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </Select>
            </FormControl>
          </VStack>
        </ModalBody>
        
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleClose}>
            取消
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={handleSubmit}
            isLoading={isSubmitting}
            loadingText="添加中"
          >
            添加
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SNMPItemAddModal;
