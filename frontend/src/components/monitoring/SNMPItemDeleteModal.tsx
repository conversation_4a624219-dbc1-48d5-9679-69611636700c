'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  Modal<PERSON><PERSON>lay,
  Modal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON>er,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Text,
  Box,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react'
import { SNMPItem } from '@/services/snmpItemsService'

interface SNMPItemDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: (id: number) => void;
  item: SNMPItem | null;
}

const SNMPItemDeleteModal: React.FC<SNMPItemDeleteModalProps> = ({ isOpen, onClose, onDelete, item }) => {
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  
  // 处理删除
  const handleDelete = async () => {
    if (!item) return;
    
    setIsDeleting(true);
    try {
      await onDelete(item.id);
    } catch (error) {
      console.error('删除SNMP监控项失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };
  
  if (!item) return null;
  
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>删除SNMP监控项</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Alert status="error" borderRadius="md" mb={4}>
            <AlertIcon />
            <Box>
              <AlertTitle>确认删除</AlertTitle>
              <AlertDescription>
                此操作无法撤销，请确认是否要删除此监控项。
              </AlertDescription>
            </Box>
          </Alert>
          
          <Text mb={4}>
            您即将删除以下SNMP监控项：
          </Text>
          
          <Box p={4} bg="gray.50" borderRadius="md">
            <Text><strong>名称：</strong> {item.name}</Text>
            <Text><strong>设备ID：</strong> {item.device_id}</Text>
            <Text><strong>键值：</strong> {item.key}</Text>
            <Text><strong>OID：</strong> {item.oid}</Text>
            <Text><strong>位置：</strong> {item.location} {item.position ? `(${item.position})` : ''}</Text>
          </Box>
        </ModalBody>
        
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button 
            colorScheme="red" 
            onClick={handleDelete}
            isLoading={isDeleting}
            loadingText="删除中"
          >
            删除
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SNMPItemDeleteModal;
