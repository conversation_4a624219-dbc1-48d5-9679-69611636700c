'use client'

import React, { useState, useEffect } from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  VStack,
  HStack,
  Divider,
  Text,
  useToast,
} from '@chakra-ui/react'
import { SNMPItem, SNMPItemUpdateRequest } from '@/services/snmpItemsService'

interface SNMPItemEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (id: number, item: SNMPItemUpdateRequest) => void;
  item: SNMPItem | null;
}

const SNMPItemEditModal: React.FC<SNMPItemEditModalProps> = ({ isOpen, onClose, onEdit, item }) => {
  // 状态
  const [formData, setFormData] = useState<SNMPItemUpdateRequest>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // Toast
  const toast = useToast();
  
  // 当item变化时更新表单数据
  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name,
        key: item.key,
        oid: item.oid,
        unit: item.unit,
        item_type: item.item_type,
        location: item.location,
        position: item.position,
        description: item.description,
        status: item.status,
      });
    }
  }, [item]);
  
  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // 处理提交
  const handleSubmit = async () => {
    // 验证必填字段
    if (!formData.name || !formData.key || !formData.oid || !formData.item_type || !formData.location) {
      toast({
        title: '表单不完整',
        description: '请填写所有必填字段',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }
    
    if (!item) return;
    
    setIsSubmitting(true);
    try {
      await onEdit(item.id, formData);
    } catch (error) {
      console.error('更新SNMP监控项失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!item) return null;
  
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>编辑SNMP监控项</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text fontWeight="medium" color="gray.600">基本信息</Text>
            
            <FormControl>
              <FormLabel>设备ID</FormLabel>
              <Input value={item.device_id} isReadOnly bg="gray.100" />
            </FormControl>
            
            <HStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>监控项名称</FormLabel>
                <Input
                  name="name"
                  value={formData.name || ''}
                  onChange={handleChange}
                  placeholder="例如：冷通道1-温度01"
                />
              </FormControl>
              
              <FormControl isRequired>
                <FormLabel>键值</FormLabel>
                <Input
                  name="key"
                  value={formData.key || ''}
                  onChange={handleChange}
                  placeholder="例如：cold_aisle1_temp1"
                />
              </FormControl>
            </HStack>
            
            <FormControl isRequired>
              <FormLabel>SNMP OID</FormLabel>
              <Input
                name="oid"
                value={formData.oid || ''}
                onChange={handleChange}
                placeholder="例如：1.3.6.1.4.1.1.268435795.2.0"
              />
            </FormControl>
            
            <HStack spacing={4}>
              <FormControl>
                <FormLabel>单位</FormLabel>
                <Input
                  name="unit"
                  value={formData.unit || ''}
                  onChange={handleChange}
                  placeholder="例如：℃"
                />
              </FormControl>
              
              <FormControl isRequired>
                <FormLabel>监控项类型</FormLabel>
                <Select
                  name="item_type"
                  value={formData.item_type || ''}
                  onChange={handleChange}
                >
                  <option value="temperature">温度</option>
                  <option value="humidity">湿度</option>
                  <option value="smoke">烟感</option>
                  <option value="water">水浸</option>
                </Select>
              </FormControl>
            </HStack>
            
            <Divider />
            
            <Text fontWeight="medium" color="gray.600">位置信息</Text>
            
            <HStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>位置</FormLabel>
                <Select
                  name="location"
                  value={formData.location || ''}
                  onChange={handleChange}
                >
                  <option value="cold_aisle1">冷通道1</option>
                  <option value="cold_aisle2">冷通道2</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel>具体位置</FormLabel>
                <Input
                  name="position"
                  value={formData.position || ''}
                  onChange={handleChange}
                  placeholder="例如：01"
                />
              </FormControl>
            </HStack>
            
            <FormControl>
              <FormLabel>描述</FormLabel>
              <Textarea
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                placeholder="监控项描述..."
                rows={3}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel>状态</FormLabel>
              <Select
                name="status"
                value={formData.status || ''}
                onChange={handleChange}
              >
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </Select>
            </FormControl>
          </VStack>
        </ModalBody>
        
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button 
            colorScheme="blue" 
            onClick={handleSubmit}
            isLoading={isSubmitting}
            loadingText="保存中"
          >
            保存
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SNMPItemEditModal;
