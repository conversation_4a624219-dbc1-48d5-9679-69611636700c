'use client'

import React from 'react'
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  SimpleGrid,
  Flex,
  Icon,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Divider,
  useColorModeValue,
} from '@chakra-ui/react'
import { Thermometer, CaretUp, CaretDown, Buildings } from '@phosphor-icons/react'

interface DatacenterTemperatureProps {
  data: {
    id: string;
    location: string;
    floor: string;
    temperature: number;
    cold_aisle1_temp1?: number;
    cold_aisle1_temp2?: number;
    cold_aisle2_temp1?: number;
    cold_aisle2_temp2?: number;
    status: string;
    lastUpdated: string;
  };
}

const DatacenterTemperature: React.FC<DatacenterTemperatureProps> = ({ data }) => {
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')
  const headerBg = useColorModeValue('gray.50', 'gray.700')

  // 判断温度是否在正常范围内
  const isTemperatureNormal = (temp: number) => {
    return temp >= 18 && temp <= 27
  }

  // 获取温度状态颜色
  const getTemperatureColor = (temp: number) => {
    if (temp < 18) return "blue.500"
    if (temp > 27) return "red.500"
    return "green.500"
  }

  // 获取温度状态文本
  const getTemperatureStatus = (temp: number) => {
    if (temp < 18) return "偏低"
    if (temp > 27) return "偏高"
    return "正常"
  }

  return (
    <Card borderRadius="lg" boxShadow="md" overflow="hidden">
      <CardHeader bg={headerBg} py={4} px={6}>
        <Flex justify="space-between" align="center">
          <Flex align="center">
            <Icon as={Buildings} mr={2} boxSize={5} color="teal.500" />
            <Heading size="md">{data.floor} {data.location}</Heading>
          </Flex>
          <Badge
            colorScheme={data.status === 'normal' ? 'green' : data.status === 'warning' ? 'yellow' : 'red'}
            fontSize="0.8em"
            px={2}
            py={1}
            borderRadius="full"
          >
            {data.status === 'normal' ? '正常' : data.status === 'warning' ? '警告' : '异常'}
          </Badge>
        </Flex>
      </CardHeader>
      <CardBody p={6}>
        {/* 主温度显示 */}
        <Stat mb={4}>
          <Flex align="center" mb={2}>
            <Icon as={Thermometer} color="orange.500" mr={2} />
            <StatLabel>平均温度</StatLabel>
          </Flex>
          <StatNumber>{data.temperature}°C</StatNumber>
          <StatHelpText>
            <Flex align="center">
              <Icon 
                as={data.temperature > 23 ? CaretUp : CaretDown} 
                color={getTemperatureColor(data.temperature)} 
                mr={1} 
              />
              <Text color={getTemperatureColor(data.temperature)}>
                {getTemperatureStatus(data.temperature)}
              </Text>
            </Flex>
          </StatHelpText>
        </Stat>

        <Divider my={4} />

        {/* 冷通道温度详情 */}
        <Text fontWeight="bold" mb={3}>冷通道温度详情</Text>
        <SimpleGrid columns={2} spacing={4}>
          {/* 冷通道1-温度01 */}
          {data.cold_aisle1_temp1 !== undefined && (
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Flex align="center" mb={1}>
                <Icon as={Thermometer} color="blue.400" mr={2} boxSize={4} />
                <Text fontWeight="medium">冷通道1-温度01</Text>
              </Flex>
              <Flex justify="space-between" align="center">
                <Text fontSize="xl" fontWeight="bold">{data.cold_aisle1_temp1}°C</Text>
                <Badge colorScheme={isTemperatureNormal(data.cold_aisle1_temp1) ? "green" : "red"}>
                  {getTemperatureStatus(data.cold_aisle1_temp1)}
                </Badge>
              </Flex>
            </Box>
          )}

          {/* 冷通道1-温度02 */}
          {data.cold_aisle1_temp2 !== undefined && (
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Flex align="center" mb={1}>
                <Icon as={Thermometer} color="blue.400" mr={2} boxSize={4} />
                <Text fontWeight="medium">冷通道1-温度02</Text>
              </Flex>
              <Flex justify="space-between" align="center">
                <Text fontSize="xl" fontWeight="bold">{data.cold_aisle1_temp2}°C</Text>
                <Badge colorScheme={isTemperatureNormal(data.cold_aisle1_temp2) ? "green" : "red"}>
                  {getTemperatureStatus(data.cold_aisle1_temp2)}
                </Badge>
              </Flex>
            </Box>
          )}

          {/* 冷通道2-温度01 */}
          {data.cold_aisle2_temp1 !== undefined && (
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Flex align="center" mb={1}>
                <Icon as={Thermometer} color="blue.400" mr={2} boxSize={4} />
                <Text fontWeight="medium">冷通道2-温度01</Text>
              </Flex>
              <Flex justify="space-between" align="center">
                <Text fontSize="xl" fontWeight="bold">{data.cold_aisle2_temp1}°C</Text>
                <Badge colorScheme={isTemperatureNormal(data.cold_aisle2_temp1) ? "green" : "red"}>
                  {getTemperatureStatus(data.cold_aisle2_temp1)}
                </Badge>
              </Flex>
            </Box>
          )}

          {/* 冷通道2-温度02 */}
          {data.cold_aisle2_temp2 !== undefined && (
            <Box p={3} borderWidth="1px" borderRadius="md" borderColor={borderColor}>
              <Flex align="center" mb={1}>
                <Icon as={Thermometer} color="blue.400" mr={2} boxSize={4} />
                <Text fontWeight="medium">冷通道2-温度02</Text>
              </Flex>
              <Flex justify="space-between" align="center">
                <Text fontSize="xl" fontWeight="bold">{data.cold_aisle2_temp2}°C</Text>
                <Badge colorScheme={isTemperatureNormal(data.cold_aisle2_temp2) ? "green" : "red"}>
                  {getTemperatureStatus(data.cold_aisle2_temp2)}
                </Badge>
              </Flex>
            </Box>
          )}
        </SimpleGrid>

        <Text fontSize="sm" color="gray.500" mt={4}>
          最后更新: {data.lastUpdated}
        </Text>
      </CardBody>
    </Card>
  )
}

export default DatacenterTemperature
