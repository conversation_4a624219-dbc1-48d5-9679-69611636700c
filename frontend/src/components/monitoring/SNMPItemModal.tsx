import React, { useState, useEffect } from 'react'
import { SNMPItem } from '@/services/snmpItemsServiceSimple'
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  Input,
  Select,
  SelectItem,
  Textarea
} from '@nextui-org/react'

interface SNMPItemModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (item: Partial<SNMPItem>) => void
  item?: SNMPItem
  title: string
}

const initialItemState: Partial<SNMPItem> = {
  device_id: '',
  name: '',
  key: '',
  oid: '',
  unit: '',
  item_type: 'temperature',
  location: 'cold_aisle1',
  position: '01',
  description: '',
  status: 'active'
}

const SNMPItemModal: React.FC<SNMPItemModalProps> = ({
  isOpen,
  onClose,
  onSave,
  item,
  title
}) => {
  const [formData, setFormData] = useState<Partial<SNMPItem>>(initialItemState)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (item) {
      setFormData(item)
    } else {
      setFormData(initialItemState)
    }
    setErrors({})
  }, [item, isOpen])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.device_id) newErrors.device_id = '设备ID不能为空'
    if (!formData.name) newErrors.name = '名称不能为空'
    if (!formData.key) newErrors.key = '键值不能为空'
    if (!formData.oid) newErrors.oid = 'OID不能为空'
    if (!formData.item_type) newErrors.item_type = '类型不能为空'
    if (!formData.location) newErrors.location = '位置不能为空'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      onSave(formData)
    }
  }

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))

    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="3xl"
      scrollBehavior="inside"
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              {title}
            </ModalHeader>
            <ModalBody>
              <form id="snmp-item-form" onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="设备ID"
                    name="device_id"
                    value={formData.device_id || ''}
                    onChange={(e) => handleInputChange('device_id', e.target.value)}
                    variant="bordered"
                    isInvalid={!!errors.device_id}
                    errorMessage={errors.device_id}
                    isRequired
                  />

                  <Input
                    label="名称"
                    name="name"
                    value={formData.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    variant="bordered"
                    isInvalid={!!errors.name}
                    errorMessage={errors.name}
                    isRequired
                  />

                  <Input
                    label="键值"
                    name="key"
                    value={formData.key || ''}
                    onChange={(e) => handleInputChange('key', e.target.value)}
                    variant="bordered"
                    isInvalid={!!errors.key}
                    errorMessage={errors.key}
                    isRequired
                  />

                  <Input
                    label="OID"
                    name="oid"
                    value={formData.oid || ''}
                    onChange={(e) => handleInputChange('oid', e.target.value)}
                    variant="bordered"
                    isInvalid={!!errors.oid}
                    errorMessage={errors.oid}
                    isRequired
                  />

                  <Input
                    label="单位"
                    name="unit"
                    value={formData.unit || ''}
                    onChange={(e) => handleInputChange('unit', e.target.value)}
                    variant="bordered"
                  />

                  <Select
                    label="类型"
                    name="item_type"
                    selectedKeys={[formData.item_type || '']}
                    onChange={(e) => handleInputChange('item_type', e.target.value)}
                    variant="bordered"
                    isInvalid={!!errors.item_type}
                    errorMessage={errors.item_type}
                    isRequired
                  >
                    <SelectItem key="temperature" value="temperature">温度</SelectItem>
                    <SelectItem key="humidity" value="humidity">湿度</SelectItem>
                    <SelectItem key="smoke" value="smoke">烟感</SelectItem>
                    <SelectItem key="water" value="water">水浸</SelectItem>
                    <SelectItem key="other" value="other">其他</SelectItem>
                  </Select>

                  <Select
                    label="位置"
                    name="location"
                    selectedKeys={[formData.location || '']}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    variant="bordered"
                    isInvalid={!!errors.location}
                    errorMessage={errors.location}
                    isRequired
                  >
                    <SelectItem key="cold_aisle1" value="cold_aisle1">冷通道1</SelectItem>
                    <SelectItem key="cold_aisle2" value="cold_aisle2">冷通道2</SelectItem>
                    <SelectItem key="hot_aisle" value="hot_aisle">热通道</SelectItem>
                    <SelectItem key="other" value="other">其他</SelectItem>
                  </Select>

                  <Input
                    label="位置编号"
                    name="position"
                    value={formData.position || ''}
                    onChange={(e) => handleInputChange('position', e.target.value)}
                    variant="bordered"
                  />

                  <Select
                    label="状态"
                    name="status"
                    selectedKeys={[formData.status || '']}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    variant="bordered"
                  >
                    <SelectItem key="active" value="active">活跃</SelectItem>
                    <SelectItem key="inactive" value="inactive">禁用</SelectItem>
                  </Select>
                </div>

                <div className="mt-4">
                  <Textarea
                    label="描述"
                    name="description"
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    variant="bordered"
                    minRows={3}
                  />
                </div>
              </form>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                取消
              </Button>
              <Button color="primary" type="submit" form="snmp-item-form">
                保存
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  )
}

export default SNMPItemModal
