import {
  Box,
  Text,
  Tooltip,
  Progress,
  VStack,
  HStack,
  useColorMode,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverBody,
  Portal
} from '@chakra-ui/react'

interface RackLayoutProps {
  totalU: number
  usedU: number
  rackData: {
    position: number
    device: string
    user: string
  }[]
}

export const RackLayout = ({ totalU, usedU, rackData }: RackLayoutProps) => {
  const { colorMode } = useColorMode()
  const utilization = (usedU / totalU) * 100
  const remainingU = totalU - usedU

  return (
    <Box
      borderWidth="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
      borderRadius="lg"
      p={4}
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
    >
      <VStack spacing={4} align="stretch">
        <HStack justify="space-between">
          <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
            空间利用率
          </Text>
          <Text fontSize="sm" fontWeight="medium">
            {utilization.toFixed(1)}%
          </Text>
        </HStack>
        
        <Progress
          value={utilization}
          size="sm"
          colorScheme="green"
          borderRadius="full"
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
        />

        <HStack justify="space-between">
          <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
            剩余U位
          </Text>
          <Text fontSize="sm" fontWeight="medium" color="komodo.green">
            {remainingU}U
          </Text>
        </HStack>

        <Box>
          {rackData.map((item) => (
            <Popover key={item.position} placement="right" trigger="hover">
              <PopoverTrigger>
                <Box
                  p={2}
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
                  borderRadius="md"
                  mb={2}
                  cursor="pointer"
                  _hover={{
                    bg: colorMode === 'dark' ? 'gray.700' : 'gray.50',
                    borderColor: 'komodo.green'
                  }}
                  transition="all 0.2s"
                >
                  <Text fontSize="sm">U{item.position}</Text>
                </Box>
              </PopoverTrigger>
              <Portal>
                <PopoverContent
                  w="auto"
                  minW="200px"
                  bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                  borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
                  boxShadow="lg"
                >
                  <PopoverBody p={3}>
                    <VStack align="stretch" spacing={2}>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                          设备
                        </Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {item.device}
                        </Text>
                      </HStack>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                          使用人
                        </Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {item.user}
                        </Text>
                      </HStack>
                    </VStack>
                  </PopoverBody>
                </PopoverContent>
              </Portal>
            </Popover>
          ))}
        </Box>
      </VStack>
    </Box>
  )
} 