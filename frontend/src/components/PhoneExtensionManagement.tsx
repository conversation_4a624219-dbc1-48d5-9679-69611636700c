import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Container,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Select,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useDisclosure,
  useToast,
  VStack,
} from '@chakra-ui/react';
import {
  PhoneExtension,
  CreatePhoneExtensionDTO,
  getAllExtensions,
  getExtension,
  createExtension,
  updateExtension,
  deleteExtension,
  searchExtensions,
  filterExtensionsByType,
  exportExtensions,
} from '../services/phoneExtensionService';

const PhoneExtensionManagement: React.FC = () => {
  const [extensions, setExtensions] = useState<PhoneExtension[]>([]);
  const [selectedExtension, setSelectedExtension] = useState<PhoneExtension | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<'all' | 'internal' | 'external'>('all');
  const [formData, setFormData] = useState<CreatePhoneExtensionDTO>({
    extension_number: '',
    name: '',
    department: '',
    type: 'internal',
    status: 'active',
    notes: '',
  });

  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  useEffect(() => {
    loadExtensions();
  }, []);

  const loadExtensions = async () => {
    try {
      const data = await getAllExtensions();
      setExtensions(data);
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载分机列表',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleSearch = async () => {
    try {
      const results = await searchExtensions(searchQuery);
      setExtensions(results);
    } catch (error) {
      toast({
        title: '搜索失败',
        description: '无法执行搜索操作',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleFilter = async (type: 'all' | 'internal' | 'external') => {
    try {
      setSelectedType(type);
      if (type === 'all') {
        await loadExtensions();
      } else {
        const results = await filterExtensionsByType(type);
        setExtensions(results);
      }
    } catch (error) {
      toast({
        title: '筛选失败',
        description: '无法执行筛选操作',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleExport = async () => {
    try {
      await exportExtensions();
      toast({
        title: '导出成功',
        description: '分机列表已导出',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: '导出失败',
        description: '无法导出分机列表',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedExtension) {
        await updateExtension(selectedExtension.id, formData);
        toast({
          title: '更新成功',
          description: '分机信息已更新',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else {
        await createExtension(formData);
        toast({
          title: '创建成功',
          description: '新分机已创建',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
      onClose();
      loadExtensions();
      setFormData({
        extension_number: '',
        name: '',
        department: '',
        type: 'internal',
        status: 'active',
        notes: '',
      });
    } catch (error) {
      toast({
        title: selectedExtension ? '更新失败' : '创建失败',
        description: '操作未能完成',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('确定要删除这个分机吗？')) {
      try {
        await deleteExtension(id);
        toast({
          title: '删除成功',
          description: '分机已删除',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        loadExtensions();
      } catch (error) {
        toast({
          title: '删除失败',
          description: '无法删除分机',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  const openEditModal = async (id: string) => {
    try {
      const extension = await getExtension(id);
      setSelectedExtension(extension);
      setFormData({
        extension_number: extension.extension_number,
        name: extension.name,
        department: extension.department,
        type: extension.type,
        status: extension.status,
        notes: extension.notes || '',
      });
      onOpen();
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法加载分机信息',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const openCreateModal = () => {
    setSelectedExtension(null);
    setFormData({
      extension_number: '',
      name: '',
      department: '',
      type: 'internal',
      status: 'active',
      notes: '',
    });
    onOpen();
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <Flex justify="space-between" align="center">
          <Text fontSize="2xl" fontWeight="bold">分机管理</Text>
          <Button colorScheme="blue" onClick={openCreateModal}>添加分机</Button>
        </Flex>

        <Flex gap={4} align="center">
          <Input
            placeholder="搜索分机..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Select value={selectedType} onChange={(e) => handleFilter(e.target.value as 'all' | 'internal' | 'external')}>
            <option value="all">全部</option>
            <option value="internal">内部</option>
            <option value="external">外部</option>
          </Select>
          <Button onClick={handleExport}>导出</Button>
        </Flex>

        <Box overflowX="auto">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>分机号</Th>
                <Th>姓名</Th>
                <Th>部门</Th>
                <Th>类型</Th>
                <Th>状态</Th>
                <Th>备注</Th>
                <Th>操作</Th>
              </Tr>
            </Thead>
            <Tbody>
              {extensions.map((extension) => (
                <Tr key={extension.id}>
                  <Td>{extension.extension_number}</Td>
                  <Td>{extension.name}</Td>
                  <Td>{extension.department}</Td>
                  <Td>{extension.type === 'internal' ? '内部' : '外部'}</Td>
                  <Td>{extension.status === 'active' ? '启用' : '停用'}</Td>
                  <Td>{extension.notes}</Td>
                  <Td>
                    <Button size="sm" mr={2} onClick={() => openEditModal(extension.id)}>编辑</Button>
                    <Button size="sm" colorScheme="red" onClick={() => handleDelete(extension.id)}>删除</Button>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>

        <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
          <ModalOverlay />
          <ModalContent maxW="500px">
            <form onSubmit={handleSubmit}>
              <ModalHeader>{selectedExtension ? '编辑分机' : '添加分机'}</ModalHeader>
              <ModalCloseButton />
              <ModalBody>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>分机号</FormLabel>
                    <Input
                      value={formData.extension_number}
                      onChange={(e) => setFormData({ ...formData, extension_number: e.target.value })}
                    />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>姓名</FormLabel>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>部门</FormLabel>
                    <Input
                      value={formData.department}
                      onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                    />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>类型</FormLabel>
                    <Select
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value as 'internal' | 'external' })}
                    >
                      <option value="internal">内部</option>
                      <option value="external">外部</option>
                    </Select>
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>状态</FormLabel>
                    <Select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
                    >
                      <option value="active">启用</option>
                      <option value="inactive">停用</option>
                    </Select>
                  </FormControl>
                  <FormControl>
                    <FormLabel>备注</FormLabel>
                    <Input
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    />
                  </FormControl>
                </VStack>
              </ModalBody>
              <ModalFooter>
                <Button type="submit" colorScheme="blue" mr={3}>
                  {selectedExtension ? '保存' : '创建'}
                </Button>
                <Button onClick={onClose}>取消</Button>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      </VStack>
    </Container>
  );
};

export default PhoneExtensionManagement; 