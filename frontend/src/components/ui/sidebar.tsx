'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  House,
  Cube,
  Package,
  Wrench,
  Gear,
  Bell,
  Globe,
  WifiHigh,
  Database,
  Warning,
  Lightning,
  Printer,
  CaretDown,
  CaretRight,
  Desktop,
  ChartBar,
  Gauge,
  Buildings,
  Drop,
  Power,
  Plugs,
  Network
} from '@phosphor-icons/react'

interface NavItemProps {
  icon: React.ElementType
  title: string
  href: string
  isActive?: boolean
  onClick?: () => void
  children?: React.ReactNode
}

const NavItem = ({ icon: Icon, title, href, isActive, onClick, children }: NavItemProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const hasChildren = <PERSON>olean(children)

  const handleClick = () => {
    if (hasChildren) {
      setIsOpen(!isOpen)
    }
    if (onClick) {
      onClick()
    }
  }

  return (
    <div>
      <Link
        href={hasChildren ? '#' : href}
        className="text-decoration-none"
        onClick={hasChildren ? (e) => e.preventDefault() : undefined}
      >
        <div
          className={cn(
            "flex items-center justify-between p-2 mx-2 rounded-md cursor-pointer relative",
            "transition-all duration-200",
            isActive
              ? "bg-primary/10 text-primary"
              : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
          )}
          onClick={handleClick}
        >
          <div className="flex items-center">
            {isActive && (
              <div className="absolute left-0 top-1/2 -translate-y-1/2 w-[3px] h-[70%] bg-primary rounded-r-sm" />
            )}
            <div className={cn(
              "text-xl mr-3",
              isActive ? "ml-3 text-primary" : "ml-4"
            )}>
              <Icon size={20} />
            </div>
            <span className={cn(
              "text-sm",
              isActive ? "font-medium" : "font-normal"
            )}>
              {title}
            </span>
          </div>
          {hasChildren && (
            <div className="text-muted-foreground">
              {isOpen ? <CaretDown size={16} /> : <CaretRight size={16} />}
            </div>
          )}
        </div>
      </Link>
      {hasChildren && isOpen && (
        <div className="ml-4 pl-4 border-l border-border">
          {children}
        </div>
      )}
    </div>
  )
}

interface SubNavItemProps {
  title: string
  href: string
  isActive?: boolean
}

const SubNavItem = ({ title, href, isActive }: SubNavItemProps) => {
  return (
    <Link href={href} className="text-decoration-none">
      <div
        className={cn(
          "flex items-center p-2 rounded-md cursor-pointer relative",
          "transition-all duration-200",
          isActive
            ? "bg-primary/10 text-primary"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
        )}
      >
        {isActive && (
          <div className="absolute left-0 top-1/2 -translate-y-1/2 w-[3px] h-[70%] bg-primary rounded-r-sm" />
        )}
        <span className={cn(
          "text-sm",
          isActive ? "font-medium" : "font-normal"
        )}>
          {title}
        </span>
      </div>
    </Link>
  )
}

export function Sidebar() {
  const pathname = usePathname() ?? '/'

  return (
    <div className="fixed left-0 top-0 h-full w-60 overflow-y-auto z-40 bg-background border-r">
      <div className="flex items-center justify-center h-16 border-b">
        <div className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          RS资产管理系统
        </div>
      </div>

      <div className="py-4">
        <NavItem
          icon={House}
          title="仪表盘"
          href="/"
          isActive={pathname === '/'}
        />

        <div className="mt-6 mb-2 px-6">
          <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            主要功能
          </div>
        </div>

        {/* 网络管理 */}
        <NavItem
          icon={Network}
          title="网络管理"
          href="#"
          isActive={pathname.startsWith('/network')}
        >
          <SubNavItem
            title="故障影响"
            href="/network/fault-impact"
            isActive={pathname === '/network/fault-impact'}
          />
          <SubNavItem
            title="路径可视化"
            href="/network/path-visualization"
            isActive={pathname === '/network/path-visualization'}
          />
          <SubNavItem
            title="配置备份"
            href="/network/config-backup"
            isActive={pathname === '/network/config-backup'}
          />
          <SubNavItem
            title="配置管理"
            href="/network/config-management"
            isActive={pathname === '/network/config-management'}
          />
          <SubNavItem
            title="数字IP管理"
            href="/network/digital-ip"
            isActive={pathname === '/network/digital-ip'}
          />
          <SubNavItem
            title="电话分机管理"
            href="/network/phone-extensions"
            isActive={pathname === '/network/phone-extensions'}
          />
          <SubNavItem
            title="虚拟机管理"
            href="/network/vm-management"
            isActive={pathname === '/network/vm-management'}
          />
          <SubNavItem
            title="打印机监控"
            href="/network/printer-monitoring"
            isActive={pathname === '/network/printer-monitoring'}
          />
          <SubNavItem
            title="无线网络监控"
            href="/network/wireless-monitoring"
            isActive={pathname === '/network/wireless-monitoring'}
          />
          <SubNavItem
            title="网络带宽监控"
            href="/network/bandwidth-monitoring"
            isActive={pathname === '/network/bandwidth-monitoring'}
          />
        </NavItem>

        {/* 基础设施监控 */}
        <NavItem
          icon={Buildings}
          title="基础设施监控"
          href="#"
          isActive={pathname.startsWith('/power-monitoring')}
        >
          <SubNavItem
            title="环境监控"
            href="/power-monitoring/environment"
            isActive={pathname === '/power-monitoring/environment'}
          />
          <SubNavItem
            title="UPS监控"
            href="/power-monitoring/ups"
            isActive={pathname === '/power-monitoring/ups' || pathname.startsWith('/power-monitoring/ups/')}
          />
          <SubNavItem
            title="市电监控"
            href="/power-monitoring/mains"
            isActive={pathname === '/power-monitoring/mains'}
          />
          <SubNavItem
            title="SNMP采集管理"
            href="/power-monitoring/snmp-collection"
            isActive={pathname === '/power-monitoring/snmp-collection'}
          />
        </NavItem>

        {/* 资产管理 */}
        <NavItem
          icon={Package}
          title="资产管理"
          href="#"
          isActive={pathname.startsWith('/assets') || pathname.startsWith('/resources/asset')}
        >
          <SubNavItem
            title="资产概览"
            href="/assets"
            isActive={pathname === '/assets'}
          />
          <SubNavItem
            title="资产地图"
            href="/resources/asset-map"
            isActive={pathname === '/resources/asset-map'}
          />
        </NavItem>

        <div className="mt-6 mb-2 px-6">
          <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            系统
          </div>
        </div>

        <NavItem
          icon={Bell}
          title="通知"
          href="/notifications"
          isActive={pathname === '/notifications'}
        />

        <NavItem
          icon={Gear}
          title="设置"
          href="/settings"
          isActive={pathname === '/settings'}
        />
      </div>
    </div>
  )
}

export default Sidebar
