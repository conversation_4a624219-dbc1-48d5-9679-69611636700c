import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const tableVariants = cva(
  "w-full caption-bottom text-sm",
  {
    variants: {
      variant: {
        default: "",
        primary: "bg-primary-50/50 dark:bg-primary-950/20 [&_thead_tr]:bg-primary-100 dark:[&_thead_tr]:bg-primary-900/30 [&_tfoot]:bg-primary-100 dark:[&_tfoot]:bg-primary-900/30",
        secondary: "bg-secondary-50/50 dark:bg-secondary-950/20 [&_thead_tr]:bg-secondary-100 dark:[&_thead_tr]:bg-secondary-900/30 [&_tfoot]:bg-secondary-100 dark:[&_tfoot]:bg-secondary-900/30",
        accent: "bg-accent-50/50 dark:bg-accent-950/20 [&_thead_tr]:bg-accent-100 dark:[&_thead_tr]:bg-accent-900/30 [&_tfoot]:bg-accent-100 dark:[&_tfoot]:bg-accent-900/30",
        info: "bg-info-50/50 dark:bg-info-950/20 [&_thead_tr]:bg-info-100 dark:[&_thead_tr]:bg-info-900/30 [&_tfoot]:bg-info-100 dark:[&_tfoot]:bg-info-900/30",
        success: "bg-success-50/50 dark:bg-success-950/20 [&_thead_tr]:bg-success-100 dark:[&_thead_tr]:bg-success-900/30 [&_tfoot]:bg-success-100 dark:[&_tfoot]:bg-success-900/30",
        warning: "bg-warning-50/50 dark:bg-warning-950/20 [&_thead_tr]:bg-warning-100 dark:[&_thead_tr]:bg-warning-900/30 [&_tfoot]:bg-warning-100 dark:[&_tfoot]:bg-warning-900/30",
        destructive: "bg-destructive-50/50 dark:bg-destructive-950/20 [&_thead_tr]:bg-destructive-100 dark:[&_thead_tr]:bg-destructive-900/30 [&_tfoot]:bg-destructive-100 dark:[&_tfoot]:bg-destructive-900/30",
      },
      bordered: {
        true: "border",
        false: "",
      },
      striped: {
        true: "[&_tbody_tr:nth-child(even)]:bg-muted/50",
        false: "",
      },
      hoverable: {
        true: "[&_tbody_tr]:hover:bg-muted/80",
        false: "",
      },
      rounded: {
        true: "rounded-lg overflow-hidden",
        false: "",
      },
      compact: {
        true: "[&_td]:p-1 [&_th]:p-1 text-xs",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      bordered: false,
      striped: false,
      hoverable: true,
      rounded: false,
      compact: false,
    },
  }
)

export interface TableProps
  extends React.HTMLAttributes<HTMLTableElement>,
    VariantProps<typeof tableVariants> {}

const Table = React.forwardRef<
  HTMLTableElement,
  TableProps
>(({ className, variant, bordered, striped, hoverable, rounded, compact, ...props }, ref) => (
  <div className={cn(
    "relative w-full overflow-auto",
    rounded && "rounded-lg",
    className
  )}>
    <table
      ref={ref}
      className={cn(
        tableVariants({
          variant,
          bordered,
          striped,
          hoverable,
          rounded,
          compact
        })
      )}
      {...props}
    />
  </div>
))
Table.displayName = "Table"

const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead ref={ref} className={cn("[&_tr]:border-b", className)} {...props} />
))
TableHeader.displayName = "TableHeader"

const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn("[&_tr:last-child]:border-0", className)}
    {...props}
  />
))
TableBody.displayName = "TableBody"

const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn(
      "border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",
      className
    )}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"

interface TableRowProps extends
  React.HTMLAttributes<HTMLTableRowElement> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
  isSelected?: boolean;
}

const TableRow = React.forwardRef<
  HTMLTableRowElement,
  TableRowProps
>(({ className, variant = "default", isSelected, ...props }, ref) => {
  // 根据variant确定行的样式
  const getRowStyle = () => {
    if (isSelected) {
      switch (variant) {
        case 'primary': return 'bg-primary-100 dark:bg-primary-900/30'
        case 'secondary': return 'bg-secondary-100 dark:bg-secondary-900/30'
        case 'accent': return 'bg-accent-100 dark:bg-accent-900/30'
        case 'info': return 'bg-info-100 dark:bg-info-900/30'
        case 'success': return 'bg-success-100 dark:bg-success-900/30'
        case 'warning': return 'bg-warning-100 dark:bg-warning-900/30'
        case 'destructive': return 'bg-destructive-100 dark:bg-destructive-900/30'
        default: return 'bg-muted'
      }
    }
    return ''
  }

  // 根据variant确定行的悬停样式
  const getHoverStyle = () => {
    switch (variant) {
      case 'primary': return 'hover:bg-primary-100/70 dark:hover:bg-primary-900/20'
      case 'secondary': return 'hover:bg-secondary-100/70 dark:hover:bg-secondary-900/20'
      case 'accent': return 'hover:bg-accent-100/70 dark:hover:bg-accent-900/20'
      case 'info': return 'hover:bg-info-100/70 dark:hover:bg-info-900/20'
      case 'success': return 'hover:bg-success-100/70 dark:hover:bg-success-900/20'
      case 'warning': return 'hover:bg-warning-100/70 dark:hover:bg-warning-900/20'
      case 'destructive': return 'hover:bg-destructive-100/70 dark:hover:bg-destructive-900/20'
      default: return 'hover:bg-muted/50'
    }
  }

  return (
    <tr
      ref={ref}
      className={cn(
        "border-b transition-colors",
        getHoverStyle(),
        isSelected && getRowStyle(),
        className
      )}
      data-state={isSelected ? "selected" : undefined}
      {...props}
    />
  )
})
TableRow.displayName = "TableRow"

interface TableHeadProps extends
  React.ThHTMLAttributes<HTMLTableCellElement> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
}

const TableHead = React.forwardRef<
  HTMLTableCellElement,
  TableHeadProps
>(({ className, variant = "default", ...props }, ref) => {
  // 根据variant确定表头的文本颜色
  const getHeadTextColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-700 dark:text-primary-300'
      case 'secondary': return 'text-secondary-700 dark:text-secondary-300'
      case 'accent': return 'text-accent-700 dark:text-accent-300'
      case 'info': return 'text-info-700 dark:text-info-300'
      case 'success': return 'text-success-700 dark:text-success-300'
      case 'warning': return 'text-warning-700 dark:text-warning-300'
      case 'destructive': return 'text-destructive-700 dark:text-destructive-300'
      default: return 'text-muted-foreground'
    }
  }

  return (
    <th
      ref={ref}
      className={cn(
        "h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] transition-colors",
        getHeadTextColor(),
        className
      )}
      {...props}
    />
  )
})
TableHead.displayName = "TableHead"

interface TableCellProps extends
  React.TdHTMLAttributes<HTMLTableCellElement> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
  highlight?: boolean;
}

const TableCell = React.forwardRef<
  HTMLTableCellElement,
  TableCellProps
>(({ className, variant = "default", highlight = false, ...props }, ref) => {
  // 根据variant和highlight确定单元格的样式
  const getCellStyle = () => {
    if (highlight) {
      switch (variant) {
        case 'primary': return 'bg-primary-100/70 dark:bg-primary-900/20 font-medium text-primary-900 dark:text-primary-100'
        case 'secondary': return 'bg-secondary-100/70 dark:bg-secondary-900/20 font-medium text-secondary-900 dark:text-secondary-100'
        case 'accent': return 'bg-accent-100/70 dark:bg-accent-900/20 font-medium text-accent-900 dark:text-accent-100'
        case 'info': return 'bg-info-100/70 dark:bg-info-900/20 font-medium text-info-900 dark:text-info-100'
        case 'success': return 'bg-success-100/70 dark:bg-success-900/20 font-medium text-success-900 dark:text-success-100'
        case 'warning': return 'bg-warning-100/70 dark:bg-warning-900/20 font-medium text-warning-900 dark:text-warning-100'
        case 'destructive': return 'bg-destructive-100/70 dark:bg-destructive-900/20 font-medium text-destructive-900 dark:text-destructive-100'
        default: return 'bg-muted/70 font-medium'
      }
    }
    return ''
  }

  return (
    <td
      ref={ref}
      className={cn(
        "p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] transition-colors",
        getCellStyle(),
        className
      )}
      {...props}
    />
  )
})
TableCell.displayName = "TableCell"

const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption
    ref={ref}
    className={cn("mt-4 text-sm text-muted-foreground", className)}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}