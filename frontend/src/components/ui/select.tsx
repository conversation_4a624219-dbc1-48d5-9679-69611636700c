import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, ChevronUp } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const selectTriggerVariants = cva(
  "flex h-9 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 transition-all duration-300",
  {
    variants: {
      variant: {
        default: "border-input focus:ring-ring",
        primary: "border-primary/30 focus:ring-primary hover:border-primary/50",
        secondary: "border-secondary/30 focus:ring-secondary hover:border-secondary/50",
        accent: "border-accent/30 focus:ring-accent hover:border-accent/50",
        info: "border-info/30 focus:ring-info hover:border-info/50",
        success: "border-success/30 focus:ring-success hover:border-success/50",
        warning: "border-warning/30 focus:ring-warning hover:border-warning/50",
        destructive: "border-destructive/30 focus:ring-destructive hover:border-destructive/50",
      },
      rounded: {
        default: "rounded-md",
        sm: "rounded-sm",
        lg: "rounded-lg",
        full: "rounded-full",
      },
      size: {
        default: "h-9 px-3 py-2",
        sm: "h-8 px-2 py-1 text-xs",
        lg: "h-12 px-4 py-3 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      rounded: "default",
      size: "default",
    },
  }
)

const Select = SelectPrimitive.Root

const SelectGroup = SelectPrimitive.Group

const SelectValue = SelectPrimitive.Value

interface SelectTriggerProps extends
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>,
  VariantProps<typeof selectTriggerVariants> {}

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  SelectTriggerProps
>(({ className, children, variant, rounded, size, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      selectTriggerVariants({ variant, rounded, size, className }),
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className={cn(
        "h-4 w-4 transition-colors duration-300",
        variant ? `text-${variant}/70 group-hover:text-${variant}` : "opacity-50"
      )} />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
))
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
))
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName

interface SelectContentProps extends
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
}

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  SelectContentProps
>(({ className, children, position = "popper", variant = "default", ...props }, ref) => {
  // 根据variant确定内容的样式
  const getContentStyle = () => {
    switch (variant) {
      case 'primary': return 'border-primary/20 shadow-primary/10'
      case 'secondary': return 'border-secondary/20 shadow-secondary/10'
      case 'accent': return 'border-accent/20 shadow-accent/10'
      case 'info': return 'border-info/20 shadow-info/10'
      case 'success': return 'border-success/20 shadow-success/10'
      case 'warning': return 'border-warning/20 shadow-warning/10'
      case 'destructive': return 'border-destructive/20 shadow-destructive/10'
      default: return 'border-input shadow-md'
    }
  }

  return (
    <SelectPrimitive.Portal>
      <SelectPrimitive.Content
        ref={ref}
        className={cn(
          "relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 transition-all duration-300",
          position === "popper" &&
            "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
          getContentStyle(),
          className
        )}
        position={position}
        {...props}
      >
        <SelectScrollUpButton />
        <SelectPrimitive.Viewport
          className={cn(
            "p-1",
            position === "popper" &&
              "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
          )}
        >
          {children}
        </SelectPrimitive.Viewport>
        <SelectScrollDownButton />
      </SelectPrimitive.Content>
    </SelectPrimitive.Portal>
  )
})
SelectContent.displayName = SelectPrimitive.Content.displayName

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("px-2 py-1.5 text-sm font-semibold", className)}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

interface SelectItemProps extends
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
}

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  SelectItemProps
>(({ className, children, variant = "default", ...props }, ref) => {
  // 根据variant确定项目的焦点样式
  const getFocusStyle = () => {
    switch (variant) {
      case 'primary': return 'focus:bg-primary-100 focus:text-primary-900 dark:focus:bg-primary-900/30 dark:focus:text-primary-100'
      case 'secondary': return 'focus:bg-secondary-100 focus:text-secondary-900 dark:focus:bg-secondary-900/30 dark:focus:text-secondary-100'
      case 'accent': return 'focus:bg-accent-100 focus:text-accent-900 dark:focus:bg-accent-900/30 dark:focus:text-accent-100'
      case 'info': return 'focus:bg-info-100 focus:text-info-900 dark:focus:bg-info-900/30 dark:focus:text-info-100'
      case 'success': return 'focus:bg-success-100 focus:text-success-900 dark:focus:bg-success-900/30 dark:focus:text-success-100'
      case 'warning': return 'focus:bg-warning-100 focus:text-warning-900 dark:focus:bg-warning-900/30 dark:focus:text-warning-100'
      case 'destructive': return 'focus:bg-destructive-100 focus:text-destructive-900 dark:focus:bg-destructive-900/30 dark:focus:text-destructive-100'
      default: return 'focus:bg-accent focus:text-accent-foreground'
    }
  }

  // 根据variant确定复选标记的颜色
  const getCheckColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary'
      case 'secondary': return 'text-secondary'
      case 'accent': return 'text-accent'
      case 'info': return 'text-info'
      case 'success': return 'text-success'
      case 'warning': return 'text-warning'
      case 'destructive': return 'text-destructive'
      default: return ''
    }
  }

  return (
    <SelectPrimitive.Item
      ref={ref}
      className={cn(
        "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 transition-colors duration-200",
        getFocusStyle(),
        className
      )}
      {...props}
    >
      <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <Check className={cn("h-4 w-4", getCheckColor())} />
        </SelectPrimitive.ItemIndicator>
      </span>
      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
    </SelectPrimitive.Item>
  )
})
SelectItem.displayName = SelectPrimitive.Item.displayName

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}