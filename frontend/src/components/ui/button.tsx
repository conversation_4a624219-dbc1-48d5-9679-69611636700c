import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-md hover:shadow-primary/20",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md hover:shadow-destructive/20",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md hover:shadow-secondary/20",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // 丰富多彩的渐变按钮
        gradient: "bg-gradient-to-r from-primary to-secondary text-white hover:shadow-md hover:shadow-primary/20 border-none",
        primaryGradient: "bg-gradient-to-r from-primary-400 to-primary-600 text-white hover:shadow-md hover:shadow-primary/20 border-none",
        secondaryGradient: "bg-gradient-to-r from-secondary-400 to-secondary-600 text-white hover:shadow-md hover:shadow-secondary/20 border-none",
        accentGradient: "bg-gradient-to-r from-accent-400 to-accent-600 text-white hover:shadow-md hover:shadow-accent/20 border-none",
        infoGradient: "bg-gradient-to-r from-info-400 to-info-600 text-white hover:shadow-md hover:shadow-info/20 border-none",
        successGradient: "bg-gradient-to-r from-success-400 to-success-600 text-white hover:shadow-md hover:shadow-success/20 border-none",
        warningGradient: "bg-gradient-to-r from-warning-400 to-warning-600 text-white hover:shadow-md hover:shadow-warning/20 border-none",
        destructiveGradient: "bg-gradient-to-r from-destructive-400 to-destructive-600 text-white hover:shadow-md hover:shadow-destructive/20 border-none",
        // 丰富多彩的轮廓按钮
        primaryOutline: "border-2 border-primary text-primary hover:bg-primary/10 hover:text-primary-600 dark:hover:text-primary-400",
        secondaryOutline: "border-2 border-secondary text-secondary hover:bg-secondary/10 hover:text-secondary-600 dark:hover:text-secondary-400",
        accentOutline: "border-2 border-accent text-accent hover:bg-accent/10 hover:text-accent-600 dark:hover:text-accent-400",
        infoOutline: "border-2 border-info text-info hover:bg-info/10 hover:text-info-600 dark:hover:text-info-400",
        successOutline: "border-2 border-success text-success hover:bg-success/10 hover:text-success-600 dark:hover:text-success-400",
        warningOutline: "border-2 border-warning text-warning hover:bg-warning/10 hover:text-warning-600 dark:hover:text-warning-400",
        destructiveOutline: "border-2 border-destructive text-destructive hover:bg-destructive/10 hover:text-destructive-600 dark:hover:text-destructive-400",
        // 丰富多彩的轻量按钮
        primaryLight: "bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/30 dark:text-primary-300 dark:hover:bg-primary-900/50",
        secondaryLight: "bg-secondary-100 text-secondary-700 hover:bg-secondary-200 dark:bg-secondary-900/30 dark:text-secondary-300 dark:hover:bg-secondary-900/50",
        accentLight: "bg-accent-100 text-accent-700 hover:bg-accent-200 dark:bg-accent-900/30 dark:text-accent-300 dark:hover:bg-accent-900/50",
        infoLight: "bg-info-100 text-info-700 hover:bg-info-200 dark:bg-info-900/30 dark:text-info-300 dark:hover:bg-info-900/50",
        successLight: "bg-success-100 text-success-700 hover:bg-success-200 dark:bg-success-900/30 dark:text-success-300 dark:hover:bg-success-900/50",
        warningLight: "bg-warning-100 text-warning-700 hover:bg-warning-200 dark:bg-warning-900/30 dark:text-warning-300 dark:hover:bg-warning-900/50",
        destructiveLight: "bg-destructive-100 text-destructive-700 hover:bg-destructive-200 dark:bg-destructive-900/30 dark:text-destructive-300 dark:hover:bg-destructive-900/50",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
      animation: {
        none: "",
        pulse: "animate-pulse",
        bounce: "hover:animate-bounce",
        spin: "hover:animate-spin",
        ping: "hover:animate-ping",
      },
      rounded: {
        default: "rounded-md",
        full: "rounded-full",
        none: "rounded-none",
        sm: "rounded-sm",
        lg: "rounded-lg",
        xl: "rounded-xl",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
      rounded: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, animation, rounded, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, animation, rounded, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }