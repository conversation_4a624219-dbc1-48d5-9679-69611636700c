import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-card",
        primary: "bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/40 dark:to-primary-800/40 border-primary-200 dark:border-primary-700",
        secondary: "bg-gradient-to-br from-secondary-50 to-secondary-100 dark:from-secondary-900/40 dark:to-secondary-800/40 border-secondary-200 dark:border-secondary-700",
        accent: "bg-gradient-to-br from-accent-50 to-accent-100 dark:from-accent-900/40 dark:to-accent-800/40 border-accent-200 dark:border-accent-700",
        info: "bg-gradient-to-br from-info-50 to-info-100 dark:from-info-900/40 dark:to-info-800/40 border-info-200 dark:border-info-700",
        success: "bg-gradient-to-br from-success-50 to-success-100 dark:from-success-900/40 dark:to-success-800/40 border-success-200 dark:border-success-700",
        warning: "bg-gradient-to-br from-warning-50 to-warning-100 dark:from-warning-900/40 dark:to-warning-800/40 border-warning-200 dark:border-warning-700",
        destructive: "bg-gradient-to-br from-destructive-50 to-destructive-100 dark:from-destructive-900/40 dark:to-destructive-800/40 border-destructive-200 dark:border-destructive-700",
        outline: "bg-transparent border-gray-200 dark:border-gray-700",
      },
      hover: {
        default: "hover:shadow-md",
        lift: "hover:shadow-lg hover:-translate-y-1",
        glow: "hover:shadow-lg",
        none: "",
      },
      borderAccent: {
        default: "border-l-4",
        top: "border-t-4",
        right: "border-r-4",
        bottom: "border-b-4",
        none: "",
      }
    },
    defaultVariants: {
      variant: "default",
      hover: "default",
      borderAccent: "none",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, hover, borderAccent, ...props }, ref) => {
    // 根据variant确定边框强调色
    const getBorderAccentColor = () => {
      switch (variant) {
        case 'primary': return 'border-primary-500'
        case 'secondary': return 'border-secondary-500'
        case 'accent': return 'border-accent-500'
        case 'info': return 'border-info-500'
        case 'success': return 'border-success-500'
        case 'warning': return 'border-warning-500'
        case 'destructive': return 'border-destructive-500'
        default: return 'border-gray-300 dark:border-gray-600'
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          cardVariants({ variant, hover, borderAccent, className }),
          borderAccent !== 'none' ? getBorderAccentColor() : ''
        )}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement> & { variant?: string }
>(({ className, variant, ...props }, ref) => {
  // 根据variant确定标题颜色
  const getTitleColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-700 dark:text-primary-300'
      case 'secondary': return 'text-secondary-700 dark:text-secondary-300'
      case 'accent': return 'text-accent-700 dark:text-accent-300'
      case 'info': return 'text-info-700 dark:text-info-300'
      case 'success': return 'text-success-700 dark:text-success-300'
      case 'warning': return 'text-warning-700 dark:text-warning-300'
      case 'destructive': return 'text-destructive-700 dark:text-destructive-300'
      default: return ''
    }
  }

  return (
    <h3
      ref={ref}
      className={cn(
        "text-lg font-semibold leading-none tracking-tight",
        variant ? getTitleColor() : "",
        className
      )}
      {...props}
    />
  )
})
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement> & { variant?: string }
>(({ className, variant, ...props }, ref) => {
  // 根据variant确定描述颜色
  const getDescriptionColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-600/70 dark:text-primary-400/70'
      case 'secondary': return 'text-secondary-600/70 dark:text-secondary-400/70'
      case 'accent': return 'text-accent-600/70 dark:text-accent-400/70'
      case 'info': return 'text-info-600/70 dark:text-info-400/70'
      case 'success': return 'text-success-600/70 dark:text-success-400/70'
      case 'warning': return 'text-warning-600/70 dark:text-warning-400/70'
      case 'destructive': return 'text-destructive-600/70 dark:text-destructive-400/70'
      default: return 'text-muted-foreground'
    }
  }

  return (
    <p
      ref={ref}
      className={cn(
        "text-sm",
        getDescriptionColor(),
        className
      )}
      {...props}
    />
  )
})
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }