import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const inputVariants = cva(
  "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300",
  {
    variants: {
      variant: {
        default: "border-input focus-visible:ring-ring",
        primary: "border-primary/30 focus-visible:ring-primary/50 hover:border-primary/50",
        secondary: "border-secondary/30 focus-visible:ring-secondary/50 hover:border-secondary/50",
        accent: "border-accent/30 focus-visible:ring-accent/50 hover:border-accent/50",
        info: "border-info/30 focus-visible:ring-info/50 hover:border-info/50",
        success: "border-success/30 focus-visible:ring-success/50 hover:border-success/50",
        warning: "border-warning/30 focus-visible:ring-warning/50 hover:border-warning/50",
        destructive: "border-destructive/30 focus-visible:ring-destructive/50 hover:border-destructive/50",
      },
      rounded: {
        default: "rounded-md",
        sm: "rounded-sm",
        lg: "rounded-lg",
        full: "rounded-full",
      },
      size: {
        default: "h-10 px-3 py-2",
        sm: "h-8 px-2 py-1 text-xs",
        lg: "h-12 px-4 py-3 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      rounded: "default",
      size: "default",
    },
  }
)

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant, rounded, size, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          inputVariants({ variant, rounded, size, className }),
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
