'use client'

import React from 'react'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { ThemeProvider } from '@/components/theme-provider'

interface LayoutProps {
  children: React.ReactNode
}

export function Layout({ children }: LayoutProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="flex min-h-screen overflow-hidden">
        <Sidebar />
        <div className="flex flex-col flex-1 ml-60 bg-background min-h-screen overflow-auto">
          <Header />
          <main className="flex-1 p-4 md:p-6 max-w-[1600px] mx-auto w-full">
            {children}
          </main>
        </div>
      </div>
    </ThemeProvider>
  )
}

export default Layout
