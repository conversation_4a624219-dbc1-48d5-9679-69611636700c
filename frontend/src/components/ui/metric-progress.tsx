'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Progress } from './progress'

interface MetricProgressProps {
  label: string
  value: number
  max?: number
  colorScheme?: 'green' | 'blue' | 'purple' | 'amber' | 'red'
  showPercentage?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
  target?: number
  previous?: number
}

export function MetricProgress({
  label,
  value,
  max = 100,
  colorScheme = 'blue',
  showPercentage = true,
  size = 'md',
  className,
  target,
  previous
}: MetricProgressProps) {
  const percentage = (value / max) * 100
  
  const colorMap = {
    green: {
      dot: 'bg-gradient-to-r from-green-500 to-green-600',
      text: 'from-green-600 to-green-700',
      progress: 'from-green-500 to-green-600'
    },
    blue: {
      dot: 'bg-gradient-to-r from-blue-500 to-blue-600',
      text: 'from-blue-600 to-blue-700',
      progress: 'from-blue-500 to-blue-600'
    },
    purple: {
      dot: 'bg-gradient-to-r from-purple-500 to-purple-600',
      text: 'from-purple-600 to-purple-700',
      progress: 'from-purple-500 to-purple-600'
    },
    amber: {
      dot: 'bg-gradient-to-r from-amber-500 to-amber-600',
      text: 'from-amber-600 to-amber-700',
      progress: 'from-amber-500 to-amber-600'
    },
    red: {
      dot: 'bg-gradient-to-r from-red-500 to-red-600',
      text: 'from-red-600 to-red-700',
      progress: 'from-red-500 to-red-600'
    }
  }

  const colors = colorMap[colorScheme]
  
  const sizeMap = {
    sm: {
      height: 'h-1.5',
      dot: 'w-2 h-2',
      text: 'text-xs'
    },
    md: {
      height: 'h-2.5',
      dot: 'w-3 h-3',
      text: 'text-sm'
    },
    lg: {
      height: 'h-3.5',
      dot: 'w-4 h-4',
      text: 'text-base'
    }
  }
  
  const sizes = sizeMap[size]

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className={cn("rounded-full", colors.dot, sizes.dot)}></div>
          <span className={cn("font-medium", sizes.text)}>{label}</span>
        </div>
        {showPercentage && (
          <span className={cn(
            "font-medium bg-gradient-to-r bg-clip-text text-transparent",
            colors.text,
            sizes.text
          )}>
            {typeof value === 'number' ? value : percentage.toFixed(1)}
            {typeof value === 'number' && max !== 100 ? `/${max}` : '%'}
          </span>
        )}
      </div>
      <Progress 
        value={percentage} 
        className={cn(
          "bg-slate-100 dark:bg-slate-800 rounded-full",
          sizes.height
        )} 
        indicatorClassName={cn(
          "bg-gradient-to-r rounded-full",
          colors.progress
        )} 
      />
      {(target !== undefined || previous !== undefined) && (
        <div className="flex justify-between text-xs text-muted-foreground">
          {target !== undefined && <span>目标: {target}{max !== 100 ? '' : '%'}</span>}
          {previous !== undefined && <span>上期: {previous}{max !== 100 ? '' : '%'}</span>}
        </div>
      )}
    </div>
  )
}
