import React from 'react'
import { cn } from '@/lib/utils'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { cva, type VariantProps } from 'class-variance-authority'

const colorfulCardVariants = cva(
  "transition-all duration-300 overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700",
        primary: "bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/40 dark:to-primary-800/40 border-primary-200 dark:border-primary-700",
        secondary: "bg-gradient-to-br from-secondary-50 to-secondary-100 dark:from-secondary-900/40 dark:to-secondary-800/40 border-secondary-200 dark:border-secondary-700",
        accent: "bg-gradient-to-br from-accent-50 to-accent-100 dark:from-accent-900/40 dark:to-accent-800/40 border-accent-200 dark:border-accent-700",
        info: "bg-gradient-to-br from-info-50 to-info-100 dark:from-info-900/40 dark:to-info-800/40 border-info-200 dark:border-info-700",
        success: "bg-gradient-to-br from-success-50 to-success-100 dark:from-success-900/40 dark:to-success-800/40 border-success-200 dark:border-success-700",
        warning: "bg-gradient-to-br from-warning-50 to-warning-100 dark:from-warning-900/40 dark:to-warning-800/40 border-warning-200 dark:border-warning-700",
        destructive: "bg-gradient-to-br from-destructive-50 to-destructive-100 dark:from-destructive-900/40 dark:to-destructive-800/40 border-destructive-200 dark:border-destructive-700",
        outline: "bg-transparent border-gray-200 dark:border-gray-700",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
      },
      hover: {
        default: "hover:shadow-md",
        lift: "hover:shadow-lg hover:-translate-y-1",
        glow: "hover:shadow-lg",
        none: "",
      },
      borderAccent: {
        default: "border-l-4",
        top: "border-t-4",
        right: "border-r-4",
        bottom: "border-b-4",
        none: "",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      hover: "default",
      borderAccent: "none",
    },
  }
)

export interface ColorfulCardProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof colorfulCardVariants> {
  title?: React.ReactNode
  description?: React.ReactNode
  icon?: React.ReactNode
  footer?: React.ReactNode
  stats?: {
    value: string | number
    label: string
    trend?: 'up' | 'down' | 'neutral'
    trendValue?: string | number
  }
}

export function ColorfulCard({
  className,
  variant,
  size,
  hover,
  borderAccent,
  title,
  description,
  icon,
  footer,
  stats,
  children,
  ...props
}: ColorfulCardProps) {
  // 根据variant确定标题和描述的颜色
  const getTitleColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-700 dark:text-primary-300'
      case 'secondary': return 'text-secondary-700 dark:text-secondary-300'
      case 'accent': return 'text-accent-700 dark:text-accent-300'
      case 'info': return 'text-info-700 dark:text-info-300'
      case 'success': return 'text-success-700 dark:text-success-300'
      case 'warning': return 'text-warning-700 dark:text-warning-300'
      case 'destructive': return 'text-destructive-700 dark:text-destructive-300'
      default: return 'text-gray-900 dark:text-gray-100'
    }
  }

  const getDescriptionColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-600/70 dark:text-primary-400/70'
      case 'secondary': return 'text-secondary-600/70 dark:text-secondary-400/70'
      case 'accent': return 'text-accent-600/70 dark:text-accent-400/70'
      case 'info': return 'text-info-600/70 dark:text-info-400/70'
      case 'success': return 'text-success-600/70 dark:text-success-400/70'
      case 'warning': return 'text-warning-600/70 dark:text-warning-400/70'
      case 'destructive': return 'text-destructive-600/70 dark:text-destructive-400/70'
      default: return 'text-gray-500 dark:text-gray-400'
    }
  }

  // 根据variant确定边框强调色
  const getBorderAccentColor = () => {
    switch (variant) {
      case 'primary': return 'border-primary-500'
      case 'secondary': return 'border-secondary-500'
      case 'accent': return 'border-accent-500'
      case 'info': return 'border-info-500'
      case 'success': return 'border-success-500'
      case 'warning': return 'border-warning-500'
      case 'destructive': return 'border-destructive-500'
      default: return 'border-gray-300 dark:border-gray-600'
    }
  }

  // 根据trend确定趋势颜色和图标
  const getTrendColor = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up': return 'text-success-600 dark:text-success-400'
      case 'down': return 'text-destructive-600 dark:text-destructive-400'
      default: return 'text-gray-500 dark:text-gray-400'
    }
  }

  const getTrendIcon = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up': return '↑'
      case 'down': return '↓'
      default: return '→'
    }
  }

  return (
    <Card 
      className={cn(
        colorfulCardVariants({ variant, size, hover, borderAccent, className }),
        borderAccent !== 'none' ? getBorderAccentColor() : '',
        "border"
      )}
      {...props}
    >
      {(title || description || icon) && (
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            {title && (
              <CardTitle className={cn("text-xl font-bold", getTitleColor())}>
                {title}
              </CardTitle>
            )}
            {description && (
              <CardDescription className={cn("mt-1", getDescriptionColor())}>
                {description}
              </CardDescription>
            )}
          </div>
          {icon && (
            <div className={cn(
              "p-2 rounded-full",
              variant !== 'default' && variant !== 'outline' 
                ? `bg-${variant}-200/50 dark:bg-${variant}-800/30 text-${variant}-700 dark:text-${variant}-300` 
                : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
            )}>
              {icon}
            </div>
          )}
        </CardHeader>
      )}
      <CardContent className={cn(
        "pt-2",
        !title && !description && !icon ? "pt-6" : ""
      )}>
        {stats ? (
          <div className="space-y-1">
            <div className="text-3xl font-bold tracking-tight">
              {stats.value}
            </div>
            <div className="flex items-center text-sm">
              <span className={getDescriptionColor()}>{stats.label}</span>
              {stats.trend && stats.trendValue && (
                <span className={cn("ml-2 flex items-center", getTrendColor(stats.trend))}>
                  {getTrendIcon(stats.trend)} {stats.trendValue}
                </span>
              )}
            </div>
          </div>
        ) : children}
      </CardContent>
      {footer && (
        <CardFooter className={cn(
          "pt-2 border-t",
          variant !== 'default' && variant !== 'outline' 
            ? `border-${variant}-200 dark:border-${variant}-700/50` 
            : 'border-gray-200 dark:border-gray-700'
        )}>
          {footer}
        </CardFooter>
      )}
    </Card>
  )
}
