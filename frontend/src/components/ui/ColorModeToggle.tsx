import React from 'react';
import { Button, useColorMode, useColorModeValue, Icon, Tooltip } from '@chakra-ui/react';
import { MoonIcon, SunIcon } from '@chakra-ui/icons';

interface ColorModeToggleProps {
  variant?: 'icon' | 'button';
  size?: 'sm' | 'md' | 'lg';
  ml?: number;
}

const ColorModeToggle: React.FC<ColorModeToggleProps> = ({
  variant = 'icon',
  size = 'md',
  ml
}) => {
  const { colorMode, toggleColorMode } = useColorMode();
  const isDark = colorMode === 'dark';

  // 根据当前颜色模式设置不同的样式
  const bgColor = useColorModeValue('gray.100', 'whiteAlpha.200');
  const hoverBgColor = useColorModeValue('gray.200', 'whiteAlpha.300');
  const color = useColorModeValue('gray.800', 'white');

  // 图标模式
  if (variant === 'icon') {
    return (
      <Tooltip label={isDark ? '切换到亮色模式' : '切换到暗黑模式'}>
        <Button
          onClick={toggleColorMode}
          size={size}
          variant="ghost"
          aria-label={isDark ? '切换到亮色模式' : '切换到暗黑模式'}
          borderRadius="full"
          p={2}
          minW="auto"
          h="auto"
          _hover={{ bg: hoverBgColor }}
          ml={ml}
        >
          {isDark ? <SunIcon /> : <MoonIcon />}
        </Button>
      </Tooltip>
    );
  }

  // 按钮模式
  return (
    <Button
      onClick={toggleColorMode}
      size={size}
      leftIcon={isDark ? <SunIcon /> : <MoonIcon />}
      bg={bgColor}
      color={color}
      _hover={{ bg: hoverBgColor }}
    >
      {isDark ? '亮色模式' : '暗黑模式'}
    </Button>
  );
};

export default ColorModeToggle;
