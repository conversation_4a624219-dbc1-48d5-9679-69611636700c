'use client'

import React, { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { ArrowClockwise } from '@phosphor-icons/react'
import { Button } from './button'

interface PageHeaderProps {
  title: string
  description?: string
  children?: ReactNode
  className?: string
}

export function PageHeader({ title, description, children, className }: PageHeaderProps) {
  return (
    <div className={cn("flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6", className)}>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && <p className="text-muted-foreground mt-1">{description}</p>}
      </div>
      {children}
    </div>
  )
}

interface PageContainerProps {
  children: ReactNode
  className?: string
}

export function PageContainer({ children, className }: PageContainerProps) {
  return (
    <div className={cn("container mx-auto py-6 space-y-6", className)}>
      {children}
    </div>
  )
}

interface RefreshButtonProps {
  onClick: () => void
  isLoading?: boolean
  loadingText?: string
  text?: string
  className?: string
  variant?: 'default' | 'gradient' | 'gradient-green' | 'outline' | 'secondary' | 'ghost'
}

export function RefreshButton({ 
  onClick, 
  isLoading = false, 
  loadingText = '刷新中...', 
  text = '刷新', 
  className,
  variant = 'gradient-green'
}: RefreshButtonProps) {
  return (
    <Button
      onClick={onClick}
      disabled={isLoading}
      variant={variant}
      size="sm"
      className={cn("gap-1", className)}
    >
      <ArrowClockwise size={16} className={isLoading ? "animate-spin" : ""} />
      {isLoading ? loadingText : text}
    </Button>
  )
}

interface PageSectionProps {
  title?: string
  description?: string
  children: ReactNode
  className?: string
}

export function PageSection({ title, description, children, className }: PageSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div>
          {title && <h2 className="text-xl font-semibold">{title}</h2>}
          {description && <p className="text-muted-foreground text-sm">{description}</p>}
        </div>
      )}
      {children}
    </div>
  )
}
