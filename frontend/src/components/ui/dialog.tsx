import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const dialogContentVariants = cva(
  "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
  {
    variants: {
      variant: {
        default: "border-border",
        primary: "border-primary/20 shadow-primary/10",
        secondary: "border-secondary/20 shadow-secondary/10",
        accent: "border-accent/20 shadow-accent/10",
        info: "border-info/20 shadow-info/10",
        success: "border-success/20 shadow-success/10",
        warning: "border-warning/20 shadow-warning/10",
        destructive: "border-destructive/20 shadow-destructive/10",
      },
      size: {
        default: "max-w-lg",
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-xl",
        "2xl": "max-w-2xl",
        "3xl": "max-w-3xl",
        "4xl": "max-w-4xl",
        "5xl": "max-w-5xl",
        full: "max-w-[95vw]",
      },
      withHeader: {
        true: "pt-0",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      withHeader: false,
    },
  }
)

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

interface DialogContentProps
  extends React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>,
    VariantProps<typeof dialogContentVariants> {
  hideCloseButton?: boolean;
}

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  DialogContentProps
>(({ className, children, variant, size, withHeader, hideCloseButton = false, ...props }, ref) => {
  // 根据variant确定关闭按钮的样式
  const getCloseButtonStyle = () => {
    switch (variant) {
      case 'primary': return 'hover:bg-primary-100 dark:hover:bg-primary-900/30 focus:ring-primary'
      case 'secondary': return 'hover:bg-secondary-100 dark:hover:bg-secondary-900/30 focus:ring-secondary'
      case 'accent': return 'hover:bg-accent-100 dark:hover:bg-accent-900/30 focus:ring-accent'
      case 'info': return 'hover:bg-info-100 dark:hover:bg-info-900/30 focus:ring-info'
      case 'success': return 'hover:bg-success-100 dark:hover:bg-success-900/30 focus:ring-success'
      case 'warning': return 'hover:bg-warning-100 dark:hover:bg-warning-900/30 focus:ring-warning'
      case 'destructive': return 'hover:bg-destructive-100 dark:hover:bg-destructive-900/30 focus:ring-destructive'
      default: return 'data-[state=open]:bg-accent data-[state=open]:text-muted-foreground'
    }
  }

  return (
    <DialogPortal>
      <DialogOverlay />
      <DialogPrimitive.Content
        ref={ref}
        className={cn(
          dialogContentVariants({ variant, size, withHeader }),
          className
        )}
        {...props}
      >
        {children}
        {!hideCloseButton && (
          <DialogPrimitive.Close
            className={cn(
              "absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-all duration-200 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none",
              getCloseButtonStyle()
            )}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </DialogPrimitive.Close>
        )}
      </DialogPrimitive.Content>
    </DialogPortal>
  )
})
DialogContent.displayName = DialogPrimitive.Content.displayName

interface DialogHeaderProps extends
  React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
  withBackground?: boolean;
  withBorder?: boolean;
}

const DialogHeader = ({
  className,
  variant = "default",
  withBackground = false,
  withBorder = false,
  ...props
}: DialogHeaderProps) => {
  // 根据variant确定头部的背景样式
  const getHeaderStyle = () => {
    if (withBackground) {
      switch (variant) {
        case 'primary': return 'bg-primary-50 dark:bg-primary-900/20'
        case 'secondary': return 'bg-secondary-50 dark:bg-secondary-900/20'
        case 'accent': return 'bg-accent-50 dark:bg-accent-900/20'
        case 'info': return 'bg-info-50 dark:bg-info-900/20'
        case 'success': return 'bg-success-50 dark:bg-success-900/20'
        case 'warning': return 'bg-warning-50 dark:bg-warning-900/20'
        case 'destructive': return 'bg-destructive-50 dark:bg-destructive-900/20'
        default: return 'bg-muted/50'
      }
    }
    return ''
  }

  // 根据variant确定头部的边框样式
  const getBorderStyle = () => {
    if (withBorder) {
      switch (variant) {
        case 'primary': return 'border-b border-primary-200 dark:border-primary-800/30'
        case 'secondary': return 'border-b border-secondary-200 dark:border-secondary-800/30'
        case 'accent': return 'border-b border-accent-200 dark:border-accent-800/30'
        case 'info': return 'border-b border-info-200 dark:border-info-800/30'
        case 'success': return 'border-b border-success-200 dark:border-success-800/30'
        case 'warning': return 'border-b border-warning-200 dark:border-warning-800/30'
        case 'destructive': return 'border-b border-destructive-200 dark:border-destructive-800/30'
        default: return 'border-b'
      }
    }
    return ''
  }

  return (
    <div
      className={cn(
        "flex flex-col space-y-1.5 text-center sm:text-left p-6 -mx-6 -mt-6 mb-6 rounded-t-lg",
        getHeaderStyle(),
        getBorderStyle(),
        className
      )}
      {...props}
    />
  )
}
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

interface DialogTitleProps extends
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
}

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  DialogTitleProps
>(({ className, variant = "default", ...props }, ref) => {
  // 根据variant确定标题的颜色
  const getTitleColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-700 dark:text-primary-300'
      case 'secondary': return 'text-secondary-700 dark:text-secondary-300'
      case 'accent': return 'text-accent-700 dark:text-accent-300'
      case 'info': return 'text-info-700 dark:text-info-300'
      case 'success': return 'text-success-700 dark:text-success-300'
      case 'warning': return 'text-warning-700 dark:text-warning-300'
      case 'destructive': return 'text-destructive-700 dark:text-destructive-300'
      default: return ''
    }
  }

  return (
    <DialogPrimitive.Title
      ref={ref}
      className={cn(
        "text-lg font-semibold leading-none tracking-tight",
        getTitleColor(),
        className
      )}
      {...props}
    />
  )
})
DialogTitle.displayName = DialogPrimitive.Title.displayName

interface DialogDescriptionProps extends
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description> {
  variant?: "default" | "primary" | "secondary" | "accent" | "info" | "success" | "warning" | "destructive";
}

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  DialogDescriptionProps
>(({ className, variant = "default", ...props }, ref) => {
  // 根据variant确定描述的颜色
  const getDescriptionColor = () => {
    switch (variant) {
      case 'primary': return 'text-primary-600/70 dark:text-primary-400/70'
      case 'secondary': return 'text-secondary-600/70 dark:text-secondary-400/70'
      case 'accent': return 'text-accent-600/70 dark:text-accent-400/70'
      case 'info': return 'text-info-600/70 dark:text-info-400/70'
      case 'success': return 'text-success-600/70 dark:text-success-400/70'
      case 'warning': return 'text-warning-600/70 dark:text-warning-400/70'
      case 'destructive': return 'text-destructive-600/70 dark:text-destructive-400/70'
      default: return 'text-muted-foreground'
    }
  }

  return (
    <DialogPrimitive.Description
      ref={ref}
      className={cn(
        "text-sm",
        getDescriptionColor(),
        className
      )}
      {...props}
    />
  )
})
DialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}