'use client'

import React, { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle } from './card'

type ColorScheme = 'green' | 'blue' | 'purple' | 'amber' | 'red' | 'indigo' | 'teal' | 'pink'

interface StatCardProps {
  title: string
  value: string | number
  icon: ReactNode
  helpText?: string
  colorScheme?: ColorScheme
  className?: string
  trend?: {
    value: string | number
    isPositive?: boolean
  }
}

export function StatCard({ 
  title, 
  value, 
  icon, 
  helpText, 
  colorScheme = 'blue',
  className,
  trend
}: StatCardProps) {
  const colorMap = {
    green: {
      bg: 'from-green-500 to-green-700',
      bgHover: 'from-green-500/5 to-green-700/5',
      shadow: 'shadow-green-500/20',
      text: 'from-green-600 to-green-800',
      badge: 'bg-green-50 text-green-700 border-green-100 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30'
    },
    blue: {
      bg: 'from-blue-500 to-blue-700',
      bgHover: 'from-blue-500/5 to-blue-700/5',
      shadow: 'shadow-blue-500/20',
      text: 'from-blue-600 to-blue-800',
      badge: 'bg-blue-50 text-blue-700 border-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30'
    },
    purple: {
      bg: 'from-purple-500 to-purple-700',
      bgHover: 'from-purple-500/5 to-purple-700/5',
      shadow: 'shadow-purple-500/20',
      text: 'from-purple-600 to-purple-800',
      badge: 'bg-purple-50 text-purple-700 border-purple-100 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/30'
    },
    amber: {
      bg: 'from-amber-500 to-amber-700',
      bgHover: 'from-amber-500/5 to-amber-700/5',
      shadow: 'shadow-amber-500/20',
      text: 'from-amber-600 to-amber-800',
      badge: 'bg-amber-50 text-amber-700 border-amber-100 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/30'
    },
    red: {
      bg: 'from-red-500 to-red-700',
      bgHover: 'from-red-500/5 to-red-700/5',
      shadow: 'shadow-red-500/20',
      text: 'from-red-600 to-red-800',
      badge: 'bg-red-50 text-red-700 border-red-100 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30'
    },
    indigo: {
      bg: 'from-indigo-500 to-indigo-700',
      bgHover: 'from-indigo-500/5 to-indigo-700/5',
      shadow: 'shadow-indigo-500/20',
      text: 'from-indigo-600 to-indigo-800',
      badge: 'bg-indigo-50 text-indigo-700 border-indigo-100 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30'
    },
    teal: {
      bg: 'from-teal-500 to-teal-700',
      bgHover: 'from-teal-500/5 to-teal-700/5',
      shadow: 'shadow-teal-500/20',
      text: 'from-teal-600 to-teal-800',
      badge: 'bg-teal-50 text-teal-700 border-teal-100 dark:bg-teal-900/20 dark:text-teal-400 dark:border-teal-800/30'
    },
    pink: {
      bg: 'from-pink-500 to-pink-700',
      bgHover: 'from-pink-500/5 to-pink-700/5',
      shadow: 'shadow-pink-500/20',
      text: 'from-pink-600 to-pink-800',
      badge: 'bg-pink-50 text-pink-700 border-pink-100 dark:bg-pink-900/20 dark:text-pink-400 dark:border-pink-800/30'
    }
  }

  const colors = colorMap[colorScheme]

  return (
    <Card className={cn(
      "group relative overflow-hidden border-0 bg-white dark:bg-slate-950 shadow-md hover:shadow-xl transition-all duration-500",
      className
    )}>
      <div className={`absolute inset-0 bg-gradient-to-r ${colors.bgHover} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
      <div className={`absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r ${colors.bg}`}></div>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-bold">{title}</CardTitle>
          <div className={`p-2.5 rounded-full bg-gradient-to-br ${colors.bg} text-white shadow-lg ${colors.shadow}`}>
            {icon}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className={`text-4xl font-bold mb-2 bg-gradient-to-r ${colors.text} bg-clip-text text-transparent`}>
          {value}
        </div>
        {(helpText || trend) && (
          <div className="flex items-center">
            {helpText && (
              <span className={`text-xs px-2 py-1 rounded-full border ${colors.badge}`}>
                {helpText}
              </span>
            )}
            {trend && (
              <span className={`text-xs ml-2 ${trend.isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {trend.isPositive ? '↑' : '↓'} {trend.value}
              </span>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
