'use client'

import React, { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from './card'

interface DashboardCardProps {
  title: string
  description?: string
  icon?: ReactNode
  children: ReactNode
  className?: string
  footer?: ReactNode
  gradient?: string
  headerClassName?: string
}

export function DashboardCard({
  title,
  description,
  icon,
  children,
  className,
  footer,
  gradient,
  headerClassName
}: DashboardCardProps) {
  return (
    <Card className={cn(
      "border-0 shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg",
      gradient && `bg-gradient-to-br ${gradient}`,
      className
    )}>
      <CardHeader className={cn("pb-2", headerClassName)}>
        <div className="flex items-center gap-2">
          {icon && (
            <div className="p-2 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 text-white">
              {icon}
            </div>
          )}
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
      {footer && (
        <CardFooter className="border-t pt-4">
          {footer}
        </CardFooter>
      )}
    </Card>
  )
}

interface InfoCardProps {
  title: string
  description: string
  icon: ReactNode
  className?: string
  gradient?: string
  actions?: ReactNode
}

export function InfoCard({
  title,
  description,
  icon,
  className,
  gradient = "from-indigo-50 to-blue-100 dark:from-indigo-950/40 dark:to-blue-900/30",
  actions
}: InfoCardProps) {
  return (
    <Card className={cn(
      "border-0 bg-gradient-to-br overflow-hidden shadow-md hover:shadow-lg transition-all duration-300",
      gradient,
      className
    )}>
      <div className="absolute right-0 top-0 h-24 w-24 rounded-full bg-blue-400/20 blur-2xl"></div>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 text-white">
            {icon}
          </div>
          <CardTitle>{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">
          {description}
        </p>
        {actions}
      </CardContent>
    </Card>
  )
}
