'use client'

import React from 'react';
import { Flex, Text, Badge } from '@chakra-ui/react';

interface TabItemProps {
  icon: React.ReactNode;
  text: string;
  count?: number;
  status?: 'success' | 'error' | 'warning';
}

const TabItem: React.FC<TabItemProps> = ({ icon, text, count, status }) => {
  return (
    <Flex align="center">
      {icon}
      <Text ml={2}>{text}</Text>
      {count !== undefined && (
        <Badge 
          ml={2} 
          colorScheme={status === 'error' ? 'red' : status === 'warning' ? 'yellow' : 'green'} 
          borderRadius="full"
        >
          {count}
        </Badge>
      )}
    </Flex>
  );
};

export default TabItem; 