'use client';

import ShadcnSidebar from './ShadcnSidebar';
import ShadcnHeader from './ShadcnHeader';
import { Providers } from '../app/providers';
import { ToastProvider } from './ui/toast';

export default function RootLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Providers>
      <ToastProvider>
        <div className="flex min-h-screen overflow-hidden">
          <ShadcnSidebar />
          <div className="flex flex-col flex-1 ml-60 bg-background min-h-screen overflow-auto">
            <ShadcnHeader />
            <main className="flex-1 p-4 md:p-6 max-w-[1600px] mx-auto w-full">
              {children}
            </main>
          </div>
        </div>
      </ToastProvider>
    </Providers>
  );
}