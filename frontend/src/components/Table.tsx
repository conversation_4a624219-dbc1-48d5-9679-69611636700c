'use client'

import React from 'react'
import {
  Table as ShadcnTable,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from "@/components/ui/table"

// 兼容NextUI和Chakra UI的Table属性
export interface TableProps extends React.HTMLAttributes<HTMLTableElement> {
  // 通用属性
  variant?: 'simple' | 'striped' | 'bordered'
  size?: 'sm' | 'md' | 'lg'
  
  // NextUI特有属性
  isStriped?: boolean
  isHeaderSticky?: boolean
  
  // Chakra UI特有属性
  colorScheme?: string
}

// 子组件类型
export interface TableColumnProps extends React.ThHTMLAttributes<HTMLTableCellElement> {
  key?: string
  align?: 'start' | 'center' | 'end'
  width?: string | number
}

export interface TableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  key?: string
  isSelected?: boolean
}

export interface TableCellProps extends React.TdHTMLAttributes<HTMLTableCellElement> {
  align?: 'start' | 'center' | 'end'
}

/**
 * Table组件 - 兼容NextUI和Chakra UI的API
 */
export function Table({
  children,
  variant = 'simple',
  size = 'md',
  isStriped = false,
  isHeaderSticky = false,
  colorScheme,
  className = '',
  ...props
}: TableProps) {
  // 处理变体
  const variantClasses = {
    simple: '',
    striped: '[&_tr:nth-child(even)]:bg-muted/50',
    bordered: '[&_td]:border [&_th]:border',
  }
  
  // 处理大小
  const sizeClasses = {
    sm: '[&_td]:py-2 [&_td]:px-3 [&_th]:py-2 [&_th]:px-3 text-xs',
    md: '',
    lg: '[&_td]:py-4 [&_td]:px-6 [&_th]:py-4 [&_th]:px-6 text-base',
  }
  
  // 处理条纹
  const stripedClass = isStriped ? variantClasses.striped : ''
  
  // 处理粘性表头
  const stickyHeaderClass = isHeaderSticky ? '[&_thead]:sticky [&_thead]:top-0 [&_thead]:z-10 [&_thead]:bg-background' : ''
  
  // 处理颜色方案
  const colorSchemeClass = colorScheme ? `[&_th]:text-${colorScheme}-700 [&_thead]:bg-${colorScheme}-50` : ''
  
  return (
    <ShadcnTable 
      className={`${variantClasses[variant]} ${sizeClasses[size]} ${stripedClass} ${stickyHeaderClass} ${colorSchemeClass} ${className}`}
      {...props}
    >
      {children}
    </ShadcnTable>
  )
}

/**
 * TableHeader组件
 */
export function Thead(props: React.HTMLAttributes<HTMLTableSectionElement>) {
  return <TableHeader {...props} />
}

/**
 * TableBody组件
 */
export function Tbody(props: React.HTMLAttributes<HTMLTableSectionElement>) {
  return <TableBody {...props} />
}

/**
 * TableFooter组件
 */
export function Tfoot(props: React.HTMLAttributes<HTMLTableSectionElement>) {
  return <TableFooter {...props} />
}

/**
 * TableRow组件
 */
export function Tr({ isSelected, className = '', ...props }: TableRowProps) {
  const selectedClass = isSelected ? 'bg-primary/10' : ''
  return <TableRow className={`${selectedClass} ${className}`} {...props} />
}

/**
 * TableHead组件
 */
export function Th({ align = 'start', width, className = '', ...props }: TableColumnProps) {
  const alignClass = {
    start: 'text-left',
    center: 'text-center',
    end: 'text-right',
  }
  
  const style = width ? { width: typeof width === 'number' ? `${width}px` : width } : {}
  
  return (
    <TableHead 
      className={`${alignClass[align]} ${className}`}
      style={style}
      {...props} 
    />
  )
}

/**
 * TableCell组件
 */
export function Td({ align = 'start', className = '', ...props }: TableCellProps) {
  const alignClass = {
    start: 'text-left',
    center: 'text-center',
    end: 'text-right',
  }
  
  return <TableCell className={`${alignClass[align]} ${className}`} {...props} />
}

/**
 * TableCaption组件
 */
export function Caption(props: React.HTMLAttributes<HTMLTableCaptionElement>) {
  return <TableCaption {...props} />
}

/**
 * 用法示例:
 * 
 * <Table variant="striped" size="md">
 *   <Thead>
 *     <Tr>
 *       <Th>Name</Th>
 *       <Th>Age</Th>
 *       <Th align="end">Actions</Th>
 *     </Tr>
 *   </Thead>
 *   <Tbody>
 *     <Tr>
 *       <Td>John</Td>
 *       <Td>25</Td>
 *       <Td align="end">
 *         <Button size="sm">Edit</Button>
 *       </Td>
 *     </Tr>
 *   </Tbody>
 * </Table>
 */
