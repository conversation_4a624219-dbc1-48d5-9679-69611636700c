import React from 'react';
import { 
  Box, 
  Text, 
  Flex, 
  Stat, 
  StatLabel, 
  StatNumber, 
  StatHelpText, 
  useColorModeValue, 
  Badge
} from '@chakra-ui/react';
import { useTranslation } from '@/contexts/LanguageContext';

interface DeviceMetricsCardProps {
  metricName: string;
  metricValue: number;
  unit: string;
  timestamp: string;
  status?: 'normal' | 'warning' | 'critical';
  thresholds?: {
    warning?: number;
    critical?: number;
  };
}

const DeviceMetricsCard: React.FC<DeviceMetricsCardProps> = ({
  metricName,
  metricValue,
  unit,
  timestamp,
  status = 'normal',
  thresholds
}) => {
  const { t } = useTranslation();
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  // Format value based on units
  const formattedValue = (() => {
    if (unit === '%') {
      return `${metricValue.toFixed(1)}%`;
    } else if (unit === 'MB' || unit === 'GB') {
      return `${metricValue.toFixed(2)} ${unit}`;
    } else if (unit === 'C' || unit === '°C') {
      return `${metricValue.toFixed(1)}°C`;
    } else {
      return `${metricValue} ${unit}`;
    }
  })();

  // Determine status color
  const statusColor = (() => {
    switch (status) {
      case 'critical':
        return 'red';
      case 'warning':
        return 'orange';
      case 'normal':
      default:
        return 'green';
    }
  })();

  // Format timestamp to readable format
  const formattedTime = new Date(timestamp).toLocaleString();
  
  return (
    <Box 
      bg={cardBg} 
      borderRadius="lg" 
      borderWidth="1px" 
      borderColor={borderColor}
      p={4}
      boxShadow="sm"
      position="relative"
      overflow="hidden"
    >
      <Flex justifyContent="space-between" alignItems="center" mb={1}>
        <Text fontWeight="medium" fontSize="sm" color="gray.500">
          {t(`metrics.${metricName}`) || metricName}
        </Text>
        <Badge colorScheme={statusColor} fontSize="xs" borderRadius="full" px={2}>
          {t(`status.${status}`) || status}
        </Badge>
      </Flex>
      
      <Stat mt={2}>
        <StatNumber fontSize="2xl" fontWeight="bold">{formattedValue}</StatNumber>
        <StatHelpText fontSize="xs" mt={1}>
          {t('lastUpdate') || 'Last Update'}: {formattedTime}
        </StatHelpText>
      </Stat>
      
      {thresholds && (
        <Text fontSize="xs" color="gray.500" mt={2}>
          {thresholds.warning && (
            <Text as="span" mr={2}>
              {t('warning') || 'Warning'}: {thresholds.warning}{unit}
            </Text>
          )}
          {thresholds.critical && (
            <Text as="span">
              {t('critical') || 'Critical'}: {thresholds.critical}{unit}
            </Text>
          )}
        </Text>
      )}
    </Box>
  );
};

export default DeviceMetricsCard; 