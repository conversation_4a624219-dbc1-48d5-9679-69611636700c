'use client';

import React, { useState } from 'react';
import {
    Box,
    TextField,
    Button,
    Paper,
    Typography,
    Alert,
    Snackbar
} from '@mui/material';
import * as phoneExtensionService from '../../services/phoneExtensionService';

export const PhoneExtensionForm: React.FC = () => {
    const [formData, setFormData] = useState({
        cabinetNumber: '',
        extensionNumber: '',
        socketNumber: '',
        user: ''
    });
    const [snackbar, setSnackbar] = useState({
        open: false,
        message: '',
        severity: 'success' as 'success' | 'error'
    });

    const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
        setFormData(prev => ({
            ...prev,
            [field]: event.target.value
        }));
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        
        try {
            await phoneExtensionService.createExtension({
                extension_number: formData.extensionNumber,
                name: formData.user,
                department: '',
                type: 'internal',
                status: 'active',
                notes: `柜号: ${formData.cabinetNumber}, 插座号: ${formData.socketNumber}`
            });

            // 清空表单
            setFormData({
                cabinetNumber: '',
                extensionNumber: '',
                socketNumber: '',
                user: ''
            });

            setSnackbar({
                open: true,
                message: '添加成功',
                severity: 'success'
            });
        } catch (error) {
            setSnackbar({
                open: true,
                message: '添加失败',
                severity: 'error'
            });
            console.error('Error creating phone extension:', error);
        }
    };

    return (
        <Paper sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
            <Typography variant="h6" gutterBottom>
                增加电话分机
            </Typography>
            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
                <TextField
                    fullWidth
                    required
                    label="分机柜号"
                    type="number"
                    value={formData.cabinetNumber}
                    onChange={handleChange('cabinetNumber')}
                    margin="normal"
                />
                <TextField
                    fullWidth
                    label="分机号"
                    value={formData.extensionNumber}
                    onChange={handleChange('extensionNumber')}
                    margin="normal"
                />
                <TextField
                    fullWidth
                    label="插座号"
                    value={formData.socketNumber}
                    onChange={handleChange('socketNumber')}
                    margin="normal"
                />
                <TextField
                    fullWidth
                    label="使用人"
                    value={formData.user}
                    onChange={handleChange('user')}
                    margin="normal"
                />
                <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    sx={{ mt: 3 }}
                    fullWidth
                >
                    添加
                </Button>
            </Box>

            <Snackbar
                open={snackbar.open}
                autoHideDuration={3000}
                onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            >
                <Alert severity={snackbar.severity}>
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Paper>
    );
}; 