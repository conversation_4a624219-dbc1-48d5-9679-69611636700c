'use client';

import React, { useEffect, useState } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    TextField,
    IconButton,
    Tooltip,
    CircularProgress,
    Alert,
    Snackbar
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import { PhoneExtension } from '../../types/phoneExtension';
import * as phoneExtensionService from '../../services/phoneExtensionService';

export const PhoneExtensionList: React.FC = () => {
    const [extensions, setExtensions] = useState<PhoneExtension[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [editedFields, setEditedFields] = useState<Record<number, Partial<PhoneExtension>>>({});
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

    // 加载数据
    useEffect(() => {
        loadExtensions();
    }, []);

    const loadExtensions = async () => {
        try {
            const data = await phoneExtensionService.getAllExtensions();
            setExtensions(data);
            setError(null);
        } catch (err) {
            setError('加载数据失败');
            console.error('Error loading extensions:', err);
        } finally {
            setLoading(false);
        }
    };

    // 处理字段编辑
    const handleFieldChange = (extension: PhoneExtension, field: keyof PhoneExtension, value: string) => {
        setEditedFields(prev => ({
            ...prev,
            [extension.id]: {
                ...prev[extension.id],
                [field]: value
            }
        }));
    };

    // 保存更改
    const handleSave = async (extension: PhoneExtension) => {
        const changes = editedFields[extension.id];
        if (!changes) return;

        try {
            await phoneExtensionService.updateExtension(extension.id, {
                extension_number: changes.extensionNumber || extension.extensionNumber,
                name: changes.user || extension.user,
                department: '',
                type: 'internal',
                status: 'active'
            });

            // 更新本地数据
            setExtensions(prev => prev.map(ext => 
                ext.id === extension.id
                    ? { ...ext, ...changes }
                    : ext
            ));

            // 清除编辑状态
            setEditedFields(prev => {
                const newState = { ...prev };
                delete newState[extension.id];
                return newState;
            });

            setSnackbar({
                open: true,
                message: '保存成功',
                severity: 'success'
            });
        } catch (err) {
            setSnackbar({
                open: true,
                message: '保存失败',
                severity: 'error'
            });
            console.error('Error saving extension:', err);
        }
    };

    if (loading) {
        return <CircularProgress />;
    }

    if (error) {
        return <Alert severity="error">{error}</Alert>;
    }

    return (
        <>
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>分机柜号</TableCell>
                            <TableCell>分机号</TableCell>
                            <TableCell>插座号</TableCell>
                            <TableCell>使用人</TableCell>
                            <TableCell>操作</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {extensions.map((extension) => {
                            const edited = editedFields[extension.id];
                            const hasChanges = !!edited;

                            return (
                                <TableRow key={extension.id}>
                                    <TableCell>{extension.cabinetNumber}</TableCell>
                                    <TableCell>
                                        <TextField
                                            size="small"
                                            value={edited?.extensionNumber ?? extension.extensionNumber}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                                                handleFieldChange(extension, 'extensionNumber', e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <TextField
                                            size="small"
                                            value={edited?.socketNumber ?? extension.socketNumber}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                                                handleFieldChange(extension, 'socketNumber', e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <TextField
                                            size="small"
                                            value={edited?.user ?? extension.user}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                                                handleFieldChange(extension, 'user', e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        {hasChanges && (
                                            <Tooltip title="保存">
                                                <IconButton
                                                    color="primary"
                                                    onClick={() => handleSave(extension)}
                                                >
                                                    <SaveIcon />
                                                </IconButton>
                                            </Tooltip>
                                        )}
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                    </TableBody>
                </Table>
            </TableContainer>

            <Snackbar
                open={snackbar.open}
                autoHideDuration={3000}
                onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            >
                <Alert severity={snackbar.severity}>
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </>
    );
}; 