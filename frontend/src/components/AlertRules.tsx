'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { AlertRule } from '@/types/alert'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { toast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'

const alertSchema = z.object({
  name: z.string().min(2, '名称至少需要2个字符'),
  metric: z.string().min(1, '请选择监控指标'),
  condition: z.enum(['>', '<', '=', '!=', 'contains', 'not_contains']),
  threshold: z.string().min(1, '阈值不能为空'),
  severity: z.enum(['info', 'warning', 'critical']),
  isActive: z.boolean()
})

interface AlertRulesProps {
  deviceId: number
  deviceType: 'snmp' | 'ssh'
  metrics: string[]
}

export function AlertRules({ deviceId, deviceType, metrics }: AlertRulesProps) {
  const [rules, setRules] = useState<AlertRule[]>([])
  const [loading, setLoading] = useState(true)

  const form = useForm<z.infer<typeof alertSchema>>({
    resolver: zodResolver(alertSchema),
    defaultValues: {
      name: '',
      metric: '',
      condition: '>',
      threshold: '',
      severity: 'warning',
      isActive: true
    }
  })

  const loadRules = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/alerts?deviceId=${deviceId}`)
      const data = await response.json()
      setRules(data)
    } catch (err) {
      console.error('加载报警规则失败:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRules()
  }, [deviceId])

  const onSubmit = async (values: z.infer<typeof alertSchema>) => {
    try {
      const ruleData: AlertRule = {
        ...values,
        deviceId,
        deviceType,
        metric: values.metric
      }

      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ruleData)
      })

      if (response.ok) {
        toast({
          title: "操作成功",
          description: "报警规则已保存",
        })
        loadRules()
        form.reset()
      }
    } catch (err) {
      toast({
        title: "操作失败",
        description: err instanceof Error ? err.message : "保存报警规则失败",
        variant: "destructive",
      })
    }
  }

  const severityVariant = {
    info: 'default',
    warning: 'secondary',
    critical: 'destructive'
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 p-4 border rounded-lg">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>规则名称</FormLabel>
                  <FormControl>
                    <Input placeholder="CPU使用率过高" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metric"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>监控指标</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择监控指标" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {metrics.map((metric) => (
                        <SelectItem key={metric} value={metric}>
                          {metric}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="condition"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>条件</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择条件" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value=">">大于</SelectItem>
                      <SelectItem value="<">小于</SelectItem>
                      <SelectItem value="=">等于</SelectItem>
                      <SelectItem value="!=">不等于</SelectItem>
                      <SelectItem value="contains">包含</SelectItem>
                      <SelectItem value="not_contains">不包含</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="threshold"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>阈值</FormLabel>
                  <FormControl>
                    <Input placeholder="90" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>严重程度</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择严重程度" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="info">信息</SelectItem>
                      <SelectItem value="warning">警告</SelectItem>
                      <SelectItem value="critical">严重</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel>启用规则</FormLabel>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Button type="submit">保存规则</Button>
        </form>
      </Form>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>名称</TableHead>
              <TableHead>指标</TableHead>
              <TableHead>条件</TableHead>
              <TableHead>阈值</TableHead>
              <TableHead>严重程度</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rules.map((rule) => (
              <TableRow key={rule.id}>
                <TableCell>{rule.name}</TableCell>
                <TableCell>{rule.metric}</TableCell>
                <TableCell>{rule.condition}</TableCell>
                <TableCell>{rule.threshold}</TableCell>
                <TableCell>
                  <Badge variant={severityVariant[rule.severity]}>
                    {rule.severity}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                    {rule.isActive ? '启用' : '禁用'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm">
                    编辑
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}