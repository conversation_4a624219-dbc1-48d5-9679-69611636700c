'use client'

import * as React from 'react'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { CalendarIcon, Loader2, Server, Network, Database, HardDrive, Shield, Cpu, MemoryStick, Wifi, QrCode, RefreshCw, Save } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'

// 表单验证模式
const assetFormSchema = z.object({
  // 基础信息
  name: z.string().min(2, '资产名称至少需要2个字符'),
  code: z.string().min(3, '资产编码至少需要3个字符'),
  category_id: z.number().min(1, '请选择资产分类').optional(),
  manufacturer: z.string().optional(),
  model: z.string().optional(),
  serial_number: z.string().optional(),
  status: z.enum(['active', 'maintenance', 'standby', 'offline', 'retired']),
  location: z.string().optional(),
  department: z.string().optional(),
  responsible_person: z.string().optional(),
  description: z.string().optional(),
  
  // 采购信息
  purchase_date: z.date().optional(),
  supplier: z.string().optional(),
  purchase_order_number: z.string().optional(),
  price: z.number().min(0).optional(),
  warranty_expire_date: z.date().optional(),
  asset_value: z.number().min(0).optional(),
  
  // 网络配置
  ip_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的IP地址'
  ).optional().or(z.literal('')),
  business_ip_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的业务IP地址'
  ).optional().or(z.literal('')),
  management_ip_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的管理IP地址'
  ).optional().or(z.literal('')),
  idrac_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的iDRAC管理地址'
  ).optional().or(z.literal('')),
  mac_address: z.string().regex(
    /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
    '请输入有效的MAC地址'
  ).optional().or(z.literal('')),
  hostname: z.string().optional(),
  domain: z.string().optional(),
  subnet_mask: z.string().optional(),
  gateway: z.string().optional(),
  dns_servers: z.string().optional(),
  vlan_id: z.number().min(1).max(4094).optional(),
  
  // 硬件规格
  cpu_model: z.string().optional(),
  cpu_cores: z.number().min(1).optional(),
  memory_size: z.number().min(1).optional(),
  storage_size: z.number().min(1).optional(),
  storage_type: z.enum(['SSD', 'HDD', 'NVMe', 'Hybrid']).optional(),
  port_count: z.number().min(1).optional(),
  port_speed: z.string().optional(),
  power_consumption: z.number().min(0).optional(),
  operating_temperature: z.string().optional(),
  
  // 网络耗材硬件配置
  memory_type: z.string().optional(), // 内存类型：DDR3, DDR4, DDR5
  memory_frequency: z.string().optional(), // 内存频率：2400MHz, 3200MHz
  memory_capacity: z.string().optional(), // 内存容量：8GB, 16GB, 32GB
  storage_interface: z.string().optional(), // 存储接口：SATA, NVMe, M.2
  storage_capacity: z.string().optional(), // 存储容量：256GB, 512GB, 1TB
  cpu_socket: z.string().optional(), // CPU插槽：LGA1151, AM4
  cpu_generation: z.string().optional(), // CPU代数：第10代, 第11代
  adapter_type: z.string().optional(), // 转接头类型：USB-C to HDMI, VGA to DVI
  connector_specification: z.string().optional(), // 连接器规格：USB 3.0, Type-C, Lightning
  
  // 软件信息
  operating_system: z.string().optional(),
  os_version: z.string().optional(),
  firmware_version: z.string().optional(),
  firmware_update_date: z.date().optional(),
  firmware_vendor: z.string().optional(),
  middleware_services: z.string().optional(),
  container_name: z.string().optional(),
  business_name: z.string().optional(),
  business_port: z.string().optional(),
  remarks: z.string().optional(),
  
  // 网络设备专用字段
  device_type: z.string().optional(),
  port_configuration: z.string().optional(),
  vlan_support: z.boolean().optional(),
  routing_protocols: z.string().optional(),
  management_interface: z.string().optional(),
  
  // 终端设备专用字段
  screen_size: z.string().optional(),
  screen_resolution: z.string().optional(),
  battery_capacity: z.string().optional(),
  wireless_support: z.string().optional(),
  peripheral_ports: z.string().optional(),
  
  // 服务器业务信息
  business_service: z.string().optional(),
  business_services: z.string().optional(),
  business_ports: z.string().optional(),
  service_ports: z.string().optional(),
  management_ip: z.string().optional(),
  cluster_info: z.string().optional(),
  virtualization_platform: z.string().optional(),
  business_notes: z.string().optional(),
  
  // 合同资产专用字段
  contract_number: z.string().optional(),
  contract_start_date: z.date().optional(),
  contract_end_date: z.date().optional(),
  contract_amount: z.number().min(0).optional(),
  contract_supplier: z.string().optional(),
  contract_vendor: z.string().optional(),
  contract_type: z.string().optional(),
  contract_notes: z.string().optional(),
  auto_renewal: z.boolean().optional(),
  
  // 维保资产专用字段
  maintenance_provider: z.string().optional(),
  maintenance_level: z.string().optional(),
  maintenance_cost: z.number().min(0).optional(),
  maintenance_contact: z.string().optional(),
  maintenance_notes: z.string().optional(),
  response_time: z.string().optional(),
  maintenance_scope: z.string().optional(),
  maintenance_start_date: z.date().optional(),
  maintenance_end_date: z.date().optional(),
  
  // 授权信息
  license_type: z.string().optional(),
  license_key: z.string().optional(),
  license_start_date: z.date().optional(),
  license_end_date: z.date().optional(),
  license_seats: z.number().min(1).optional(),
  license_cost: z.number().min(0).optional(),
  license_notes: z.string().optional(),
  license_user_count: z.number().min(1).optional(),
  license_concurrent_users: z.number().min(1).optional(),
  
  // 位置信息字段
  building: z.string().optional(),
  floor: z.string().optional(),
  room: z.string().optional(),
  rack_id: z.string().optional(),
  rack_position: z.string().optional(),
  rack_unit: z.number().optional(),
  
  // 机房管理字段（网络设备和服务器专用）
  area: z.string().optional(), // 区域
  management_scope: z.string().optional(), // 管理范围
  cold_aisle: z.enum(['cold_aisle_1', 'cold_aisle_2']).optional(), // 冷通道1或冷通道2
  cabinet_number: z.string().optional(), // 机柜号
  cabinet_u_position: z.string().optional(), // 机柜U位
  occupied_u_units: z.number().min(1).optional(), // 占用U位
  mains_pdu_number: z.string().optional(), // 市电PDU编号
  ups_pdu_number: z.string().optional(), // UPS PDU编号
  
  // 安全配置
  security_level: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  encryption_enabled: z.boolean(),
  
  // 耗材相关字段
  is_consumable: z.boolean(),
  quantity: z.number().min(0).optional(),
  unit: z.string().optional(),
  min_stock_level: z.number().min(0).optional(),
  consumable_type: z.string().optional(),
  consumable_unit: z.string().optional(),
  consumable_quantity: z.number().min(0).optional(),
  consumable_unit_price: z.number().min(0).optional(),
  consumable_total_price: z.number().min(0).optional(),
  consumable_expiry_date: z.date().optional(),
  consumable_storage_location: z.string().optional(),
  consumable_notes: z.string().optional(),
})

type AssetFormData = z.infer<typeof assetFormSchema>

interface AssetCategory {
  id: number
  name: string
  code: string
  description?: string
  level: number
  parent_id?: number
}

interface ITAssetRegistrationFormProps {
  onSubmit: (data: AssetFormData) => Promise<void>
  initialData?: Partial<AssetFormData>
  isLoading?: boolean
  assetType?: 'server' | 'network' | 'storage' | 'other'
}

export function ITAssetRegistrationForm({ 
  onSubmit, 
  initialData, 
  isLoading = false,
  assetType = 'server'
}: ITAssetRegistrationFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [autoGenerateCode, setAutoGenerateCode] = useState(true)

  const form = useForm<AssetFormData>({
    resolver: zodResolver(assetFormSchema),
    defaultValues: {
      name: '',
      code: '',
      category_id: undefined,
      status: 'active' as const,
      encryption_enabled: false,
      is_consumable: false,
      manufacturer: '',
      model: '',
      serial_number: '',
      location: '',
      department: '',
      responsible_person: '',
      description: '',
      supplier: '',
      purchase_order_number: '',
      hostname: '',
      domain: '',
      subnet_mask: '',
      gateway: '',
      dns_servers: '',
      cpu_model: '',
      storage_type: undefined,
      port_speed: '',
      operating_temperature: '',
      operating_system: '',
      os_version: '',
      firmware_version: '',
      firmware_vendor: '',
      middleware_services: '',
      container_name: '',
      business_name: '',
      business_port: '',
      remarks: '',
      device_type: '',
      port_configuration: '',
      routing_protocols: '',
      management_interface: '',
      screen_size: '',
      screen_resolution: '',
      battery_capacity: '',
      wireless_support: '',
      peripheral_ports: '',
      business_service: '',
      business_services: '',
      business_ports: '',
      service_ports: '',
      management_ip: '',
      management_ip_address: '',
      cluster_info: '',
      virtualization_platform: '',
      business_notes: '',
      contract_number: '',
      contract_amount: 0,
      contract_supplier: '',
      contract_vendor: '',
      contract_type: '',
      contract_notes: '',
      contract_start_date: undefined,
      contract_end_date: undefined,
      auto_renewal: false,
      maintenance_provider: '',
      maintenance_level: '',
      maintenance_cost: 0,
      maintenance_contact: '',
      maintenance_notes: '',
      response_time: '',
      maintenance_scope: '',
      maintenance_start_date: undefined,
      maintenance_end_date: undefined,
      license_type: '',
      license_key: '',
      license_start_date: undefined,
      license_end_date: undefined,
      license_seats: 1,
      license_cost: 0,
      license_notes: '',
      license_user_count: 1,
      license_concurrent_users: 1,
      building: '',
      floor: '',
      room: '',
      rack_id: '',
      rack_position: '',
      security_level: undefined,
      // 耗材相关字段
      quantity: 0,
      unit: '',
      min_stock_level: 0,
      consumable_type: '',
      consumable_unit: '',
      consumable_quantity: 0,
      consumable_unit_price: 0,
      consumable_total_price: 0,
      consumable_expiry_date: undefined,
      consumable_storage_location: '',
      consumable_notes: '',
      ...initialData
    }
  })

  // 监听category_id变化
  const watchedCategoryId = form.watch('category_id') || 0

  // 根据选择的资产分类获取图标
  const getAssetIcon = (categoryId?: number) => {
    const selectedCategoryId = categoryId || watchedCategoryId
    if (selectedCategoryId && categories.length > 0) {
      const selectedCategory = categories.find(c => c.id === selectedCategoryId)
      if (selectedCategory) {
        const categoryName = selectedCategory.name.toLowerCase()
        const categoryCode = selectedCategory.code.toLowerCase()
        
        if (categoryName.includes('服务器') || categoryCode.includes('srv')) {
          return <Server className="h-5 w-5" />
        } else if (categoryName.includes('网络') || categoryName.includes('交换') || categoryName.includes('路由') || categoryCode.includes('net')) {
          return <Network className="h-5 w-5" />
        } else if (categoryName.includes('存储') || categoryName.includes('硬盘') || categoryCode.includes('str')) {
          return <Database className="h-5 w-5" />
        } else if (categoryName.includes('安全') || categoryName.includes('防火墙')) {
          return <Shield className="h-5 w-5" />
        } else if (categoryName.includes('cpu') || categoryName.includes('处理器')) {
          return <Cpu className="h-5 w-5" />
        } else if (categoryName.includes('内存')) {
          return <MemoryStick className="h-5 w-5" />
        } else if (categoryName.includes('无线') || categoryName.includes('wifi')) {
          return <Wifi className="h-5 w-5" />
        }
      }
    }
    return <HardDrive className="h-5 w-5" />
  }

  // 根据选择的资产分类获取标题
  const getAssetTitle = (categoryId?: number) => {
    const selectedCategoryId = categoryId || watchedCategoryId
    if (selectedCategoryId && categories.length > 0) {
      const selectedCategory = categories.find(c => c.id === selectedCategoryId)
      if (selectedCategory) {
        return `${selectedCategory.name}资产登记`
      }
    }
    return '资产登记'
  }

  // 获取可见的标签页
  const getVisibleTabs = (categoryId?: number) => {
    const selectedCategoryId = categoryId || watchedCategoryId
    if (!selectedCategoryId || categories.length === 0) {
      return ['basic']
    }

    const selectedCategory = categories.find(c => c.id === selectedCategoryId)
    if (!selectedCategory) {
      return ['basic']
    }

    const categoryName = selectedCategory.name.toLowerCase()
    const categoryCode = selectedCategory.code.toLowerCase()

    // 根据不同的资产分类显示不同的标签页
    if (categoryName.includes('服务器') || categoryCode.includes('srv')) {
      return ['basic', 'purchase', 'network', 'hardware', 'software', 'business']
    } else if (categoryName.includes('打印耗材') || categoryName.includes('墨盒') || categoryName.includes('硒鼓') || categoryName.includes('碳粉') || categoryName.includes('打印纸')) {
      return ['basic', 'purchase', 'consumable']
    } else if (categoryName.includes('网络耗材') || categoryName.includes('内存条') || categoryName.includes('硬盘') || categoryName.includes('cpu') || categoryName.includes('处理器') || categoryName.includes('移动硬盘') || categoryName.includes('转接头') || categoryName.includes('其他配件') || categoryCode.includes('cons-net')) {
      return ['basic', 'purchase', 'hardware', 'consumable']
    } else if (categoryName.includes('办公耗材')) {
      return ['basic', 'purchase', 'consumable']
    } else if (categoryName.includes('安防') || categoryName.includes('监控') || categoryName.includes('摄像头') || categoryName.includes('录像机') || categoryName.includes('门禁') || categoryCode.includes('sec')) {
      return ['basic', 'purchase', 'network', 'hardware']
    } else if (categoryName.includes('网络') || categoryName.includes('交换') || categoryName.includes('路由') || categoryName.includes('防火墙') || categoryCode.includes('net')) {
      return ['basic', 'purchase', 'network', 'hardware', 'firmware']
    } else if (categoryName.includes('终端') || categoryName.includes('台式') || categoryName.includes('笔记本') || categoryCode.includes('end')) {
      return ['basic', 'purchase', 'hardware', 'terminal']
    } else if (categoryName.includes('消耗品') || categoryName.includes('耗材') || categoryCode.includes('cons')) {
      return ['basic', 'purchase', 'consumable']
    } else if (categoryName.includes('存储') || categoryCode.includes('str')) {
      return ['basic', 'purchase', 'hardware']
    } else if (categoryName.includes('软件') || categoryCode === 'sw') {
      return ['basic', 'purchase', 'software', 'license']
    } else if (categoryName.includes('合同') || categoryCode.includes('contract')) {
      return ['basic', 'contract', 'license']
    } else if (categoryName.includes('维保') || categoryCode.includes('maint')) {
      return ['basic', 'maintenance', 'license']
    } else {
      return ['basic', 'purchase']
    }
  }

  // 获取标签页配置
  const getTabConfig = () => {
    return {
      basic: { label: '基础信息', icon: <HardDrive className="h-4 w-4" /> },
      purchase: { label: '采购信息', icon: <QrCode className="h-4 w-4" /> },
      network: { label: '网络配置', icon: <Network className="h-4 w-4" /> },
      hardware: { label: '硬件规格', icon: <Cpu className="h-4 w-4" /> },
      software: { label: '软件信息', icon: <Database className="h-4 w-4" /> },
      firmware: { label: '固件信息', icon: <RefreshCw className="h-4 w-4" /> },
      terminal: { label: '终端规格', icon: <MemoryStick className="h-4 w-4" /> },
      business: { label: '业务信息', icon: <Server className="h-4 w-4" /> },
      contract: { label: '合同信息', icon: <QrCode className="h-4 w-4" /> },
      maintenance: { label: '维保信息', icon: <Shield className="h-4 w-4" /> },
      license: { label: '授权信息', icon: <Shield className="h-4 w-4" /> },
      consumable: { label: '耗材信息', icon: <MemoryStick className="h-4 w-4" /> }
    }
  }

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/asset-categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data)
        }
      } catch (error) {
        console.error('获取资产分类失败:', error)
        toast({
          title: '错误',
          description: '获取资产分类失败',
          variant: 'destructive'
        })
      }
    }
    fetchCategories()
  }, [])

  // 构建层级化的分类选项
  const buildHierarchicalCategories = (categories: AssetCategory[], parentId: number | null = null, level: number = 0): any[] => {
    const result: any[] = []
    const children = categories.filter(cat => cat.parent_id === parentId)
    
    children.forEach(category => {
      const indent = '　'.repeat(level)
      result.push({
        ...category,
        displayName: `${indent}${category.name}`
      })
      
      const subCategories = buildHierarchicalCategories(categories, category.id, level + 1)
      result.push(...subCategories)
    })
    
    return result
  }

  const hierarchicalCategories = buildHierarchicalCategories(categories)

  // 生成资产编码
  const generateAssetCode = (categoryId: number, name: string) => {
    const category = categories.find(c => c.id === categoryId)
    const timestamp = Date.now().toString().slice(-6)
    
    if (category) {
      const categoryPrefix = category.code.toUpperCase()
      const namePrefix = name.slice(0, 2).toUpperCase()
      return `${categoryPrefix}-${namePrefix}-${timestamp}`
    }
    
    return `AST-${name.slice(0, 2).toUpperCase()}-${timestamp}`
  }

  // 监听表单变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name: fieldName }) => {
      if (fieldName === 'category_id' || fieldName === 'name') {
        const categoryId = value.category_id as number
        const assetName = value.name as string
        
        if (categoryId && assetName && autoGenerateCode) {
          const newCode = generateAssetCode(categoryId, assetName)
          form.setValue('code', newCode)
        }
      }
    })
    
    return () => subscription.unsubscribe()
  }, [form, autoGenerateCode, categories])

  // 监听分类变化，自动切换标签页
  useEffect(() => {
    if (watchedCategoryId && categories.length > 0) {
      const visibleTabs = getVisibleTabs(watchedCategoryId)
      console.log('Selected Category ID:', watchedCategoryId)
      console.log('Visible Tabs:', visibleTabs)
      console.log('Active Tab:', activeTab)
      
      if (!visibleTabs.includes(activeTab)) {
        setActiveTab(visibleTabs[0])
      }
    }
  }, [watchedCategoryId, categories, activeTab])

  // 表单提交处理
  const handleSubmit = async (data: AssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      toast({
        title: '成功',
        description: '资产登记成功'
      })
    } catch (error) {
      console.error('提交失败:', error)
      toast({
        title: '错误',
        description: '资产登记失败',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 渲染基础信息
  const renderBasicInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产分类 *</FormLabel>
              <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString() || ''}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择资产分类" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hierarchicalCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择资产状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="active">使用中</SelectItem>
                  <SelectItem value="maintenance">维护中</SelectItem>
                  <SelectItem value="standby">备用</SelectItem>
                  <SelectItem value="offline">离线</SelectItem>
                  <SelectItem value="retired">已退役</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产名称 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入资产名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                资产编码 *
                <div className="flex items-center gap-2">
                  <Switch
                    checked={autoGenerateCode}
                    onCheckedChange={setAutoGenerateCode}
                  />
                  <span className="text-xs text-muted-foreground">自动生成</span>
                </div>
              </FormLabel>
              <FormControl>
                <Input 
                  placeholder="请输入资产编码" 
                  {...field} 
                  disabled={autoGenerateCode}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="manufacturer"
          render={({ field }) => (
            <FormItem>
              <FormLabel>制造商</FormLabel>
              <FormControl>
                <Input placeholder="请输入制造商" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="model"
          render={({ field }) => (
            <FormItem>
              <FormLabel>型号</FormLabel>
              <FormControl>
                <Input placeholder="请输入型号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="serial_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>序列号</FormLabel>
              <FormControl>
                <Input placeholder="请输入序列号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>位置</FormLabel>
              <FormControl>
                <Input placeholder="请输入位置" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>所属部门</FormLabel>
              <FormControl>
                <Input placeholder="请输入所属部门" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>责任人</FormLabel>
              <FormControl>
                <Input placeholder="请输入责任人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>描述</FormLabel>
            <FormControl>
              <Textarea
                placeholder="请输入资产描述..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      {/* 机房管理字段 - 仅对网络设备和服务器显示 */}
      {(() => {
        const selectedCategoryId = form.watch('category_id')
        const selectedCategory = categories.find(c => c.id === selectedCategoryId)
        const categoryName = selectedCategory?.name.toLowerCase() || ''
        const categoryCode = selectedCategory?.code.toLowerCase() || ''
        
        // 判断是否为网络设备或服务器
        const isNetworkOrServer = categoryName.includes('网络设备') || categoryName.includes('服务器') || categoryCode.includes('net-') || categoryCode.includes('server')
        
        if (!isNetworkOrServer) return null
        
        return (
          <div className="space-y-4">
            <div className="border-t pt-4">
              <h4 className="text-lg font-medium mb-4">机房管理信息</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>区域</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择区域" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="2f_weak_current_room">2F 弱电间</SelectItem>
                          <SelectItem value="2f_gym">2F 健身房</SelectItem>
                          <SelectItem value="3f_data_center">3F 数据中心</SelectItem>
                          <SelectItem value="4f_weak_current_room">4F 弱电间</SelectItem>
                          <SelectItem value="7f_weak_current_room">7F 弱电间</SelectItem>
                          <SelectItem value="shanghai_office">上海办公区</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="management_scope"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>管理范围</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入管理范围" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <FormField
                  control={form.control}
                  name="cold_aisle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>冷通道</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择冷通道" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="cold_aisle_1">冷通道1</SelectItem>
                          <SelectItem value="cold_aisle_2">冷通道2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="cabinet_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>机柜号</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择机柜号" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 15 }, (_, i) => {
                            const cabinetNum = `A${String(i + 1).padStart(2, '0')}`
                            return (
                              <SelectItem key={cabinetNum} value={cabinetNum}>
                                {cabinetNum}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="cabinet_u_position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>机柜U位</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择机柜U位" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 42 }, (_, i) => {
                            const uPosition = String(i + 1)
                            return (
                              <SelectItem key={uPosition} value={uPosition}>
                                U{uPosition}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <FormField
                  control={form.control}
                  name="occupied_u_units"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>占用U位</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="请输入占用U位数量" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="mains_pdu_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>市电PDU编号</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择市电PDU编号" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const pduNum = String(i + 1)
                            return (
                              <SelectItem key={pduNum} value={pduNum}>
                                {pduNum}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="ups_pdu_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>UPS PDU编号</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择UPS PDU编号" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const pduNum = String(i + 1)
                            return (
                              <SelectItem key={pduNum} value={pduNum}>
                                {pduNum}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        )
      })()
    }
      </div>
    );
  }

  // 渲染采购信息
  const renderPurchaseInfo = () => {
    return (
      <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="purchase_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>采购日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="warranty_expire_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>保修到期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="supplier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>供应商</FormLabel>
              <FormControl>
                <Input placeholder="请输入供应商" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="purchase_order_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>采购订单号</FormLabel>
              <FormControl>
                <Input placeholder="请输入采购订单号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>采购价格</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入采购价格" 
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="asset_value"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产价值</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入资产价值" 
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        </div>
      </div>
    );
  }

  // 渲染网络配置
  const renderNetworkInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
          control={form.control}
          name="ip_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>IP地址</FormLabel>
              <FormControl>
                <Input placeholder="*************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="business_ip_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>业务IP地址</FormLabel>
              <FormControl>
                <Input placeholder="**********" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="management_ip_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>管理IP地址</FormLabel>
              <FormControl>
                <Input placeholder="************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="mac_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>MAC地址</FormLabel>
              <FormControl>
                <Input placeholder="00:11:22:33:44:55" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="hostname"
          render={({ field }) => (
            <FormItem>
              <FormLabel>主机名</FormLabel>
              <FormControl>
                <Input placeholder="server-001" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="subnet_mask"
          render={({ field }) => (
            <FormItem>
              <FormLabel>子网掩码</FormLabel>
              <FormControl>
                <Input placeholder="*************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="gateway"
          render={({ field }) => (
            <FormItem>
              <FormLabel>网关</FormLabel>
              <FormControl>
                <Input placeholder="192.168.1.1" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="dns_servers"
          render={({ field }) => (
            <FormItem>
              <FormLabel>DNS服务器</FormLabel>
              <FormControl>
                <Input placeholder="8.8.8.8, 8.8.4.4" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        </div>
      </div>
    );
  }

  // 渲染硬件规格
  const renderHardwareInfo = () => {
    const selectedCategoryId = form.watch('category_id')
    const selectedCategory = categories.find(c => c.id === selectedCategoryId)
    const categoryName = selectedCategory?.name.toLowerCase() || ''
    const categoryCode = selectedCategory?.code.toLowerCase() || ''
    
    // 判断是否为网络耗材
    const isNetworkConsumable = categoryName.includes('网络耗材') || categoryName.includes('内存条') || categoryName.includes('硬盘') || categoryName.includes('cpu') || categoryName.includes('处理器') || categoryName.includes('移动硬盘') || categoryName.includes('转接头') || categoryName.includes('其他配件') || categoryCode.includes('cons-net')
    
    if (isNetworkConsumable) {
      return (
        <div className="space-y-6">
          {/* 内存条配置 */}
          {(categoryName.includes('内存') || categoryName.includes('ram')) && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">内存条配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="memory_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>内存类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择内存类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="DDR3">DDR3</SelectItem>
                          <SelectItem value="DDR4">DDR4</SelectItem>
                          <SelectItem value="DDR5">DDR5</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="memory_frequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>内存频率</FormLabel>
                      <FormControl>
                        <Input placeholder="2400MHz, 3200MHz" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="memory_capacity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>内存容量</FormLabel>
                      <FormControl>
                        <Input placeholder="8GB, 16GB, 32GB" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* 硬盘配置 */}
          {(categoryName.includes('硬盘') || categoryName.includes('移动硬盘')) && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">存储设备配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="storage_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>存储类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择存储类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="SSD">固态硬盘(SSD)</SelectItem>
                          <SelectItem value="HDD">机械硬盘(HDD)</SelectItem>
                          <SelectItem value="NVMe">NVMe SSD</SelectItem>
                          <SelectItem value="Hybrid">混合硬盘</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="storage_interface"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>接口类型</FormLabel>
                      <FormControl>
                        <Input placeholder="SATA, NVMe, M.2, USB 3.0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="storage_capacity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>存储容量</FormLabel>
                      <FormControl>
                        <Input placeholder="256GB, 512GB, 1TB" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* CPU配置 */}
          {(categoryName.includes('cpu') || categoryName.includes('处理器')) && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">CPU处理器配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="cpu_model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU型号</FormLabel>
                      <FormControl>
                        <Input placeholder="Intel i7-12700K, AMD Ryzen 7 5800X" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="cpu_socket"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU插槽</FormLabel>
                      <FormControl>
                        <Input placeholder="LGA1700, AM4, LGA1151" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="cpu_generation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU代数</FormLabel>
                      <FormControl>
                        <Input placeholder="第12代, 第11代, Zen3" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* 转接头配置 */}
          {categoryName.includes('转接头') && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">转接头配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="adapter_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>转接头类型</FormLabel>
                      <FormControl>
                        <Input placeholder="USB-C to HDMI, VGA to DVI, Type-C to USB" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="connector_specification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>连接器规格</FormLabel>
                      <FormControl>
                        <Input placeholder="USB 3.0, Type-C, Lightning, Thunderbolt" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* 通用硬件信息 */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium">通用硬件信息</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="manufacturer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>制造商</FormLabel>
                    <FormControl>
                      <Input placeholder="Intel, AMD, Kingston, Samsung" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>产品型号</FormLabel>
                    <FormControl>
                      <Input placeholder="具体产品型号" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
      )
    }

    // 默认硬件配置（非网络耗材）
    return (
      <div className="space-y-6">
        <p>Hardware configuration placeholder</p>
      </div>
    )
  }

  // 渲染软件信息
  const renderSoftwareInfo = () => (
    <div className="space-y-6">
      <p>Software configuration placeholder</p>
    </div>
  )

  // 渲染固件信息
  const renderFirmwareInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firmware_version"
          render={({ field }) => (
            <FormItem>
              <FormLabel>固件版本</FormLabel>
              <FormControl>
                <Input placeholder="v2.1.3" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="firmware_vendor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>固件厂商</FormLabel>
              <FormControl>
                <Input placeholder="Cisco, Huawei, H3C" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firmware_update_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>固件更新日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择固件更新日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date > new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="device_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>设备类型</FormLabel>
              <FormControl>
                <Input placeholder="核心交换机, 接入交换机" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="management_interface"
          render={({ field }) => (
            <FormItem>
              <FormLabel>管理接口类型</FormLabel>
              <FormControl>
                <Input placeholder="Console, SSH, Telnet, Web" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="vlan_support"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">VLAN支持</FormLabel>
                <FormDescription>
                  设备是否支持VLAN功能
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="routing_protocols"
        render={({ field }) => (
          <FormItem>
            <FormLabel>支持的路由协议</FormLabel>
            <FormControl>
              <Input placeholder="OSPF, BGP, RIP, EIGRP" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="port_configuration"
        render={({ field }) => (
          <FormItem>
            <FormLabel>端口配置信息</FormLabel>
            <FormControl>
              <Textarea
                placeholder="请输入端口配置详情，如端口数量、类型、速率等..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    )

  // 渲染终端规格信息
  const renderTerminalInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="screen_size"
          render={({ field }) => (
            <FormItem>
              <FormLabel>屏幕尺寸</FormLabel>
              <FormControl>
                <Input placeholder="15.6英寸, 24英寸" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="screen_resolution"
          render={({ field }) => (
            <FormItem>
              <FormLabel>屏幕分辨率</FormLabel>
              <FormControl>
                <Input placeholder="1920x1080, 2560x1440" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="battery_capacity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>电池容量</FormLabel>
              <FormControl>
                <Input placeholder="4000mAh, 6芯锂电池" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="wireless_support"
          render={({ field }) => (
            <FormItem>
              <FormLabel>无线支持</FormLabel>
              <FormControl>
                <Input placeholder="WiFi 6, 蓝牙 5.0, NFC" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="peripheral_ports"
        render={({ field }) => (
          <FormItem>
            <FormLabel>外设接口</FormLabel>
            <FormControl>
              <Textarea
                placeholder="USB 3.0 x4, HDMI x1, Type-C x2, 3.5mm音频接口..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    )

  // 渲染业务信息
  const renderBusinessInfo = (): JSX.Element => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="business_service"
            render={({ field }) => (
              <FormItem>
                <FormLabel>业务服务</FormLabel>
                <FormControl>
                  <Input placeholder="请输入业务服务名称" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="business_port"
            render={({ field }) => (
              <FormItem>
                <FormLabel>业务端口</FormLabel>
                <FormControl>
                  <Input placeholder="80, 443, 8080" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="management_ip"
            render={({ field }) => (
              <FormItem>
                <FormLabel>管理IP地址</FormLabel>
                <FormControl>
                  <Input placeholder="*************" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="cluster_info"
            render={({ field }) => (
              <FormItem>
                <FormLabel>集群信息</FormLabel>
                <FormControl>
                  <Input placeholder="请输入集群名称或配置" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="virtualization_platform"
          render={({ field }) => (
            <FormItem>
              <FormLabel>虚拟化平台</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="选择虚拟化平台" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="vmware">VMware vSphere</SelectItem>
                  <SelectItem value="hyper-v">Microsoft Hyper-V</SelectItem>
                  <SelectItem value="kvm">KVM</SelectItem>
                  <SelectItem value="xen">Citrix Xen</SelectItem>
                  <SelectItem value="docker">Docker</SelectItem>
                  <SelectItem value="kubernetes">Kubernetes</SelectItem>
                  <SelectItem value="none">无虚拟化</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="business_notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>业务备注</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入业务相关备注信息" 
                  className="min-h-[100px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    )
  }

  // 渲染合同信息
  const renderContractInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="contract_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>合同编号</FormLabel>
                <FormControl>
                  <Input placeholder="请输入合同编号" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="contract_amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>合同金额</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入合同金额" 
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="contract_start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>合同开始日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择开始日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="contract_end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>合同结束日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择结束日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="contract_supplier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同供应商</FormLabel>
              <FormControl>
                <Input placeholder="请输入供应商名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="contract_notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同备注</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入合同相关备注信息" 
                  className="min-h-[100px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    )
  }

  // 渲染维保信息
  const renderMaintenanceInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="maintenance_provider"
            render={({ field }) => (
              <FormItem>
                <FormLabel>维保服务商</FormLabel>
                <FormControl>
                  <Input placeholder="请输入维保服务商名称" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="maintenance_level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>维保级别</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择维保级别" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="basic">基础维保</SelectItem>
                    <SelectItem value="standard">标准维保</SelectItem>
                    <SelectItem value="premium">高级维保</SelectItem>
                    <SelectItem value="enterprise">企业级维保</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="maintenance_start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>维保开始日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择开始日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="maintenance_end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>维保结束日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择结束日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="maintenance_cost"
            render={({ field }) => (
              <FormItem>
                <FormLabel>维保费用</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入维保费用" 
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="maintenance_contact"
            render={({ field }) => (
              <FormItem>
                <FormLabel>维保联系人</FormLabel>
                <FormControl>
                  <Input placeholder="请输入维保联系人" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="maintenance_notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>维保备注</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入维保相关备注信息" 
                  className="min-h-[100px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    )
  }

  // 渲染许可证信息
  const renderLicenseInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="license_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>许可证类型</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择许可证类型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="software">软件许可证</SelectItem>
                    <SelectItem value="hardware">硬件许可证</SelectItem>
                    <SelectItem value="service">服务许可证</SelectItem>
                    <SelectItem value="subscription">订阅许可证</SelectItem>
                    <SelectItem value="perpetual">永久许可证</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="license_key"
            render={({ field }) => (
              <FormItem>
                <FormLabel>许可证密钥</FormLabel>
                <FormControl>
                  <Input placeholder="请输入许可证密钥" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="license_start_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>许可证开始日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择开始日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="license_end_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>许可证结束日期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择结束日期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="license_seats"
            render={({ field }) => (
              <FormItem>
                <FormLabel>许可证席位数</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入席位数量" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="license_cost"
            render={({ field }) => (
              <FormItem>
                <FormLabel>许可证费用</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入许可证费用" 
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="license_notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>许可证备注</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入许可证相关备注信息" 
                  className="min-h-[100px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    )
  }

  // 渲染耗材信息
  const renderConsumableInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="consumable_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>耗材类型</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择耗材类型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="toner">墨粉/墨盒</SelectItem>
                    <SelectItem value="paper">纸张</SelectItem>
                    <SelectItem value="cable">线缆</SelectItem>
                    <SelectItem value="battery">电池</SelectItem>
                    <SelectItem value="filter">滤芯</SelectItem>
                    <SelectItem value="cleaning">清洁用品</SelectItem>
                    <SelectItem value="other">其他耗材</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="consumable_unit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>计量单位</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="选择计量单位" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="piece">个</SelectItem>
                    <SelectItem value="box">盒</SelectItem>
                    <SelectItem value="pack">包</SelectItem>
                    <SelectItem value="roll">卷</SelectItem>
                    <SelectItem value="meter">米</SelectItem>
                    <SelectItem value="kg">千克</SelectItem>
                    <SelectItem value="liter">升</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="consumable_quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>数量</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="请输入数量" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="consumable_unit_price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>单价</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    step="0.01"
                    placeholder="请输入单价" 
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="consumable_total_price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>总价</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    step="0.01"
                    placeholder="请输入总价" 
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="consumable_expiry_date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>有效期</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>选择有效期</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="consumable_storage_location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>存放位置</FormLabel>
                <FormControl>
                  <Input placeholder="请输入存放位置" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <FormField
          control={form.control}
          name="consumable_notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>耗材备注</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="请输入耗材相关备注信息" 
                  className="min-h-[100px]"
                  {...field} 
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {watchedCategoryId ? getAssetIcon(watchedCategoryId) : <HardDrive className="h-5 w-5" />}
          {watchedCategoryId ? getAssetTitle(watchedCategoryId) : '资产登记'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {watchedCategoryId && (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                {(() => {
                  const visibleTabs = getVisibleTabs()
                  const tabConfig = getTabConfig()

                  return (
                    <>
                      <TabsList className={`grid w-full ${
                        visibleTabs.length === 1 ? 'grid-cols-1' :
                        visibleTabs.length === 2 ? 'grid-cols-2' :
                        visibleTabs.length === 3 ? 'grid-cols-3' :
                        visibleTabs.length === 4 ? 'grid-cols-4' :
                        'grid-cols-5'
                      }`}>
                        {visibleTabs.map((tabKey: string) => {
                          const config = tabConfig[tabKey as keyof typeof tabConfig]
                          return (
                            <TabsTrigger key={tabKey} value={tabKey} className="flex items-center gap-2">
                              {config.icon}
                              {config.label}
                            </TabsTrigger>
                          )
                        })}
                      </TabsList>

                      {visibleTabs.includes('basic') && (
                        <TabsContent value="basic" className="space-y-6">
                          {renderBasicInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('purchase') && (
                        <TabsContent value="purchase" className="space-y-6">
                          {renderPurchaseInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('network') && (
                        <TabsContent value="network" className="space-y-6">
                          {renderNetworkInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('hardware') && (
                        <TabsContent value="hardware" className="space-y-6">
                          {renderHardwareInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('software') && (
                        <TabsContent value="software" className="space-y-6">
                          {renderSoftwareInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('firmware') && (
                        <TabsContent value="firmware" className="space-y-6">
                          {renderFirmwareInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('terminal') && (
                        <TabsContent value="terminal" className="space-y-6">
                          {renderTerminalInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('business') && (
                        <TabsContent value="business" className="space-y-6">
                          {renderBusinessInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('contract') && (
                        <TabsContent value="contract" className="space-y-6">
                          {renderContractInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('maintenance') && (
                        <TabsContent value="maintenance" className="space-y-6">
                          {renderMaintenanceInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('license') && (
                        <TabsContent value="license" className="space-y-6">
                          {renderLicenseInfo()}
                        </TabsContent>
                      )}

                      {visibleTabs.includes('consumable') && (
                        <TabsContent value="consumable" className="space-y-6">
                          {renderConsumableInfo()}
                        </TabsContent>
                      )}
                    </>
                  )
                })()}
              </Tabs>
            )}

            <div className="flex justify-between items-center">
              <div className="flex space-x-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => window.open('/resources/assets/register/add', '_blank')}
                >
                  新增资产
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => window.open('/resources/assets/register/categories', '_blank')}
                >
                  资产分类
                </Button>
              </div>
              <div className="flex space-x-4">
                <Button type="button" variant="outline" onClick={() => form.reset()}>
                  重置
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? '提交中...' : '提交'}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default ITAssetRegistrationForm;