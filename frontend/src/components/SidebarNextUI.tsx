'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTranslation } from '@/contexts/LanguageContext'
import { useNextUITheme } from '@/contexts/NextUIThemeContext'
import { Box } from '@chakra-ui/react'
import {
  House,
  Cube,
  Package,
  Wrench,
  Gear,
  Bell,
  Globe,
  WifiHigh,
  Database,
  Warning,
  Lightning,
  Printer,
  CaretDown,
  CaretRight,
} from '@phosphor-icons/react'

interface NavItemProps {
  icon: React.ElementType
  translationKey: string
  href: string
  isActive?: boolean
}

const NavItem = ({ icon, translationKey, href, isActive }: NavItemProps) => {
  const { t } = useTranslation()
  const { isDarkMode } = useNextUITheme()

  // 为不同的菜单项分配不同的颜色
  const getMenuColor = (key: string): string => {
    const colorMap: Record<string, string> = {
      'dashboard': 'from-primary-400 to-primary-600',
      'resources': 'from-secondary-400 to-secondary-600',
      'network': 'from-info-400 to-info-600',
      'wireless': 'from-accent-400 to-accent-600',
      'vulnerability.scan': 'from-destructive-400 to-destructive-600',
      'assets': 'from-success-400 to-success-600',
      'deployments': 'from-warning-400 to-warning-600',
      'notifications': 'from-colorful-pink to-colorful-purple',
      'settings': 'from-colorful-blue to-colorful-indigo',
      'power.monitoring': 'from-colorful-teal to-colorful-green',
      'smart.ops': 'from-colorful-orange to-colorful-yellow'
    }
    return colorMap[key] || 'from-gray-400 to-gray-600'
  }

  const getChineseTitle = (key: string): string => {
    const menuTitles: Record<string, string> = {
      'dashboard': '仪表盘',
      'resources': '资源管理',
      'network': '网络管理',
      'network.topology': '网络拓扑',
      'wireless': '无线网络',
      'vulnerability.scan': '漏洞扫描',
      'assets': '资产管理',
      'deployments': '部署',
      'procedures': '操作规程',
      'notifications': '通知',
      'updates': '更新',
      'settings': '设置',
      'power.monitoring': '基础设施监控',
      'smart.ops': '智能运维',
      'mobile.demo': '移动端演示'
    };
    return menuTitles[key] || t(key);
  }

  return (
    <Link href={href} className="text-decoration-none">
      <div
        className={`
          flex items-center p-2 mx-2 rounded-md cursor-pointer relative
          transition-all duration-300
          ${isActive
            ? `bg-gradient-to-r ${getMenuColor(translationKey)}`
            : 'bg-transparent'}
          hover:${isDarkMode ? 'bg-white/10' : 'bg-black/5'}
        `}
      >
        {isActive && (
          <div
            className="absolute left-0 top-1/2 -translate-y-1/2 w-[3px] h-[70%] bg-white rounded-r-sm"
          />
        )}
        <div
          className={`
            ${isActive ? 'ml-3' : 'ml-4'} mr-3 text-xl
            ${isActive
              ? 'text-white'
              : (isDarkMode ? 'text-gray-400' : 'text-gray-600')}
            transition-all duration-300
          `}
        >
          {React.createElement(icon)}
        </div>
        <span
          className={`
            text-sm
            ${isActive
              ? 'text-white font-medium'
              : (isDarkMode ? 'text-gray-400 font-normal' : 'text-gray-600 font-normal')}
            transition-all duration-300
          `}
        >
          {getChineseTitle(translationKey)}
        </span>
      </div>
    </Link>
  )
}

interface SubNavItemProps {
  translationKey: string
  href: string
  isActive?: boolean
}

const SubNavItem = ({ translationKey, href, isActive }: SubNavItemProps) => {
  const { t } = useTranslation()
  const { isDarkMode } = useNextUITheme()

  // 为子菜单项分配不同的颜色
  const getSubMenuColor = (key: string): string => {
    // 根据父菜单类型分配不同的颜色系列
    if (key.startsWith('network')) {
      return 'from-info-300 to-info-500'
    } else if (key.includes('monitoring')) {
      return 'from-accent-300 to-accent-500'
    } else if (key.includes('asset')) {
      return 'from-success-300 to-success-500'
    } else if (key.includes('management')) {
      return 'from-primary-300 to-primary-500'
    } else if (key.includes('config')) {
      return 'from-secondary-300 to-secondary-500'
    } else if (key.includes('ups')) {
      return 'from-warning-300 to-warning-500'
    }
    return 'from-gray-300 to-gray-500'
  }

  const getChineseSubTitle = (key: string): string => {
    const subMenuTitles: Record<string, string> = {
      'network.fault.impact': '故障影响',
      'network.path.visualization': '路径可视化',
      'terminal.info': '终端信息',
      'network.config.backup': '配置备份',
      'config.management': '配置管理',
      'digital.ip.management': '数字IP管理',
      'phone.extension.management': '电话分机管理',
      'vm.management': '虚拟机管理',
      'printer.management': '打印机管理',
      'wireless.monitoring': '无线网络监控',
      'bandwidth.monitoring': '网络带宽监控',
      'environment.monitoring': '环境监控',
      'ups.monitoring': 'UPS监控',
      'mains.power.monitoring': '市电监控',
      'snmp.config.management': 'SNMP配置管理',
      'snmp.collection.management': 'SNMP采集管理',
      'asset.management.system': '资产概览',
      'asset.register': '资产登记',
      'asset.map': '资产地图',
    };
    return subMenuTitles[key] || t(key);
  }

  return (
    <Link href={href} className="text-decoration-none">
      <div
        className={`
          flex items-center p-2 pl-9 mx-2 rounded-md cursor-pointer relative
          transition-all duration-300
          ${isActive
            ? `bg-gradient-to-r ${getSubMenuColor(translationKey)}`
            : 'bg-transparent'}
          hover:${isDarkMode ? 'bg-white/10' : 'bg-black/5'}
        `}
        onClick={(e) => e.stopPropagation()}
      >
        {isActive && (
          <div
            className="absolute left-0 top-1/2 -translate-y-1/2 w-[3px] h-[70%] bg-white rounded-r-sm"
          />
        )}
        <span
          className={`
            text-sm transition-all duration-300
            ${isActive
              ? 'text-white font-medium'
              : (isDarkMode ? 'text-gray-400 font-normal' : 'text-gray-600 font-normal')}
          `}
        >
          {getChineseSubTitle(translationKey)}
        </span>
      </div>
    </Link>
  )
}

// 网络管理子菜单
const NetworkSubmenu = () => {
  const { isDarkMode } = useNextUITheme()
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/network')

  return (
    <Box>
      <div className="pt-0 pb-1">
        <SubNavItem
          translationKey="network.fault.impact"
          href="/network/fault-impact"
          isActive={pathname === '/network/fault-impact'}
        />
        <SubNavItem
          translationKey="network.path.visualization"
          href="/network/path-visualization"
          isActive={pathname === '/network/path-visualization'}
        />
        <SubNavItem
          translationKey="terminal.info"
          href="/network/terminal-info"
          isActive={pathname === '/network/terminal-info'}
        />
        <SubNavItem
          translationKey="network.config.backup"
          href="/network/config-backup"
          isActive={pathname === '/network/config-backup'}
        />
        <SubNavItem
          translationKey="config.management"
          href="/network/config-management"
          isActive={pathname === '/network/config-management'}
        />
        <SubNavItem
          translationKey="digital.ip.management"
          href="/network/digital-ip"
          isActive={pathname === '/network/digital-ip'}
        />
        <SubNavItem
          translationKey="phone.extension.management"
          href="/network/phone-extensions"
          isActive={pathname === '/network/phone-extensions'}
        />
        <SubNavItem
          translationKey="vm.management"
          href="/network/vm-management"
          isActive={pathname === '/network/vm-management'}
        />
        <SubNavItem
          translationKey="printer.management"
          href="/network/printer-monitoring"
          isActive={pathname === '/network/printer-monitoring'}
        />
        <SubNavItem
          translationKey="wireless.monitoring"
          href="/network/wireless-monitoring"
          isActive={pathname === '/network/wireless-monitoring'}
        />
        <SubNavItem
          translationKey="bandwidth.monitoring"
          href="/network/bandwidth-monitoring"
          isActive={pathname === '/network/bandwidth-monitoring'}
        />
      </div>
    </Box>
  )
}

// 基础设施监控子菜单
const PowerMonitoringSubmenu = () => {
  const { isDarkMode } = useNextUITheme()
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/power-monitoring')

  return (
    <Box>
      <div className="pt-0 pb-1">
        <SubNavItem
          translationKey="environment.monitoring"
          href="/power-monitoring/environment"
          isActive={pathname === '/power-monitoring/environment'}
        />
        <SubNavItem
          translationKey="ups.monitoring"
          href="/power-monitoring/ups"
          isActive={pathname === '/power-monitoring/ups' || pathname.startsWith('/power-monitoring/ups/')}
        />
        <SubNavItem
          translationKey="mains.power.monitoring"
          href="/power-monitoring/mains"
          isActive={pathname === '/power-monitoring/mains'}
        />
        <SubNavItem
          translationKey="snmp.collection.management"
          href="/power-monitoring/snmp-collection"
          isActive={pathname === '/power-monitoring/snmp-collection'}
        />
      </div>
    </Box>
  )
}

// 资产管理子菜单
const AssetsSubmenu = () => {
  const { isDarkMode } = useNextUITheme()
  const pathname = usePathname() ?? '/'
  const isActive = pathname.startsWith('/resources/asset') || pathname === '/assets' || pathname.startsWith('/assets/')

  return (
    <Box>
      <div className="pt-0 pb-1">
        <SubNavItem
          translationKey="asset.management.system"
          href="/assets"
          isActive={pathname === '/assets' && !pathname.startsWith('/assets/register')}
        />
        <SubNavItem
          translationKey="asset.register"
          href="/assets/register-new"
          isActive={pathname.startsWith('/assets/register')}
        />
        <SubNavItem
          translationKey="asset.map"
          href="/resources/asset-map"
          isActive={pathname === '/resources/asset-map'}
        />
      </div>
    </Box>
  )
}

// 主侧边栏组件
const SidebarNextUI = () => {
  const { isDarkMode } = useNextUITheme()
  const pathname = usePathname() ?? '/'

  // 默认展开当前路径所在的菜单
  const defaultExpandedKeys = new Set([
    pathname.startsWith('/network') ? 'network' : '',
    pathname.startsWith('/power-monitoring') ? 'power-monitoring' : '',
    pathname.startsWith('/resources/asset') || pathname === '/assets' || pathname.startsWith('/assets/') ? 'assets' : '',
  ].filter(Boolean));

  return (
    <div className={`
      fixed left-0 top-0 h-full w-60 overflow-y-auto z-40
      ${isDarkMode ? 'bg-gray-900' : 'bg-white'}
      border-r ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
      transition-all duration-300 shadow-lg
    `}>
      <div className="flex items-center justify-center h-16 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-500 to-secondary-500">
        <div className="text-xl font-bold text-white">RS资产管理系统</div>
      </div>

      <div className="py-4">
        <NavItem
          icon={House}
          translationKey="dashboard"
          href="/"
          isActive={pathname === '/'}
        />

        <div className="mt-6 mb-2 px-6">
          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            主要功能
          </div>
        </div>

        <Box>
          <NetworkSubmenu />
          <PowerMonitoringSubmenu />
          <AssetsSubmenu />
        </Box>

        <div className="mt-6 mb-2 px-6">
          <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            系统
          </div>
        </div>

        <NavItem
          icon={Bell}
          translationKey="notifications"
          href="/notifications"
          isActive={pathname === '/notifications'}
        />

        <NavItem
          icon={Gear}
          translationKey="settings"
          href="/settings"
          isActive={pathname === '/settings'}
        />
      </div>
    </div>
  )
}

export default SidebarNextUI
