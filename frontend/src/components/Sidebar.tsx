'use client'

import {
  Box,
  VStack,
  Icon,
  Text,
  Flex,
  useColorMode,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
} from '@chakra-ui/react'
import {
  House,
  Cube,
  Package,
  Wrench,
  Gear,
  Bell,
  Globe,
  WifiHigh,
  Database,
  Warning,
  Lightning,
  Printer,
} from '@phosphor-icons/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTranslation } from '@/contexts/LanguageContext'
import { CustomAccordionIcon } from './CustomAccordionIcon'
import React, { useState } from 'react'

interface NavItemProps {
  icon: React.ElementType
  translationKey: string
  href: string
  isActive?: boolean
}

const NavItem = ({ icon, translationKey, href, isActive }: NavItemProps) => {
  const { t } = useTranslation()
  const { colorMode } = useColorMode()

  const getChineseTitle = (key: string): string => {
    const menuTitles: Record<string, string> = {
      'dashboard': '仪表盘',
      'resources': '资源管理',
      'network': '网络管理',
      'network.topology': '网络拓扑',
      'wireless': '无线网络',
      'vulnerability.scan': '漏洞扫描',
      'assets': '资产管理',
      'deployments': '部署',
      'procedures': '操作规程',
      'notifications': '通知',
      'updates': '更新',
      'settings': '设置',
      'power.monitoring': '基础设施监控',
      'smart.ops': '智能运维',
      'mobile.demo': '移动端演示'
    };
    return menuTitles[key] || t(key);
  }

  return (
    <Link href={href} style={{ textDecoration: 'none' }}>
      <Flex
        align="center"
        p={2}
        mx={2}
        borderRadius="md"
        role="group"
        cursor="pointer"
        position="relative"
        _hover={{
          bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50',
        }}
        bg={isActive
          ? (colorMode === 'dark' ? 'whiteAlpha.200' : 'gray.100')
          : 'transparent'
        }
        transition="all 0.2s"
      >
        {isActive && (
          <Box
            position="absolute"
            left={0}
            top="50%"
            transform="translateY(-50%)"
            w="3px"
            h="70%"
            bg="komodo.green"
            borderRightRadius="sm"
          />
        )}
        <Icon
          as={icon}
          ml={isActive ? "3" : "4"}
          mr={3}
          fontSize="20"
          color={isActive
            ? (colorMode === 'dark' ? 'komodo.green' : 'komodo.green')
            : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
          }
          _groupHover={{
            color: colorMode === 'dark' ? 'komodo.green' : 'komodo.green'
          }}
          transition="all 0.2s"
        />
        <Text
          color={isActive
            ? (colorMode === 'dark' ? 'white' : 'gray.900')
            : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
          }
          fontSize="14px"
          fontWeight={isActive ? "medium" : "normal"}
          _groupHover={{
            color: colorMode === 'dark' ? 'white' : 'gray.900'
          }}
        >
          {getChineseTitle(translationKey)}
        </Text>
      </Flex>
    </Link>
  )
}

interface SubNavItemProps {
  translationKey: string
  href: string
  isActive?: boolean
}

const SubNavItem = ({ translationKey, href, isActive }: SubNavItemProps) => {
  const { t } = useTranslation()
  const { colorMode } = useColorMode()

  const getChineseSubTitle = (key: string): string => {
    const subMenuTitles: Record<string, string> = {
      'network.fault.impact': '故障影响',
      'network.path.visualization': '路径可视化',
      'terminal.info': '终端信息',
      'network.config.backup': '配置备份',
      'config.management': '配置管理',
      'digital.ip.management': '数字IP管理',
      'phone.extension.management': '电话分机管理',
      'vm.management': '虚拟机管理',
      'printer.management': '打印机管理',
      'wireless.monitoring': '无线网络监控',
      'bandwidth.monitoring': '网络带宽监控',
      'environment.monitoring': '环境监控',
      'ups.monitoring': 'UPS监控',
      'mains.power.monitoring': '市电监控',
      'snmp.config.management': 'SNMP配置管理',
      'snmp.collection.management': 'SNMP采集管理',
      'asset.management.system': '资产概览',
      'asset.register': '资产登记',
      'asset.map': '资产地图',
      'auto.inspection': '自动巡检',
      'inspection.template': '巡检模板',
      'one.click.inspection': '一键巡检',
      'inspection.report': '巡检报告',
      'inspection.anomaly': '巡检异常',
      'maintenance.health': '维护健康',
      'maintenance.prediction': '维护预测',
      'maintenance.suggestion': '维护建议',
      'capacity.trend': '容量趋势',
      'capacity.suggestion': '容量建议',
      'capacity.optimization': '容量优化'
    };
    return subMenuTitles[key] || t(key);
  }

  return (
    <Link href={href} style={{ textDecoration: 'none' }}>
      <Flex
        align="center"
        p={2}
        pl={9}
        mx={2}
        borderRadius="md"
        role="group"
        cursor="pointer"
        position="relative"
        onClick={(e) => e.stopPropagation()}
        _hover={{
          bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50',
        }}
        bg={isActive
          ? (colorMode === 'dark' ? 'whiteAlpha.200' : 'komodo.lightGray')
          : 'transparent'
        }
        transition="all 0.2s"
      >
        {isActive && (
          <Box
            position="absolute"
            left={0}
            top="50%"
            transform="translateY(-50%)"
            w="3px"
            h="70%"
            bg="komodo.green"
            borderRightRadius="sm"
          />
        )}
        <Text
          color={isActive
            ? (colorMode === 'dark' ? 'white' : 'gray.900')
            : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
          }
          fontSize="14px"
          fontWeight={isActive ? "medium" : "normal"}
          _groupHover={{
            color: colorMode === 'dark' ? 'white' : 'gray.900'
          }}
        >
          {getChineseSubTitle(translationKey)}
        </Text>
      </Flex>
    </Link>
  )
}

// 智能运维子菜单（未使用）
// const SmartOpsSubmenu = () => {
//   const { t } = useTranslation()
//   const pathname = usePathname() ?? '/'
//
//   return (
//     <Box>
//       <SubNavItem
//         translationKey="auto.inspection"
//         href="/network/inspection"
//         isActive={pathname === '/network/inspection'}
//       />
//     </Box>
//   )
// }

const NetworkSubmenu = () => {
  const { colorMode } = useColorMode()
  const pathname = usePathname() ?? '/'

  return (
    <AccordionItem border="none">
      {({ isExpanded }) => (
        <>
          <AccordionButton
            p={2}
            mx={2}
            borderRadius="md"
            _hover={{
              bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50',
            }}
            transition="all 0.2s"
          >
            <Flex align="center" flex="1">
              <Icon
                as={Globe}
                mr={3}
                fontSize="20"
                color={pathname.startsWith('/network') || isExpanded
                  ? (colorMode === 'dark' ? 'komodo.green' : 'komodo.green')
                  : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
                }
                transition="all 0.2s"
              />
              <Text
                color={pathname.startsWith('/network') || isExpanded
                  ? (colorMode === 'dark' ? 'white' : 'gray.900')
                  : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
                }
                fontSize="14px"
                fontWeight={pathname.startsWith('/network') || isExpanded ? "medium" : "normal"}
              >
                网络管理
              </Text>
            </Flex>
            <CustomAccordionIcon isOpen={isExpanded} />
          </AccordionButton>
          <AccordionPanel pb={1} pt={0} px={0}>
            <SubNavItem
              translationKey="network.fault.impact"
              href="/network/fault-impact"
              isActive={pathname === '/network/fault-impact'}
            />
            <SubNavItem
              translationKey="network.path.visualization"
              href="/network/path-visualization"
              isActive={pathname === '/network/path-visualization'}
            />
            <SubNavItem
              translationKey="terminal.info"
              href="/network/terminal-info"
              isActive={pathname === '/network/terminal-info'}
            />
            <SubNavItem
              translationKey="network.config.backup"
              href="/network/config-backup"
              isActive={pathname === '/network/config-backup'}
            />
            <SubNavItem
              translationKey="config.management"
              href="/network/config-management"
              isActive={pathname === '/network/config-management'}
            />
            <SubNavItem
              translationKey="digital.ip.management"
              href="/network/digital-ip"
              isActive={pathname === '/network/digital-ip'}
            />
            <SubNavItem
              translationKey="phone.extension.management"
              href="/network/phone-extensions"
              isActive={pathname === '/network/phone-extensions'}
            />
            <SubNavItem
              translationKey="vm.management"
              href="/network/vm-management"
              isActive={pathname === '/network/vm-management'}
            />
            <SubNavItem
              translationKey="printer.management"
              href="/network/printer-monitoring"
              isActive={pathname === '/network/printer-monitoring'}
            />
            <SubNavItem
              translationKey="wireless.monitoring"
              href="/network/wireless-monitoring"
              isActive={pathname === '/network/wireless-monitoring'}
            />
            <SubNavItem
              translationKey="bandwidth.monitoring"
              href="/network/bandwidth-monitoring"
              isActive={pathname === '/network/bandwidth-monitoring'}
            />
          </AccordionPanel>
        </>
      )}
    </AccordionItem>
  )
}

const PowerMonitoringSubmenu = () => {
  const { colorMode } = useColorMode()
  const pathnameRaw = usePathname()
  const pathname = pathnameRaw ?? '/'

  return (
    <AccordionItem border="none">
      {({ isExpanded }) => (
        <>
          <AccordionButton
            p={2}
            mx={2}
            borderRadius="md"
            _hover={{
              bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50',
            }}
            transition="all 0.2s"
          >
            <Flex align="center" flex="1">
              <Icon
                as={Lightning}
                mr={3}
                fontSize="20"
                color={pathname.startsWith('/power-monitoring') || isExpanded
                  ? 'komodo.green'
                  : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
                }
                transition="all 0.2s"
              />
              <Text
                color={pathname.startsWith('/power-monitoring') || isExpanded
                  ? (colorMode === 'dark' ? 'white' : 'gray.900')
                  : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
                }
                fontSize="14px"
                fontWeight={pathname.startsWith('/power-monitoring') || isExpanded ? "medium" : "normal"}
              >
                基础设施监控
              </Text>
            </Flex>
            <CustomAccordionIcon isOpen={isExpanded} />
          </AccordionButton>
          <AccordionPanel pb={1} pt={0} px={0}>
            <SubNavItem
              translationKey="environment.monitoring"
              href="/power-monitoring/environment"
              isActive={pathname === '/power-monitoring/environment'}
            />
            <SubNavItem
              translationKey="ups.monitoring"
              href="/power-monitoring/ups"
              isActive={pathname === '/power-monitoring/ups' || pathname.startsWith('/power-monitoring/ups/')}
            />
            <SubNavItem
              translationKey="mains.power.monitoring"
              href="/power-monitoring/mains"
              isActive={pathname === '/power-monitoring/mains'}
            />
            <SubNavItem
              translationKey="snmp.collection.management"
              href="/power-monitoring/snmp-collection"
              isActive={pathname === '/power-monitoring/snmp-collection'}
            />
          </AccordionPanel>
        </>
      )}
    </AccordionItem>
  )
}

const AssetsSubmenu = () => {
  const { colorMode } = useColorMode()
  const pathname = usePathname() ?? '/'

  return (
    <AccordionItem border="none">
      {({ isExpanded }) => (
        <>
          <AccordionButton
            p={2}
            mx={2}
            borderRadius="md"
            _hover={{
              bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50',
            }}
            transition="all 0.2s"
          >
            <Flex align="center" flex="1">
              <Icon
                as={Database}
                mr={3}
                fontSize="20"
                color={pathname.startsWith('/resources/asset') || isExpanded
                  ? (colorMode === 'dark' ? 'komodo.green' : 'komodo.green')
                  : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
                }
                transition="all 0.2s"
              />
              <Text
                color={pathname.startsWith('/resources/asset') || isExpanded
                  ? (colorMode === 'dark' ? 'white' : 'gray.900')
                  : (colorMode === 'dark' ? 'gray.400' : 'gray.600')
                }
                fontSize="14px"
                fontWeight={pathname.startsWith('/resources/asset') || isExpanded ? "medium" : "normal"}
              >
                资产管理
              </Text>
            </Flex>
            <CustomAccordionIcon isOpen={isExpanded} />
          </AccordionButton>
          <AccordionPanel pb={1} pt={0} px={0}>
            <SubNavItem
              translationKey="asset.management.system"
              href="/assets"
              isActive={pathname === '/assets' || pathname.startsWith('/assets/')}
            />
            <SubNavItem
              translationKey="asset.map"
              href="/resources/asset-map"
              isActive={pathname === '/resources/asset-map'}
            />
          </AccordionPanel>
        </>
      )}
    </AccordionItem>
  )
}

const Sidebar = () => {
  const { colorMode } = useColorMode()
  const pathname = usePathname() ?? '/'
  // 智能运维分组折叠状态
  const [openGroups, setOpenGroups] = useState({
    network: false,
    inspection: false,
    prediction: false,
    capacity: false,
  })

  const toggleGroup = (group: keyof typeof openGroups) => {
    setOpenGroups(prev => ({
      ...prev,
      [group]: !prev[group],
    }))
  }

  return (
    <Box
      as="nav"
      position="fixed"
      left={0}
      top={0}
      h="100vh"
      w="240px"
      bg={colorMode === 'dark' ? 'gray.900' : 'white'}
      borderRight="1px"
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
      transition="all 0.3s"
    >
      <VStack spacing={6} align="stretch" h="full">
        <Box p={4}>
          <Flex
            alignItems="center"
            justifyContent="flex-start"
            borderBottom="1px"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            pb={3}
            mb={1}
          >
            <Box
              bg={colorMode === 'dark' ? 'whiteAlpha.900' : 'teal.500'}
              width="4px"
              height="28px"
              mr={3}
              borderRadius="sm"
            />
            <Flex direction="column" alignItems="flex-start">
              <Text
                fontWeight="bold"
                fontSize="18px"
                letterSpacing="tight"
                color={colorMode === 'dark' ? 'white' : 'gray.800'}
                mb={0}
                lineHeight="1.2"
              >
                燃石医学
              </Text>
              <Text
                fontSize="12px"
                color={colorMode === 'dark' ? 'whiteAlpha.700' : 'gray.600'}
                fontWeight="medium"
                letterSpacing="0.5px"
                textTransform="uppercase"
              >
                Burning Rock
              </Text>
            </Flex>
          </Flex>
        </Box>

        <VStack
          spacing={1}
          align="stretch"
          flex="1"
          overflowY="auto"
          maxH="calc(100vh - 120px)"
          css={{
            '&::-webkit-scrollbar': {
              width: '4px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: colorMode === 'dark' ? '#444' : '#ccc',
              borderRadius: '2px',
            },
          }}
        >
          <NavItem icon={House} translationKey="dashboard" href="/" isActive={pathname === '/'} />
          <NavItem icon={Cube} translationKey="resources" href="/resources" isActive={pathname.startsWith('/resources')} />
          <Accordion allowMultiple border="none">
            {/* 网络管理菜单 */}
            <NetworkSubmenu />

            {/* 智能运维主菜单 */}
            <AccordionItem border="none">
              {({ isExpanded }) => (
                <>
                  <AccordionButton
                    p={2}
                    mx={2}
                    borderRadius="md"
                    _hover={{ bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50' }}
                    transition="all 0.2s"
                  >
                    <Flex align="center" flex="1">
                      <Icon as={Wrench} mr={3} fontSize="20" color={pathname.startsWith('/smart-ops') || isExpanded ? 'komodo.green' : (colorMode === 'dark' ? 'gray.400' : 'gray.600')} />
                      <Text
                        color={pathname.startsWith('/smart-ops') || isExpanded ? (colorMode === 'dark' ? 'white' : 'gray.900') : (colorMode === 'dark' ? 'gray.400' : 'gray.600')}
                        fontSize="14px"
                        fontWeight={pathname.startsWith('/smart-ops') || isExpanded ? "medium" : "normal"}
                      >
                        智能运维
                      </Text>
                    </Flex>
                    <CustomAccordionIcon isOpen={isExpanded} />
                  </AccordionButton>
                  <AccordionPanel pb={1} pt={0} px={0}>
                    {/* 自定义分组折叠区 */}
                    <Box>
                      <Flex align="center" px={4} py={2} borderRadius="md" _hover={{ bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50' }} cursor="pointer" onClick={() => toggleGroup('inspection')}>
                        <Text fontSize="13px" fontWeight="bold" color={openGroups.inspection ? (colorMode === 'dark' ? 'white' : 'gray.900') : (colorMode === 'dark' ? 'gray.400' : 'gray.600')}>自动化巡检</Text>
                        <Box flex="1" />
                        <CustomAccordionIcon isOpen={openGroups.inspection} />
                      </Flex>
                      {openGroups.inspection && (
                        <Box onClick={(e) => e.stopPropagation()}>
                          <SubNavItem translationKey="inspection.template" href="/smart-ops/inspection-template" isActive={pathname === '/smart-ops/inspection-template'} />
                          <SubNavItem translationKey="one.click.inspection" href="/smart-ops/one-click-inspection" isActive={pathname === '/smart-ops/one-click-inspection'} />
                          <SubNavItem translationKey="inspection.report" href="/smart-ops/inspection-report" isActive={pathname === '/smart-ops/inspection-report'} />
                          <SubNavItem translationKey="inspection.anomaly" href="/smart-ops/inspection-anomaly" isActive={pathname === '/smart-ops/inspection-anomaly'} />
                          <SubNavItem translationKey="mobile.demo" href="/mobile-demo" isActive={pathname === '/mobile-demo'} />
                        </Box>
                      )}
                    </Box>
                    <Box>
                      <Flex align="center" px={4} py={2} borderRadius="md" _hover={{ bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50' }} cursor="pointer" onClick={() => toggleGroup('prediction')}>
                        <Text fontSize="13px" fontWeight="bold" color={openGroups.prediction ? (colorMode === 'dark' ? 'white' : 'gray.900') : (colorMode === 'dark' ? 'gray.400' : 'gray.600')}>预测性维护</Text>
                        <Box flex="1" />
                        <CustomAccordionIcon isOpen={openGroups.prediction} />
                      </Flex>
                      {openGroups.prediction && (
                        <Box onClick={(e) => e.stopPropagation()}>
                          <SubNavItem translationKey="maintenance.health" href="/smart-ops/maintenance-health" isActive={pathname === '/smart-ops/maintenance-health'} />
                          <SubNavItem translationKey="maintenance.prediction" href="/smart-ops/maintenance-prediction" isActive={pathname === '/smart-ops/maintenance-prediction'} />
                          <SubNavItem translationKey="maintenance.suggestion" href="/smart-ops/maintenance-suggestion" isActive={pathname === '/smart-ops/maintenance-suggestion'} />
                        </Box>
                      )}
                    </Box>
                    <Box>
                      <Flex align="center" px={4} py={2} borderRadius="md" _hover={{ bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.50' }} cursor="pointer" onClick={() => toggleGroup('capacity')}>
                        <Text fontSize="13px" fontWeight="bold" color={openGroups.capacity ? (colorMode === 'dark' ? 'white' : 'gray.900') : (colorMode === 'dark' ? 'gray.400' : 'gray.600')}>容量规划</Text>
                        <Box flex="1" />
                        <CustomAccordionIcon isOpen={openGroups.capacity} />
                      </Flex>
                      {openGroups.capacity && (
                        <Box onClick={(e) => e.stopPropagation()}>
                          <SubNavItem translationKey="capacity.trend" href="/smart-ops/capacity-trend" isActive={pathname === '/smart-ops/capacity-trend'} />
                          <SubNavItem translationKey="capacity.suggestion" href="/smart-ops/capacity-suggestion" isActive={pathname === '/smart-ops/capacity-suggestion'} />
                          <SubNavItem translationKey="capacity.optimization" href="/smart-ops/capacity-optimization" isActive={pathname === '/smart-ops/capacity-optimization'} />
                        </Box>
                      )}
                    </Box>
                  </AccordionPanel>
                </>
              )}
            </AccordionItem>
            {/* 其余菜单项 */}
            <PowerMonitoringSubmenu />
            <AssetsSubmenu />
          </Accordion>
          <NavItem icon={WifiHigh} translationKey="wireless" href="/wireless" isActive={pathname.startsWith('/wireless')} />
          <NavItem icon={Warning} translationKey="vulnerability.scan" href="/vulnerability-scan" isActive={pathname.startsWith('/vulnerability-scan')} />
          <NavItem icon={Package} translationKey="deployments" href="/deployments" isActive={pathname.startsWith('/deployments')} />
          <NavItem icon={Bell} translationKey="notifications" href="/notifications" isActive={pathname.startsWith('/notifications')} />
          <NavItem icon={Gear} translationKey="settings" href="/settings" isActive={pathname.startsWith('/settings')} />
        </VStack>
      </VStack>
    </Box>
  )
}

export default Sidebar
