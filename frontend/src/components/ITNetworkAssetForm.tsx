'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { CalendarIcon, Server, Network, Shield, HardDrive, Cpu, Monitor } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

// 表单数据类型定义
interface ITAssetFormData {
  // 基础信息
  name: string
  code: string
  category: string
  manufacturer: string
  model: string
  serialNumber: string
  status: string
  location: string
  department: string
  responsiblePerson: string
  description?: string
  
  // 采购信息
  purchaseDate?: Date
  supplier?: string
  purchaseOrderNumber?: string
  price?: number
  warrantyExpireDate?: Date
  
  // 网络配置
  ipAddress?: string
  macAddress?: string
  hostname?: string
  domain?: string
  subnetMask?: string
  gateway?: string
  dnsServers?: string
  vlanId?: number
  
  // 硬件规格
  cpuModel?: string
  cpuCores?: number
  memorySize?: number
  storageSize?: number
  storageType?: string
  portCount?: number
  portSpeed?: string
  powerConsumption?: number
  operatingTemperature?: string
  
  // 软件信息
  operatingSystem?: string
  osVersion?: string
  firmwareVersion?: string
  
  // 安全配置
  securityLevel?: string
  encryptionEnabled: boolean
  
  // 监控配置
  monitoringEnabled: boolean
  monitoringType?: 'snmp' | 'ssh' | 'ping'
  snmpCommunity?: string
  snmpPort?: number
  sshUsername?: string
  sshPort?: number
}

// 表单验证规则
const formSchema = z.object({
  // 基础信息 - 必填
  name: z.string().min(2, '设备名称至少需要2个字符'),
  code: z.string().min(3, '资产编码至少需要3个字符'),
  category: z.string().min(1, '请选择设备类别'),
  manufacturer: z.string().min(1, '请输入制造商'),
  model: z.string().min(1, '请输入设备型号'),
  serialNumber: z.string().min(1, '请输入序列号'),
  status: z.string().min(1, '请选择设备状态'),
  location: z.string().min(1, '请输入设备位置'),
  department: z.string().min(1, '请输入所属部门'),
  responsiblePerson: z.string().min(1, '请输入责任人'),
  description: z.string().optional(),
  
  // 采购信息 - 可选
  purchaseDate: z.date().optional(),
  supplier: z.string().optional(),
  purchaseOrderNumber: z.string().optional(),
  price: z.number().positive('价格必须大于0').optional(),
  warrantyExpireDate: z.date().optional(),
  
  // 网络配置 - 可选但有格式要求
  ipAddress: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的IP地址'
  ).optional().or(z.literal('')),
  macAddress: z.string().regex(
    /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
    '请输入有效的MAC地址'
  ).optional().or(z.literal('')),
  hostname: z.string().optional(),
  domain: z.string().optional(),
  subnetMask: z.string().optional(),
  gateway: z.string().optional(),
  dnsServers: z.string().optional(),
  vlanId: z.number().int().min(1).max(4094).optional(),
  
  // 硬件规格 - 可选
  cpuModel: z.string().optional(),
  cpuCores: z.number().int().positive().optional(),
  memorySize: z.number().positive().optional(),
  storageSize: z.number().positive().optional(),
  storageType: z.string().optional(),
  portCount: z.number().int().positive().optional(),
  portSpeed: z.string().optional(),
  powerConsumption: z.number().positive().optional(),
  operatingTemperature: z.string().optional(),
  
  // 软件信息 - 可选
  operatingSystem: z.string().optional(),
  osVersion: z.string().optional(),
  firmwareVersion: z.string().optional(),
  
  // 安全配置
  securityLevel: z.string().optional(),
  encryptionEnabled: z.boolean(),
  
  // 监控配置
  monitoringEnabled: z.boolean(),
  monitoringType: z.enum(['snmp', 'ssh', 'ping']).optional(),
  snmpCommunity: z.string().optional(),
  snmpPort: z.number().int().min(1).max(65535).optional(),
  sshUsername: z.string().optional(),
  sshPort: z.number().int().min(1).max(65535).optional(),
})

interface ITNetworkAssetFormProps {
  onSubmit: (data: ITAssetFormData) => Promise<void>
  initialData?: Partial<ITAssetFormData>
}

export function ITNetworkAssetForm({ onSubmit, initialData }: ITNetworkAssetFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<ITAssetFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      code: '',
      category: '',
      manufacturer: '',
      model: '',
      serialNumber: '',
      status: 'active',
      location: '',
      department: '',
      responsiblePerson: '',
      description: '',
      encryptionEnabled: false,
      monitoringEnabled: false,
      snmpPort: 161,
      sshPort: 22,
      ...initialData
    }
  })

  const handleSubmit = async (data: ITAssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
    } finally {
      setIsSubmitting(false)
    }
  }

  const monitoringEnabled = form.watch('monitoringEnabled')
  const monitoringType = form.watch('monitoringType')

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              基础信息
            </TabsTrigger>
            <TabsTrigger value="network" className="flex items-center gap-2">
              <Network className="h-4 w-4" />
              网络配置
            </TabsTrigger>
            <TabsTrigger value="hardware" className="flex items-center gap-2">
              <Cpu className="h-4 w-4" />
              硬件规格
            </TabsTrigger>
            <TabsTrigger value="software" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              软件信息
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              安全配置
            </TabsTrigger>
            <TabsTrigger value="lifecycle" className="flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              生命周期
            </TabsTrigger>
          </TabsList>

          {/* 基础信息 */}
          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>基础信息</CardTitle>
                <CardDescription>设备的基本标识和管理信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>设备名称 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="例如：核心交换机-01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>资产编码 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="例如：IT-NET-001" {...field} />
                        </FormControl>
                        <FormDescription>唯一的资产标识编码</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>设备类别 <Badge variant="destructive">必填</Badge></FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择设备类别" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="server">服务器</SelectItem>
                            <SelectItem value="switch">交换机</SelectItem>
                            <SelectItem value="router">路由器</SelectItem>
                            <SelectItem value="firewall">防火墙</SelectItem>
                            <SelectItem value="storage">存储设备</SelectItem>
                            <SelectItem value="ups">UPS电源</SelectItem>
                            <SelectItem value="printer">打印机</SelectItem>
                            <SelectItem value="workstation">工作站</SelectItem>
                            <SelectItem value="laptop">笔记本电脑</SelectItem>
                            <SelectItem value="monitor">显示器</SelectItem>
                            <SelectItem value="other">其他设备</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>设备状态 <Badge variant="destructive">必填</Badge></FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择设备状态" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">正常运行</SelectItem>
                            <SelectItem value="maintenance">维护中</SelectItem>
                            <SelectItem value="standby">备用</SelectItem>
                            <SelectItem value="offline">离线</SelectItem>
                            <SelectItem value="retired">已退役</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="manufacturer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>制造商 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="例如：华为、思科、戴尔" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="model"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>设备型号 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="例如：S5720-28P-SI" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="serialNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>序列号 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="设备序列号" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>设备位置 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="例如：机房A-机柜01-U10" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="building"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>建筑物</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入建筑物" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="floor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>楼层</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入楼层" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="room"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>机房/房间</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入机房或房间" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="rack_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>机柜编号</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入机柜编号" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="rack_position"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>机柜位置</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入机柜内位置" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>所属部门 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="例如：信息技术部" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="responsiblePerson"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>责任人 <Badge variant="destructive">必填</Badge></FormLabel>
                        <FormControl>
                          <Input placeholder="设备责任人姓名" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>设备描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="设备的详细描述、用途、特殊配置等"
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* 网络配置 */}
          <TabsContent value="network">
            <Card>
              <CardHeader>
                <CardTitle>网络配置</CardTitle>
                <CardDescription>设备的网络连接和配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="ipAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>IP地址</FormLabel>
                        <FormControl>
                          <Input placeholder="*************" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="macAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>MAC地址</FormLabel>
                        <FormControl>
                          <Input placeholder="00:11:22:33:44:55" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="hostname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>主机名</FormLabel>
                        <FormControl>
                          <Input placeholder="switch-core-01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="domain"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>域名</FormLabel>
                        <FormControl>
                          <Input placeholder="company.local" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="subnetMask"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>子网掩码</FormLabel>
                        <FormControl>
                          <Input placeholder="255.255.255.0" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="gateway"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>网关地址</FormLabel>
                        <FormControl>
                          <Input placeholder="192.168.1.1" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dnsServers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>DNS服务器</FormLabel>
                        <FormControl>
                          <Input placeholder="*******, *******" {...field} />
                        </FormControl>
                        <FormDescription>多个DNS服务器用逗号分隔</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vlanId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VLAN ID</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="100"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormDescription>VLAN标识符 (1-4094)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 硬件规格 */}
          <TabsContent value="hardware">
            <Card>
              <CardHeader>
                <CardTitle>硬件规格</CardTitle>
                <CardDescription>设备的硬件配置和性能参数</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="cpuModel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CPU型号</FormLabel>
                        <FormControl>
                          <Input placeholder="Intel Xeon E5-2680 v4" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="cpuCores"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CPU核心数</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="8"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="memorySize"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>内存大小 (GB)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="32"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="storageSize"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>存储大小 (GB)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="1000"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="storageType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>存储类型</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择存储类型" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SSD">SSD固态硬盘</SelectItem>
                            <SelectItem value="HDD">HDD机械硬盘</SelectItem>
                            <SelectItem value="NVMe">NVMe固态硬盘</SelectItem>
                            <SelectItem value="Hybrid">混合存储</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="portCount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>端口数量</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="24"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormDescription>网络设备的端口数量</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="portSpeed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>端口速度</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择端口速度" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="10Mbps">10Mbps</SelectItem>
                            <SelectItem value="100Mbps">100Mbps</SelectItem>
                            <SelectItem value="1Gbps">1Gbps</SelectItem>
                            <SelectItem value="10Gbps">10Gbps</SelectItem>
                            <SelectItem value="25Gbps">25Gbps</SelectItem>
                            <SelectItem value="40Gbps">40Gbps</SelectItem>
                            <SelectItem value="100Gbps">100Gbps</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="powerConsumption"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>功耗 (W)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="150"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="operatingTemperature"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>工作温度范围</FormLabel>
                        <FormControl>
                          <Input placeholder="0°C ~ 40°C" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 软件信息 */}
          <TabsContent value="software">
            <Card>
              <CardHeader>
                <CardTitle>软件信息</CardTitle>
                <CardDescription>设备的操作系统和软件配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="operatingSystem"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>操作系统</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：Windows Server 2019, Ubuntu 20.04" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="osVersion"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>系统版本</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：10.0.17763.1" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="firmwareVersion"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>固件版本</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：V200R010C00SPC600" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 安全配置 */}
          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>安全配置</CardTitle>
                <CardDescription>设备的安全等级和监控配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="securityLevel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>安全等级</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择安全等级" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">低级</SelectItem>
                            <SelectItem value="medium">中级</SelectItem>
                            <SelectItem value="high">高级</SelectItem>
                            <SelectItem value="critical">关键</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="encryptionEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">启用加密</FormLabel>
                          <FormDescription>
                            是否启用数据传输加密
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {/* 监控配置 */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="monitoringEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">启用监控</FormLabel>
                          <FormDescription>
                            是否启用设备状态监控
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {monitoringEnabled && (
                    <div className="space-y-4 pl-4 border-l-2 border-muted">
                      <FormField
                        control={form.control}
                        name="monitoringType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>监控类型</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择监控类型" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="ping">Ping监控</SelectItem>
                                <SelectItem value="snmp">SNMP监控</SelectItem>
                                <SelectItem value="ssh">SSH监控</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {monitoringType === 'snmp' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="snmpCommunity"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>SNMP Community</FormLabel>
                                <FormControl>
                                  <Input placeholder="public" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="snmpPort"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>SNMP端口</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="161"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}

                      {monitoringType === 'ssh' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="sshUsername"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>SSH用户名</FormLabel>
                                <FormControl>
                                  <Input placeholder="admin" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="sshPort"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>SSH端口</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="22"
                                    {...field}
                                    onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 生命周期管理 */}
          <TabsContent value="lifecycle">
            <Card>
              <CardHeader>
                <CardTitle>生命周期管理</CardTitle>
                <CardDescription>设备的采购、保修和财务信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="purchaseDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>采购日期</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>选择采购日期</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() || date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="warrantyExpireDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>保修到期日期</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>选择保修到期日期</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) => date < new Date("1900-01-01")}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="supplier"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>供应商</FormLabel>
                        <FormControl>
                          <Input placeholder="设备供应商" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="purchaseOrderNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>采购订单号</FormLabel>
                        <FormControl>
                          <Input placeholder="PO-2024-001" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>采购价格 (元)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="10000"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => window.history.back()}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? '保存中...' : '保存资产信息'}
          </Button>
        </div>
      </form>
    </Form>
  )
}