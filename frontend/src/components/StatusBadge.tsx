import React from 'react';
import { Badge } from '@chakra-ui/react';
import { useTranslation } from '@/contexts/LanguageContext';

interface StatusBadgeProps {
  status: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const { t } = useTranslation();

  const getColorScheme = () => {
    switch (status) {
      case 'normal':
        return 'green';
      case 'warning':
        return 'yellow';
      case 'error':
        return 'red';
      case 'offline':
        return 'gray';
      default:
        return 'gray';
    }
  };

  return (
    <Badge colorScheme={getColorScheme()} variant="subtle">
      {t(`status.${status}`) || status}
    </Badge>
  );
};

export default StatusBadge; 