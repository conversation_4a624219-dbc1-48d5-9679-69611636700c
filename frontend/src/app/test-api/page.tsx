'use client'
import React, { useEffect, useState } from 'react'

export default function TestApiPage() {
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const response = await fetch('/api/asset-categories')
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`)
        }
        const data = await response.json()
        setCategories(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return (
    <div style={{ padding: '20px' }}>
      <h1>API Test Page</h1>
      <h2>Asset Categories</h2>
      
      {loading && <p>Loading...</p>}
      
      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <h3>Error:</h3>
          <p>{error}</p>
        </div>
      )}
      
      {!loading && !error && (
        <div>
          {categories.length === 0 ? (
            <p>No categories found. You may need to create some first.</p>
          ) : (
            <ul>
              {categories.map((category) => (
                <li key={category.id}>
                  <strong>{category.name}</strong> ({category.code})
                  <p>{category.description}</p>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  )
}
