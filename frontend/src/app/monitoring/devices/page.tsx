'use client';

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Flex, 
  Heading, 
  Text, 
  Table, 
  Thead, 
  Tbody, 
  Tr, 
  Th, 
  Td, 
  Button, 
  Badge, 
  Input, 
  Select, 
  IconButton, 
  useColorModeValue, 
  useDisclosure,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Divider,
  HStack,
  Spinner,
  Icon
} from '@chakra-ui/react';
import { useTranslation } from '@/contexts/LanguageContext';
import Link from 'next/link';
import { Device, DeviceFilter } from '@/types/monitoring';

// SearchIcon component
const SearchIcon = (props: any) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M23.384,21.619,16.855,15.09a9.284,9.284,0,1,0-1.768,1.768l6.529,6.529a1.266,1.266,0,0,0,1.768,0A1.251,1.251,0,0,0,23.384,21.619ZM2.75,9.5a6.75,6.75,0,1,1,6.75,6.75A6.758,6.758,0,0,1,2.75,9.5Z"
    />
  </Icon>
);

const mockDevices: Device[] = Array(10).fill(null).map((_, i) => {
  const statusOptions: ('online' | 'warning' | 'offline' | 'critical')[] = ['online', 'warning', 'offline', 'critical'];
  const typeOptions = ['server', 'switch', 'router', 'storage'];
  
  return {
    id: `dev-${i + 1}`,
    name: `Device-${i + 1}`,
    ip: `192.168.1.${i + 10}`,
    type: typeOptions[i % 4],
    status: statusOptions[i % 4],
    last_seen: new Date().toISOString(),
    created_at: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
    updated_at: new Date().toISOString()
  };
});

export default function DevicesPage() {
  const { t } = useTranslation();
  const [devices, setDevices] = useState<Device[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<DeviceFilter>({});
  const tableHeaderBg = useColorModeValue('gray.50', 'gray.700');
  const tableBorderColor = useColorModeValue('gray.200', 'gray.600');
  const cardBg = useColorModeValue('white', 'gray.800');
  
  // Count devices by status
  const deviceCounts = {
    total: devices.length,
    online: devices.filter(d => d.status === 'online').length,
    warning: devices.filter(d => d.status === 'warning').length,
    critical: devices.filter(d => d.status === 'critical').length,
    offline: devices.filter(d => d.status === 'offline').length
  };
  
  useEffect(() => {
    const loadDevices = async () => {
      try {
        // In a real app, this would be an API call
        // Simulating API delay
        setTimeout(() => {
          setDevices(mockDevices);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Failed to fetch devices:', error);
        setIsLoading(false);
      }
    };
    
    loadDevices();
  }, []);
  
  const getStatusBadge = (status: string) => {
    let colorScheme;
    switch (status) {
      case 'online':
        colorScheme = 'green';
        break;
      case 'offline':
        colorScheme = 'gray';
        break;
      case 'warning':
        colorScheme = 'yellow';
        break;
      case 'critical':
        colorScheme = 'red';
        break;
      default:
        colorScheme = 'gray';
    }
    return (
      <Badge colorScheme={colorScheme} px={2} py={1} borderRadius="md">
        {t(`status.${status}`) || status}
      </Badge>
    );
  };
  
  // Apply filters and search
  const filteredDevices = devices.filter(device => {
    // Apply search filter
    const matchesSearch = 
      device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      device.ip.toLowerCase().includes(searchQuery.toLowerCase()) ||
      device.type.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Apply status filter
    const matchesStatus = !filters.status || filters.status.length === 0 || 
      filters.status.includes(device.status);
    
    // Apply type filter
    const matchesType = !filters.type || filters.type.length === 0 || 
      filters.type.includes(device.type);
    
    return matchesSearch && matchesStatus && matchesType;
  });
  
  return (
    <Box p={4}>
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Heading size="lg">{t('monitoring.devices')}</Heading>
        <Button colorScheme="blue" as={Link} href="/monitoring/devices/register">
          {t('add.device')}
        </Button>
      </Flex>
      
      {/* Status summary cards */}
      <SimpleGrid columns={{ base: 2, md: 5 }} spacing={4} mb={6}>
        <Card 
          variant="elevated" 
          shadow="md"
          bg="white"
          _dark={{ bg: "komodo.darkGray" }}
          borderRadius="lg"
          overflow="hidden"
          transition="all 0.3s"
          _hover={{ transform: "translateY(-2px)", shadow: "lg" }}
        >
          <CardBody>
            <Flex justify="space-between" align="center">
              <Box>
                <Text color="gray.500" fontSize="sm" fontWeight="medium">{t('status.all') || 'All Devices'}</Text>
                <Text fontSize="2xl" fontWeight="bold" mt={1}>{deviceCounts.total}</Text>
              </Box>
              <Flex 
                align="center" 
                justify="center" 
                bg="blue.50" 
                _dark={{ bg: "blue.900" }} 
                p={3} 
                borderRadius="full"
              >
                <Icon viewBox="0 0 24 24" boxSize={6} color="komodo.blue" _dark={{ color: "blue.300" }}>
                  <path
                    fill="currentColor"
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
                  />
                </Icon>
              </Flex>
            </Flex>
          </CardBody>
        </Card>
        
        <Card 
          variant="elevated" 
          shadow="md"
          bg="white"
          _dark={{ bg: "komodo.darkGray" }}
          borderRadius="lg"
          overflow="hidden"
          transition="all 0.3s"
          _hover={{ transform: "translateY(-2px)", shadow: "lg" }}
        >
          <CardBody>
            <Flex justify="space-between" align="center">
              <Box>
                <Text color="gray.500" fontSize="sm" fontWeight="medium">{t('status.online')}</Text>
                <Text fontSize="2xl" fontWeight="bold" mt={1}>{deviceCounts.online}</Text>
              </Box>
              <Flex 
                align="center" 
                justify="center" 
                bg="green.50" 
                _dark={{ bg: "green.900" }} 
                p={3} 
                borderRadius="full"
              >
                <Icon viewBox="0 0 24 24" boxSize={6} color="komodo.green" _dark={{ color: "green.300" }}>
                  <path
                    fill="currentColor"
                    d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"
                  />
                </Icon>
              </Flex>
            </Flex>
          </CardBody>
        </Card>
        
        <Card 
          variant="elevated" 
          shadow="md"
          bg="white"
          _dark={{ bg: "komodo.darkGray" }}
          borderRadius="lg"
          overflow="hidden"
          transition="all 0.3s"
          _hover={{ transform: "translateY(-2px)", shadow: "lg" }}
        >
          <CardBody>
            <Flex justify="space-between" align="center">
              <Box>
                <Text color="gray.500" fontSize="sm" fontWeight="medium">{t('status.warning')}</Text>
                <Text fontSize="2xl" fontWeight="bold" mt={1}>{deviceCounts.warning}</Text>
              </Box>
              <Flex 
                align="center" 
                justify="center" 
                bg="yellow.50" 
                _dark={{ bg: "yellow.900" }} 
                p={3} 
                borderRadius="full"
              >
                <Icon viewBox="0 0 24 24" boxSize={6} color="yellow.500" _dark={{ color: "yellow.300" }}>
                  <path
                    fill="currentColor"
                    d="M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"
                  />
                </Icon>
              </Flex>
            </Flex>
          </CardBody>
        </Card>
        
        <Card 
          variant="elevated" 
          shadow="md"
          bg="white"
          _dark={{ bg: "komodo.darkGray" }}
          borderRadius="lg"
          overflow="hidden"
          transition="all 0.3s"
          _hover={{ transform: "translateY(-2px)", shadow: "lg" }}
        >
          <CardBody>
            <Flex justify="space-between" align="center">
              <Box>
                <Text color="gray.500" fontSize="sm" fontWeight="medium">{t('status.critical')}</Text>
                <Text fontSize="2xl" fontWeight="bold" mt={1}>{deviceCounts.critical}</Text>
              </Box>
              <Flex 
                align="center" 
                justify="center" 
                bg="red.50" 
                _dark={{ bg: "red.900" }} 
                p={3} 
                borderRadius="full"
              >
                <Icon viewBox="0 0 24 24" boxSize={6} color="red.500" _dark={{ color: "red.300" }}>
                  <path
                    fill="currentColor"
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 11c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1 4h-2v-2h2v2z"
                  />
                </Icon>
              </Flex>
            </Flex>
          </CardBody>
        </Card>
        
        <Card 
          variant="elevated" 
          shadow="md"
          bg="white"
          _dark={{ bg: "komodo.darkGray" }}
          borderRadius="lg"
          overflow="hidden"
          transition="all 0.3s"
          _hover={{ transform: "translateY(-2px)", shadow: "lg" }}
        >
          <CardBody>
            <Flex justify="space-between" align="center">
              <Box>
                <Text color="gray.500" fontSize="sm" fontWeight="medium">{t('status.offline')}</Text>
                <Text fontSize="2xl" fontWeight="bold" mt={1}>{deviceCounts.offline}</Text>
              </Box>
              <Flex 
                align="center" 
                justify="center" 
                bg="gray.100" 
                _dark={{ bg: "gray.700" }} 
                p={3} 
                borderRadius="full"
              >
                <Icon viewBox="0 0 24 24" boxSize={6} color="gray.500" _dark={{ color: "gray.400" }}>
                  <path
                    fill="currentColor"
                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8 0-1.85.63-3.55 1.69-4.9L16.9 18.31C15.55 19.37 13.85 20 12 20zm6.31-3.1L7.1 5.69C8.45 4.63 10.15 4 12 4c4.42 0 8 3.58 8 8 0 1.85-.63 3.55-1.69 4.9z"
                  />
                </Icon>
              </Flex>
            </Flex>
          </CardBody>
        </Card>
      </SimpleGrid>
      
      <Flex mb={4} gap={4} flexDirection={{ base: 'column', md: 'row' }}>
        <Flex flex={1} alignItems="center" bg={useColorModeValue('white', 'gray.800')} 
              borderRadius="md" borderWidth="1px" borderColor={tableBorderColor} px={2}>
          <SearchIcon color="gray.400" mr={2} />
          <Input 
            placeholder={t('search')} 
            variant="unstyled"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </Flex>
        
        <Select 
          placeholder={t('device.status')}
          maxW={{ base: "100%", md: "200px" }}
          onChange={(e) => setFilters(prev => ({...prev, status: e.target.value ? [e.target.value as any] : undefined}))}
        >
          <option value="online">{t('status.online')}</option>
          <option value="offline">{t('status.offline')}</option>
          <option value="warning">{t('status.warning')}</option>
          <option value="critical">{t('status.critical')}</option>
        </Select>
        
        <Select 
          placeholder={t('device.type')}
          maxW={{ base: "100%", md: "200px" }}
          onChange={(e) => setFilters(prev => ({...prev, type: e.target.value ? [e.target.value] : undefined}))}
        >
          <option value="server">{t('device.type.server')}</option>
          <option value="switch">{t('device.type.switch')}</option>
          <option value="router">{t('device.type.router')}</option>
          <option value="storage">{t('device.type.storage')}</option>
        </Select>
      </Flex>
      
      <Box overflowX="auto" boxShadow="sm" borderRadius="lg">
        <Table variant="simple">
          <Thead bg={tableHeaderBg}>
            <Tr>
              <Th borderColor={tableBorderColor}>{t('device.name')}</Th>
              <Th borderColor={tableBorderColor}>{t('ip') || 'IP'}</Th>
              <Th borderColor={tableBorderColor}>{t('device.type')}</Th>
              <Th borderColor={tableBorderColor}>{t('status')}</Th>
              <Th borderColor={tableBorderColor}>{t('device.lastSeen')}</Th>
              <Th borderColor={tableBorderColor} isNumeric>{t('actions') || 'Actions'}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {isLoading ? (
              <Tr>
                <Td colSpan={6} textAlign="center" py={4}>
                  <Flex justify="center" align="center" py={8}>
                    <Spinner size="lg" />
                  </Flex>
                </Td>
              </Tr>
            ) : filteredDevices.length === 0 ? (
              <Tr>
                <Td colSpan={6} textAlign="center" py={4}>{t('noDevices') || 'No devices found'}</Td>
              </Tr>
            ) : (
              filteredDevices.map(device => (
                <Tr key={device.id} _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}>
                  <Td borderColor={tableBorderColor}>
                    <Link href={`/monitoring/devices/${device.id}`} passHref>
                      <Text color="blue.500" cursor="pointer" fontWeight="medium">{device.name}</Text>
                    </Link>
                  </Td>
                  <Td borderColor={tableBorderColor}>{device.ip}</Td>
                  <Td borderColor={tableBorderColor}>{t(`device.type.${device.type}`) || device.type}</Td>
                  <Td borderColor={tableBorderColor}>{getStatusBadge(device.status)}</Td>
                  <Td borderColor={tableBorderColor}>
                    {new Date(device.last_seen).toLocaleString()}
                  </Td>
                  <Td borderColor={tableBorderColor} isNumeric>
                    <HStack spacing={2} justifyContent="flex-end">
                      <Button 
                        as={Link}
                        href={`/monitoring/devices/${device.id}`}
                        size="sm" 
                        colorScheme="blue" 
                        variant="outline"
                      >
                        {t('view') || 'View'}
                      </Button>
                      {(device.status === 'warning' || device.status === 'critical') && (
                        <Button 
                          as={Link}
                          href={`/monitoring/devices/${device.id}?tab=alerts`}
                          size="sm" 
                          colorScheme="red" 
                          variant="solid"
                        >
                          {t('alerts')}
                        </Button>
                      )}
                    </HStack>
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </Box>
      
      {!isLoading && filteredDevices.length > 0 && (
        <Text mt={4} color="gray.500" fontSize="sm">
          {t('showing') || 'Showing'} {filteredDevices.length} {t('of') || 'of'} {devices.length} {t('devices') || 'devices'}
        </Text>
      )}
    </Box>
  );
}