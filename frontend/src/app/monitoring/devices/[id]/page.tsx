'use client'

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, ArrowLeft, RefreshCw } from 'lucide-react';

// 模拟数据类型
interface Device {
  id: string;
  name: string;
  ip: string;
  type: string;
  status: 'online' | 'offline' | 'warning' | 'critical';
  last_seen: string;
  created_at: string;
  updated_at: string;
  rack_id?: string;
  rack_position?: number;
}

interface DeviceMetric {
  id: string;
  device_id: string;
  metric_name: string;
  metric_value: number;
  unit: string;
  timestamp: string;
}

interface MetricData {
  metric_name: string;
  unit: string;
  data: Array<{
    timestamp: string;
    value: number;
  }>;
}

// 页面参数类型
interface PageProps {
  params: {
    id: string;
  };
  searchParams: Record<string, string | string[] | undefined>;
}

export default function DeviceDetailsPage({ params }: PageProps) {
  const { id } = params;
  // 简化版本，不使用翻译
  const t = (key: string) => key;
  const [device, setDevice] = useState<Device | null>(null);
  const [currentMetrics, setCurrentMetrics] = useState<DeviceMetric[]>([]);
  const [historicalMetrics, setHistoricalMetrics] = useState<MetricData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // 模拟数据生成函数
  const getCurrentMetrics = (deviceId: string): DeviceMetric[] => {
    return [
      {
        id: `cpu-${deviceId}`,
        device_id: deviceId,
        metric_name: 'cpu_usage',
        metric_value: Math.floor(Math.random() * 100),
        unit: '%',
        timestamp: new Date().toISOString()
      },
      {
        id: `mem-${deviceId}`,
        device_id: deviceId,
        metric_name: 'memory_usage',
        metric_value: Math.floor(Math.random() * 100),
        unit: '%',
        timestamp: new Date().toISOString()
      },
      {
        id: `disk-${deviceId}`,
        device_id: deviceId,
        metric_name: 'disk_usage',
        metric_value: Math.floor(Math.random() * 100),
        unit: '%',
        timestamp: new Date().toISOString()
      }
    ];
  };

  const generateAllMetrics = (deviceId: string): MetricData[] => {
    const now = new Date();
    const data: MetricData[] = [];

    for (let i = 0; i < 3; i++) {
      const metricNames = ['cpu_usage', 'memory_usage', 'disk_usage'];
      const units = ['%', '%', '%'];

      const points: Array<{ timestamp: string; value: number }> = [];
      for (let j = 0; j < 24; j++) {
        const time = new Date(now);
        time.setHours(now.getHours() - j);

        points.push({
          timestamp: time.toISOString(),
          value: Math.floor(Math.random() * 100)
        });
      }

      data.push({
        metric_name: metricNames[i],
        unit: units[i],
        data: points
      });
    }

    return data;
  };

  // Load device data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        // In a real app, fetch from API
        // This is mock data
        const statusOptions: ('online' | 'offline' | 'warning' | 'critical')[] = ['online', 'warning', 'offline', 'critical'];
        const mockDevice: Device = {
          id,
          name: `Device-${id}`,
          ip: `192.168.1.${parseInt(id, 10) % 255}`,
          type: ['server', 'switch', 'router', 'storage'][parseInt(id, 10) % 4],
          status: statusOptions[parseInt(id, 10) % 4],
          last_seen: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        setDevice(mockDevice);
        setCurrentMetrics(getCurrentMetrics(id));
        setHistoricalMetrics(generateAllMetrics(id));
      } catch (error) {
        console.error('Failed to load device data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [id]);

  // 模拟阈值数据
  const getMetricThresholds = (deviceId: string) => {
    return {
      cpu_usage: {
        warning: 70,
        critical: 90,
        comparison: 'gt'
      },
      memory_usage: {
        warning: 80,
        critical: 95,
        comparison: 'gt'
      },
      disk_usage: {
        warning: 85,
        critical: 95,
        comparison: 'gt'
      }
    };
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    let variant;
    switch (status) {
      case 'online':
        variant = 'success';
        break;
      case 'offline':
        variant = 'secondary';
        break;
      case 'warning':
        variant = 'warning';
        break;
      case 'critical':
        variant = 'destructive';
        break;
      default:
        variant = 'secondary';
    }
    return (
      <Badge variant={variant}>
        {t(`status.${status}`) || status}
      </Badge>
    );
  };

  // Refresh device data
  const refreshData = async () => {
    setRefreshing(true);
    try {
      setCurrentMetrics(getCurrentMetrics(id));
      setHistoricalMetrics(generateAllMetrics(id));
      // Update last_seen timestamp
      if (device) {
        setDevice({
          ...device,
          last_seen: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Get metric status based on thresholds
  const getMetricStatus = (metricName: string, value: number): 'normal' | 'warning' | 'critical' => {
    const thresholds = getMetricThresholds(id);
    const metricThresholds = thresholds[metricName as keyof typeof thresholds];

    if (!metricThresholds) return 'normal';

    const { warning, critical, comparison } = metricThresholds;

    if (comparison === 'gt') {
      if (critical !== null && value >= critical) return 'critical';
      if (warning !== null && value >= warning) return 'warning';
    } else if (comparison === 'lt') {
      if (critical !== null && value <= critical) return 'critical';
      if (warning !== null && value <= warning) return 'warning';
    }

    return 'normal';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[500px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!device) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">{t('device.notFound') || 'Device not found'}</h2>
        <Button asChild>
          <Link href="/monitoring/devices">
            {t('back.to.devices') || 'Back to Devices'}
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6 flex-wrap">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold">{device.name}</h1>
            {getStatusBadge(device.status)}
          </div>
          <p className="text-muted-foreground">{device.ip}</p>
        </div>

        <Button
          onClick={refreshData}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={refreshing ? "animate-spin h-4 w-4" : "h-4 w-4"} />
          {t('refresh.data') || 'Refresh Data'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>{t('device.info') || 'Device Info'}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="font-medium">{t('device.type') || 'Type'}</p>
                <p>{t(`device.type.${device.type}`) || device.type}</p>
              </div>

              <div>
                <p className="font-medium">{t('device.status') || 'Status'}</p>
                <div>{getStatusBadge(device.status)}</div>
              </div>

              <div>
                <p className="font-medium">{t('device.lastSeen') || 'Last Seen'}</p>
                <p>{new Date(device.last_seen).toLocaleString()}</p>
              </div>

              {device.rack_id && (
                <div>
                  <p className="font-medium">{t('device.rackLocation') || 'Rack Location'}</p>
                  <p>
                    {t('rack')}: {device.rack_id}, {t('position')}: {device.rack_position}U
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-3">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {currentMetrics.map((metric) => (
              <Card key={metric.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">
                    {t(`metric.${metric.metric_name}`) || metric.metric_name.replace('_', ' ')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {metric.metric_value}{metric.unit}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {new Date(metric.timestamp).toLocaleString()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      <h2 className="text-xl font-semibold mb-4">{t('historical.data') || 'Historical Data'}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {historicalMetrics.map((metric) => (
          <Card key={metric.metric_name}>
            <CardHeader>
              <CardTitle className="text-base">
                {t(`metric.${metric.metric_name}`) || metric.metric_name.replace('_', ' ')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[180px] flex items-center justify-center">
                <p>Chart would be displayed here</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}