'use client'

import React from 'react'
import { 
  Box, 
  Heading, 
  Card, 
  CardBody, 
  useColorMode, 
  SimpleGrid,
  Tag,
  TagLabel,
  Icon,
  Text
} from '@chakra-ui/react'
import { Cube } from '@phosphor-icons/react'

export default function TestLayout() {
  const { colorMode } = useColorMode()
  
  return (
    <Box p={4} bg={colorMode === 'dark' ? 'gray.900' : 'gray.50'} minH="100vh">
      <Card 
        mb={6} 
        borderRadius="xl" 
        overflow="hidden" 
        boxShadow="lg"
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
        borderWidth="1px"
        borderColor={colorMode === 'dark' ? 'blue.800' : 'blue.100'}
      >
        <CardBody p={5}>
          <Heading 
            size="lg" 
            fontWeight="bold" 
            bgGradient={colorMode === 'dark' ? 
              'linear(to-r, cyan.400, blue.500, purple.600)' : 
              'linear(to-r, cyan.600, blue.700, purple.700)'} 
            bgClip="text"
            letterSpacing="tight"
          >
            测试页面
          </Heading>
          
          <Tag size="md" variant="subtle" colorScheme="blue" ml={1} borderRadius="full" mt={2}>
            <Icon as={Cube} weight="fill" mr={1} />
            <TagLabel>测试标签</TagLabel>
          </Tag>
          
          <SimpleGrid columns={{ base: 1, sm: 3 }} spacing={4} mt={4}>
            <Box p={3} bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'} borderRadius="md">
              <Text>测试区块 1</Text>
            </Box>
            <Box p={3} bg={colorMode === 'dark' ? 'blue.700' : 'blue.50'} borderRadius="md">
              <Text>测试区块 2</Text>
            </Box>
            <Box p={3} bg={colorMode === 'dark' ? 'purple.700' : 'purple.50'} borderRadius="md">
              <Text>测试区块 3</Text>
            </Box>
          </SimpleGrid>
        </CardBody>
      </Card>
    </Box>
  )
} 