'use client'

import React, { useState } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Stack,
  StackDivider,
  Switch,
  FormControl,
  FormLabel,
  Input,
  Button,
  Flex,
  Icon,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  useColorModeValue,
  useToast,
  VStack,
  HStack,
  Divider,
  Badge,
  Tooltip,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  Select,
  Textarea,
  SimpleGrid,
} from '@chakra-ui/react'
import {
  BellIcon,
  EmailIcon,
  SettingsIcon,
  AddIcon,
  DeleteIcon,
  EditIcon,
  CheckIcon,
  InfoIcon,
} from '@chakra-ui/icons'
import {
  Gear,
  Bell,
  EnvelopeSimple,
  ChatCircle,
  ChatCircleDots,
  Chats,
  Robot,
  Plus,
  Trash,
  PencilSimple,
  Check,
  Warning,
  Info,
  X,
  Prohibit
} from '@phosphor-icons/react'

// 通知渠道类型
interface NotificationChannel {
  id: string
  name: string
  type: 'email' | 'wechat' | 'feishu' | 'dingtalk' | 'webhook' | 'sms'
  enabled: boolean
  config: {
    [key: string]: string
  }
}

// 通知规则类型
interface NotificationRule {
  id: string
  name: string
  description: string
  enabled: boolean
  severity: 'info' | 'warning' | 'error' | 'critical'
  channels: string[] // 通知渠道ID列表
}

// 测试通知类型
interface TestNotification {
  title: string
  message: string
  severity: 'info' | 'warning' | 'error' | 'critical'
}

// 导入模态框组件
import NotificationChannelModal from '@/components/notification/NotificationChannelModal'
import NotificationRuleModal from '@/components/notification/NotificationRuleModal'
import TestNotificationModal from '@/components/notification/TestNotificationModal'

export default function NotificationsPage() {
  const toast = useToast()
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 模态框状态
  const {
    isOpen: isChannelModalOpen,
    onOpen: onChannelModalOpen,
    onClose: onChannelModalClose
  } = useDisclosure()

  const {
    isOpen: isRuleModalOpen,
    onOpen: onRuleModalOpen,
    onClose: onRuleModalClose
  } = useDisclosure()

  const {
    isOpen: isTestModalOpen,
    onOpen: onTestModalOpen,
    onClose: onTestModalClose
  } = useDisclosure()

  // 通知渠道状态
  const [channels, setChannels] = useState<NotificationChannel[]>([
    {
      id: '1',
      name: '系统管理员邮箱',
      type: 'email',
      enabled: true,
      config: {
        email: '<EMAIL>',
        smtp_server: 'smtp.example.com',
        smtp_port: '587',
        username: 'admin',
        password: '********'
      }
    },
    {
      id: '2',
      name: '运维团队企业微信机器人',
      type: 'wechat',
      enabled: true,
      config: {
        webhook_url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=abcdef',
        secret: '********'
      }
    }
  ])

  // 通知规则状态
  const [rules, setRules] = useState<NotificationRule[]>([
    {
      id: '1',
      name: '服务器CPU告警',
      description: '当服务器CPU使用率超过90%时发送通知',
      enabled: true,
      severity: 'warning',
      channels: ['1', '2']
    },
    {
      id: '2',
      name: '网络设备离线告警',
      description: '当网络设备离线时发送通知',
      enabled: true,
      severity: 'error',
      channels: ['2']
    }
  ])

  // 编辑状态
  const [editingChannel, setEditingChannel] = useState<NotificationChannel | null>(null)
  const [editingRule, setEditingRule] = useState<NotificationRule | null>(null)

  // 测试通知状态
  const [testNotification, setTestNotification] = useState<TestNotification>({
    title: '测试通知',
    message: '这是一条测试通知消息',
    severity: 'info'
  })

  // 新建通知渠道
  const handleAddChannel = () => {
    setEditingChannel(null)
    onChannelModalOpen()
  }

  // 编辑通知渠道
  const handleEditChannel = (channel: NotificationChannel) => {
    setEditingChannel({...channel})
    onChannelModalOpen()
  }

  // 删除通知渠道
  const handleDeleteChannel = (id: string) => {
    setChannels(channels.filter(channel => channel.id !== id))
    toast({
      title: '通知渠道已删除',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  // 切换通知渠道启用状态
  const handleToggleChannel = (id: string) => {
    setChannels(channels.map(channel =>
      channel.id === id
        ? {...channel, enabled: !channel.enabled}
        : channel
    ))
  }

  // 保存通知渠道
  const handleSaveChannel = (channel: NotificationChannel) => {
    if (editingChannel) {
      // 更新现有渠道
      setChannels(channels.map(c =>
        c.id === editingChannel.id ? channel : c
      ))
    } else {
      // 添加新渠道
      const newChannel = {
        ...channel,
        id: `channel-${Date.now()}`
      }
      setChannels([...channels, newChannel])
    }

    onChannelModalClose()
    toast({
      title: editingChannel ? '通知渠道已更新' : '通知渠道已添加',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  // 新建通知规则
  const handleAddRule = () => {
    setEditingRule(null)
    onRuleModalOpen()
  }

  // 编辑通知规则
  const handleEditRule = (rule: NotificationRule) => {
    setEditingRule({...rule})
    onRuleModalOpen()
  }

  // 删除通知规则
  const handleDeleteRule = (id: string) => {
    setRules(rules.filter(rule => rule.id !== id))
    toast({
      title: '通知规则已删除',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  // 切换通知规则启用状态
  const handleToggleRule = (id: string) => {
    setRules(rules.map(rule =>
      rule.id === id
        ? {...rule, enabled: !rule.enabled}
        : rule
    ))
  }

  // 保存通知规则
  const handleSaveRule = (rule: NotificationRule) => {
    if (editingRule) {
      // 更新现有规则
      setRules(rules.map(r =>
        r.id === editingRule.id ? rule : r
      ))
    } else {
      // 添加新规则
      const newRule = {
        ...rule,
        id: `rule-${Date.now()}`
      }
      setRules([...rules, newRule])
    }

    onRuleModalClose()
    toast({
      title: editingRule ? '通知规则已更新' : '通知规则已添加',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
  }

  // 发送测试通知
  const handleSendTestNotification = () => {
    toast({
      title: '测试通知已发送',
      description: `标题: ${testNotification.title}, 严重性: ${testNotification.severity}`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    })
    onTestModalClose()
  }

  return (
    <Container maxW="container.xl" py={6} mx="auto">
      <Box mb={8}>
        <Flex justify="space-between" align="center" mb={4}>
          <Heading size="lg">通知设置</Heading>
          <HStack spacing={4}>
            <Button
              leftIcon={<Info weight="bold" />}
              colorScheme="teal"
              variant="outline"
              onClick={onTestModalOpen}
              size="md"
            >
              发送测试通知
            </Button>
          </HStack>
        </Flex>
        <Text color="gray.600">
          配置系统通知方式和规则，确保重要的事件和告警能够及时送达
        </Text>
      </Box>

      {/* 通知渠道部分 */}
      <Card
        variant="outline"
        mb={8}
        borderColor={borderColor}
        boxShadow="sm"
      >
        <CardHeader bg={useColorModeValue('gray.50', 'gray.700')} borderBottomWidth="1px" borderColor={borderColor}>
          <Flex justify="space-between" align="center">
            <HStack>
              <Icon as={Bell} boxSize={5} color={useColorModeValue('blue.500', 'blue.300')} />
              <Heading size="md">通知渠道</Heading>
            </HStack>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="blue"
              onClick={handleAddChannel}
              size="sm"
            >
              添加通知渠道
            </Button>
          </Flex>
        </CardHeader>

        <CardBody>
          <Text mb={6} color="gray.600">
            配置各种通知方式，用于接收系统告警和事件通知。支持邮件、企业微信、飞书、钉钉等多种通知方式。
          </Text>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
            {channels.map(channel => (
              <Card
                key={channel.id}
                borderWidth="1px"
                borderColor={borderColor}
                borderRadius="md"
                overflow="hidden"
                transition="all 0.2s"
                _hover={{ boxShadow: 'md' }}
                height="100%"
              >
                <Box
                  bg={
                    channel.type === 'email' ? 'blue.50' :
                    channel.type === 'wechat' ? 'green.50' :
                    channel.type === 'feishu' ? 'cyan.50' :
                    channel.type === 'dingtalk' ? 'red.50' :
                    channel.type === 'webhook' ? 'purple.50' :
                    'gray.50'
                  }
                  py={2}
                  px={4}
                  borderBottomWidth="1px"
                  borderColor={borderColor}
                >
                  <Flex justify="space-between" align="center">
                    <HStack>
                      {channel.type === 'email' && <Icon as={EnvelopeSimple} boxSize={5} color="blue.500" />}
                      {channel.type === 'wechat' && <Icon as={ChatCircle} boxSize={5} color="green.500" />}
                      {channel.type === 'feishu' && <Icon as={ChatCircleDots} boxSize={5} color="blue.400" />}
                      {channel.type === 'dingtalk' && <Icon as={Chats} boxSize={5} color="red.500" />}
                      {channel.type === 'webhook' && <Icon as={Robot} boxSize={5} color="purple.500" />}
                      {channel.type === 'sms' && <Icon as={ChatCircle} boxSize={5} color="orange.500" />}
                      <Text fontWeight="bold">{channel.name}</Text>
                    </HStack>
                    <Badge
                      colorScheme={channel.enabled ? 'green' : 'gray'}
                      variant="solid"
                      borderRadius="full"
                      px={2}
                    >
                      {channel.enabled ? '已启用' : '已禁用'}
                    </Badge>
                  </Flex>
                </Box>

                <CardBody py={3}>
                  <Text fontSize="sm" color="gray.600" mb={3}>
                    {channel.type === 'email' && '通过SMTP服务器发送邮件通知'}
                    {channel.type === 'wechat' && '通过企业微信机器人发送群消息通知'}
                    {channel.type === 'feishu' && '通过飞书机器人发送群消息通知'}
                    {channel.type === 'dingtalk' && '通过钉钉机器人发送群消息通知'}
                    {channel.type === 'webhook' && '通过HTTP Webhook发送自定义通知'}
                    {channel.type === 'sms' && '通过短信服务发送手机短信通知'}
                  </Text>

                  <Text fontSize="xs" color="gray.500" mb={2}>
                    {channel.type === 'email' && `发送至: ${channel.config.email || '未设置'}`}
                    {channel.type === 'wechat' && '企业微信群机器人'}
                    {channel.type === 'feishu' && '飞书群机器人'}
                    {channel.type === 'dingtalk' && '钉钉群机器人'}
                    {channel.type === 'webhook' && `${channel.config.method || 'POST'} 请求`}
                    {channel.type === 'sms' && `手机号: ${channel.config.phone_number || '未设置'}`}
                  </Text>
                </CardBody>

                <Flex
                  p={3}
                  borderTopWidth="1px"
                  borderColor={borderColor}
                  bg={useColorModeValue('gray.50', 'gray.700')}
                  justify="space-between"
                  align="center"
                >
                  <Box position="relative" zIndex={1}>
                    <Switch
                      isChecked={channel.enabled}
                      onChange={() => handleToggleChannel(channel.id)}
                      colorScheme="green"
                      size="md"
                      cursor="pointer"
                    />
                  </Box>
                  <HStack>
                    <Tooltip label="编辑">
                      <IconButton
                        icon={<PencilSimple weight="bold" />}
                        aria-label="编辑"
                        variant="ghost"
                        colorScheme="blue"
                        size="sm"
                        onClick={() => handleEditChannel(channel)}
                      />
                    </Tooltip>
                    <Tooltip label="删除">
                      <IconButton
                        icon={<Trash weight="bold" />}
                        aria-label="删除"
                        variant="ghost"
                        colorScheme="red"
                        size="sm"
                        onClick={() => handleDeleteChannel(channel.id)}
                      />
                    </Tooltip>
                  </HStack>
                </Flex>
              </Card>
            ))}

            {/* 添加通知渠道卡片 */}
            {channels.length > 0 && (
              <Card
                borderWidth="1px"
                borderColor={borderColor}
                borderStyle="dashed"
                borderRadius="md"
                bg="transparent"
                height="100%"
                _hover={{
                  borderColor: 'blue.300',
                  boxShadow: 'sm',
                  bg: useColorModeValue('gray.50', 'gray.700')
                }}
                transition="all 0.2s"
                cursor="pointer"
                onClick={handleAddChannel}
              >
                <CardBody>
                  <Flex direction="column" align="center" justify="center" h="100%" py={6}>
                    <Icon as={Plus} boxSize={10} color="blue.400" mb={4} />
                    <Text color="blue.500" fontWeight="medium">添加通知渠道</Text>
                    <Text color="gray.500" fontSize="sm" textAlign="center" mt={2}>
                      配置新的通知方式
                    </Text>
                  </Flex>
                </CardBody>
              </Card>
            )}
          </SimpleGrid>

          {channels.length === 0 && (
            <Card borderWidth="1px" borderColor={borderColor} bg={useColorModeValue('gray.50', 'gray.700')}>
              <CardBody>
                <Flex direction="column" align="center" justify="center" py={8}>
                  <Icon as={Bell} boxSize={12} color="gray.400" mb={4} />
                  <Text color="gray.500" fontSize="lg" mb={2}>暂无通知渠道</Text>
                  <Text color="gray.500" fontSize="sm" mb={6} textAlign="center" maxW="md">
                    通知渠道用于接收系统告警和事件通知，支持邮件、企业微信、飞书、钉钉等多种通知方式
                  </Text>
                  <Button
                    leftIcon={<Plus weight="bold" />}
                    colorScheme="blue"
                    onClick={handleAddChannel}
                    size="md"
                  >
                    添加通知渠道
                  </Button>
                </Flex>
              </CardBody>
            </Card>
          )}
        </CardBody>
      </Card>

      {/* 通知规则部分 */}
      <Card
        variant="outline"
        borderColor={borderColor}
        boxShadow="sm"
      >
        <CardHeader bg={useColorModeValue('gray.50', 'gray.700')} borderBottomWidth="1px" borderColor={borderColor}>
          <Flex justify="space-between" align="center">
            <HStack>
              <Icon as={Gear} boxSize={5} color={useColorModeValue('purple.500', 'purple.300')} />
              <Heading size="md">通知规则</Heading>
            </HStack>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="purple"
              onClick={handleAddRule}
              size="sm"
            >
              添加通知规则
            </Button>
          </Flex>
        </CardHeader>

        <CardBody>
          <Text mb={6} color="gray.600">
            设置何时发送通知以及使用哪些通知渠道。通知规则决定了系统在什么情况下通过哪些渠道发送通知。
          </Text>

          <VStack spacing={4} align="stretch">
            {rules.map(rule => (
              <Card
                key={rule.id}
                borderWidth="1px"
                borderColor={borderColor}
                borderRadius="md"
                overflow="hidden"
                transition="all 0.2s"
                _hover={{ boxShadow: 'md' }}
                position="relative"
                borderLeftWidth="4px"
                borderLeftColor={
                  rule.severity === 'info' ? 'blue.400' :
                  rule.severity === 'warning' ? 'yellow.400' :
                  rule.severity === 'error' ? 'orange.400' : 'red.400'
                }
              >
                <CardBody p={0}>
                  <Flex>
                    {/* 左侧严重性指示器 */}
                    <Box
                      w="60px"
                      bg={
                        rule.severity === 'info' ? 'blue.50' :
                        rule.severity === 'warning' ? 'yellow.50' :
                        rule.severity === 'error' ? 'orange.50' : 'red.50'
                      }
                      display="flex"
                      flexDirection="column"
                      alignItems="center"
                      justifyContent="center"
                      py={4}
                      borderRightWidth="1px"
                      borderColor={borderColor}
                    >
                      <Icon
                        as={
                          rule.severity === 'info' ? Info :
                          rule.severity === 'warning' ? Warning :
                          rule.severity === 'error' ? X : Prohibit
                        }
                        boxSize={6}
                        color={
                          rule.severity === 'info' ? 'blue.500' :
                          rule.severity === 'warning' ? 'yellow.500' :
                          rule.severity === 'error' ? 'orange.500' : 'red.500'
                        }
                        mb={2}
                      />
                      <Badge
                        colorScheme={
                          rule.severity === 'info' ? 'blue' :
                          rule.severity === 'warning' ? 'yellow' :
                          rule.severity === 'error' ? 'orange' : 'red'
                        }
                        fontSize="xs"
                      >
                        {rule.severity === 'info' && '信息'}
                        {rule.severity === 'warning' && '警告'}
                        {rule.severity === 'error' && '错误'}
                        {rule.severity === 'critical' && '严重'}
                      </Badge>
                    </Box>

                    {/* 右侧内容 */}
                    <Box flex="1" p={4}>
                      <Flex justify="space-between" align="flex-start" mb={2}>
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="bold" fontSize="md">{rule.name}</Text>
                          <Text fontSize="sm" color="gray.600">{rule.description}</Text>
                        </VStack>

                        <HStack>
                          <Badge
                            colorScheme={rule.enabled ? 'green' : 'gray'}
                            variant="solid"
                            borderRadius="full"
                            px={2}
                          >
                            {rule.enabled ? '已启用' : '已禁用'}
                          </Badge>
                          <Box position="relative" zIndex={1}>
                            <Switch
                              isChecked={rule.enabled}
                              onChange={() => handleToggleRule(rule.id)}
                              colorScheme="green"
                              size="md"
                              cursor="pointer"
                            />
                          </Box>
                        </HStack>
                      </Flex>

                      <Box mt={3}>
                        <Text fontSize="xs" color="gray.500" mb={1}>通知渠道:</Text>
                        <Flex wrap="wrap" gap={2}>
                          {rule.channels.map(channelId => {
                            const channel = channels.find(c => c.id === channelId)
                            return channel ? (
                              <HStack
                                key={channelId}
                                bg={useColorModeValue('gray.100', 'gray.700')}
                                px={2}
                                py={1}
                                borderRadius="md"
                                fontSize="xs"
                              >
                                {channel.type === 'email' && <Icon as={EnvelopeSimple} boxSize={3} color="blue.500" />}
                                {channel.type === 'wechat' && <Icon as={ChatCircle} boxSize={3} color="green.500" />}
                                {channel.type === 'feishu' && <Icon as={ChatCircleDots} boxSize={3} color="blue.400" />}
                                {channel.type === 'dingtalk' && <Icon as={Chats} boxSize={3} color="red.500" />}
                                {channel.type === 'webhook' && <Icon as={Robot} boxSize={3} color="purple.500" />}
                                <Text>{channel.name}</Text>
                              </HStack>
                            ) : null
                          })}
                          {rule.channels.length === 0 && (
                            <Text fontSize="xs" color="gray.500">未选择通知渠道</Text>
                          )}
                        </Flex>
                      </Box>
                    </Box>

                    {/* 操作按钮 */}
                    <Flex
                      direction="column"
                      borderLeftWidth="1px"
                      borderColor={borderColor}
                      p={2}
                      justify="center"
                      bg={useColorModeValue('gray.50', 'gray.700')}
                    >
                      <Tooltip label="编辑">
                        <IconButton
                          icon={<PencilSimple weight="bold" />}
                          aria-label="编辑"
                          variant="ghost"
                          colorScheme="blue"
                          size="sm"
                          onClick={() => handleEditRule(rule)}
                          mb={2}
                        />
                      </Tooltip>
                      <Tooltip label="删除">
                        <IconButton
                          icon={<Trash weight="bold" />}
                          aria-label="删除"
                          variant="ghost"
                          colorScheme="red"
                          size="sm"
                          onClick={() => handleDeleteRule(rule.id)}
                        />
                      </Tooltip>
                    </Flex>
                  </Flex>
                </CardBody>
              </Card>
            ))}

            {rules.length === 0 && (
              <Card borderWidth="1px" borderColor={borderColor} bg={useColorModeValue('gray.50', 'gray.700')}>
                <CardBody>
                  <Flex direction="column" align="center" justify="center" py={8}>
                    <Icon as={Gear} boxSize={12} color="gray.400" mb={4} />
                    <Text color="gray.500" fontSize="lg" mb={2}>暂无通知规则</Text>
                    <Text color="gray.500" fontSize="sm" mb={6} textAlign="center" maxW="md">
                      通知规则决定了系统在什么情况下通过哪些渠道发送通知，例如设备告警、系统事件等
                    </Text>
                    <Button
                      leftIcon={<Plus weight="bold" />}
                      colorScheme="purple"
                      onClick={handleAddRule}
                      size="md"
                    >
                      添加通知规则
                    </Button>
                  </Flex>
                </CardBody>
              </Card>
            )}
          </VStack>
        </CardBody>
      </Card>

      {/* 通知渠道模态框 */}
      <NotificationChannelModal
        isOpen={isChannelModalOpen}
        onClose={onChannelModalClose}
        onSave={handleSaveChannel}
        channel={editingChannel}
      />

      {/* 通知规则模态框 */}
      <NotificationRuleModal
        isOpen={isRuleModalOpen}
        onClose={onRuleModalClose}
        onSave={handleSaveRule}
        rule={editingRule}
        channels={channels}
      />

      {/* 测试通知模态框 */}
      <TestNotificationModal
        isOpen={isTestModalOpen}
        onClose={onTestModalClose}
        onSend={handleSendTestNotification}
        channels={channels}
      />
    </Container>
  )
}
