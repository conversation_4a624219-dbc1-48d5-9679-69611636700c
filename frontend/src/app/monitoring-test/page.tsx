'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { snmpService, sshService } from '@/services'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function MonitoringTestPage() {
  const [snmpHost, setSnmpHost] = useState('')
  const [snmpCommunity, setSnmpCommunity] = useState('public')
  const [snmpOid, setSnmpOid] = useState('*******.*******.0')
  const [sshHost, setSshHost] = useState('')
  const [sshUser, setSshUser] = useState('admin')
  const [sshPassword, setSshPassword] = useState('')
  const [sshCommand, setSshCommand] = useState('uptime')

  const testSnmp = async () => {
    try {
      await snmpService.addDevice({
        host: snmpHost,
        community: snmpCommunity,
        oids: [snmpOid],
        interval: 60
      })
      toast({
        title: "SNMP测试成功",
        description: "设备已添加到监控",
      })
    } catch (err) {
      toast({
        title: "SNMP测试失败",
        description: err instanceof Error ? err.message : "连接SNMP设备失败",
        variant: "destructive",
      })
    }
  }

  const testSsh = async () => {
    try {
      await sshService.addDevice({
        host: sshHost,
        username: sshUser,
        password: sshPassword,
        commands: [sshCommand],
        interval: 60,
        port: 22
      })
      toast({
        title: "SSH测试成功",
        description: "设备已添加到监控",
      })
    } catch (err) {
      toast({
        title: "SSH测试失败",
        description: err instanceof Error ? err.message : "连接SSH设备失败",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <h1 className="text-2xl font-bold">监控功能测试</h1>

      <div className="grid gap-8 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>SNMP测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>设备IP</Label>
              <Input
                value={snmpHost}
                onChange={(e) => setSnmpHost(e.target.value)}
                placeholder="***********"
              />
            </div>
            <div className="space-y-2">
              <Label>Community</Label>
              <Input
                value={snmpCommunity}
                onChange={(e) => setSnmpCommunity(e.target.value)}
                placeholder="public"
              />
            </div>
            <div className="space-y-2">
              <Label>OID</Label>
              <Input
                value={snmpOid}
                onChange={(e) => setSnmpOid(e.target.value)}
                placeholder="*******.*******.0"
              />
            </div>
            <Button onClick={testSnmp}>测试SNMP</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>SSH测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>设备IP</Label>
              <Input
                value={sshHost}
                onChange={(e) => setSshHost(e.target.value)}
                placeholder="***********"
              />
            </div>
            <div className="space-y-2">
              <Label>用户名</Label>
              <Input
                value={sshUser}
                onChange={(e) => setSshUser(e.target.value)}
                placeholder="admin"
              />
            </div>
            <div className="space-y-2">
              <Label>密码</Label>
              <Input
                type="password"
                value={sshPassword}
                onChange={(e) => setSshPassword(e.target.value)}
                placeholder="密码"
              />
            </div>
            <div className="space-y-2">
              <Label>命令</Label>
              <Input
                value={sshCommand}
                onChange={(e) => setSshCommand(e.target.value)}
                placeholder="uptime"
              />
            </div>
            <Button onClick={testSsh}>测试SSH</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}