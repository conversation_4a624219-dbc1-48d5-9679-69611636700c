'use client'

import React from 'react'
import dynamic from 'next/dynamic'

// 动态导入仪表盘组件，确保它只在客户端渲染
const ModernDashboard = dynamic(
  () => import('@/components/Dashboard/ModernDashboard'),
  { ssr: false }
)

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white">
      <div className="container mx-auto px-4 py-8">
        <ModernDashboard />
      </div>
    </div>
  )
}