'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Flex,
  Heading,
  Grid,
  GridItem,
  useColorMode,
  Button,
  IconButton,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  VStack,
  HStack,
  Text,
  Badge,
  Tooltip,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  Card,
  CardBody,
  Divider,
  useDisclosure,
  Icon,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  CircularProgress,
  CircularProgressLabel,
  ChakraProvider,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  PopoverArrow,
  PopoverCloseButton,
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useTranslation } from '@/contexts/LanguageContext'
import {
  MagnifyingGlass,
  MapPin,
  Buildings,
  Warehouse,
  CaretDown,
  Plus,
  ArrowsOut,
  ArrowsIn,
  Crosshair,
  StackSimple,
  ListDashes,
  Database,
  Desktop,
  HardDrives,
  Graph,
  Pulse,
  ArrowsOutCardinal,
  Network,
} from '@phosphor-icons/react'

const AssetMapPage = () => {
  const { colorMode } = useColorMode()
  const router = useRouter()
  const { t } = useTranslation()
  
  // States
  const [selectedLocation, setSelectedLocation] = useState<string>('datacenter3f')
  const [zoomLevel, setZoomLevel] = useState(1)
  const [selectedFloor, setSelectedFloor] = useState(3)
  const [assetStats, setAssetStats] = useState({
    total: 223,
    online: 198,
    warning: 15,
    offline: 10
  })
  // Add state for details popup
  const [showDetails, setShowDetails] = useState(false)
  
  // Handle location click to show details
  const handleLocationClick = (locationId: string, e: React.MouseEvent) => {
    setSelectedLocation(locationId)
    setShowDetails(true)
    // Prevent event from bubbling up
    e.stopPropagation()
  }
  
  // Close details popover
  const closeDetails = () => {
    setShowDetails(false)
  }
  
  // Mock data for locations based on requirements
  const locations = [
    {
      id: 'datacenter3f',
      name: '3F 数据中心',
      position: { row: 2, col: 2 },
      type: 'datacenter',
      assets: 128,
      status: 'normal',
      uptime: 99.9,
      utilization: 78,
      temperature: 22.5,
      isCore: true
    },
    {
      id: 'electric2f',
      name: '2F 弱电间',
      position: { row: 1, col: 1 },
      type: 'electric',
      assets: 23,
      status: 'normal',
      uptime: 99.8,
      utilization: 45,
      temperature: 23.1,
      isCore: false
    },
    {
      id: 'gym2f',
      name: '2F 健身房',
      position: { row: 1, col: 3 },
      type: 'office',
      assets: 12,
      status: 'normal',
      uptime: 99.7,
      utilization: 32,
      temperature: 24.2,
      isCore: false
    },
    {
      id: 'electric4f',
      name: '4F 弱电间',
      position: { row: 3, col: 1 },
      type: 'electric',
      assets: 18,
      status: 'warning',
      uptime: 99.5,
      utilization: 67,
      temperature: 25.8,
      isCore: false
    },
    {
      id: 'electric7f',
      name: '7F 弱电间',
      position: { row: 3, col: 3 },
      type: 'electric',
      assets: 22,
      status: 'normal',
      uptime: 99.8,
      utilization: 41,
      temperature: 23.0,
      isCore: false
    },
    {
      id: 'shanghaiOffice',
      name: '上海办公区',
      position: { row: 1, col: 2 },
      type: 'office',
      assets: 42,
      status: 'normal',
      uptime: 99.6,
      utilization: 52,
      temperature: 22.8,
      isCore: false
    },
  ]

  // 获取位置图标
  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'datacenter':
        return HardDrives
      case 'electric':
        return Database
      case 'office':
        return Desktop
      default:
        return MapPin
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'warning':
        return 'yellow'
      case 'error':
        return 'red'
      default:
        return 'green'
    }
  }

  // Get detailed information for the selected location
  const getLocationDetails = () => {
    return locations.find(loc => loc.id === selectedLocation)
  }

  return (
    <Box p={4}>
      {/* Header Section */}
      <Flex direction="column" mb={6}>
        <Breadcrumb mb={4}>
          <BreadcrumbItem>
            <BreadcrumbLink onClick={() => router.push('/resources')}>
              {t('resources')}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>资产地图</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <Flex justify="space-between" align="center" mb={4}>
          <Heading size="lg" fontWeight="bold">
            资产地图
          </Heading>
          <HStack spacing={2}>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="blue"
              size="md"
              variant="solid"
              px={4}
            >
              添加位置
            </Button>
            <Menu>
              <MenuButton
                as={Button}
                rightIcon={<CaretDown />}
                variant="outline"
                size="md"
              >
                更多操作
              </MenuButton>
              <MenuList>
                <MenuItem icon={<ListDashes weight="bold" />}>导出位置列表</MenuItem>
                <MenuItem icon={<StackSimple weight="bold" />}>批量管理</MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </Flex>

        <Grid templateColumns="repeat(12, 1fr)" gap={4}>
          <GridItem colSpan={{ base: 12, md: 12 }}>
            <InputGroup>
              <InputLeftElement pointerEvents="none" height="100%" pl={2}>
                <MagnifyingGlass />
              </InputLeftElement>
              <Input 
                placeholder="搜索位置..." 
                paddingLeft="40px"
                height="40px"
              />
            </InputGroup>
          </GridItem>
        </Grid>

        <HStack spacing={4} mt={4} justify="flex-end">
              <IconButton
                aria-label="Zoom out"
                icon={<ArrowsIn weight="bold" />}
                onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.1))}
                isDisabled={zoomLevel <= 0.5}
              />
              <IconButton
                aria-label="Reset zoom"
                icon={<Crosshair weight="bold" />}
                onClick={() => setZoomLevel(1)}
              />
              <IconButton
                aria-label="Zoom in"
                icon={<ArrowsOut weight="bold" />}
                onClick={() => setZoomLevel(Math.min(2, zoomLevel + 0.1))}
                isDisabled={zoomLevel >= 2}
              />
            </HStack>
      </Flex>

      {/* Main Content */}
          <Box
            bg={colorMode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            p={6}
            height="600px"
            position="relative"
        overflow="hidden"
        style={{
          transform: `scale(${zoomLevel})`,
          transformOrigin: 'center center',
          transition: 'transform 0.3s ease'
        }}
          >
        {/* 背景网格 - 创建科技感 */}
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          opacity="0.15"
          backgroundImage={`
            linear-gradient(${colorMode === 'dark' ? '#2D3748' : '#CBD5E0'} 1px, transparent 1px),
            linear-gradient(90deg, ${colorMode === 'dark' ? '#2D3748' : '#CBD5E0'} 1px, transparent 1px)
          `}
          backgroundSize="20px 20px"
        />

        {/* 连接线 - 从数据中心到其他区域 */}
        <Box position="absolute" top="0" left="0" width="100%" height="100%">
          {/* 连接 3F数据中心 到 2F弱电间 */}
          <Box
            position="absolute"
            top="130px"
            left="130px"
            width="250px"
            height="150px"
            borderTop="none"
            borderRight="none"
            borderBottom="2px dashed"
            borderLeft="2px dashed"
            borderColor={colorMode === 'dark' ? 'blue.300' : 'blue.500'}
            opacity="0.5"
            borderRadius="0 0 0 20px"
            className="path-animation"
          />
          
          {/* 连接 3F数据中心 到 上海办公区 */}
          <Box
            position="absolute"
            top="130px"
            left="50%"
            transform="translateX(-50%)"
            width="2px"
            height="120px"
            borderLeft="2px dashed"
            borderColor={colorMode === 'dark' ? 'blue.300' : 'blue.500'}
            opacity="0.5"
            className="path-animation"
          />
          
          {/* 连接 3F数据中心 到 2F健身房 */}
          <Box
            position="absolute"
            top="130px"
            right="130px"
            width="250px"
            height="150px"
            borderTop="none"
            borderRight="2px dashed"
            borderBottom="2px dashed"
            borderLeft="none"
            borderColor={colorMode === 'dark' ? 'blue.300' : 'blue.500'}
            opacity="0.5"
            borderRadius="0 0 20px 0"
            className="path-animation"
          />
          
          {/* 连接 3F数据中心 到 4F弱电间 */}
          <Box
            position="absolute"
            top="310px"
            left="130px"
            width="250px"
            height="150px"
            borderTop="2px dashed"
            borderRight="none"
            borderBottom="none"
            borderLeft="2px dashed"
            borderColor={colorMode === 'dark' ? 'blue.300' : 'blue.500'}
            opacity="0.5"
            borderRadius="20px 0 0 0"
            className="path-animation"
          />
          
          {/* 连接 3F数据中心 到 7F弱电间 */}
          <Box
            position="absolute"
            top="310px"
            right="130px"
            width="250px"
            height="150px"
            borderTop="2px dashed"
            borderRight="2px dashed"
            borderBottom="none"
            borderLeft="none"
            borderColor={colorMode === 'dark' ? 'blue.300' : 'blue.500'}
            opacity="0.5"
            borderRadius="0 20px 0 0"
            className="path-animation"
          />
        </Box>
        
        {/* 添加CSS动画效果 */}
        <style jsx global>{`
          .path-animation {
            animation: pulse 1.5s infinite alternate;
          }
          
          @keyframes pulse {
            0% {
              opacity: 0.3;
            }
            100% {
              opacity: 0.7;
            }
          }
          
          .pulse-animation {
            animation: pulse-grow 2s infinite;
          }
          
          @keyframes pulse-grow {
            0% {
              transform: translate(-50%, -50%) scale(0.8);
              opacity: 0.2;
            }
            50% {
              transform: translate(-50%, -50%) scale(1.2);
              opacity: 0.4;
            }
            100% {
              transform: translate(-50%, -50%) scale(0.8);
              opacity: 0.2;
            }
          }
        `}</style>
        
        {/* 简化的资产地图布局 - 像截图一样 */}
        <Box position="relative" height="100%">
          {/* 2F 弱电间 */}
          <Box 
            position="absolute" 
            top="30px" 
            left="30px" 
            width="200px"
            onClick={(e) => handleLocationClick('electric2f', e)}
            cursor="pointer"
          >
            <Box
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="md"
              p={5}
              boxShadow="sm"
              border="1px solid" 
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
              _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
              transition="all 0.2s"
            >
              <Flex direction="column" align="center">
                <Icon as={Database} boxSize={8} color="#4299E1" mb={3} />
                <Text fontWeight="bold" fontSize="lg" mb={3}>2F 弱电间</Text>
                <Flex width="100%" mt={2} justify="space-between">
                  <HStack>
                    <Icon as={HardDrives} boxSize={4} />
                    <Text>23</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Graph} boxSize={4} />
                    <Text>45%</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Pulse} boxSize={4} />
                    <Text>23.1°C</Text>
                  </HStack>
                </Flex>
              </Flex>
            </Box>
          </Box>
          
          {/* 上海办公区 */}
          <Box 
            position="absolute" 
            top="30px" 
            left="50%" 
            transform="translateX(-50%)"
            width="200px"
            onClick={(e) => handleLocationClick('shanghaiOffice', e)}
            cursor="pointer"
          >
            <Box
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="md"
              p={5}
              boxShadow="sm"
              border="1px solid" 
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
              _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
              transition="all 0.2s"
            >
              <Flex direction="column" align="center">
                <Icon as={Desktop} boxSize={8} color="#4299E1" mb={3} />
                <Text fontWeight="bold" fontSize="lg" mb={3}>上海办公区</Text>
                <Flex width="100%" mt={2} justify="space-between">
                  <HStack>
                    <Icon as={HardDrives} boxSize={4} />
                    <Text>42</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Graph} boxSize={4} />
                    <Text>52%</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Pulse} boxSize={4} />
                    <Text>22.8°C</Text>
                  </HStack>
                </Flex>
              </Flex>
            </Box>
          </Box>
          
          {/* 2F 健身房 */}
          <Box 
            position="absolute" 
            top="30px" 
            right="30px" 
            width="200px"
            onClick={(e) => handleLocationClick('gym2f', e)}
            cursor="pointer"
          >
            <Box
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="md"
              p={5}
              boxShadow="sm"
              border="1px solid" 
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
              _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
              transition="all 0.2s"
            >
              <Flex direction="column" align="center">
                <Icon as={Desktop} boxSize={8} color="#4299E1" mb={3} />
                <Text fontWeight="bold" fontSize="lg" mb={3}>2F 健身房</Text>
                <Flex width="100%" mt={2} justify="space-between">
                  <HStack>
                    <Icon as={HardDrives} boxSize={4} />
                    <Text>12</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Graph} boxSize={4} />
                    <Text>32%</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Pulse} boxSize={4} />
                    <Text>24.2°C</Text>
                  </HStack>
                </Flex>
              </Flex>
            </Box>
          </Box>
          
          {/* 3F 数据中心 */}
          <Box 
            position="absolute" 
            top="250px" 
            left="50%" 
            transform="translate(-50%, 0)"
            width="280px"
            onClick={(e) => handleLocationClick('datacenter3f', e)}
            cursor="pointer"
            zIndex={2}
          >
            <Box
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="md"
              p={6}
              boxShadow="lg"
              border="2px solid" 
              borderColor="#ED8936"
              _hover={{ transform: 'scale(1.03)' }}
              transition="all 0.3s"
              position="relative"
              overflow="hidden"
            >
              {/* 脉动效果 */}
              <Box
                position="absolute"
                top="50%"
                left="50%"
                transform="translate(-50%, -50%)"
                width="200px"
                height="200px"
                borderRadius="full"
                border="1px solid"
                borderColor="#ED8936"
                opacity="0.2"
                zIndex="0"
                className="pulse-animation"
              />
              
              <Flex direction="column" align="center" position="relative" zIndex="1">
                <Icon as={HardDrives} boxSize={10} color="#ED8936" mb={3} />
                <Text fontWeight="bold" fontSize="xl" color="#ED8936" mb={3}>3F 数据中心</Text>
                
                <Flex width="100%" mt={2} mb={3} justify="space-between">
                  <HStack>
                    <Icon as={HardDrives} boxSize={4} />
                    <Text>128</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Graph} boxSize={4} />
                    <Text>78%</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Pulse} boxSize={4} />
                    <Text>22.5°C</Text>
                  </HStack>
                </Flex>
                
                <CircularProgress 
                  value={99.9} 
                  color="#ED8936" 
                  size="60px"
                  thickness="4px"
                >
                  <CircularProgressLabel fontWeight="bold">
                    99.9%
                  </CircularProgressLabel>
                </CircularProgress>
              </Flex>
            </Box>
          </Box>
          
          {/* 4F 弱电间 */}
          <Box 
            position="absolute" 
            bottom="30px" 
            left="30px" 
            width="200px"
            onClick={(e) => handleLocationClick('electric4f', e)}
            cursor="pointer"
          >
            <Box
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="md"
              p={5}
              boxShadow="sm"
              border="1px solid" 
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
              _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
              transition="all 0.2s"
            >
              <Flex direction="column" align="center">
                <Icon as={Database} boxSize={8} color="#ECC94B" mb={3} />
                <Text fontWeight="bold" fontSize="lg" mb={3}>4F 弱电间</Text>
                <Flex width="100%" mt={2} justify="space-between">
                  <HStack>
                    <Icon as={HardDrives} boxSize={4} />
                    <Text>18</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Graph} boxSize={4} color="#ECC94B" />
                    <Text color="#ECC94B">67%</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Pulse} boxSize={4} />
                    <Text>25.8°C</Text>
                  </HStack>
                </Flex>
              </Flex>
            </Box>
          </Box>
          
          {/* 7F 弱电间 */}
          <Box 
            position="absolute" 
            bottom="30px" 
            right="30px" 
            width="200px"
            onClick={(e) => handleLocationClick('electric7f', e)}
            cursor="pointer"
          >
            <Box
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="md"
              p={5}
              boxShadow="sm"
              border="1px solid" 
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
              _hover={{ boxShadow: 'md', transform: 'translateY(-2px)' }}
              transition="all 0.2s"
            >
              <Flex direction="column" align="center">
                <Icon as={Network} boxSize={8} color="#4299E1" mb={3} />
                <Text fontWeight="bold" fontSize="lg" mb={3}>7F 弱电间</Text>
                <Flex width="100%" mt={2} justify="space-between">
                  <HStack>
                    <Icon as={HardDrives} boxSize={4} />
                    <Text>12</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Graph} boxSize={4} color="#4299E1" />
                    <Text color="#4299E1">54%</Text>
                  </HStack>
                  <HStack>
                    <Icon as={Pulse} boxSize={4} />
                    <Text>22.3°C</Text>
                  </HStack>
                </Flex>
              </Flex>
            </Box>
          </Box>
          
          {/* Location Details Popup */}
          {showDetails && selectedLocation && (
            <Box
              position="absolute"
              top="50%"
              left="50%"
              transform="translate(-50%, -50%)"
              width="400px"
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              borderRadius="lg"
              boxShadow="xl"
              p={5}
              zIndex={10}
              border="2px solid"
              borderColor={locations.find(l => l.id === selectedLocation)?.isCore ? 'orange.500' : 'blue.500'}
            >
              <Flex justify="space-between" align="center" mb={4}>
                <HStack>
                  <Icon 
                    as={getLocationIcon(locations.find(l => l.id === selectedLocation)?.type || 'datacenter')}
                    color={locations.find(l => l.id === selectedLocation)?.isCore ? 'orange.500' : 'blue.500'}
                    boxSize={6}
                  />
                  <Heading size="md">
                    {locations.find(l => l.id === selectedLocation)?.name}
                    {locations.find(l => l.id === selectedLocation)?.isCore && (
                      <Badge ml={2} colorScheme="orange" variant="solid" fontSize="xs">
                        核心
                      </Badge>
                    )}
                  </Heading>
                </HStack>
                <IconButton
                  aria-label="Close details"
                  icon={<ArrowsIn weight="bold" />}
                  size="sm"
                  onClick={closeDetails}
                />
            </Flex>
              
              <Divider mb={4} />

              <Grid templateColumns="repeat(2, 1fr)" gap={6} mb={4}>
                <VStack align="flex-start">
                  <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                    位置类型
                  </Text>
                  <Badge colorScheme={
                    locations.find(l => l.id === selectedLocation)?.type === 'datacenter' ? 'purple' :
                    locations.find(l => l.id === selectedLocation)?.type === 'electric' ? 'teal' :
                    'green'
                  }>
                    {locations.find(l => l.id === selectedLocation)?.type === 'datacenter' ? '数据中心' :
                     locations.find(l => l.id === selectedLocation)?.type === 'electric' ? '弱电间' : '办公区'}
                  </Badge>
                </VStack>
                
                <VStack align="flex-start">
                  <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                    资产数量
                  </Text>
                  <Text fontWeight="bold">
                    {locations.find(l => l.id === selectedLocation)?.assets} 个
                  </Text>
                </VStack>

                <VStack align="flex-start">
                  <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                    可用率
                  </Text>
                  <Text fontWeight="bold" color="green.500">
                    {locations.find(l => l.id === selectedLocation)?.uptime}%
                  </Text>
                </VStack>

                <VStack align="flex-start">
                  <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                    使用率
                  </Text>
                  <Text fontWeight="bold" color={
                    (locations.find(l => l.id === selectedLocation)?.utilization || 0) > 80 ? 'red.500' :
                    (locations.find(l => l.id === selectedLocation)?.utilization || 0) > 60 ? 'yellow.500' :
                    'green.500'
                  }>
                    {locations.find(l => l.id === selectedLocation)?.utilization}%
                  </Text>
                </VStack>

                <VStack align="flex-start">
                  <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                    环境温度
                  </Text>
                  <Text fontWeight="bold" color={
                    (locations.find(l => l.id === selectedLocation)?.temperature || 0) > 25 ? 'yellow.500' : 'blue.500'
                  }>
                    {locations.find(l => l.id === selectedLocation)?.temperature}°C
                  </Text>
                </VStack>

                <VStack align="flex-start">
                  <Text fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                    状态
                  </Text>
                  <Badge colorScheme={getStatusColor(locations.find(l => l.id === selectedLocation)?.status || 'normal')}>
                    {locations.find(l => l.id === selectedLocation)?.status === 'warning' ? '需要关注' : 
                     locations.find(l => l.id === selectedLocation)?.status === 'error' ? '需要维护' : '正常'}
                  </Badge>
                </VStack>
              </Grid>
              
              <Divider mb={4} />
              
              <HStack spacing={4} justify="flex-end">
                <Button size="sm" variant="outline">查看资产列表</Button>
                <Button size="sm" colorScheme="blue">打开详情页</Button>
              </HStack>
            </Box>
          )}

          {/* Overlay for when details are shown */}
          {showDetails && (
            <Box
              position="absolute"
              top="0"
              left="0"
              right="0"
              bottom="0"
              bg="blackAlpha.300"
              zIndex={5}
              onClick={closeDetails}
            />
          )}
        </Box>

            {/* Map Controls */}
            <VStack
              position="absolute"
              top={4}
              right={4}
              spacing={2}
              bg={colorMode === 'dark' ? 'gray.700' : 'white'}
              p={2}
              borderRadius="md"
              boxShadow="md"
          zIndex={3}
            >
              <Tooltip label="定位到当前位置" placement="left">
                <IconButton
                  aria-label="Current location"
                  icon={<Crosshair weight="bold" />}
                  size="sm"
                />
              </Tooltip>
              <Divider />
              <Tooltip label="放大" placement="left">
                <IconButton
                  aria-label="Zoom in"
                  icon={<ArrowsOut weight="bold" />}
                  size="sm"
                  onClick={() => setZoomLevel(Math.min(2, zoomLevel + 0.1))}
                  isDisabled={zoomLevel >= 2}
                />
              </Tooltip>
              <Tooltip label="缩小" placement="left">
                <IconButton
                  aria-label="Zoom out"
                  icon={<ArrowsIn weight="bold" />}
                  size="sm"
                  onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.1))}
                  isDisabled={zoomLevel <= 0.5}
                />
              </Tooltip>
            </VStack>
          </Box>
    </Box>
  )
}

export default AssetMapPage 