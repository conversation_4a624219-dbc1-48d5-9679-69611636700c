'use client'

import React, { useState, useEffect } from 'react'
import { 
  <PERSON>, 
  <PERSON>ing, 
  Ta<PERSON>, 
  TabList, 
  TabPanels, 
  Tab, 
  TabPanel,
  Text,
  Button,
  Icon,
  useToast
} from '@chakra-ui/react'
import { FaSync } from 'react-icons/fa'
import { useTranslation } from '@/contexts/LanguageContext'
import NetworkPortStatus, { NetworkPort, NetworkDevice } from '@/components/NetworkPortStatus'
import { fetchDevicePorts } from '@/api/device'
import { Device } from '@/types/device'

// 生成随机端口状态，以生成更加真实的演示数据
const generateRandomStatus = (): 'up' | 'down' | 'error' | 'warning' | 'disabled' => {
  const statuses: Array<'up' | 'down' | 'error' | 'warning' | 'disabled'> = [
    'up', 'up', 'up', 'up', 'up',  // 更多的'up'状态，使大多数端口显示为已连接
    'down', 'down', 'down',
    'error', 
    'warning',
    'disabled'
  ];
  return statuses[Math.floor(Math.random() * statuses.length)];
};

// 生成端口数据
const generatePorts = (count: number, prefix: string = 'GI', startIndex: number = 1): NetworkPort[] => {
  const ports: NetworkPort[] = [];
  const types: Array<'ethernet' | 'fiber' | 'sfp' | 'sfpplus' | 'management'> = [
    'ethernet', 'ethernet', 'ethernet', 'ethernet', // 更多的以太网端口
    'fiber', 'fiber',
    'sfp', 'sfpplus',
    'management'
  ];
  
  // 计算每种类型的数量比例
  const ethernetCount = Math.floor(count * 0.6); // 60%以太网
  const sfpCount = Math.floor(count * 0.2);      // 20% SFP/SFP+
  const fiberCount = Math.floor(count * 0.15);   // 15% 光纤
  const mgmtCount = Math.max(1, Math.floor(count * 0.05)); // 5% 管理口，至少1个
  
  // 创建以太网端口
  for (let i = 0; i < ethernetCount; i++) {
    const portNum = startIndex + i;
    const status = generateRandomStatus();
    ports.push({
      id: `eth-${portNum}`,
      name: `${prefix}1/0/${portNum}`,
      type: 'ethernet',
      status,
      speed: '1Gbps',
      connection: status === 'up' ? `Device-${Math.floor(Math.random() * 100)}` : undefined
    });
  }
  
  // 创建SFP/SFP+端口
  for (let i = 0; i < sfpCount; i++) {
    const portNum = i + 1;
    const status = generateRandomStatus();
    ports.push({
      id: `sfp-${portNum}`,
      name: `Te1/1/${portNum}`,
      type: Math.random() > 0.3 ? 'sfp' : 'sfpplus',
      status,
      speed: '10Gbps',
      connection: status === 'up' ? `Core-${Math.floor(Math.random() * 10)}` : undefined
    });
  }
  
  // 创建光纤端口
  for (let i = 0; i < fiberCount; i++) {
    const portNum = i + 1;
    const status = generateRandomStatus();
    ports.push({
      id: `fiber-${portNum}`,
      name: `Fo1/2/${portNum}`,
      type: 'fiber',
      status,
      speed: '40Gbps',
      connection: status === 'up' ? `Backbone-${Math.floor(Math.random() * 5)}` : undefined
    });
  }
  
  // 创建管理端口
  for (let i = 0; i < mgmtCount; i++) {
    const status = Math.random() > 0.2 ? 'up' : 'down'; // 管理口通常是启用的
    ports.push({
      id: `mgmt-${i+1}`,
      name: `Mgmt${i}`,
      type: 'management',
      status,
      speed: '1Gbps',
      connection: status === 'up' ? 'Management Network' : undefined
    });
  }
  
  return ports;
};

interface DevicePortsProps {
  device: Device;
}

const DevicePorts: React.FC<DevicePortsProps> = ({ device }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [loading, setLoading] = useState(false);
  const [ports, setPorts] = useState<NetworkPort[]>([]);
  
  // 创建NetworkDevice对象用于展示
  const networkDevice: NetworkDevice = {
    id: device.id,
    name: device.name,
    type: device.category || 'network',
    ports: ports,
    status: device.status
  };
  
  // 加载端口数据
  const loadPorts = async (options?: { count?: number; prefix?: string; startIndex?: number }) => {
    setLoading(true);
    try {
      if (device.id) {
        // 从API获取端口信息
        const data = await fetchDevicePorts(device.id, options);
        setPorts(data);
      } else {
        // 如果没有设备ID，生成模拟数据
        const count = options?.count || 24;
        const prefix = options?.prefix || 'GI';
        const startIndex = options?.startIndex || 1;
        const generatedPorts = generatePorts(count, prefix, startIndex);
        setPorts(generatedPorts);
      }
    } catch (error) {
      console.error('Failed to load ports:', error);
      toast({
        title: '加载端口失败',
        description: '无法获取设备端口信息',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };
  
  // 自定义端口
  const handleCustomizePorts = (options: { count: number; prefix: string; startIndex: number }) => {
    loadPorts(options);
    toast({
      title: '端口配置已更新',
      description: `已生成${options.count}个端口，前缀: ${options.prefix}`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };
  
  // 首次加载
  useEffect(() => {
    loadPorts();
  }, [device.id]);
  
  return (
    <Box>
      <Tabs variant="enclosed" colorScheme="blue" isLazy>
        <TabList>
          <Tab>端口状态</Tab>
          <Tab>端口详情</Tab>
        </TabList>
        
        <TabPanels>
          <TabPanel>
            <Box mb={4} display="flex" justifyContent="flex-end">
              <Button 
                leftIcon={<Icon as={FaSync} />} 
                colorScheme="blue" 
                size="sm"
                isLoading={loading}
                onClick={() => loadPorts()}
              >
                刷新端口
              </Button>
            </Box>
            
            <NetworkPortStatus 
              device={networkDevice} 
              onCustomize={handleCustomizePorts} 
            />
          </TabPanel>
          
          <TabPanel>
            <Box>
              <Heading size="md" mb={4}>端口列表详情</Heading>
              <Text>设备端口详细列表将在这里显示</Text>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default DevicePorts; 