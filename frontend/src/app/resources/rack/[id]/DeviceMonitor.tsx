import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Spinner,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useColorMode,
  useToast,
  Icon,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Tooltip,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  HStack,
  VStack,
  Progress
} from '@chakra-ui/react';
import { ArrowsClockwise, Warning } from '@phosphor-icons/react';
import { useTranslation } from '@/contexts/LanguageContext';
import { deviceService, DeviceInfo } from '@/services/device';

interface DeviceMonitorProps {
  deviceId: string;
  rackId: string;
}

const DeviceMonitor: React.FC<DeviceMonitorProps> = ({ deviceId, rackId }) => {
  const { colorMode } = useColorMode();
  const { t } = useTranslation();
  const toast = useToast();
  
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 获取设备信息
  const fetchDeviceInfo = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const info = await deviceService.getDeviceInfo(deviceId);
      setDeviceInfo(info);
    } catch (err) {
      console.error('获取设备信息失败:', err);
      setError(t('device.fetch.error'));
      
      // 设置模拟数据用于演示
      if (process.env.NODE_ENV === 'development') {
        setDeviceInfo({
          device_id: deviceId,
          status: Math.random() > 0.7 ? 'warning' : 'normal',
          metrics: {
            cpu_usage: Math.random() * 100,
            memory_usage: Math.random() * 100,
            disk_usage: Math.random() * 100,
            temperature: 25 + Math.random() * 20,
            uptime: '10 days, 5 hours',
            model: 'PowerEdge R740',
            os_version: 'CentOS 7.9'
          },
          last_update: new Date().toISOString()
        });
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // 刷新设备数据
  const refreshDeviceData = async () => {
    setIsLoading(true);
    
    try {
      const info = await deviceService.refreshDevice(deviceId);
      setDeviceInfo(info);
      
      toast({
        title: t('device.data.refreshed'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('刷新设备数据失败:', err);
      
      toast({
        title: t('device.refresh.error'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 初始加载
  useEffect(() => {
    if (deviceId) {
      fetchDeviceInfo();
    }
  }, [deviceId]);
  
  // 渲染状态徽章
  const renderStatusBadge = (status: string) => {
    const statusColor = deviceService.getStatusColor(status);
    return (
      <Badge colorScheme={statusColor} variant="subtle" px={2} py={1} borderRadius="full">
        {status === 'normal' ? t('status.normal') : 
         status === 'warning' ? t('status.warning') : 
         status === 'error' ? t('status.error') : 
         t('status.unknown')}
      </Badge>
    );
  };
  
  // 渲染关键指标
  const renderKeyMetrics = () => {
    if (!deviceInfo || !deviceInfo.metrics) return null;
    
    const { metrics } = deviceInfo;
    
    return (
      <HStack spacing={4} wrap="wrap" justify="space-around">
        {/* CPU使用率 */}
        {metrics.cpu_usage !== undefined && (
          <Stat minW="120px" textAlign="center">
            <StatLabel>{t('cpu.usage')}</StatLabel>
            <StatNumber>{deviceService.formatMetricValue('cpu_usage', metrics.cpu_usage)}</StatNumber>
            <Progress 
              mt={2} 
              size="sm" 
              value={metrics.cpu_usage} 
              colorScheme={metrics.cpu_usage > 80 ? 'red' : metrics.cpu_usage > 60 ? 'orange' : 'green'} 
            />
          </Stat>
        )}
        
        {/* 内存使用率 */}
        {metrics.memory_usage !== undefined && (
          <Stat minW="120px" textAlign="center">
            <StatLabel>{t('memory.usage')}</StatLabel>
            <StatNumber>{deviceService.formatMetricValue('memory_usage', metrics.memory_usage)}</StatNumber>
            <Progress 
              mt={2} 
              size="sm" 
              value={metrics.memory_usage} 
              colorScheme={metrics.memory_usage > 80 ? 'red' : metrics.memory_usage > 60 ? 'orange' : 'green'} 
            />
          </Stat>
        )}
        
        {/* 磁盘使用率 */}
        {metrics.disk_usage !== undefined && (
          <Stat minW="120px" textAlign="center">
            <StatLabel>{t('disk.usage')}</StatLabel>
            <StatNumber>{deviceService.formatMetricValue('disk_usage', metrics.disk_usage)}</StatNumber>
            <Progress 
              mt={2} 
              size="sm" 
              value={metrics.disk_usage} 
              colorScheme={metrics.disk_usage > 80 ? 'red' : metrics.disk_usage > 60 ? 'orange' : 'green'} 
            />
          </Stat>
        )}
        
        {/* 温度 */}
        {metrics.temperature !== undefined && (
          <Stat minW="120px" textAlign="center">
            <StatLabel>{t('temperature')}</StatLabel>
            <StatNumber>{metrics.temperature}°C</StatNumber>
            <StatHelpText>
              {metrics.temperature > 40 ? (
                <Text color="red.500">{t('temperature.high')}</Text>
              ) : (
                <Text color="komodo.green">{t('temperature.normal')}</Text>
              )}
            </StatHelpText>
          </Stat>
        )}
      </HStack>
    );
  };
  
  // 渲染所有指标表格
  const renderMetricsTable = () => {
    if (!deviceInfo || !deviceInfo.metrics) return null;
    
    const { metrics } = deviceInfo;
    const metricEntries = Object.entries(metrics);
    
    // 按字母排序
    metricEntries.sort(([keyA], [keyB]) => keyA.localeCompare(keyB));
    
    return (
      <Box overflowX="auto">
        <Table variant="simple" size="sm">
          <Thead>
            <Tr>
              <Th>{t('metric.name')}</Th>
              <Th>{t('metric.value')}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {metricEntries.map(([key, value]) => (
              <Tr key={key}>
                <Td>
                  <Text fontWeight="medium">
                    {t(`metric.${key}`) || key}
                  </Text>
                </Td>
                <Td>
                  {deviceService.formatMetricValue(key, value)}
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    );
  };
  
  // 主渲染
  return (
    <Box 
      borderWidth="1px" 
      borderRadius="md" 
      p={4}
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      shadow="sm"
      mt={4}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md">
          {t('device.monitoring')}
        </Heading>
        
        <HStack>
          {deviceInfo && (
            <Tooltip label={t('last.update') + ': ' + new Date(deviceInfo.last_update).toLocaleString()}>
              <Box>{renderStatusBadge(deviceInfo.status)}</Box>
            </Tooltip>
          )}
          
          <Button
            size="sm"
            leftIcon={<Icon as={ArrowsClockwise} />}
            onClick={refreshDeviceData}
            isLoading={isLoading}
          >
            {t('refresh')}
          </Button>
        </HStack>
      </Flex>
      
      {isLoading && !deviceInfo ? (
        <Flex justify="center" align="center" py={10}>
          <Spinner size="xl" />
        </Flex>
      ) : error ? (
        <Flex 
          direction="column" 
          align="center" 
          justify="center" 
          py={10}
          borderWidth="1px"
          borderRadius="md"
          borderColor={colorMode === 'dark' ? 'red.500' : 'red.300'}
          bg={colorMode === 'dark' ? 'red.900' : 'red.50'}
        >
          <Icon as={Warning} fontSize="3xl" color="red.500" mb={2} />
          <Text color="red.500">{error}</Text>
          <Button mt={4} size="sm" onClick={fetchDeviceInfo}>
            {t('retry')}
          </Button>
        </Flex>
      ) : deviceInfo ? (
        <Box>
          {/* 关键指标 */}
          {renderKeyMetrics()}
          
          <Divider my={4} />
          
          {/* 详细指标折叠面板 */}
          <Accordion allowToggle>
            <AccordionItem>
              <h2>
                <AccordionButton>
                  <Box flex="1" textAlign="left">
                    {t('detailed.metrics')}
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel pb={4}>
                {renderMetricsTable()}
              </AccordionPanel>
            </AccordionItem>
          </Accordion>
        </Box>
      ) : null}
    </Box>
  );
};

export default DeviceMonitor;