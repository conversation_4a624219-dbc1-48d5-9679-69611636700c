'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Heading,
  Text,
  Grid,
  GridItem,
  Badge,
  Flex,
  Icon,
  HStack,
  VStack,
  Divider,
  Card,
  CardHeader,
  CardBody,
  useColorMode,
  Button,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Tooltip,
  Tab,
  Tabs,
  TabList,
  TabPanel,
  TabPanels,
  ModalBody,
  ModalFooter,
  SimpleGrid,
  useColorModeValue,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Input,
  FormControl,
  FormLabel,
  Stack
} from '@chakra-ui/react'
import { 
  FaServer, 
  FaNetworkWired, 
  FaDatabase, 
  FaCog, 
  FaInfoCircle, 
  FaMemory, 
  FaHdd, 
  FaMicrochip,
  FaThermometerHalf,
  FaBolt,
  FaNetworkWired as FaPort,
  FaArrowUp,
  FaArrowDown,
  FaSyncAlt
} from 'react-icons/fa'
import { useTranslation } from '@/contexts/LanguageContext'
import NetworkPortStatus from '@/components/RackLayout/NetworkPortStatus'
import { Device, Port } from '@/types/device'
import { fetchDevicePorts } from '@/api/device'
import PortStatus from '@/components/PortStatus'
import DevicePorts from './DevicePorts'

interface DeviceDetailProps {
  device: Device
  onClose: () => void
  onEdit: (device: Device) => void
}

const DeviceDetail: React.FC<DeviceDetailProps> = ({ device, onClose, onEdit }) => {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const [ports, setPorts] = useState<Port[]>([])
  const [loading, setLoading] = useState(false)
  const bgColor = useColorModeValue('gray.50', 'gray.700')
  const borderColor = useColorModeValue('gray.200', 'gray.600')
  
  // 端口自定义选项
  const [portCount, setPortCount] = useState<number>(24)
  const [portPrefix, setPortPrefix] = useState<string>('GE')
  const [startIndex, setStartIndex] = useState<number>(1)
  const [showPortConfig, setShowPortConfig] = useState(false)

  // 加载设备端口
  const loadPorts = async (customOptions?: boolean) => {
    if (device.category === 'network') {
      setLoading(true)
      try {
        // 如果是自定义选项，传递端口配置
        const options = customOptions ? { 
          count: portCount, 
          prefix: portPrefix,
          startIndex: startIndex
        } : undefined;
        
        const data = await fetchDevicePorts(device.id, options);
        setPorts(data);
      } catch (error) {
        console.error('Failed to fetch device ports:', error)
      } finally {
        setLoading(false)
      }
    }
  }

  useEffect(() => {
    // 如果是网络设备，加载端口信息
    loadPorts();
  }, [device])

  // 按照端口号排序
  const sortedPorts = [...ports].sort((a, b) => {
    // 尝试提取数字部分进行排序
    const numA = parseInt(a.name.replace(/\D/g, ''))
    const numB = parseInt(b.name.replace(/\D/g, ''))
    if (!isNaN(numA) && !isNaN(numB)) {
      return numA - numB
    }
    // 如果无法提取数字，按字符串排序
    return a.name.localeCompare(b.name)
  })

  // 处理设备类型显示
  const getDeviceTypeInfo = (type: string) => {
    switch(type) {
      case 'server':
        return { 
          icon: FaServer, 
          label: t('type.server'),
          color: 'blue'
        }
      case 'network':
        return { 
          icon: FaNetworkWired, 
          label: t('type.network'),
          color: 'purple'
        }
      case 'storage':
        return { 
          icon: FaDatabase, 
          label: t('type.storage'),
          color: 'green'
        }
      default:
        return { 
          icon: FaCog, 
          label: t('type.other'),
          color: 'gray'
        }
    }
  }
  
  // 处理设备状态显示
  const getStatusInfo = (status: string) => {
    switch(status) {
      case 'normal':
        return {
          color: 'green',
          label: t('status.normal')
        }
      case 'warning':
        return {
          color: 'yellow',
          label: t('status.warning')
        }
      case 'error':
        return {
          color: 'red',
          label: t('status.error')
        }
      default:
        return {
          color: 'gray',
          label: t('status.unknown')
        }
    }
  }
  
  // 应用自定义端口配置并重新加载端口
  const applyPortCustomization = () => {
    loadPorts(true);
    setShowPortConfig(false);
  }
  
  const typeInfo = getDeviceTypeInfo(device.category)
  const statusInfo = getStatusInfo(device.status)
  
  return (
    <>
      <ModalBody>
        <Tabs colorScheme={typeInfo.color} variant="enclosed" size="md">
          <TabList>
            <Tab>{t('device.basicInfo')}</Tab>
            {device.category === 'network' && <Tab>{t('device.ports')}</Tab>}
          </TabList>
          
          <TabPanels>
            <TabPanel>
              <Grid 
                templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} 
                gap={4}
                mt={2}
                p={4}
                bg={bgColor}
                borderRadius="md"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <GridItem>
                  <VStack align="stretch" spacing={4}>
                    <Box>
                      <Heading size="sm" mb={2} display="flex" alignItems="center">
                        <Icon as={FaInfoCircle} mr={2} />
                        设备基本信息
                      </Heading>
                      <Grid templateColumns="1fr 2fr" gap={2}>
                        <Text fontWeight="medium">{t('device.name')}:</Text>
                        <Text>{device.name}</Text>
                        
                        <Text fontWeight="medium">{t('device.category')}:</Text>
                        <Text>{t(`device.categories.${device.category}`)}</Text>
                        
                        <Text fontWeight="medium">{t('device.model')}:</Text>
                        <Text>{device.model}</Text>
                        
                        <Text fontWeight="medium">{t('device.manufacturer')}:</Text>
                        <Text>{device.manufacturer}</Text>
                        
                        <Text fontWeight="medium">{t('device.serialNumber')}:</Text>
                        <Text>{device.serialNumber}</Text>
                        
                        <Text fontWeight="medium">{t('device.position')}:</Text>
                        <Text>{`U${device.position}`}</Text>
                        
                        <Text fontWeight="medium">{t('device.height')}:</Text>
                        <Text>{`${device.height}U`}</Text>
                        
                        <Text fontWeight="medium">{t('device.status')}:</Text>
                        <Badge
                          colorScheme={
                            device.status === 'active'
                              ? 'green'
                              : device.status === 'inactive'
                              ? 'gray'
                              : device.status === 'maintenance'
                              ? 'yellow'
                              : 'red'
                          }
                        >
                          {t(`device.statuses.${device.status}`)}
                        </Badge>
                      </Grid>
                    </Box>
                    
                    {device.ipAddress && (
                      <Box>
                        <Heading size="sm" mb={2} display="flex" alignItems="center">
                          <Icon as={FaNetworkWired} mr={2} />
                          网络信息
                        </Heading>
                        <Grid templateColumns="1fr 2fr" gap={2}>
                          <Text fontWeight="medium">{t('device.ipAddress')}:</Text>
                          <Text>{device.ipAddress}</Text>
                        </Grid>
                      </Box>
                    )}
                  </VStack>
                </GridItem>
                
                <GridItem>
                  <VStack align="stretch" spacing={4}>
                    {device.assetCode && (
                      <Box>
                        <Heading size="sm" mb={2} display="flex" alignItems="center">
                          <Icon as={FaDatabase} mr={2} />
                          资产信息
                        </Heading>
                        <Grid templateColumns="1fr 2fr" gap={2}>
                          <Text fontWeight="medium">资产编码:</Text>
                          <Text>{device.assetCode}</Text>
                          
                          {device.fixedAssetId && (
                            <>
                              <Text fontWeight="medium">固定资产ID:</Text>
                              <Text>{device.fixedAssetId}</Text>
                            </>
                          )}
                          
                          {device.installDate && (
                            <>
                              <Text fontWeight="medium">安装日期:</Text>
                              <Text>{device.installDate}</Text>
                            </>
                          )}
                          
                          {device.purchaseDate && (
                            <>
                              <Text fontWeight="medium">采购日期:</Text>
                              <Text>{device.purchaseDate}</Text>
                            </>
                          )}
                        </Grid>
                      </Box>
                    )}
                    
                    {(device.owner || device.ownerContact || device.businessName) && (
                      <Box>
                        <Heading size="sm" mb={2} display="flex" alignItems="center">
                          <Icon as={FaServer} mr={2} />
                          责任信息
                        </Heading>
                        <Grid templateColumns="1fr 2fr" gap={2}>
                          {device.owner && (
                            <>
                              <Text fontWeight="medium">负责人:</Text>
                              <Text>{device.owner}</Text>
                            </>
                          )}
                          
                          {device.ownerContact && (
                            <>
                              <Text fontWeight="medium">联系方式:</Text>
                              <Text>{device.ownerContact}</Text>
                            </>
                          )}
                          
                          {device.businessName && (
                            <>
                              <Text fontWeight="medium">业务名称:</Text>
                              <Text>{device.businessName}</Text>
                            </>
                          )}
                        </Grid>
                      </Box>
                    )}
                    
                    {device.description && (
                      <Box>
                        <Heading size="sm" mb={2}>描述</Heading>
                        <Box
                          p={3}
                          borderWidth="1px"
                          borderRadius="md"
                          bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                        >
                          <Text>{device.description}</Text>
                        </Box>
                      </Box>
                    )}
                  </VStack>
                </GridItem>
              </Grid>
            </TabPanel>
            
            {device.category === 'network' && (
              <TabPanel>
                <DevicePorts device={device} />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </ModalBody>
      <ModalFooter>
        <Button colorScheme={typeInfo.color} mr={3} onClick={() => onEdit(device)}>
          {t('common.edit')}
        </Button>
        <Button variant="ghost" onClick={onClose}>
          {t('common.close')}
        </Button>
      </ModalFooter>
    </>
  )
}

export default DeviceDetail 