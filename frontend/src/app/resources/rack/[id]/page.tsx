'use client'

import React, { useState, useEffect } from 'react'
import { 
  Box, 
  Heading, 
  Flex, 
  Text, 
  Grid, 
  GridItem, 
  Badge, 
  useColorMode, 
  Button,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Icon,
  Tooltip,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  useDisclosure,
  HStack,
  VStack,
  Divider,
  Tab,
  Tabs,
  TabList,
  TabPanel,
  TabPanels,
  useToast,
  Spacer,
  Textarea,
  Container,
  Card,
  CardBody,
  SimpleGrid,
  Tag,
  TagLabel,
  Progress,
  chakra,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Skeleton,
  useBreakpointValue,
  Stack
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useTranslation } from '@/contexts/LanguageContext'
import { 
  ArrowLeft, 
  Info, 
  Warning, 
  Plus, 
  Pencil, 
  Trash, 
  Lightning, 
  FloppyDisk, 
  ListPlus, 
  TrashSimple, 
  UserCircle,
  X,
  CaretUp,
  CaretDown,
  Cube,
  Gauge,
  CircleWavyWarning,
  CheckCircle
} from '@phosphor-icons/react'
import { useParams } from 'next/navigation'
import DeviceMonitor from './DeviceMonitor'
import { FaServer, FaNetworkWired, FaDatabase, FaCog, FaCheckCircle, FaExclamationTriangle, FaTimesCircle } from 'react-icons/fa'
import NetworkPortStatus from '@/components/RackLayout/NetworkPortStatus'
import DeviceDetail from './DeviceDetail'

// 设备类型定义
interface Device {
  id: string;
  name: string;
  uStart: number;
  uSize: number;
  uEnd?: number; // 结束U位
  type: 'server' | 'network' | 'storage' | 'power' | 'other';
  status: 'normal' | 'warning' | 'error';
  description?: string;
  serialNumber?: string;
  manufacturer?: string;
  model?: string;
  installDate?: string;
  powerOutlets?: number;
  owner?: string;
  ownerContact?: string;
  assetCode?: string; // 资产编码
  fixedAssetId?: string; // 固定资产ID
  os?: string; // 操作系统
  purchaseDate?: string; // 采购日期
  ipAddress?: string; // 设备IP地址
  managementAddress?: string; // 设备管理地址
  businessName?: string; // 业务名称
  customColor?: string; // 自定义颜色
}

// PDU插座类型定义
interface PowerOutlet {
  id: string;
  position: number;
  side: 'main' | 'ups';
  isUsed: boolean;
  connectedDeviceId?: string;
  connectedDeviceName?: string;
  connectedUPosition?: number;
}

// 模拟设备数据
const generateRackDevices = (rackId: string): Device[] => {
  // 根据机柜ID生成模拟数据
  const devices: Device[] = [];
  
  // 添加一些服务器、网络设备等
  if (rackId === 'A01') {
    devices.push(
      { id: `${rackId}-SRV-1`, name: '应用服务器1', uStart: 10, uSize: 2, type: 'server', status: 'normal', manufacturer: 'Dell', model: 'PowerEdge R740', serialNumber: 'DELL1234567' },
      { id: `${rackId}-SRV-2`, name: '应用服务器2', uStart: 12, uSize: 2, type: 'server', status: 'normal', manufacturer: 'Dell', model: 'PowerEdge R740', serialNumber: 'DELL1234568' },
      { id: `${rackId}-NET-1`, name: '网络交换机', uStart: 40, uSize: 1, type: 'network', status: 'normal', manufacturer: 'Cisco', model: 'Catalyst 9300', serialNumber: 'CISCO87654' }
    );
  } else if (rackId === 'A13') {
    devices.push(
      { id: `${rackId}-STR-1`, name: '存储阵列', uStart: 20, uSize: 4, type: 'storage', status: 'normal', manufacturer: 'NetApp', model: 'FAS8300', serialNumber: 'NTAP987654' },
      { id: `${rackId}-SRV-1`, name: '数据库服务器', uStart: 25, uSize: 3, type: 'server', status: 'warning', manufacturer: 'HP', model: 'ProLiant DL380', serialNumber: '**********' },
      { id: `${rackId}-NET-1`, name: '核心交换机', uStart: 38, uSize: 2, type: 'network', status: 'normal', manufacturer: 'Juniper', model: 'EX4300', serialNumber: 'JUN987654' }
    );
  } else {
    // 为其他机柜添加一些随机设备
    const numDevices = Math.floor(Math.random() * 8) + 2; // 2-10个设备
    const usedPositions = new Set<number>();
    
    for (let i = 0; i < numDevices; i++) {
      const uSize = Math.random() > 0.7 ? (Math.random() > 0.5 ? 3 : 2) : 1;
      let uStart: number;
      do {
        uStart = Math.floor(Math.random() * (42 - uSize)) + 1;
        // 检查是否与已有设备重叠
        let overlaps = false;
        for (let u = uStart; u < uStart + uSize; u++) {
          if (usedPositions.has(u)) {
            overlaps = true;
            break;
          }
        }
        if (!overlaps) {
          for (let u = uStart; u < uStart + uSize; u++) {
            usedPositions.add(u);
          }
          break;
        }
      } while (true);
      
      const deviceTypes = ['server', 'network', 'storage', 'other'] as const;
      const type = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
      const status = Math.random() > 0.85 ? (Math.random() > 0.5 ? 'warning' : 'error') : 'normal';
      
      devices.push({
        id: `${rackId}-DEV-${i+1}`,
        name: type === 'server' ? `服务器${i+1}` : 
              type === 'network' ? `网络设备${i+1}` : 
              type === 'storage' ? `存储设备${i+1}` : `设备${i+1}`,
        uStart,
        uSize,
        type,
        status
      });
    }
  }
  
  return devices;
};

// 生成PDU插座数据
const generatePowerOutlets = (rackId: string): PowerOutlet[] => {
  const outlets: PowerOutlet[] = [];
  const devices = generateRackDevices(rackId);
  
  // 为两侧PDU创建插座（每侧24个）
  for (let i = 1; i <= 24; i++) {
    // 随机决定是否使用以及连接到哪个U位
    const isMainUsed = Math.random() > 0.3;
    const isUpsUsed = Math.random() > 0.4;
    
    let mainConnectedDevice: Device | undefined;
    let upsConnectedDevice: Device | undefined;
    
    // 只有真正使用的插座才会连接到设备
    if (isMainUsed) {
      // 随机选择一个设备
      const randomDeviceIndex = Math.floor(Math.random() * devices.length);
      mainConnectedDevice = devices[randomDeviceIndex];
    }
    
    if (isUpsUsed) {
      // 随机选择一个设备
      const randomDeviceIndex = Math.floor(Math.random() * devices.length);
      upsConnectedDevice = devices[randomDeviceIndex];
    }
    
    // 市电PDU
    outlets.push({
      id: `${rackId}-MAIN-${i}`,
      position: i,
      side: 'main',
      isUsed: isMainUsed,
      connectedUPosition: mainConnectedDevice ? mainConnectedDevice.uStart : undefined,
      connectedDeviceId: mainConnectedDevice ? mainConnectedDevice.id : undefined,
      connectedDeviceName: mainConnectedDevice ? mainConnectedDevice.name : undefined
    });
    
    // UPS PDU
    outlets.push({
      id: `${rackId}-UPS-${i}`,
      position: i,
      side: 'ups',
      isUsed: isUpsUsed,
      connectedUPosition: upsConnectedDevice ? upsConnectedDevice.uStart : undefined,
      connectedDeviceId: upsConnectedDevice ? upsConnectedDevice.id : undefined,
      connectedDeviceName: upsConnectedDevice ? upsConnectedDevice.name : undefined
    });
  }
  
  return outlets;
};

const RackDetailPage = () => {
  const { colorMode } = useColorMode()
  const router = useRouter()
  const { t } = useTranslation()
  const params = useParams()
  const rackId = params?.id as string || 'unknown'
  const toast = useToast()
  
  // 状态管理
  const [devices, setDevices] = useState<Device[]>(generateRackDevices(rackId));
  const [powerOutlets, setPowerOutlets] = useState<PowerOutlet[]>(generatePowerOutlets(rackId));
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [isNewDevice, setIsNewDevice] = useState(false);
  const [selectedU, setSelectedU] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // 模态框控制
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { 
    isOpen: isDeleteOpen, 
    onOpen: onDeleteOpen, 
    onClose: onDeleteClose 
  } = useDisclosure();
  
  // 选项卡状态
  const [tabIndex, setTabIndex] = useState(0);
  
  // 用于模拟加载效果
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  // 响应式布局设置
  const isMobile = useBreakpointValue({ base: true, md: false });
  const mainWidth = useBreakpointValue({ base: '100%', md: '600px', lg: '700px', xl: '800px' });
  const sideWidth = useBreakpointValue({ base: '60px', md: '70px' });
  
  // 获取每个U位的设备（如果有）
  const getDeviceAtU = (uNumber: number): Device | undefined => {
    return devices.find(device => 
      uNumber >= device.uStart && uNumber < device.uStart + device.uSize
    );
  };
  
  // 处理设备类型的样式
  const getDeviceStyle = (device: Device | undefined, isStart: boolean): Record<string, any> => {
    if (!device) return {};
    
    let bg, color, borderColor;
    
    // 如果设备有自定义颜色，优先使用自定义颜色
    if (device.customColor) {
      bg = device.customColor;
      color = colorMode === 'dark' ? 'white' : 'black';
      borderColor = colorMode === 'dark' ? 'gray.600' : 'gray.400';
    } else {
      // 默认颜色配置
      switch (device.type) {
        case 'server':
          bg = colorMode === 'dark' ? 'gray.600' : 'gray.200';
          color = colorMode === 'dark' ? 'white' : 'black';
          borderColor = 'gray.400';
          break;
        case 'network':
          bg = colorMode === 'dark' ? 'blue.700' : 'blue.100';
          color = colorMode === 'dark' ? 'white' : 'blue.800';
          borderColor = 'blue.400';
          break;
        case 'storage':
          bg = colorMode === 'dark' ? 'purple.700' : 'purple.100';
          color = colorMode === 'dark' ? 'white' : 'purple.800';
          borderColor = 'purple.400';
          break;
        case 'power':
          bg = colorMode === 'dark' ? 'red.700' : 'red.100';
          color = colorMode === 'dark' ? 'white' : 'red.800';
          borderColor = 'red.400';
          break;
        default:
          bg = colorMode === 'dark' ? 'gray.700' : 'white';
          color = colorMode === 'dark' ? 'white' : 'black';
          borderColor = 'gray.300';
      }
      
      // 如果设备状态异常，调整样式
      if (device.status === 'warning') {
        bg = colorMode === 'dark' ? 'yellow.700' : 'yellow.100';
        borderColor = 'yellow.400';
      } else if (device.status === 'error') {
        bg = colorMode === 'dark' ? 'red.700' : 'red.100';
        borderColor = 'red.400';
      }
    }
    
    return {
      bg,
      color,
      borderColor,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center', // 居中显示
      px: isStart ? 3 : 0,
      py: 1,
      cursor: 'pointer',
      _hover: {
        opacity: 0.9,
        transform: 'translateY(-1px)',
        transition: 'all 0.2s'
      }
    };
  };
  
  // 打开设备编辑模态框
  const openDeviceModal = (device: Device | null, uNumber: number | null, isNew = false) => {
    // 确保当创建新设备时，我们有一个新的设备对象可以编辑
    if (isNew) {
      const defaultDevice: Device = {
        id: '',
        name: '',
        uStart: uNumber || 1,
        uSize: 1,
        type: 'server',
        status: 'normal'
      };
      setSelectedDevice(defaultDevice);
    } else {
      setSelectedDevice(device);
    }
    
    setSelectedU(uNumber);
    setIsNewDevice(isNew);
    onOpen();
  };
  
  // 处理设备保存
  const handleSaveDevice = () => {
    if (!selectedDevice || (!selectedDevice.name.trim())) {
      toast({
        title: '错误',
        description: '请填写设备名称',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }
    
    try {
      if (isNewDevice && selectedDevice) {
        // 添加新设备，确保 uStart 有值
        const startPosition = selectedU || 1;
        const newDevice: Device = {
          ...selectedDevice,
          id: `${rackId}-DEV-${Date.now()}`,
          uStart: startPosition,
          uEnd: selectedDevice.uEnd || (startPosition + (selectedDevice.uSize - 1))
        };
        setDevices([...devices, newDevice]);
        toast({
          title: '添加成功',
          description: '设备已添加到机柜中',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else if (selectedDevice) {
        // 确保有uEnd值
        const deviceWithUEnd = {
          ...selectedDevice,
          uEnd: selectedDevice.uEnd || (selectedDevice.uStart + (selectedDevice.uSize - 1))
        };
        // 更新现有设备
        setDevices(devices.map(d => d.id === deviceWithUEnd.id ? deviceWithUEnd : d));
        toast({
          title: '更新成功',
          description: '设备信息已更新',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
      onClose();
    } catch (error) {
      toast({
        title: '操作失败',
        description: '无法保存设备信息',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };
  
  // 处理设备删除
  const handleDeleteDevice = () => {
    if (selectedDevice) {
      setDevices(devices.filter(d => d.id !== selectedDevice.id));
      onDeleteClose();
    }
  };
  
  // 检查U位是否可用于新设备
  const isUAvailable = (uNumber: number, size: number = 1): boolean => {
    for (let i = 0; i < size; i++) {
      if (getDeviceAtU(uNumber + i)) {
        return false;
      }
    }
    return true;
  };
  
  // 渲染PDU插座
  const renderPowerOutlets = (side: 'main' | 'ups') => {
    const sideOutlets = powerOutlets.filter(outlet => outlet.side === side);
    const sideName = side === 'main' ? '市电PDU' : 'UPS PDU';
    const colorScheme = side === 'main' ? 'red' : 'orange';
    
    return (
      <Box 
        width={sideWidth}
        borderWidth="1px"
        borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
        borderRadius="md"
        overflow="hidden"
        height="100%"
        display="flex"
        flexDirection="column"
      >
        <Box
          bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
          p={2}
          textAlign="center"
          borderBottomWidth="1px"
          borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
        >
          <Text fontSize="sm" fontWeight="bold">{sideName}</Text>
        </Box>
        
        <Flex 
          flex="1" 
          direction="column" 
          justify="flex-start" 
          p={2}
          pt={10} // 顶部留空，从42U开始
          pb={10} // 底部留空，到23U结束
        >
          {/* 24个插座均匀分布 */}
          <VStack spacing={0} align="stretch">
            {sideOutlets.map((outlet, index) => (
              <Tooltip
                key={outlet.id}
                label={
                  outlet.isUsed 
                    ? `已连接到 ${outlet.connectedUPosition}U: ${outlet.connectedDeviceName}` 
                    : '空闲'
                }
                placement={side === 'main' ? 'left' : 'right'}
                hasArrow
              >
                <Flex 
                  align="center" 
                  justify="space-between"
                  mb={2.5} // 更大的间距来覆盖42U-23U的范围
                  bg={colorMode === 'dark' ? 'gray.800' : 'gray.50'}
                  borderRadius="md"
                  p={1}
                  borderWidth="1px"
                  borderColor={outlet.isUsed 
                    ? (colorMode === 'dark' ? `${colorScheme}.800` : `${colorScheme}.300`)
                    : (colorMode === 'dark' ? 'gray.700' : 'gray.300')}
                  position="relative"
                  _hover={{
                    zIndex: 2,
                    boxShadow: "0 0 0 1px " + (colorMode === 'dark' ? `var(--chakra-colors-${colorScheme}-600)` : `var(--chakra-colors-${colorScheme}-400)`)
                  }}
                >
                  <Text 
                    fontSize="xs" 
                    fontWeight="medium"
                    color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}
                  >
                    {index + 1}
                  </Text>
                  <Box
                    width="32px"
                    height="18px"
                    borderRadius="sm"
                    bg={outlet.isUsed 
                      ? (colorMode === 'dark' ? `${colorScheme}.700` : `${colorScheme}.400`) 
                      : (colorMode === 'dark' ? 'gray.600' : 'gray.200')}
                    cursor="pointer"
                    _hover={{ opacity: 0.8 }}
                    border="1px solid"
                    borderColor={outlet.isUsed 
                      ? (colorMode === 'dark' ? `${colorScheme}.800` : `${colorScheme}.500`)
                      : (colorMode === 'dark' ? 'gray.700' : 'gray.300')}
                  />
                </Flex>
              </Tooltip>
            ))}
          </VStack>
        </Flex>
      </Box>
    );
  };
  
  // 添加机柜ID格式化函数
  const formatRackId = (id: string): string => {
    // 将形如 A01 的ID格式化为 A-01，确保字母为大写
    if (/^[a-zA-Z]\d+$/.test(id)) {
      const letter = id.charAt(0).toUpperCase();
      const number = id.substring(1).padStart(2, '0');
      return `${letter}-${number}`;
    }
    // 如果已经是正确格式或其他格式，返回原始ID的大写形式
    return id.toUpperCase();
  }
  
  // 设备详情模态框
  const { isOpen: isDeviceDetailOpen, onOpen: onOpenDeviceDetail, onClose: onCloseDeviceDetail } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onOpenEditDevice } = useDisclosure();

  // 数据统计
  const statusCount = {
    normal: devices.filter(d => d.status === 'normal').length,
    warning: devices.filter(d => d.status === 'warning').length,
    error: devices.filter(d => d.status === 'error').length,
    total: devices.length
  };
  
  return (
    <Box p={4} bg={colorMode === 'dark' ? 'gray.900' : 'gray.50'} minH="100vh">
      {/* 全局加载效果 */}
      {isLoading && (
        <Box position="fixed" top={0} left={0} right={0} bottom={0} bg={colorMode === 'dark' ? 'blackAlpha.700' : 'whiteAlpha.700'} zIndex={9999} display="flex" alignItems="center" justifyContent="center" backdropFilter="blur(5px)">
          <VStack spacing={6}>
            <Heading size="md" fontFamily="monospace">载入资产数据...</Heading>
            <Progress 
              hasStripe 
              isAnimated 
              value={65} 
              width="300px" 
              colorScheme="cyan" 
              borderRadius="full" 
              height="12px"
              bg={colorMode === 'dark' ? 'whiteAlpha.100' : 'blackAlpha.100'}
            />
          </VStack>
        </Box>
      )}

      {/* 顶部导航 */}
      <Card 
        mb={6} 
        borderRadius="xl" 
        overflow="hidden" 
        boxShadow="lg"
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
        borderWidth="1px"
        borderColor={colorMode === 'dark' ? 'blue.800' : 'blue.100'}
      >
        <CardBody p={0}>
          <Grid templateColumns="1fr auto" gap={0}>
            <Box p={5}>
              <Breadcrumb mb={2} fontSize="sm" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>
                <BreadcrumbItem>
                  <BreadcrumbLink onClick={() => router.push('/resources')}>
                    {t('resources')}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbItem isCurrentPage>
                  <BreadcrumbLink>
                    <Tag size="md" variant="subtle" colorScheme="blue" ml={1} borderRadius="full">
                      <Icon as={Cube} weight="fill" mr={1} />
                      <TagLabel>{t('rack')} {formatRackId(rackId)}</TagLabel>
                    </Tag>
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </Breadcrumb>

              <Flex align="center" mb={2} wrap="wrap">
                <Heading 
                  size="lg" 
                  fontWeight="bold" 
                  bgGradient={colorMode === 'dark' ? 
                    'linear(to-r, cyan.400, blue.500, purple.600)' : 
                    'linear(to-r, cyan.600, blue.700, purple.700)'} 
                  bgClip="text"
                  letterSpacing="tight"
                >
                  {formatRackId(rackId)}机柜资产
                </Heading>
                <Badge 
                  colorScheme="green" 
                  ml={3} 
                  px={2} 
                  py={1} 
                  borderRadius="md" 
                  textTransform="none"
                  fontSize="xs"
                  display="flex"
                  alignItems="center"
                >
                  <Icon as={CheckCircle} weight="fill" mr={1} />
                  {t('online')}
                </Badge>
              </Flex>

              <SimpleGrid columns={{ base: 1, sm: 4 }} spacing={4} mt={4}>
                <Stat bg={colorMode === 'dark' ? 'whiteAlpha.50' : 'blackAlpha.50'} p={3} borderRadius="lg">
                  <StatLabel fontSize="xs" color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}>总设备</StatLabel>
                  <StatNumber fontSize="2xl">{statusCount.total}</StatNumber>
                </Stat>
                <Stat bg={colorMode === 'dark' ? 'green.900' : 'green.50'} p={3} borderRadius="lg">
                  <StatLabel fontSize="xs" color={colorMode === 'dark' ? 'green.200' : 'green.700'}>正常</StatLabel>
                  <StatNumber fontSize="2xl" color={colorMode === 'dark' ? 'green.200' : 'green.700'}>{statusCount.normal}</StatNumber>
                </Stat>
                <Stat bg={colorMode === 'dark' ? 'yellow.900' : 'yellow.50'} p={3} borderRadius="lg">
                  <StatLabel fontSize="xs" color={colorMode === 'dark' ? 'yellow.200' : 'yellow.700'}>警告</StatLabel>
                  <StatNumber fontSize="2xl" color={colorMode === 'dark' ? 'yellow.200' : 'yellow.700'}>{statusCount.warning}</StatNumber>
                </Stat>
                <Stat bg={colorMode === 'dark' ? 'red.900' : 'red.50'} p={3} borderRadius="lg">
                  <StatLabel fontSize="xs" color={colorMode === 'dark' ? 'red.200' : 'red.700'}>错误</StatLabel>
                  <StatNumber fontSize="2xl" color={colorMode === 'dark' ? 'red.200' : 'red.700'}>{statusCount.error}</StatNumber>
                </Stat>
              </SimpleGrid>
            </Box>

            <Flex 
              direction="column" 
              justify="space-between" 
              p={4} 
              bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'} 
              borderLeftWidth="1px" 
              borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
            >
              <Button
                leftIcon={<Icon as={Plus} weight="bold" />}
                colorScheme="blue"
                size="md"
                mb={2}
                onClick={() => openDeviceModal(
                  { id: '', name: '', uStart: 1, uSize: 1, type: 'server', status: 'normal' }, 
                  null, 
                  true
                )}
                w="180px"
                borderRadius="lg"
                variant={colorMode === 'dark' ? 'solid' : 'outline'}
                bgGradient={colorMode === 'dark' ? "linear(to-r, blue.500, purple.500)" : undefined}
                _hover={{
                  bgGradient: colorMode === 'dark' ? "linear(to-r, blue.600, purple.600)" : undefined,
                  transform: "translateY(-2px)",
                  shadow: "lg"
                }}
                transition="all 0.2s"
              >
                {t('add.device')}
              </Button>
              <Button
                leftIcon={<Icon as={ArrowLeft} weight="bold" />}
                onClick={() => router.push('/resources')}
                variant="ghost"
                size="md"
                w="180px"
                borderRadius="lg"
              >
                {t('back')}
              </Button>
            </Flex>
          </Grid>
        </CardBody>
      </Card>
      
      {/* 标签页 */}
      <Card 
        borderRadius="xl" 
        boxShadow="lg" 
        overflow="hidden"
        borderWidth="1px"
        borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      >
        <CardBody p={0}>
          <Tabs 
            variant="soft-rounded" 
            colorScheme="blue" 
            index={tabIndex} 
            onChange={setTabIndex}
            p={4}
            pb={0}
          >
            <TabList mx={2} mb={3}>
              <Tab 
                _selected={{ 
                  bg: colorMode === 'dark' ? 'blue.700' : 'blue.100',
                  color: colorMode === 'dark' ? 'white' : 'blue.800',
                  fontWeight: 'semibold'
                }}
                px={4}
                py={2}
                borderRadius="full"
                fontSize="sm"
                display="flex"
                alignItems="center"
                gap={2}
              >
                <Icon as={Cube} weight="fill" />
                {t('rack.layout')}
              </Tab>
              <Tab 
                _selected={{ 
                  bg: colorMode === 'dark' ? 'purple.700' : 'purple.100',
                  color: colorMode === 'dark' ? 'white' : 'purple.800',
                  fontWeight: 'semibold'
                }}
                px={4}
                py={2}
                borderRadius="full"
                fontSize="sm"
                display="flex"
                alignItems="center"
                gap={2}
              >
                <Icon as={Gauge} weight="fill" />
                {t('device.monitoring')}
              </Tab>
            </TabList>
            
            <TabPanels>
              {/* 机柜布局视图 */}
              <TabPanel p={0} pt={2}>
                <Flex justify="center" pb={4}>
                  <HStack spacing={1} align="stretch">
                    {/* 左侧市电PDU */}
                    {renderPowerOutlets('main')}
                    
                    {/* 中间机柜 */}
                    <Box 
                      borderWidth="1px" 
                      borderRadius="lg" 
                      overflow="hidden" 
                      width={mainWidth}
                      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.300'}
                      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                      boxShadow="sm"
                    >
                      <Flex 
                        justify="space-between" 
                        align="center" 
                        bg={colorMode === 'dark' ? 'gray.700' : 'gray.100'} 
                        py={2} 
                        px={4}
                        borderBottomWidth="1px"
                        borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                      >
                        <HStack>
                          <Text fontWeight="bold">{formatRackId(rackId)}</Text>
                          <Badge ml={2} colorScheme="blue" borderRadius="full" px={2}>42U</Badge>
                        </HStack>
                        <Flex>
                          <Badge colorScheme="blue" variant="outline" borderRadius="full" mr={2} px={2}>{t('cold.aisle')}</Badge>
                        </Flex>
                      </Flex>
                      
                      <Box p={2}>
                        <Grid templateColumns="50px 1fr" gap={1}>
                          {/* 从上到下渲染42U的机柜 */}
                          {Array.from({ length: 42 }).map((_, index) => {
                            const uNumber = 42 - index; // 从上到下递减，顶部是42U
                            const device = getDeviceAtU(uNumber);
                            const isDeviceStart = device && device.uStart === uNumber;
                            const isAvailable = isUAvailable(uNumber);
                            
                            // 判断是否为设备的最后一个U位，用于边框样式
                            const isDeviceEnd = device && (device.uStart + device.uSize - 1) === uNumber;
                            
                            // 所在设备的U位位置标识
                            let uPositionStyle = {};
                            if (device && !isDeviceStart && !isDeviceEnd) {
                              // 中间U位无上下边框
                              uPositionStyle = {
                                borderTop: "0px",
                                borderBottom: "0px",
                                marginTop: "-1px",
                                marginBottom: "-1px"
                              };
                            } else if (device && isDeviceEnd && !isDeviceStart) {
                              // 结束U位无上边框
                              uPositionStyle = {
                                borderTop: "0px",
                                marginTop: "-1px"
                              };
                            } else if (device && isDeviceStart && !isDeviceEnd) {
                              // 开始U位无下边框
                              uPositionStyle = {
                                borderBottom: "0px",
                                marginBottom: "-1px"
                              };
                            }
                            
                            return (
                              <React.Fragment key={uNumber}>
                                <GridItem 
                                  borderWidth="1px" 
                                  textAlign="center"
                                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.300'}
                                  bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                                  py={1}
                                  fontFamily="monospace"
                                  fontSize="sm"
                                  color={colorMode === 'dark' ? 'gray.400' : 'gray.600'}
                                >
                                  {uNumber}U
                                </GridItem>
                                <GridItem 
                                  borderWidth="1px"
                                  borderColor={device ? getDeviceStyle(device, true).borderColor : (colorMode === 'dark' ? 'gray.700' : 'gray.300')}
                                  {...getDeviceStyle(device, isDeviceStart || false)}
                                  onClick={() => {
                                    if (device) {
                                      openDeviceModal(device, null, false);
                                    } else if (isAvailable) {
                                      openDeviceModal(
                                        { id: '', name: '', uStart: uNumber, uSize: 1, type: 'server', status: 'normal' },
                                        uNumber,
                                        true
                                      );
                                    }
                                  }}
                                  {...uPositionStyle}
                                  transition="all 0.2s"
                                  _hover={{
                                    transform: "translateY(-1px)",
                                    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                                    opacity: 0.9
                                  }}
                                  borderRadius={isDeviceStart || isDeviceEnd ? "md" : "none"}
                                >
                                  {isDeviceStart && device ? (
                                    <Flex 
                                      align="center" 
                                      justify="space-between"
                                      width="100%"
                                      height="100%"
                                      px={2}
                                    >
                                      <Flex 
                                        flex="1" 
                                        align="center" 
                                        justify="center"
                                      >
                                        <Text 
                                          textAlign="center" 
                                          fontWeight="medium"
                                          fontSize="sm"
                                          isTruncated
                                          maxW="70%"
                                        >
                                          {device.name}
                                        </Text>
                                        {device.uSize > 1 && (
                                          <Badge 
                                            ml={2} 
                                            colorScheme="blue" 
                                            fontSize="xs"
                                            borderRadius="full"
                                            px={1.5}
                                          >
                                            {device.uSize}U
                                          </Badge>
                                        )}
                                      </Flex>
                                      <HStack spacing={1}>
                                        {device.status !== 'normal' && (
                                          <Tooltip 
                                            label={device.status === 'warning' ? t('performance.anomaly') : t('device.fault')}
                                            hasArrow
                                          >
                                            <Icon 
                                              as={device.status === 'warning' ? CircleWavyWarning : Warning} 
                                              color={device.status === 'warning' ? 'yellow.500' : 'red.500'} 
                                              fontSize="18px"
                                              weight="fill"
                                            />
                                          </Tooltip>
                                        )}
                                        <IconButton
                                          aria-label={t('edit.device')}
                                          icon={<Icon as={Pencil} weight="fill" />}
                                          size="xs"
                                          variant="ghost"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            openDeviceModal(device, null, false);
                                          }}
                                          borderRadius="full"
                                        />
                                        <IconButton
                                          aria-label={t('delete')}
                                          icon={<Icon as={Trash} weight="fill" />}
                                          size="xs"
                                          variant="ghost"
                                          colorScheme="red"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setSelectedDevice(device);
                                            onDeleteOpen();
                                          }}
                                          borderRadius="full"
                                        />
                                      </HStack>
                                    </Flex>
                                  ) : device ? (
                                    // 设备非起始位置显示一个竖线表示连续性
                                    <Box 
                                      height="100%" 
                                      width="4px" 
                                      bg={colorMode === 'dark' ? 'gray.500' : 'gray.400'} 
                                      mx="auto"
                                      display={device.uSize > 1 ? "block" : "none"}
                                    />
                                  ) : (
                                    <Text 
                                      fontSize="xs" 
                                      color={colorMode === 'dark' ? 'gray.400' : 'gray.500'}
                                      textAlign="center"
                                      fontStyle="italic"
                                    >
                                      {t('idle')}
                                    </Text>
                                  )}
                                </GridItem>
                              </React.Fragment>
                            );
                          })}
                        </Grid>
                      </Box>
                    </Box>
                    
                    {/* 右侧UPS PDU */}
                    {renderPowerOutlets('ups')}
                  </HStack>
                </Flex>
              </TabPanel>
              
              {/* 设备监控视图 */}
              <TabPanel p={3}>
                <Flex direction="column" maxW="900px" mx="auto">
                  {devices.length > 0 ? (
                    <>
                      <SimpleGrid columns={{ base: 1, xl: 2 }} spacing={4}>
                        {devices.map(device => (
                          <Card 
                            key={device.id} 
                            borderRadius="lg" 
                            overflow="hidden" 
                            borderWidth="1px"
                            borderColor={
                              device.status === 'warning' 
                                ? (colorMode === 'dark' ? 'yellow.600' : 'yellow.300') 
                                : device.status === 'error'
                                ? (colorMode === 'dark' ? 'red.600' : 'red.300')
                                : (colorMode === 'dark' ? 'gray.700' : 'gray.200')
                            }
                            boxShadow={
                              device.status === 'warning' 
                                ? '0 0 0 1px var(--chakra-colors-yellow-500)' 
                                : device.status === 'error'
                                ? '0 0 0 1px var(--chakra-colors-red-500)'
                                : 'md'
                            }
                            bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                            transition="all 0.3s"
                            _hover={{
                              transform: "translateY(-4px)",
                              boxShadow: "xl"
                            }}
                          >
                            <DeviceMonitor
                              deviceId={device.id}
                              rackId={rackId}
                            />
                          </Card>
                        ))}
                      </SimpleGrid>
                    </>
                  ) : (
                    <Box 
                      p={8} 
                      borderWidth="1px" 
                      borderRadius="xl" 
                      textAlign="center"
                      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
                      boxShadow="md"
                    >
                      <VStack spacing={6}>
                        <Icon as={Info} boxSize={12} color={colorMode === 'dark' ? 'blue.400' : 'blue.500'} />
                        <Text fontSize="lg" mb={4}>
                          {t('no.devices')}
                        </Text>
                        <Button 
                          colorScheme="blue" 
                          onClick={() => openDeviceModal(
                            { id: '', name: '', uStart: 1, uSize: 1, type: 'server', status: 'normal' }, 
                            null, 
                            true
                          )}
                          size="lg"
                          borderRadius="lg"
                          leftIcon={<Icon as={Plus} weight="bold" />}
                          px={6}
                        >
                          {t('add.device')}
                        </Button>
                      </VStack>
                    </Box>
                  )}
                </Flex>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </CardBody>
      </Card>
      
      {/* 设备编辑模态框 */}
      <Modal 
        isOpen={isOpen} 
        onClose={onClose} 
        motionPreset="slideInBottom"
        size="xl"
        closeOnOverlayClick={true}
        blockScrollOnMount={false}
        portalProps={{ appendToParentPortal: false }}
        scrollBehavior="inside"
      >
        <ModalOverlay 
          bg="blackAlpha.300"
          backdropFilter="blur(8px)"
        />
        <ModalContent 
          bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          boxShadow="2xl"
          borderRadius="xl"
          mx="auto"
          overflow="auto"
          border="1px solid"
          borderColor={colorMode === 'dark' ? 'blue.800' : 'blue.100'}
          maxW="600px"
          position="relative"
          top="40px"
          maxH="calc(100vh - 80px)"
          transition="all 0.3s"
          transform="translateY(0)"
        >
          <Box
            bgGradient={colorMode === 'dark' ? 
              'linear(to-r, blue.900, purple.900)' : 
              'linear(to-r, blue.50, purple.50)'}
            py={3}
            px={5}
            borderBottom="1px solid"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            position="relative"
          >
            <Flex justify="space-between" align="center">
              <Heading 
                size="md" 
                bgGradient={colorMode === 'dark' ? 
                  'linear(to-r, cyan.400, blue.300, purple.400)' : 
                  'linear(to-r, cyan.600, blue.600, purple.600)'}
                bgClip="text"
                fontWeight="bold"
              >
                {isNewDevice ? '添加设备' : '编辑设备信息'}
              </Heading>
              <Icon
                as={isNewDevice ? Plus : Pencil}
                boxSize={5}
                color={colorMode === 'dark' ? 'blue.200' : 'blue.600'}
                weight="duotone"
              />
            </Flex>
            
            {/* 头部装饰元素 */}
            <Box 
              position="absolute" 
              top="0" 
              right="0" 
              h="8px" 
              w="60%" 
              bg={colorMode === 'dark' ? 'blue.500' : 'blue.400'} 
              opacity="0.5"
              borderBottomLeftRadius="md"
            />
            <Box 
              position="absolute" 
              top="0" 
              left="0" 
              h="3px" 
              w="30%" 
              bg={colorMode === 'dark' ? 'purple.500' : 'purple.400'} 
            />
          </Box>
          <ModalCloseButton 
            top="3" 
            right="3" 
            size="md"
            color={colorMode === 'dark' ? 'gray.200' : 'gray.600'} 
            borderRadius="full"
            _hover={{
              bg: colorMode === 'dark' ? 'whiteAlpha.200' : 'blackAlpha.100'
            }}
          />

          <ModalBody px={5} py={4} overflowY="auto">
            <Box
              mb={4}
              p={3}
              bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
              borderRadius="lg"
              borderLeft="4px solid"
              borderColor={colorMode === 'dark' ? 'blue.400' : 'blue.500'}
              position="relative"
              overflow="hidden"
            >
              <Flex align="center">
                <Icon as={Info} mr={2} weight="fill" boxSize="16px" color={colorMode === 'dark' ? 'blue.300' : 'blue.600'} />
                <Text fontWeight="bold" fontSize="md">基本信息</Text>
              </Flex>
              
              {/* 背景装饰 */}
              <Box 
                position="absolute" 
                top="-10px" 
                right="-10px" 
                height="40px" 
                width="40px" 
                bg={colorMode === 'dark' ? 'blue.600' : 'blue.200'} 
                opacity="0.3" 
                borderRadius="full" 
              />
            </Box>

            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel fontWeight="semibold" color={colorMode === 'dark' ? 'blue.200' : 'blue.700'}>{t('device.name')}</FormLabel>
                <Input 
                  value={selectedDevice?.name || ''}
                  onChange={(e) => setSelectedDevice(prev => prev ? {...prev, name: e.target.value} : null)}
                  placeholder="输入设备名称，如 Web服务器01"
                  autoFocus
                  size="md"
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                  _hover={{ borderColor: 'blue.500', borderWidth: "1px" }}
                  _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px var(--chakra-colors-blue-500)', borderWidth: "1px" }}
                  bg={colorMode === 'dark' ? 'gray.700' : 'white'}
                  borderRadius="md"
                />
              </FormControl>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl isRequired>
                  <FormLabel fontWeight="semibold" color={colorMode === 'dark' ? 'blue.200' : 'blue.700'}>{t('device.type')}</FormLabel>
                  <Select 
                    value={selectedDevice?.type || 'server'}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, type: e.target.value as Device['type']} : null
                    )}
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="1px"
                    size="md"
                    _hover={{ borderColor: 'blue.500' }}
                    _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px var(--chakra-colors-blue-500)' }}
                    icon={<Icon as={selectedDevice?.type === 'server' ? FaServer : selectedDevice?.type === 'network' ? FaNetworkWired : selectedDevice?.type === 'storage' ? FaDatabase : FaCog} />}
                    bg={colorMode === 'dark' ? 'gray.700' : 'white'}
                    borderRadius="md"
                    color={colorMode === 'dark' ? 'white' : 'black'}
                  >
                    <option value="server">{t('type.server')}</option>
                    <option value="network">{t('type.network')}</option>
                    <option value="storage">{t('type.storage')}</option>
                    <option value="other">{t('type.other')}</option>
                  </Select>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel fontWeight="semibold" color={colorMode === 'dark' ? 'blue.200' : 'blue.700'}>{t('device.status')}</FormLabel>
                  <Flex gap={1} h="40px">
                    <Button 
                      onClick={() => setSelectedDevice(prev => prev ? {...prev, status: 'normal'} : null)}
                      variant={selectedDevice?.status === 'normal' ? 'solid' : 'outline'}
                      px={0}
                      flex="1"
                      size="md"
                      colorScheme="green"
                      leftIcon={<Icon as={CheckCircle} weight={selectedDevice?.status === 'normal' ? 'fill' : 'regular'} boxSize="14px" />}
                      bg={selectedDevice?.status === 'normal' ? 'green.500' : 'transparent'}
                      color={selectedDevice?.status === 'normal' ? 'white' : 'green.500'}
                      _hover={{ bg: selectedDevice?.status === 'normal' ? 'green.600' : 'green.50' }}
                      borderRadius="md"
                    >
                      正常
                    </Button>
                    <Button 
                      onClick={() => setSelectedDevice(prev => prev ? {...prev, status: 'warning'} : null)}
                      variant={selectedDevice?.status === 'warning' ? 'solid' : 'outline'}
                      px={0}
                      flex="1"
                      size="md"
                      colorScheme="yellow"
                      leftIcon={<Icon as={CircleWavyWarning} weight={selectedDevice?.status === 'warning' ? 'fill' : 'regular'} boxSize="14px" />}
                      bg={selectedDevice?.status === 'warning' ? 'yellow.500' : 'transparent'}
                      color={selectedDevice?.status === 'warning' ? 'white' : 'yellow.500'}
                      _hover={{ bg: selectedDevice?.status === 'warning' ? 'yellow.600' : 'yellow.50' }}
                      borderRadius="md"
                    >
                      警告
                    </Button>
                    <Button 
                      onClick={() => setSelectedDevice(prev => prev ? {...prev, status: 'error'} : null)}
                      variant={selectedDevice?.status === 'error' ? 'solid' : 'outline'}
                      px={0}
                      flex="1"
                      size="md"
                      colorScheme="red"
                      leftIcon={<Icon as={Warning} weight={selectedDevice?.status === 'error' ? 'fill' : 'regular'} boxSize="14px" />}
                      bg={selectedDevice?.status === 'error' ? 'red.500' : 'transparent'}
                      color={selectedDevice?.status === 'error' ? 'white' : 'red.500'}
                      _hover={{ bg: selectedDevice?.status === 'error' ? 'red.600' : 'red.50' }}
                      borderRadius="md"
                    >
                      错误
                    </Button>
                  </Flex>
                </FormControl>
              </Grid>

              {/* 添加网络设备端口状态 - 仅当设备类型为网络设备时显示 */}
              {selectedDevice?.type === 'network' && !isNewDevice && (
                <Box mt={4}>
                  <Box
                    p={3}
                    bg={colorMode === 'dark' ? 'blue.900' : 'blue.50'}
                    borderRadius="md"
                    mb={4}
                  >
                    <Flex align="center">
                      <Icon as={FaNetworkWired} mr={2} boxSize="16px" color={colorMode === 'dark' ? 'blue.300' : 'blue.600'} />
                      <Text fontWeight="bold" fontSize="md">网络端口状态</Text>
                    </Flex>
                  </Box>
                  <NetworkPortStatus 
                    device={{
                      id: selectedDevice.id,
                      name: selectedDevice.name,
                      type: selectedDevice.type,
                      status: selectedDevice.status,
                      // 模拟端口数据
                      ports: [
                        { id: '1', name: 'Gi1/0/1', type: 'ethernet', status: 'up', speed: '1Gbps', duplex: 'full', vlan: 10, traffic: { inbound: 156, outbound: 89, unit: 'Mbps' } },
                        { id: '2', name: 'Gi1/0/2', type: 'ethernet', status: 'up', speed: '1Gbps', duplex: 'full', vlan: 10, traffic: { inbound: 230, outbound: 120, unit: 'Mbps' } },
                        { id: '3', name: 'Gi1/0/3', type: 'ethernet', status: 'down' },
                        { id: '4', name: 'Gi1/0/4', type: 'ethernet', status: 'down' },
                        { id: '5', name: 'Gi1/0/5', type: 'ethernet', status: 'down' },
                        { id: '6', name: 'Gi1/0/6', type: 'ethernet', status: 'up', speed: '1Gbps', duplex: 'full', vlan: 20, traffic: { inbound: 78, outbound: 45, unit: 'Mbps' } },
                        { id: '7', name: 'Gi1/0/7', type: 'ethernet', status: 'error', errors: 125 },
                        { id: '8', name: 'Gi1/0/8', type: 'ethernet', status: 'warning', traffic: { inbound: 950, outbound: 820, unit: 'Mbps' } },
                        { id: '9', name: 'Te1/1/1', type: 'sfpplus', status: 'up', speed: '10Gbps', duplex: 'full', traffic: { inbound: 2500, outbound: 1800, unit: 'Mbps' } },
                        { id: '10', name: 'Te1/1/2', type: 'sfpplus', status: 'up', speed: '10Gbps', duplex: 'full', traffic: { inbound: 3200, outbound: 2700, unit: 'Mbps' } },
                        { id: '11', name: 'Mgmt0', type: 'management', status: 'up', speed: '1Gbps', ipAddress: '***********' },
                        { id: '12', name: 'Console', type: 'console', status: 'up' }
                      ]
                    }} 
                  />
                </Box>
              )}

              <Box
                p={3}
                bg={colorMode === 'dark' ? 'blue.900' : 'blue.50'}
                borderRadius="lg"
                mt={4}
                position="relative"
                overflow="hidden"
              >
                <Flex align="center" zIndex={1} position="relative">
                  <Icon as={Cube} weight="fill" mr={2} boxSize="16px" color={colorMode === 'dark' ? 'blue.300' : 'blue.600'} />
                  <Text fontWeight="semibold" fontSize="md">设备外观设置</Text>
                </Flex>
                
                {/* 背景装饰 */}
                <Box 
                  position="absolute" 
                  top="-20px" 
                  right="-20px" 
                  height="60px" 
                  width="60px" 
                  bg={colorMode === 'dark' ? 'blue.800' : 'blue.100'} 
                  opacity="0.5" 
                  borderRadius="full" 
                />
              </Box>

              <FormControl>
                <FormLabel fontWeight="semibold" color={colorMode === 'dark' ? 'blue.200' : 'blue.700'}>机柜视图颜色</FormLabel>
                <Grid templateColumns="repeat(6, 1fr)" gap={2}>
                  {['gray.300', 'blue.200', 'green.200', 'purple.200', 'red.200', 'yellow.200', 
                    'teal.200', 'cyan.200', 'orange.200', 'pink.200', 'gray.500', 'blue.500'].map((color) => (
                    <Box
                      key={color}
                      width="100%"
                      height="24px"
                      bg={color}
                      borderRadius="md"
                      cursor="pointer"
                      borderWidth="2px"
                      borderColor={selectedDevice?.customColor === color ? 'blue.500' : 'transparent'}
                      _hover={{ borderColor: 'gray.400', transform: 'scale(1.05)' }}
                      transition="all 0.2s"
                      onClick={() => setSelectedDevice(prev => prev ? {...prev, customColor: color} : null)}
                    />
                  ))}
                </Grid>
                <Flex justify="space-between" mt={2}>
                  <Button
                    size="xs"
                    variant="outline"
                    colorScheme="red"
                    onClick={() => setSelectedDevice(prev => prev ? {...prev, customColor: undefined} : null)}
                    leftIcon={<Icon as={X} weight="bold" />}
                    borderRadius="md"
                  >
                    清除颜色
                  </Button>
                  <Text fontSize="sm" color="gray.500">
                    自定义颜色将覆盖设备类型和状态的默认颜色
                  </Text>
                </Flex>
              </FormControl>

              <Box
                p={3}
                bg={colorMode === 'dark' ? 'purple.900' : 'purple.50'}
                borderRadius="lg"
                mt={4}
                position="relative"
                overflow="hidden"
              >
                <Flex align="center" zIndex={1} position="relative">
                  <Icon as={CaretUp} weight="fill" mr={2} boxSize="16px" color={colorMode === 'dark' ? 'purple.300' : 'purple.600'} />
                  <Text fontWeight="semibold" fontSize="md">设备位置（机柜U位）</Text>
                </Flex>
                
                {/* 背景装饰 */}
                <Box 
                  position="absolute" 
                  top="-10px" 
                  left="-10px" 
                  height="40px" 
                  width="40px" 
                  bg={colorMode === 'dark' ? 'purple.800' : 'purple.100'} 
                  opacity="0.5" 
                  borderRadius="full" 
                />
              </Box>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl isRequired>
                  <FormLabel fontWeight="semibold" color={colorMode === 'dark' ? 'purple.200' : 'purple.700'}>起始U位</FormLabel>
                  <Select
                    value={selectedDevice?.uStart === undefined ? '' : selectedDevice.uStart}
                    onChange={(e) => {
                      const startValue = parseInt(e.target.value);
                      setSelectedDevice(prev => {
                        if (!prev) return null;
                        // 确保结束位置至少与起始位置相同
                        const currentEnd = prev.uEnd || startValue;
                        const endValue = currentEnd < startValue ? startValue : currentEnd;
                        // 更新uSize
                        const newSize = endValue - startValue + 1;
                        return {
                          ...prev,
                          uStart: startValue,
                          uSize: newSize,
                          uEnd: endValue
                        };
                      });
                    }}
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="1px"
                    _hover={{ borderColor: 'purple.500' }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 1px var(--chakra-colors-purple-500)' }}
                    icon={<Icon as={CaretUp} />}
                    bg={colorMode === 'dark' ? 'gray.700' : 'white'}
                    borderRadius="md"
                  >
                    <option value="">选择起始U位</option>
                    {Array.from({ length: 42 }, (_, i) => (
                      <option key={`start-${i + 1}`} value={i + 1}>{i + 1}U</option>
                    ))}
                  </Select>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel fontWeight="semibold" color={colorMode === 'dark' ? 'purple.200' : 'purple.700'}>结束U位</FormLabel>
                  <Select
                    value={selectedDevice?.uEnd === undefined ? '' : selectedDevice.uEnd}
                    onChange={(e) => {
                      const endValue = parseInt(e.target.value);
                      setSelectedDevice(prev => {
                        if (!prev) return null;
                        const startValue = prev.uStart || 1;
                        // 确保结束位置至少与起始位置相同
                        const validEnd = Math.max(endValue, startValue);
                        // 更新uSize
                        return {
                          ...prev,
                          uEnd: validEnd,
                          uSize: validEnd - startValue + 1
                        };
                      });
                    }}
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="1px"
                    _hover={{ borderColor: 'purple.500' }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 1px var(--chakra-colors-purple-500)' }}
                    icon={<Icon as={CaretDown} />}
                    isDisabled={!selectedDevice?.uStart}
                    bg={colorMode === 'dark' ? 'gray.700' : 'white'}
                    borderRadius="md"
                  >
                    <option value="">选择结束U位</option>
                    {Array.from({ length: 42 }, (_, i) => (
                      <option key={`end-${i + 1}`} value={i + 1}>{i + 1}U</option>
                    )).filter(option => {
                      const optionValue = parseInt(option.props.value);
                      return !selectedDevice?.uStart || optionValue >= selectedDevice.uStart;
                    })}
                  </Select>
                </FormControl>
              </Grid>

              <Box
                p={3}
                bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                borderRadius="md"
                borderLeft="4px solid"
                borderColor={colorMode === 'dark' ? 'purple.400' : 'purple.500'}
                mt={2}
              >
                <Flex align="center">
                  <Icon as={ListPlus} mr={2} boxSize="16px" color={colorMode === 'dark' ? 'purple.300' : 'purple.600'} />
                  <Text fontWeight="bold" fontSize="md">设备规格与资产信息</Text>
                </Flex>
              </Box>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl>
                  <FormLabel fontWeight="semibold">资产编码</FormLabel>
                  <Input 
                    value={selectedDevice?.assetCode || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, assetCode: e.target.value} : null
                    )}
                    placeholder="输入资产编码"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="semibold">固定资产ID</FormLabel>
                  <Input 
                    value={selectedDevice?.fixedAssetId || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, fixedAssetId: e.target.value} : null
                    )}
                    placeholder="输入固定资产ID"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>
              </Grid>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl>
                  <FormLabel fontWeight="semibold">{t('manufacturer')}</FormLabel>
                  <Input 
                    value={selectedDevice?.manufacturer || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, manufacturer: e.target.value} : null
                    )}
                    placeholder="输入制造商"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="semibold">{t('model')}</FormLabel>
                  <Input 
                    value={selectedDevice?.model || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, model: e.target.value} : null
                    )}
                    placeholder="输入型号"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>
              </Grid>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl>
                  <FormLabel fontWeight="semibold">{t('serial.number')}</FormLabel>
                  <Input 
                    value={selectedDevice?.serialNumber || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, serialNumber: e.target.value} : null
                    )}
                    placeholder="输入序列号"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="semibold">操作系统</FormLabel>
                  <Input 
                    value={selectedDevice?.os || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, os: e.target.value} : null
                    )}
                    placeholder="输入操作系统"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>
              </Grid>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl>
                  <FormLabel fontWeight="semibold">IP地址</FormLabel>
                  <Input 
                    value={selectedDevice?.ipAddress || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, ipAddress: e.target.value} : null
                    )}
                    placeholder="输入IP地址"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="semibold">管理地址</FormLabel>
                  <Input 
                    value={selectedDevice?.managementAddress || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, managementAddress: e.target.value} : null
                    )}
                    placeholder="输入管理地址"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>
              </Grid>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl>
                  <FormLabel fontWeight="semibold">安装日期</FormLabel>
                  <Input 
                    type="date"
                    value={selectedDevice?.installDate || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, installDate: e.target.value} : null
                    )}
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="semibold">采购日期</FormLabel>
                  <Input 
                    type="date"
                    value={selectedDevice?.purchaseDate || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, purchaseDate: e.target.value} : null
                    )}
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'purple.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'purple.500', boxShadow: '0 0 0 2px var(--chakra-colors-purple-500)', borderWidth: "2px" }}
                  />
                </FormControl>
              </Grid>

              <Box
                p={3}
                bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                borderRadius="md"
                borderLeft="4px solid"
                borderColor={colorMode === 'dark' ? 'teal.400' : 'teal.500'}
                mt={2}
              >
                <Flex align="center">
                  <Icon as={UserCircle} mr={2} boxSize="16px" color={colorMode === 'dark' ? 'teal.300' : 'teal.600'} />
                  <Text fontWeight="bold" fontSize="md">责任人信息</Text>
                </Flex>
              </Box>

              <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                <FormControl>
                  <FormLabel fontWeight="semibold">设备负责人</FormLabel>
                  <Input 
                    value={selectedDevice?.owner || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, owner: e.target.value} : null
                    )}
                    placeholder="输入负责人姓名"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'teal.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px var(--chakra-colors-teal-500)', borderWidth: "2px" }}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="semibold">联系方式</FormLabel>
                  <Input 
                    value={selectedDevice?.ownerContact || ''}
                    onChange={(e) => setSelectedDevice(prev => prev ? 
                      {...prev, ownerContact: e.target.value} : null
                    )}
                    placeholder="输入电话或邮箱"
                    size="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                    borderWidth="2px"
                    _hover={{ borderColor: 'teal.500', borderWidth: "2px" }}
                    _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px var(--chakra-colors-teal-500)', borderWidth: "2px" }}
                  />
                </FormControl>
              </Grid>

              <FormControl>
                <FormLabel fontWeight="semibold">业务名称</FormLabel>
                <Input 
                  value={selectedDevice?.businessName || ''}
                  onChange={(e) => setSelectedDevice(prev => prev ? 
                    {...prev, businessName: e.target.value} : null
                  )}
                  placeholder="输入相关业务名称"
                  size="md"
                  borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                  borderWidth="2px"
                  _hover={{ borderColor: 'teal.500', borderWidth: "2px" }}
                  _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px var(--chakra-colors-teal-500)', borderWidth: "2px" }}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontWeight="semibold">{t('description')}</FormLabel>
                <Textarea 
                  value={selectedDevice?.description || ''}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setSelectedDevice(prev => prev ? 
                    {...prev, description: e.target.value} : null
                  )}
                  placeholder="输入设备描述、用途或其他备注信息"
                  rows={3}
                  size="md"
                  borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.300'}
                  borderWidth="2px"
                  _hover={{ borderColor: 'teal.500', borderWidth: "2px" }}
                  _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px var(--chakra-colors-teal-500)', borderWidth: "2px" }}
                  resize="vertical"
                />
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter 
            p={0}
            borderTop="1px solid" 
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            bgGradient={colorMode === 'dark' ? 
              'linear(to-r, gray.800, gray.700)' : 
              'linear(to-r, gray.50, white)'}
          >
            <Flex justify="space-between" w="full" px={5} py={3}>
              {!isNewDevice && (
                <Button 
                  colorScheme="red" 
                  variant="outline"
                  leftIcon={<Icon as={Trash} boxSize="16px" weight="fill" />}
                  onClick={() => {
                    onClose();
                    onDeleteOpen();
                  }}
                  size="md"
                  borderRadius="md"
                  _hover={{
                    bg: 'red.50',
                    borderColor: 'red.500',
                    transform: 'translateY(-1px)'
                  }}
                  transition="all 0.2s"
                >
                  {t('delete')}
                </Button>
              )}
              <Spacer />
              <HStack spacing={3}>
                <Button 
                  variant="ghost"
                  onClick={onClose}
                  size="md"
                  minW="85px"
                  borderRadius="md"
                >
                  {t('cancel')}
                </Button>
                <Button 
                  colorScheme="blue" 
                  onClick={handleSaveDevice}
                  boxShadow="md"
                  leftIcon={<Icon as={FloppyDisk} weight="fill" boxSize="16px" />}
                  size="md"
                  minW="85px"
                  borderRadius="md"
                  bgGradient={colorMode === 'dark' ? 
                    'linear(to-r, blue.500, purple.500)' : 
                    'linear(to-r, blue.400, purple.400)'}
                  _hover={{
                    bgGradient: colorMode === 'dark' ? 
                      'linear(to-r, blue.600, purple.600)' : 
                      'linear(to-r, blue.500, purple.500)',
                    transform: 'translateY(-2px)',
                    boxShadow: 'lg'
                  }}
                  transition="all 0.2s"
                >
                  {t('save')}
                </Button>
              </HStack>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* 设备删除确认模态框 */}
      <Modal 
        isOpen={isDeleteOpen} 
        onClose={onDeleteClose} 
        isCentered 
        size="sm"
        motionPreset="slideInBottom"
      >
        <ModalOverlay 
          bg="blackAlpha.300"
          backdropFilter="blur(8px)"
        />
        <ModalContent
          bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          boxShadow="2xl"
          borderRadius="xl"
          mx="auto"
          overflow="hidden"
          border="1px solid"
          borderColor={colorMode === 'dark' ? 'red.800' : 'red.100'}
          maxW="420px"
          position="relative"
        >
          <Box
            bgGradient={colorMode === 'dark' ? 
              'linear(to-r, red.900, red.800)' : 
              'linear(to-r, red.50, red.100)'}
            py={4}
            px={6}
            borderBottom="1px solid"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            position="relative"
            overflow="hidden"
          >
            <Flex justify="space-between" align="center" position="relative" zIndex={1}>
              <Heading 
                size="md" 
                color={colorMode === 'dark' ? 'red.200' : 'red.600'}
                fontWeight="bold"
              >
                {t('confirm.delete')}
              </Heading>
              <Icon
                as={TrashSimple}
                boxSize={5}
                color={colorMode === 'dark' ? 'red.200' : 'red.600'}
                weight="fill"
              />
            </Flex>
            
            {/* 头部装饰元素 */}
            <Box 
              position="absolute" 
              top="0" 
              right="0" 
              h="5px" 
              w="70%" 
              bg={colorMode === 'dark' ? 'red.500' : 'red.400'} 
              opacity="0.6"
            />
          </Box>
          <ModalCloseButton 
            top="4" 
            right="4" 
            color={colorMode === 'dark' ? 'red.200' : 'red.600'} 
            borderRadius="full"
          />
          
          <ModalBody p={6}>
            <Box 
              p={5} 
              bg={colorMode === 'dark' ? 'red.900' : 'red.50'} 
              borderRadius="lg"
              borderLeft="4px solid"
              borderColor={colorMode === 'dark' ? 'red.500' : 'red.500'}
              mb={3}
              position="relative"
              overflow="hidden"
            >
              <Flex align="center" mb={3} position="relative" zIndex={1}>
                <Icon as={Warning} boxSize={6} mr={3} color="red.500" weight="fill" />
                <Text fontWeight="semibold" fontSize="lg">
                  {t('delete.confirmation')}
                </Text>
              </Flex>
              <Text position="relative" zIndex={1}>
                您确定要删除设备 <Text as="span" fontWeight="bold">{selectedDevice?.name}</Text> 吗？
              </Text>
              <Text mt={2} fontSize="sm" color={colorMode === 'dark' ? 'red.300' : 'red.600'} position="relative" zIndex={1}>
                此操作不可撤销，删除后数据将无法恢复。
              </Text>
              
              {/* 背景装饰 */}
              <Box 
                position="absolute" 
                bottom="-30px" 
                right="-30px" 
                height="100px" 
                width="100px" 
                bg={colorMode === 'dark' ? 'red.800' : 'red.200'} 
                opacity="0.2" 
                borderRadius="full" 
              />
            </Box>
          </ModalBody>

          <ModalFooter 
            p={4} 
            borderTop="1px solid"
            borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
            bgGradient={colorMode === 'dark' ? 
              'linear(to-r, gray.800, gray.700)' : 
              'linear(to-r, gray.50, white)'}
          >
            <Button 
              variant="ghost" 
              mr={3} 
              onClick={onDeleteClose}
              size="md"
              borderRadius="md"
            >
              {t('cancel')}
            </Button>
            <Button 
              colorScheme="red" 
              onClick={handleDeleteDevice}
              boxShadow="md"
              leftIcon={<Icon as={Trash} weight="fill" />}
              size="md"
              borderRadius="md"
              bgGradient={colorMode === 'dark' ? 
                'linear(to-r, red.600, red.500)' : 
                'linear(to-r, red.500, red.400)'}
              _hover={{
                bgGradient: colorMode === 'dark' ? 
                  'linear(to-r, red.700, red.600)' : 
                  'linear(to-r, red.600, red.500)',
                transform: 'translateY(-2px)',
                boxShadow: 'lg'
              }}
              transition="all 0.2s"
            >
              {t('delete')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 设备详情模态框 */}
      <Modal
        isOpen={isDeviceDetailOpen}
        onClose={onCloseDeviceDetail}
        size="xl"
        scrollBehavior="inside"
        motionPreset="slideInBottom"
      >
        <ModalOverlay backdropFilter="blur(4px)" bg="blackAlpha.300" />
        <ModalContent 
          maxW="900px"
          bg={colorMode === 'dark' ? 'gray.800' : 'white'}
          boxShadow="2xl"
          borderRadius="xl"
          overflow="hidden"
          border="1px solid"
          borderColor={colorMode === 'dark' ? 'purple.800' : 'purple.100'}
        >
          <ModalHeader
            borderBottomWidth="1px"
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            py={4}
            px={6}
            bgGradient={colorMode === 'dark' ? 
              'linear(to-r, purple.900, blue.900)' : 
              'linear(to-r, purple.50, blue.50)'}
          >
            <Text
              bgGradient={colorMode === 'dark' ? 
                'linear(to-r, purple.300, blue.300)' : 
                'linear(to-r, purple.600, blue.600)'}
              bgClip="text"
              fontWeight="bold"
            >
              {t('device.detail')}
            </Text>
            <IconButton
              aria-label="Close"
              icon={<Icon as={X} weight="bold" />}
              size="sm"
              onClick={onCloseDeviceDetail}
              variant="ghost"
              borderRadius="full"
            />
          </ModalHeader>
          {selectedDevice && (
            <DeviceDetail
              device={selectedDevice}
              onClose={onCloseDeviceDetail}
              onEdit={onOpenEditDevice}
            />
          )}
        </ModalContent>
      </Modal>
    </Box>
  )
}

export default RackDetailPage 