'use client'

import React from 'react'
import { 
  Box, 
  Heading, 
  Grid, 
  GridItem, 
  Text, 
  useColorMode,
  Flex,
  Center,
  Badge,
  Tooltip,
  Container,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Icon
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useTranslation } from '@/contexts/LanguageContext'
import { FiServer, FiAlertCircle, FiCheckCircle, FiTool } from 'react-icons/fi'

// 机柜利用率数据类型
interface RackUtilization {
  total: number;
  used: number;
  status: 'normal' | 'warning' | 'critical';
}

// 获取利用率状态
const getUtilizationStatus = (total: number, used: number): 'normal' | 'warning' | 'critical' => {
  const utilization = (used / total) * 100;
  if (utilization >= 85) return 'critical';
  if (utilization >= 70) return 'warning';
  return 'normal';
};

// 模拟数据：每个机柜的使用情况
const getRackUtilization = (rackId: string): RackUtilization => {
  // 真实项目中，这些数据应该从API获取
  const rackData: Record<string, RackUtilization> = {
    'A01': { total: 42, used: 28, status: getUtilizationStatus(42, 28) },
    'A02': { total: 42, used: 35, status: getUtilizationStatus(42, 35) },
    'A03': { total: 42, used: 29, status: getUtilizationStatus(42, 29) },
    'A04': { total: 42, used: 38, status: getUtilizationStatus(42, 38) },
    'A05': { total: 42, used: 25, status: getUtilizationStatus(42, 25) },
    'A06': { total: 42, used: 31, status: getUtilizationStatus(42, 31) },
    'A07': { total: 42, used: 18, status: getUtilizationStatus(42, 18) },
    'A08': { total: 42, used: 22, status: getUtilizationStatus(42, 22) },
    'A09': { total: 42, used: 39, status: getUtilizationStatus(42, 39) },
    'A10': { total: 42, used: 30, status: getUtilizationStatus(42, 30) },
    'A11': { total: 42, used: 34, status: getUtilizationStatus(42, 34) },
    'A12': { total: 42, used: 37, status: getUtilizationStatus(42, 37) },
    'A13': { total: 42, used: 20, status: getUtilizationStatus(42, 20) },
    'A14': { total: 42, used: 15, status: getUtilizationStatus(42, 15) },
    'A15': { total: 42, used: 41, status: getUtilizationStatus(42, 41) },
  };

  return rackData[rackId] || { total: 42, used: 0, status: 'normal' };
};

// 利用率信息类型
interface UtilizationInfo {
  available: number;
  utilizationPercent: number;
  status: 'normal' | 'warning' | 'critical';
}

const RackLayout = ({ totalU, devices }: { totalU: number; devices: any[] }) => {
  const { colorMode } = useColorMode()
  const router = useRouter()
  const { t } = useTranslation()

  // 定义机柜中的设备
  const rackItems = [
    { id: 'A08', side: 'left', row: 1 },
    { id: 'A07', side: 'right', row: 1 },
    { id: 'A09', side: 'left', row: 2 },
    { id: 'A06', side: 'right', row: 2 },
    { id: 'A10', side: 'left', row: 3 },
    { id: 'A05', side: 'right', row: 3 },
    { id: '列间空调', side: 'left', row: 4, isAC: true },
    { id: '列间空调', side: 'right', row: 4, isAC: true },
    { id: 'A11', side: 'left', row: 5 },
    { id: 'A04', side: 'right', row: 5 },
    { id: 'A12', side: 'left', row: 6 },
    { id: 'A03', side: 'right', row: 6 },
    { id: 'A13', side: 'left', row: 7 },
    { id: 'A02', side: 'right', row: 7 },
    { id: '列间空调', side: 'left', row: 8, isAC: true },
    { id: '列间空调', side: 'right', row: 8, isAC: true },
    { id: 'A14', side: 'left', row: 9 },
    { id: 'A01', side: 'right', row: 9 },
    { id: 'A15', side: 'left', row: 10 },
    { id: '配电柜', side: 'right', row: 10, isPower: true },
  ]

  // 处理点击事件
  const handleRackClick = (rackId: string) => {
    if (rackId !== '列间空调' && rackId !== '配电柜') {
      router.push(`/resources/rack/${rackId.toLowerCase()}`)
    }
  }

  // 检查是否是机柜编号 (A01-A15)
  const isRackNumber = (id: string) => {
    return /^A\d+$/.test(id);
  }

  // 根据机柜使用率获取状态颜色
  const getUtilizationColor = (status: 'normal' | 'warning' | 'critical') => {
    switch (status) {
      case 'critical':
        return 'red';
      case 'warning':
        return 'orange';
      default:
        return 'green';
    }
  }

  // 获取利用率状态的颜色
  const getStatusColor = (status: 'normal' | 'warning' | 'critical', isDark: boolean): string => {
    switch (status) {
      case 'critical':
        return isDark ? 'red.600' : 'red.100';
      case 'warning':
        return isDark ? 'orange.600' : 'orange.100';
      case 'normal':
        return isDark ? 'green.600' : 'green.100';
    }
  };

  // 获取利用率状态的文本颜色
  const getStatusTextColor = (status: 'normal' | 'warning' | 'critical', isDark: boolean): string => {
    switch (status) {
      case 'critical':
        return isDark ? 'red.200' : 'red.700';
      case 'warning':
        return isDark ? 'orange.200' : 'orange.700';
      case 'normal':
        return isDark ? 'green.200' : 'green.700';
    }
  };

  return (
    <Box 
      borderWidth="1px" 
      borderRadius="lg" 
      overflow="hidden"
      bg={colorMode === 'dark' ? 'gray.800' : 'white'}
      borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
    >
      <Flex direction="column" align="center" p={4}>
        <Box 
          bg={colorMode === 'dark' ? 'blue.900' : 'blue.50'} 
          border="1px solid" 
          borderColor={colorMode === 'dark' ? 'blue.800' : 'blue.200'} 
          borderRadius="md" 
          py={6} 
          px={8}
          width="100%"
          maxW="1000px"
        >
          <Grid templateColumns="1fr 80px 1fr" gap={4}>
            {rackItems.map((item) => {
              const isRack = isRackNumber(item.id);
              let utilizationInfo: UtilizationInfo | undefined;
              
              if (isRack) {
                const { total, used, status } = getRackUtilization(item.id);
                const available = total - used;
                const utilizationPercent = Math.round((used / total) * 100);
                utilizationInfo = {
                  available,
                  utilizationPercent,
                  status
                };
              }

              const bgColor = item.isAC 
                ? colorMode === 'dark' ? 'cyan.800' : 'cyan.50'
                : item.isPower 
                  ? colorMode === 'dark' ? 'red.800' : 'red.50'
                  : utilizationInfo 
                    ? getStatusColor(utilizationInfo.status, colorMode === 'dark')
                    : colorMode === 'dark' ? 'gray.700' : 'white';

              const textColor = utilizationInfo
                ? getStatusTextColor(utilizationInfo.status, colorMode === 'dark')
                : colorMode === 'dark' ? 'white' : 'gray.800';

              return (
                <React.Fragment key={`${item.id}-${item.row}-${item.side}`}>
                  {item.side === 'left' && (
                    <GridItem 
                      bg={bgColor}
                      border="1px solid"
                      borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
                      p={3}
                      borderRadius="md"
                      textAlign="center"
                      cursor={isRack ? 'pointer' : 'default'}
                      onClick={() => isRack && handleRackClick(item.id)}
                      color={textColor}
                      fontWeight={isRack ? 'medium' : 'normal'}
                      _hover={isRack ? {
                        transform: 'scale(1.02)',
                        boxShadow: 'lg'
                      } : undefined}
                      transition="all 0.2s"
                    >
                      {isRack && utilizationInfo ? (
                        <Tooltip 
                          label={`${t('available')}: ${utilizationInfo.available}U (${100 - utilizationInfo.utilizationPercent}%)`}
                          placement="top"
                          hasArrow
                        >
                          <Flex direction="column" align="center" gap={2}>
                            <Text>{item.id}</Text>
                            <Badge 
                              colorScheme={getUtilizationColor(utilizationInfo.status)}
                              variant="solid"
                              borderRadius="full"
                              px={2}
                              fontSize="xs"
                            >
                              {utilizationInfo.available}U
                            </Badge>
                          </Flex>
                        </Tooltip>
                      ) : (
                        <Text>{item.id}</Text>
                      )}
                    </GridItem>
                  )}
                  
                  {item.row === 1 && item.side === 'left' && (
                    <GridItem 
                      key="cold-aisle"
                      bg={colorMode === 'dark' ? 'blue.800' : 'blue.100'} 
                      rowSpan={10}
                      border="1px solid"
                      borderColor={colorMode === 'dark' ? 'blue.700' : 'blue.300'}
                      borderRadius="md"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Text
                        color={colorMode === 'dark' ? 'white' : 'blue.800'}
                        fontWeight="bold"
                        style={{ writingMode: 'vertical-rl' }}
                      >
                        {t('cold.aisle')}
                      </Text>
                    </GridItem>
                  )}
                  
                  {item.side === 'right' && (
                    <GridItem 
                      bg={bgColor}
                      border="1px solid"
                      borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
                      p={3}
                      borderRadius="md"
                      textAlign="center"
                      cursor={isRack ? 'pointer' : 'default'}
                      onClick={() => isRack && handleRackClick(item.id)}
                      color={textColor}
                      fontWeight={isRack ? 'medium' : 'normal'}
                      _hover={isRack ? {
                        transform: 'scale(1.02)',
                        boxShadow: 'lg'
                      } : undefined}
                      transition="all 0.2s"
                    >
                      {isRack && utilizationInfo ? (
                        <Tooltip 
                          label={`${t('available')}: ${utilizationInfo.available}U (${100 - utilizationInfo.utilizationPercent}%)`}
                          placement="top"
                          hasArrow
                        >
                          <Flex direction="column" align="center" gap={2}>
                            <Text>{item.id}</Text>
                            <Badge 
                              colorScheme={getUtilizationColor(utilizationInfo.status)}
                              variant="solid"
                              borderRadius="full"
                              px={2}
                              fontSize="xs"
                            >
                              {utilizationInfo.available}U
                            </Badge>
                          </Flex>
                        </Tooltip>
                      ) : (
                        <Text>{item.id}</Text>
                      )}
                    </GridItem>
                  )}
                </React.Fragment>
              );
            })}
          </Grid>
        </Box>
      
        <Flex justify="center" mt={6} wrap="wrap" gap={4}>
          <Flex align="center">
            <Box 
              w="3" 
              h="3" 
              borderRadius="full" 
              bg={getStatusColor('normal', colorMode === 'dark')} 
              mr={2} 
            />
            <Text fontSize="sm">{t('space.sufficient')}</Text>
          </Flex>
          <Flex align="center">
            <Box 
              w="3" 
              h="3" 
              borderRadius="full" 
              bg={getStatusColor('warning', colorMode === 'dark')} 
              mr={2} 
            />
            <Text fontSize="sm">{t('space.limited')}</Text>
          </Flex>
          <Flex align="center">
            <Box 
              w="3" 
              h="3" 
              borderRadius="full" 
              bg={getStatusColor('critical', colorMode === 'dark')} 
              mr={2} 
            />
            <Text fontSize="sm">{t('space.critical')}</Text>
          </Flex>
        </Flex>
      </Flex>
    </Box>
  )
}

const ResourcesPage = () => {
  const { t } = useTranslation()
  const { colorMode } = useColorMode()
  
  return (
    <Container maxW="container.xl" p={4}>
      <Heading size="lg" mb={6}>
        {t('resources')}
      </Heading>

      {/* Statistics Cards */}
      <Grid templateColumns={{ base: '1fr', md: 'repeat(4, 1fr)' }} gap={4} mb={8}>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel display="flex" alignItems="center" gap={2}>
                <Icon as={FiServer} />
                {t('total.devices')}
              </StatLabel>
              <StatNumber>128</StatNumber>
            </Stat>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel display="flex" alignItems="center" gap={2}>
                <Icon as={FiCheckCircle} color="green.500" />
                {t('normal.running')}
              </StatLabel>
              <StatNumber>98</StatNumber>
            </Stat>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel display="flex" alignItems="center" gap={2}>
                <Icon as={FiAlertCircle} color="orange.500" />
                {t('warning.devices')}
              </StatLabel>
              <StatNumber>12</StatNumber>
            </Stat>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel display="flex" alignItems="center" gap={2}>
                <Icon as={FiTool} color="red.500" />
                {t('maintenance.devices')}
              </StatLabel>
              <StatNumber>18</StatNumber>
            </Stat>
          </CardBody>
        </Card>
      </Grid>
      
      {/* Datacenter Layout */}
      <Box mb={8}>
        <Heading size="md" mb={4}>
          {t('datacenter.layout')}
        </Heading>
        <RackLayout
          totalU={42}
          devices={[]}
        />
      </Box>
    </Container>
  )
}

export default ResourcesPage 