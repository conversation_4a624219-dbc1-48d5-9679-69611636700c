'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { 
  Box, 
  Heading, 
  Text, 
  Button, 
  VStack, 
  HStack,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Icon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Flex,
  Spacer
} from '@chakra-ui/react'
import {
  ArrowLeft,
  Plus,
  Folder,
  Tag,
  ClipboardText,
  Package,
  TrendUp,
  Users,
  Clock
} from '@phosphor-icons/react'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'

export default function AssetRegisterPage() {
  const router = useRouter()

  // 获取统计数据
  const { data: stats } = useQuery({
    queryKey: ['register-stats'],
    queryFn: async () => {
      // 这里可以调用实际的API获取统计数据
      return {
        totalAssets: 1247,
        newThisMonth: 23,
        categories: 12,
        tags: 45,
        recentRegistrations: [
          { id: 1, name: 'Dell PowerEdge R740', code: 'SRV-001', date: '2024-01-15' },
          { id: 2, name: 'Cisco Catalyst 9300', code: 'NET-001', date: '2024-01-14' },
          { id: 3, name: 'HP LaserJet Pro', code: 'PRT-001', date: '2024-01-13' },
        ]
      }
    }
  })

  const mockStats = {
    totalAssets: 1247,
    newThisMonth: 23,
    categories: 12,
    tags: 45,
    recentRegistrations: [
      { id: 1, name: 'Dell PowerEdge R740', code: 'SRV-001', date: '2024-01-15' },
      { id: 2, name: 'Cisco Catalyst 9300', code: 'NET-001', date: '2024-01-14' },
      { id: 3, name: 'HP LaserJet Pro', code: 'PRT-001', date: '2024-01-13' },
    ]
  }

  const data = stats || mockStats

  return (
    <Box p={6} maxW="1200px" mx="auto">
      {/* 面包屑导航 */}
      <Breadcrumb mb={6}>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets">
            资产管理
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets/overview">
            资产概览
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <BreadcrumbLink>资产登记</BreadcrumbLink>
        </BreadcrumbItem>
      </Breadcrumb>

      {/* 页面头部 */}
      <VStack align="start" spacing={4} mb={8}>
        <HStack spacing={4} justify="space-between" w="full">
          <HStack spacing={4}>
            <Button
              leftIcon={<Icon as={ArrowLeft} />}
              variant="outline"
              onClick={() => router.back()}
            >
              返回
            </Button>
            <VStack align="start" spacing={1}>
              <Heading size="lg" fontWeight="bold" bgGradient="linear(to-r, blue.400, teal.400)" bgClip="text">
                资产登记
              </Heading>
              <Text color="gray.500">
                管理资产登记流程，包括资产录入、分类管理和标签管理
              </Text>
            </VStack>
          </HStack>
          <Button
            leftIcon={<Icon as={Plus} />}
            colorScheme="blue"
            onClick={() => router.push('/resources/assets/register/add')}
          >
            新增资产
          </Button>
        </HStack>
      </VStack>

      {/* 统计卡片 */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>总资产数</StatLabel>
              <StatNumber color="blue.500">{data.totalAssets}</StatNumber>
              <StatHelpText>
                <Icon as={TrendUp} />
                持续增长
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>本月新增</StatLabel>
              <StatNumber color="green.500">{data.newThisMonth}</StatNumber>
              <StatHelpText>
                <Icon as={Clock} />
                最近30天
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>资产分类</StatLabel>
              <StatNumber color="purple.500">{data.categories}</StatNumber>
              <StatHelpText>
                <Icon as={Folder} />
                分类体系
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>标签数量</StatLabel>
              <StatNumber color="orange.500">{data.tags}</StatNumber>
              <StatHelpText>
                <Icon as={Tag} />
                标签管理
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* 功能模块 */}
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} mb={8}>
        {/* 资产录入 */}
        <Card cursor="pointer" _hover={{ shadow: 'md', transform: 'translateY(-2px)' }} transition="all 0.2s">
          <CardHeader>
            <HStack>
              <Icon as={Plus} color="blue.500" boxSize={6} />
              <Heading size="md">资产录入</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={0}>
            <VStack align="start" spacing={3}>
              <Text color="gray.600">
                添加新的IT资产到系统中，支持多种资产类型和详细配置信息录入
              </Text>
              <Button
                colorScheme="blue"
                size="sm"
                onClick={() => router.push('/resources/assets/register/add')}
              >
                开始录入
              </Button>
            </VStack>
          </CardBody>
        </Card>

        {/* 分类管理 */}
        <Card cursor="pointer" _hover={{ shadow: 'md', transform: 'translateY(-2px)' }} transition="all 0.2s">
          <CardHeader>
            <HStack>
              <Icon as={Folder} color="green.500" boxSize={6} />
              <Heading size="md">分类管理</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={0}>
            <VStack align="start" spacing={3}>
              <Text color="gray.600">
                管理资产分类体系，支持层级分类和自定义属性配置，便于资产组织
              </Text>
              <Button
                colorScheme="green"
                size="sm"
                onClick={() => router.push('/resources/assets/register/categories')}
              >
                管理分类
              </Button>
            </VStack>
          </CardBody>
        </Card>

        {/* 标签管理 */}
        <Card cursor="pointer" _hover={{ shadow: 'md', transform: 'translateY(-2px)' }} transition="all 0.2s">
          <CardHeader>
            <HStack>
              <Icon as={Tag} color="orange.500" boxSize={6} />
              <Heading size="md">标签管理</Heading>
            </HStack>
          </CardHeader>
          <CardBody pt={0}>
            <VStack align="start" spacing={3}>
              <Text color="gray.600">
                创建和管理资产标签，支持颜色分类和快速筛选，提高资产识别效率
              </Text>
              <Button
                colorScheme="orange"
                size="sm"
                onClick={() => router.push('/resources/assets/register/tags')}
              >
                管理标签
              </Button>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* 最近登记记录 */}
      <Card>
        <CardHeader>
          <Flex>
            <HStack>
              <Icon as={ClipboardText} color="blue.500" />
              <Heading size="md">最近登记记录</Heading>
            </HStack>
            <Spacer />
            <Button
              size="sm"
              variant="outline"
              onClick={() => router.push('/resources/assets/overview/list')}
            >
              查看全部
            </Button>
          </Flex>
        </CardHeader>
        <CardBody>
          <VStack spacing={4} align="stretch">
            {data.recentRegistrations.map((asset) => (
              <HStack key={asset.id} p={3} bg="gray.50" borderRadius="md" justify="space-between">
                <HStack>
                  <Icon as={Package} color="blue.500" />
                  <VStack align="start" spacing={0}>
                    <Text fontWeight="medium">{asset.name}</Text>
                    <Text fontSize="sm" color="gray.500" fontFamily="mono">
                      {asset.code}
                    </Text>
                  </VStack>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Badge colorScheme="green">已登记</Badge>
                  <Text fontSize="sm" color="gray.500">
                    {asset.date}
                  </Text>
                </VStack>
              </HStack>
            ))}
          </VStack>
        </CardBody>
      </Card>
    </Box>
  )
}
