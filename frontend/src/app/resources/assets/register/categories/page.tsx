'use client'

import React, { useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Box, 
  Heading, 
  Text, 
  Button, 
  VStack, 
  HStack,
  useToast,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Icon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,

} from '@chakra-ui/react'
import { ArrowLeft, Plus, PencilSimple, Trash, Folder } from '@phosphor-icons/react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import Link from 'next/link'

interface AssetCategory {
  id: number
  name: string
  code?: string
  description?: string
  level: number
  parent_id?: number
  attributes?: any
  is_system: boolean
  created_at?: string
  updated_at?: string
}

export default function AssetCategoriesPage() {
  const router = useRouter()
  const toast = useToast()
  const queryClient = useQueryClient()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure()
  const cancelRef = useRef<HTMLButtonElement>(null)
  
  const [editingCategory, setEditingCategory] = useState<AssetCategory | null>(null)
  const [deletingCategory, setDeletingCategory] = useState<AssetCategory | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    parent_id: '',
  })

  // 获取资产分类列表
  const { data: categories = [], isLoading, refetch } = useQuery({
    queryKey: ['asset-categories'],
    queryFn: async () => {
      const response = await fetch('/api/asset-categories')
      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }
      return response.json()
    }
  })

  // 创建分类
  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/asset-categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to create category')
      }
      return response.json()
    },
    onSuccess: () => {
      toast({
        title: '成功',
        description: '分类创建成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      queryClient.invalidateQueries({ queryKey: ['asset-categories'] })
      onClose()
      resetForm()
    },
    onError: (error: Error) => {
      toast({
        title: '错误',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  })

  // 更新分类
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: any }) => {
      const response = await fetch(`/api/asset-categories/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to update category')
      }
      return response.json()
    },
    onSuccess: () => {
      toast({
        title: '成功',
        description: '分类更新成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      queryClient.invalidateQueries({ queryKey: ['asset-categories'] })
      onClose()
      resetForm()
    },
    onError: (error: Error) => {
      toast({
        title: '错误',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  })

  // 删除分类
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/asset-categories/${id}`, {
        method: 'DELETE'
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to delete category')
      }
    },
    onSuccess: () => {
      toast({
        title: '成功',
        description: '分类删除成功',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      queryClient.invalidateQueries({ queryKey: ['asset-categories'] })
      onDeleteClose()
      setDeletingCategory(null)
    },
    onError: (error: Error) => {
      toast({
        title: '错误',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    }
  })

  const resetForm = () => {
    setFormData({ name: '', code: '', description: '', parent_id: '' })
    setEditingCategory(null)
  }

  const handleOpenModal = (category?: AssetCategory) => {
    if (category) {
      setEditingCategory(category)
      setFormData({
        name: category.name,
        code: category.code || '',
        description: category.description || '',
        parent_id: category.parent_id?.toString() || '',
      })
    } else {
      resetForm()
    }
    onOpen()
  }

  const handleSubmit = () => {
    const data = {
      ...formData,
      parent_id: formData.parent_id ? parseInt(formData.parent_id) : null,
      level: formData.parent_id ? 2 : 1,
      attributes: {},
      is_system: false
    }

    if (editingCategory) {
      updateMutation.mutate({ id: editingCategory.id, data })
    } else {
      createMutation.mutate(data)
    }
  }

  const handleDelete = (category: AssetCategory) => {
    setDeletingCategory(category)
    onDeleteOpen()
  }

  const confirmDelete = () => {
    if (deletingCategory) {
      deleteMutation.mutate(deletingCategory.id)
    }
  }

  // 获取顶级分类（用于父分类选择）
  const topLevelCategories = categories.filter((cat: AssetCategory) => !cat.parent_id)

  return (
    <Box p={6} maxW="1200px" mx="auto">
      {/* 面包屑导航 */}
      <Breadcrumb mb={6}>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets">
            资产管理
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets/register">
            资产登记
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <BreadcrumbLink>分类管理</BreadcrumbLink>
        </BreadcrumbItem>
      </Breadcrumb>

      {/* 页面头部 */}
      <VStack align="start" spacing={4} mb={8}>
        <HStack spacing={4} justify="space-between" w="full">
          <HStack spacing={4}>
            <Button
              leftIcon={<Icon as={ArrowLeft} />}
              variant="outline"
              onClick={() => router.back()}
            >
              返回
            </Button>
            <VStack align="start" spacing={1}>
              <Heading size="lg" fontWeight="bold" bgGradient="linear(to-r, blue.400, teal.400)" bgClip="text">
                资产分类管理
              </Heading>
              <Text color="gray.500">
                管理资产分类，支持层级分类和自定义属性配置
              </Text>
            </VStack>
          </HStack>
          <Button
            leftIcon={<Icon as={Plus} />}
            colorScheme="blue"
            onClick={() => handleOpenModal()}
          >
            新增分类
          </Button>
        </HStack>
      </VStack>

      {/* 分类列表 */}
      <Box bg="white" borderRadius="lg" shadow="sm" overflow="hidden">
        <Table variant="simple">
          <Thead bg="gray.50">
            <Tr>
              <Th>分类名称</Th>
              <Th>编码</Th>
              <Th>层级</Th>
              <Th>父分类</Th>
              <Th>描述</Th>
              <Th>状态</Th>
              <Th>操作</Th>
            </Tr>
          </Thead>
          <Tbody>
            {categories.map((category: AssetCategory) => (
              <Tr key={category.id}>
                <Td>
                  <HStack>
                    <Icon as={Folder} color="blue.500" />
                    <Text fontWeight="medium">{category.name}</Text>
                  </HStack>
                </Td>
                <Td>
                  <Text fontFamily="mono" fontSize="sm">{category.code}</Text>
                </Td>
                <Td>
                  <Badge colorScheme={category.level === 1 ? 'blue' : 'green'}>
                    {category.level === 1 ? '一级' : '二级'}
                  </Badge>
                </Td>
                <Td>
                  {category.parent_id ? 
                    categories.find((c: AssetCategory) => c.id === category.parent_id)?.name || '-' 
                    : '-'
                  }
                </Td>
                <Td>
                  <Text noOfLines={1} maxW="200px">{category.description || '-'}</Text>
                </Td>
                <Td>
                  <Badge colorScheme={category.is_system ? 'gray' : 'green'}>
                    {category.is_system ? '系统' : '自定义'}
                  </Badge>
                </Td>
                <Td>
                  <HStack spacing={2}>
                    <IconButton
                      aria-label="编辑"
                      icon={<Icon as={PencilSimple} />}
                      size="sm"
                      variant="outline"
                      onClick={() => handleOpenModal(category)}
                    />
                    {!category.is_system && (
                      <IconButton
                        aria-label="删除"
                        icon={<Icon as={Trash} />}
                        size="sm"
                        variant="outline"
                        colorScheme="red"
                        onClick={() => handleDelete(category)}
                      />
                    )}
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>

      {/* 新增/编辑分类模态框 */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{editingCategory ? '编辑分类' : '新增分类'}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>分类名称</FormLabel>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="请输入分类名称"
                />
              </FormControl>
              <FormControl>
                <FormLabel>分类编码</FormLabel>
                <Input
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  placeholder="请输入分类编码"
                />
              </FormControl>
              <FormControl>
                <FormLabel>父分类</FormLabel>
                <Select
                  value={formData.parent_id}
                  onChange={(e) => setFormData({ ...formData, parent_id: e.target.value })}
                  placeholder="选择父分类（可选）"
                >
                  {topLevelCategories.map((category: AssetCategory) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
              <FormControl>
                <FormLabel>描述</FormLabel>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="请输入分类描述"
                  rows={3}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onClose}>
              取消
            </Button>
            <Button 
              colorScheme="blue" 
              onClick={handleSubmit}
              isLoading={createMutation.isPending || updateMutation.isPending}
            >
              {editingCategory ? '更新' : '创建'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 删除确认对话框 */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              删除分类
            </AlertDialogHeader>
            <AlertDialogBody>
              确定要删除分类 "{deletingCategory?.name}" 吗？此操作无法撤销。
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                取消
              </Button>
              <Button 
                colorScheme="red" 
                onClick={confirmDelete} 
                ml={3}
                isLoading={deleteMutation.isPending}
              >
                删除
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  )
}
