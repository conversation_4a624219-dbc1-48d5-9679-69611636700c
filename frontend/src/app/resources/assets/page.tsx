'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Box, Center, Spinner, Text, VStack } from '@chakra-ui/react'

const AssetsPage = () => {
  const router = useRouter()

  useEffect(() => {
    // 重定向到资产概览页面
    router.replace('/resources/assets/overview')
  }, [router])

  return (
    <Box p={8}>
      <Center h="50vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="blue.500" />
          <Text>正在跳转到资产管理...</Text>
        </VStack>
      </Center>
    </Box>
  )
}

export default AssetsPage