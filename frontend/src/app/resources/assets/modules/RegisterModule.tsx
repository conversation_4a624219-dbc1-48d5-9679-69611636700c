'use client'

import React, { useState } from 'react'
import {
  Box,
  Flex,
  Text,
  Heading,
  Button,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Grid,
  GridItem,
  Card,
  CardBody,
  HStack,
  Input,
  InputGroup,
  InputLeftElement,
  VStack,
  FormControl,
  FormLabel,
  Select,
  Textarea,
  useColorMode,
  Tag,
  TagLabel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Switch,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Divider,
  Image,
  FormHelperText,
  IconButton
} from '@chakra-ui/react'
import {
  Plus,
  UploadSimple,
  List,
  Tag as TagIcon,
  QrCode,
  Barcode,
  Database,
  Desktop,
  HardDrives,
  MonitorPlay,
  Cpu,
  DeviceTablet,
  CircleNotch,
  ArrowRight,
  MagnifyingGlass,
  Funnel,
  CaretDown,
  DotsThreeVertical,
  PencilSimple,
  Trash,
  FileArrowUp,
  BookmarkSimple
} from '@phosphor-icons/react'

const RegisterModule = () => {
  const { colorMode } = useColorMode()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [activeTab, setActiveTab] = useState(0)
  
  // 模拟数据
  const assetTypes = [
    { id: 'hardware', name: '硬件设备', count: 124, icon: Desktop },
    { id: 'software', name: '软件资产', count: 86, icon: Database },
    { id: 'network', name: '网络设备', count: 42, icon: HardDrives },
    { id: 'office', name: '办公设备', count: 68, icon: DeviceTablet },
  ]
  
  const recentAssets = [
    { id: 'A001', name: 'ThinkStation P520', type: 'hardware', category: '服务器', date: '2023-07-15' },
    { id: 'A002', name: 'Windows Server 2022', type: 'software', category: '操作系统', date: '2023-07-14' },
    { id: 'A003', name: 'Cisco Catalyst 9300', type: 'network', category: '交换机', date: '2023-07-13' },
    { id: 'A004', name: 'Dell XPS 15', type: 'hardware', category: '笔记本电脑', date: '2023-07-12' },
  ]
  
  const categories = [
    { id: 'c001', name: '服务器', parentId: null, path: '/服务器', count: 42 },
    { id: 'c002', name: '笔记本电脑', parentId: null, path: '/笔记本电脑', count: 36 },
    { id: 'c003', name: '台式电脑', parentId: null, path: '/台式电脑', count: 28 },
    { id: 'c004', name: '交换机', parentId: null, path: '/交换机', count: 18 },
    { id: 'c005', name: '工作站', parentId: 'c001', path: '/服务器/工作站', count: 15 },
    { id: 'c006', name: '机架式服务器', parentId: 'c001', path: '/服务器/机架式服务器', count: 27 },
  ]
  
  return (
    <Box p={6}>
      <Box mb={8}>
        <Heading size="lg" mb={2} fontWeight="bold" bgGradient="linear(to-r, blue.400, teal.400)" bgClip="text">
          资产登记与分类
        </Heading>
        <Text color="gray.500">
          管理和组织所有资产的关键信息，支持多种分类方式和标签生成
        </Text>
      </Box>
      
      <Tabs 
        colorScheme="blue" 
        variant="soft-rounded" 
        size="md"
        index={activeTab}
        onChange={(index) => setActiveTab(index)}
        mb={8}
      >
        <TabList mb={6} borderBottom="1px solid" borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'} pb={4}>
          <Tab 
            _selected={{ 
              color: 'white', 
              bg: 'blue.500',
              boxShadow: 'md'
            }}
            mr={4}
            fontWeight="medium"
            px={6}
          >
            资产录入
          </Tab>
          <Tab 
            _selected={{ 
              color: 'white', 
              bg: 'blue.500',
              boxShadow: 'md'
            }}
            mr={4}
            fontWeight="medium"
            px={6}
          >
            分类管理
          </Tab>
          <Tab 
            _selected={{ 
              color: 'white', 
              bg: 'blue.500',
              boxShadow: 'md'
            }}
            fontWeight="medium"
            px={6}
          >
            标签管理
          </Tab>
        </TabList>
        
        <TabPanels>
          {/* 资产录入面板 */}
          <TabPanel p={0}>
            <Grid templateColumns="repeat(12, 1fr)" gap={6}>
              {/* 左侧：资产录入表单 */}
              <GridItem colSpan={{ base: 12, lg: 8 }}>
                <Card
                  bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                  borderRadius="xl"
                  boxShadow="md"
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                >
                  <CardBody p={6}>
                    <HStack mb={6} justify="space-between">
                      <Heading size="md">资产信息录入</Heading>
                      <HStack>
                        <Button
                          leftIcon={<Icon as={UploadSimple} />}
                          variant="outline"
                          size="sm"
                        >
                          批量导入
                        </Button>
                        <Button
                          leftIcon={<Icon as={Plus} />}
                          colorScheme="blue"
                          size="sm"
                        >
                          保存资产
                        </Button>
                      </HStack>
                    </HStack>
                    
                    <Grid templateColumns="repeat(12, 1fr)" gap={5}>
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">资产名称</FormLabel>
                          <Input placeholder="输入资产名称" />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">资产编号</FormLabel>
                          <Input placeholder="自动生成或手动输入" />
                          <FormHelperText>如不填写将自动生成</FormHelperText>
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">资产类型</FormLabel>
                          <Select placeholder="选择资产类型">
                            <option value="hardware">硬件设备</option>
                            <option value="software">软件资产</option>
                            <option value="network">网络设备</option>
                            <option value="office">办公设备</option>
                          </Select>
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">资产分类</FormLabel>
                          <Select placeholder="选择资产分类">
                            <option value="c001">服务器</option>
                            <option value="c002">笔记本电脑</option>
                            <option value="c003">台式电脑</option>
                            <option value="c004">交换机</option>
                            <option value="c005">工作站</option>
                            <option value="c006">机架式服务器</option>
                          </Select>
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">品牌/厂商</FormLabel>
                          <Input placeholder="输入品牌或厂商名称" />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">型号/版本</FormLabel>
                          <Input placeholder="输入型号或版本" />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">序列号</FormLabel>
                          <Input placeholder="输入序列号" />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">采购日期</FormLabel>
                          <Input type="date" />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">保修期限</FormLabel>
                          <Input type="date" />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">采购价值</FormLabel>
                          <InputGroup>
                            <InputLeftElement
                              pointerEvents="none"
                              color="gray.500"
                              fontSize="sm"
                              children="¥"
                            />
                            <Input placeholder="输入采购价值" type="number" pl={8} />
                          </InputGroup>
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={12}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">资产描述</FormLabel>
                          <Textarea placeholder="输入资产的详细说明" rows={4} />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={12}>
                        <FormControl mb={4} display="flex" alignItems="center">
                          <FormLabel fontWeight="medium" mb="0">
                            生成资产标签
                          </FormLabel>
                          <Switch colorScheme="blue" defaultChecked />
                        </FormControl>
                      </GridItem>
                    </Grid>
                  </CardBody>
                </Card>
              </GridItem>
              
              {/* 右侧：快速信息 */}
              <GridItem colSpan={{ base: 12, lg: 4 }}>
                <VStack spacing={6} align="stretch">
                  {/* 资产统计卡片 */}
                  <Card
                    bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                    borderRadius="xl"
                    boxShadow="md"
                    borderWidth="1px"
                    borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                  >
                    <CardBody p={5}>
                      <Heading size="sm" mb={4}>资产类型统计</Heading>
                      
                      <VStack spacing={4} align="stretch">
                        {assetTypes.map((type) => (
                          <Flex
                            key={type.id}
                            p={3}
                            bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                            borderRadius="md"
                            align="center"
                          >
                            <Box 
                              bg={colorMode === 'dark' ? 'blue.900' : 'blue.50'} 
                              p={2} 
                              borderRadius="md"
                              color="blue.500"
                            >
                              <Icon as={type.icon} boxSize={5} />
                            </Box>
                            
                            <Box flex="1" ml={3}>
                              <Text fontWeight="medium">{type.name}</Text>
                              <Text fontSize="sm" color="gray.500">{type.count} 项资产</Text>
                            </Box>
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              rightIcon={<ArrowRight />}
                              fontSize="sm"
                            >
                              查看
                            </Button>
                          </Flex>
                        ))}
                      </VStack>
                    </CardBody>
                  </Card>
                  
                  {/* 最近添加资产 */}
                  <Card
                    bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                    borderRadius="xl"
                    boxShadow="md"
                    borderWidth="1px"
                    borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                  >
                    <CardBody p={5}>
                      <Heading size="sm" mb={4}>最近添加资产</Heading>
                      
                      <VStack spacing={3} align="stretch">
                        {recentAssets.map((asset) => (
                          <HStack key={asset.id} spacing={3}>
                            <Text color="gray.500" fontSize="sm" width="60px">{asset.id}</Text>
                            <Box flex="1">
                              <Text fontWeight="medium" noOfLines={1}>{asset.name}</Text>
                              <Text fontSize="xs" color="gray.500">{asset.category}</Text>
                            </Box>
                            <Text fontSize="xs" color="gray.500">{asset.date}</Text>
                          </HStack>
                        ))}
                      </VStack>
                      
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        width="full" 
                        mt={4} 
                        leftIcon={<List />}
                      >
                        查看全部资产
                      </Button>
                    </CardBody>
                  </Card>
                </VStack>
              </GridItem>
            </Grid>
          </TabPanel>
          
          {/* 分类管理面板 */}
          <TabPanel p={0}>
            <Grid templateColumns="repeat(12, 1fr)" gap={6}>
              <GridItem colSpan={{ base: 12, lg: 5 }}>
                <Card
                  bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                  borderRadius="xl"
                  boxShadow="md"
                  height="100%"
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                >
                  <CardBody p={5}>
                    <HStack mb={5} justify="space-between">
                      <Heading size="md">新建分类</Heading>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        colorScheme="blue"
                        leftIcon={<List />}
                      >
                        全部分类
                      </Button>
                    </HStack>
                    
                    <VStack spacing={4} align="stretch">
                      <FormControl>
                        <FormLabel fontWeight="medium">分类名称</FormLabel>
                        <Input placeholder="输入分类名称" />
                      </FormControl>
                      
                      <FormControl>
                        <FormLabel fontWeight="medium">父级分类</FormLabel>
                        <Select placeholder="选择父级分类">
                          <option value="">无 (顶级分类)</option>
                          <option value="c001">服务器</option>
                          <option value="c002">笔记本电脑</option>
                          <option value="c003">台式电脑</option>
                          <option value="c004">交换机</option>
                        </Select>
                      </FormControl>
                      
                      <FormControl>
                        <FormLabel fontWeight="medium">分类描述</FormLabel>
                        <Textarea placeholder="输入分类描述" rows={3} />
                      </FormControl>
                      
                      <HStack spacing={4} justify="flex-end">
                        <Button variant="outline">重置</Button>
                        <Button colorScheme="blue">保存分类</Button>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>
              </GridItem>
              
              <GridItem colSpan={{ base: 12, lg: 7 }}>
                <Card
                  bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                  borderRadius="xl"
                  boxShadow="md"
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                >
                  <CardBody p={5}>
                    <HStack mb={5} justify="space-between">
                      <Heading size="md">分类列表</Heading>
                      <InputGroup maxW="240px">
                        <InputLeftElement pointerEvents="none">
                          <MagnifyingGlass />
                        </InputLeftElement>
                        <Input placeholder="搜索分类" />
                      </InputGroup>
                    </HStack>
                    
                    <Box overflowX="auto">
                      <Table variant="simple">
                        <Thead>
                          <Tr>
                            <Th>分类名称</Th>
                            <Th>路径</Th>
                            <Th isNumeric>资产数量</Th>
                            <Th width="80px"></Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {categories.map((category) => (
                            <Tr key={category.id}>
                              <Td fontWeight="medium">{category.name}</Td>
                              <Td>
                                <Text fontSize="sm" color="gray.500">
                                  {category.path}
                                </Text>
                              </Td>
                              <Td isNumeric>
                                <Badge colorScheme="blue">{category.count}</Badge>
                              </Td>
                              <Td>
                                <HStack>
                                  <IconButton
                                    aria-label="编辑"
                                    icon={<PencilSimple />}
                                    size="sm"
                                    variant="ghost"
                                  />
                                  <IconButton
                                    aria-label="删除"
                                    icon={<Trash />}
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="red"
                                  />
                                </HStack>
                              </Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </Box>
                  </CardBody>
                </Card>
              </GridItem>
            </Grid>
          </TabPanel>
          
          {/* 标签管理面板 */}
          <TabPanel p={0}>
            <Grid templateColumns="repeat(12, 1fr)" gap={6}>
              <GridItem colSpan={{ base: 12, lg: 6 }}>
                <Card
                  bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                  borderRadius="xl"
                  boxShadow="md"
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                >
                  <CardBody p={5}>
                    <HStack mb={5} justify="space-between">
                      <Heading size="md">标签设置</Heading>
                      <Button
                        leftIcon={<Icon as={QrCode} />}
                        colorScheme="blue"
                        size="sm"
                      >
                        批量生成
                      </Button>
                    </HStack>
                    
                    <Grid templateColumns="repeat(12, 1fr)" gap={5}>
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">标签类型</FormLabel>
                          <Select defaultValue="qrcode">
                            <option value="qrcode">二维码</option>
                            <option value="barcode">条形码</option>
                            <option value="rfid">RFID</option>
                          </Select>
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={{ base: 12, md: 6 }}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">标签尺寸</FormLabel>
                          <Select defaultValue="medium">
                            <option value="small">小 (40x25mm)</option>
                            <option value="medium">中 (60x40mm)</option>
                            <option value="large">大 (80x50mm)</option>
                          </Select>
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={12}>
                        <FormControl mb={4}>
                          <FormLabel fontWeight="medium">标签内容</FormLabel>
                          <HStack mb={2}>
                            <Button size="sm" variant="outline">资产编号</Button>
                            <Button size="sm" variant="outline">资产名称</Button>
                            <Button size="sm" variant="outline">序列号</Button>
                            <Button size="sm" variant="outline">部门</Button>
                          </HStack>
                          <Textarea placeholder="输入自定义内容" rows={3} />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={12}>
                        <FormControl mb={4} display="flex" alignItems="center">
                          <FormLabel fontWeight="medium" mb="0">
                            包含公司LOGO
                          </FormLabel>
                          <Switch colorScheme="blue" defaultChecked />
                        </FormControl>
                      </GridItem>
                      
                      <GridItem colSpan={12}>
                        <HStack spacing={4} justify="flex-end">
                          <Button variant="outline">预览</Button>
                          <Button colorScheme="blue">应用</Button>
                        </HStack>
                      </GridItem>
                    </Grid>
                  </CardBody>
                </Card>
              </GridItem>
              
              <GridItem colSpan={{ base: 12, lg: 6 }}>
                <Card
                  bg={colorMode === 'dark' ? 'gray.800' : 'white'}
                  borderRadius="xl"
                  boxShadow="md"
                  height="100%"
                  borderWidth="1px"
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.100'}
                >
                  <CardBody p={5}>
                    <Heading size="md" mb={5}>标签预览</Heading>
                    
                    <VStack spacing={6} align="center" justify="center" height="350px">
                      <Box 
                        borderWidth="1px" 
                        borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'} 
                        borderRadius="md" 
                        p={4}
                        width="220px"
                      >
                        <VStack spacing={3}>
                          <Text fontSize="sm" fontWeight="bold" textAlign="center">公司名称</Text>
                          <Box bg="white" p={2} borderRadius="md">
                            <Image 
                              src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=A001-ThinkStation-P520" 
                              alt="Sample QR Code"
                              width="150px"
                              height="150px" 
                            />
                          </Box>
                          <Text fontSize="sm" textAlign="center">资产编号: A001</Text>
                          <Text fontSize="xs" textAlign="center">ThinkStation P520</Text>
                        </VStack>
                      </Box>
                      
                      <Text fontSize="sm" color="gray.500">
                        扫描二维码可查看资产详细信息
                      </Text>
                    </VStack>
                  </CardBody>
                </Card>
              </GridItem>
            </Grid>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  )
}

export default RegisterModule 