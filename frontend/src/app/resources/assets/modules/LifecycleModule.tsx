import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  HStack,
  IconButton,
  Input,
  Select,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  VStack,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { 
  PencilSimple, 
  Trash, 
  Plus, 
  ArrowClockwise, 
  CircleWavyCheck,
  Package,
  Clock,
  Wrench,
  Warning,
  X
} from '@phosphor-icons/react';

interface LifecycleEvent {
  id: string;
  assetId: string;
  assetName: string;
  eventType: string;
  status: string;
  date: string;
  notes: string;
}

const MOCK_LIFECYCLE_EVENTS: LifecycleEvent[] = [
  {
    id: '1',
    assetId: 'SRV-001',
    assetName: 'Dell PowerEdge R740',
    eventType: 'Procurement',
    status: 'Completed',
    date: '2023-01-15',
    notes: 'Server purchased from Dell'
  },
  {
    id: '2',
    assetId: 'SRV-001',
    assetName: 'Dell PowerEdge R740',
    eventType: 'Deployment',
    status: 'Completed',
    date: '2023-02-01',
    notes: 'Deployed to 3F Data Center'
  },
  {
    id: '3',
    assetId: 'SRV-001',
    assetName: 'Dell PowerEdge R740',
    eventType: 'Maintenance',
    status: 'Scheduled',
    date: '2023-08-15',
    notes: 'Routine maintenance check'
  },
  {
    id: '4',
    assetId: 'NAS-002',
    assetName: 'Synology DS1821+',
    eventType: 'Procurement',
    status: 'Completed',
    date: '2023-03-10',
    notes: 'NAS storage for backup'
  },
  {
    id: '5',
    assetId: 'NAS-002',
    assetName: 'Synology DS1821+',
    eventType: 'Issue',
    status: 'Resolved',
    date: '2023-06-22',
    notes: 'Drive failure in bay 3, replaced with WD Red Pro 8TB'
  }
];

const EVENT_TYPES = [
  'Procurement', 
  'Deployment',
  'Maintenance',
  'Repair',
  'Update',
  'Issue',
  'Transfer',
  'Decommission',
  'Disposal'
];

const STATUS_OPTIONS = [
  'Planned',
  'In Progress',
  'Completed',
  'Scheduled',
  'Pending',
  'Cancelled',
  'Delayed',
  'Failed',
  'Resolved'
];

const getEventIcon = (eventType: string) => {
  switch (eventType) {
    case 'Procurement':
      return <Package weight="fill" />;
    case 'Deployment':
      return <CircleWavyCheck weight="fill" />;
    case 'Maintenance':
      return <Wrench weight="fill" />;
    case 'Repair':
      return <Wrench weight="fill" />;
    case 'Update':
      return <ArrowClockwise weight="fill" />;
    case 'Issue':
      return <Warning weight="fill" />;
    case 'Transfer':
      return <ArrowClockwise weight="fill" />;
    case 'Decommission':
      return <X weight="fill" />;
    case 'Disposal':
      return <Trash weight="fill" />;
    default:
      return <Clock weight="fill" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Completed':
      return 'green.500';
    case 'In Progress':
      return 'blue.500';
    case 'Planned':
    case 'Scheduled':
      return 'purple.500';
    case 'Pending':
      return 'orange.500';
    case 'Cancelled':
    case 'Failed':
      return 'red.500';
    case 'Delayed':
      return 'yellow.500';
    case 'Resolved':
      return 'teal.500';
    default:
      return 'gray.500';
  }
};

const LifecycleModule: React.FC = () => {
  const [lifecycleEvents, setLifecycleEvents] = useState<LifecycleEvent[]>(MOCK_LIFECYCLE_EVENTS);
  const [newEvent, setNewEvent] = useState<Partial<LifecycleEvent>>({
    assetId: '',
    assetName: '',
    eventType: 'Procurement',
    status: 'Planned',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const toast = useToast();
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewEvent(prev => ({ ...prev, [name]: value }));
  };

  const handleAddEvent = () => {
    if (!newEvent.assetId || !newEvent.assetName || !newEvent.date) {
      toast({
        title: 'Required fields missing',
        description: 'Please fill in all required fields',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const eventToAdd: LifecycleEvent = {
      id: editingId || Date.now().toString(),
      assetId: newEvent.assetId || '',
      assetName: newEvent.assetName || '',
      eventType: newEvent.eventType || 'Procurement',
      status: newEvent.status || 'Planned',
      date: newEvent.date || new Date().toISOString().split('T')[0],
      notes: newEvent.notes || ''
    };

    if (editingId) {
      setLifecycleEvents(prev => prev.map(event => 
        event.id === editingId ? eventToAdd : event
      ));
      setEditingId(null);
      toast({
        title: 'Event updated',
        status: 'success',
        duration: 2000,
      });
    } else {
      setLifecycleEvents(prev => [...prev, eventToAdd]);
      toast({
        title: 'Event added',
        status: 'success',
        duration: 2000,
      });
    }

    setNewEvent({
      assetId: '',
      assetName: '',
      eventType: 'Procurement',
      status: 'Planned',
      date: new Date().toISOString().split('T')[0],
      notes: ''
    });
  };

  const handleEdit = (event: LifecycleEvent) => {
    setNewEvent(event);
    setEditingId(event.id);
  };

  const handleDelete = (id: string) => {
    setLifecycleEvents(prev => prev.filter(event => event.id !== id));
    toast({
      title: 'Event deleted',
      status: 'info',
      duration: 2000,
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setNewEvent({
      assetId: '',
      assetName: '',
      eventType: 'Procurement',
      status: 'Planned',
      date: new Date().toISOString().split('T')[0],
      notes: ''
    });
  };

  return (
    <Box p={4}>
      <Heading size="lg" mb={6}>Asset Lifecycle Management</Heading>
      
      <Card mb={6}>
        <CardHeader>
          <Heading size="md">{editingId ? 'Update Event' : 'Add New Lifecycle Event'}</Heading>
        </CardHeader>
        <CardBody>
          <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
            <VStack flex={1} spacing={4} align="flex-start">
              <FormControl isRequired>
                <FormLabel>Asset ID</FormLabel>
                <Input 
                  name="assetId" 
                  value={newEvent.assetId} 
                  onChange={handleInputChange} 
                  placeholder="e.g., SRV-001"
                />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>Asset Name</FormLabel>
                <Input 
                  name="assetName" 
                  value={newEvent.assetName} 
                  onChange={handleInputChange} 
                  placeholder="e.g., Dell PowerEdge R740"
                />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>Event Type</FormLabel>
                <Select 
                  name="eventType" 
                  value={newEvent.eventType} 
                  onChange={handleInputChange}
                >
                  {EVENT_TYPES.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </Select>
              </FormControl>
            </VStack>
            <VStack flex={1} spacing={4} align="flex-start">
              <FormControl isRequired>
                <FormLabel>Status</FormLabel>
                <Select 
                  name="status" 
                  value={newEvent.status} 
                  onChange={handleInputChange}
                >
                  {STATUS_OPTIONS.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </Select>
              </FormControl>
              <FormControl isRequired>
                <FormLabel>Date</FormLabel>
                <Input 
                  type="date" 
                  name="date" 
                  value={newEvent.date} 
                  onChange={handleInputChange} 
                />
              </FormControl>
              <FormControl>
                <FormLabel>Notes</FormLabel>
                <Input 
                  name="notes" 
                  value={newEvent.notes} 
                  onChange={handleInputChange} 
                  placeholder="Additional information..."
                />
              </FormControl>
            </VStack>
          </Flex>
          <Flex mt={6} justify="flex-end" gap={3}>
            {editingId && (
              <Button 
                leftIcon={<X />} 
                variant="outline" 
                onClick={handleCancelEdit}
              >
                Cancel
              </Button>
            )}
            <Button 
              leftIcon={editingId ? <PencilSimple /> : <Plus />} 
              colorScheme="blue" 
              onClick={handleAddEvent}
            >
              {editingId ? 'Update Event' : 'Add Event'}
            </Button>
          </Flex>
        </CardBody>
      </Card>

      <Card>
        <CardHeader>
          <Heading size="md">Lifecycle Events</Heading>
        </CardHeader>
        <CardBody>
          <Box overflowX="auto">
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Asset</Th>
                  <Th>Event Type</Th>
                  <Th>Status</Th>
                  <Th>Date</Th>
                  <Th>Notes</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {lifecycleEvents.map(event => (
                  <Tr key={event.id}>
                    <Td>
                      <VStack align="flex-start" spacing={1}>
                        <Text fontWeight="bold">{event.assetId}</Text>
                        <Text fontSize="sm" color="gray.600">{event.assetName}</Text>
                      </VStack>
                    </Td>
                    <Td>
                      <HStack>
                        <Box color={getStatusColor(event.status)}>
                          {getEventIcon(event.eventType)}
                        </Box>
                        <Text>{event.eventType}</Text>
                      </HStack>
                    </Td>
                    <Td>
                      <Text color={getStatusColor(event.status)} fontWeight="medium">
                        {event.status}
                      </Text>
                    </Td>
                    <Td>{event.date}</Td>
                    <Td>{event.notes}</Td>
                    <Td>
                      <HStack spacing={2}>
                        <IconButton
                          aria-label="Edit event"
                          icon={<PencilSimple />}
                          size="sm"
                          colorScheme="blue"
                          variant="ghost"
                          onClick={() => handleEdit(event)}
                        />
                        <IconButton
                          aria-label="Delete event"
                          icon={<Trash />}
                          size="sm"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => handleDelete(event.id)}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        </CardBody>
      </Card>
    </Box>
  );
};

export default LifecycleModule; 