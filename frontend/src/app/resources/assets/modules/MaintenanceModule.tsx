import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  HStack,
  IconButton,
  Input,
  Select,
  Table,
  Tbody,
  Td,
  Text,
  Textarea,
  Th,
  Thead,
  Tr,
  VStack,
  Badge,
  useDisclosure,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
} from '@chakra-ui/react';
import { 
  PencilSimple, 
  Trash, 
  Plus, 
  Wrench,
  Clock,
  CircleWavyCheck,
  Warning,
  X,
  CalendarCheck,
  CheckCircle,
  ClockCounterClockwise,
  ArrowClockwise
} from '@phosphor-icons/react';

interface MaintenanceRecord {
  id: string;
  assetId: string;
  assetName: string;
  maintenanceType: string;
  priority: string;
  status: string;
  description: string;
  scheduledDate: string;
  completedDate: string | null;
  assignedTo: string;
  cost: string;
  notes: string;
}

const MOCK_MAINTENANCE_RECORDS: MaintenanceRecord[] = [
  {
    id: '1',
    assetId: 'SRV-001',
    assetName: 'Dell PowerEdge R740',
    maintenanceType: '定期维护',
    priority: '中',
    status: '已计划',
    description: '服务器季度例行维护',
    scheduledDate: '2023-08-15',
    completedDate: null,
    assignedTo: '王工程师',
    cost: '0.00',
    notes: '检查硬件状况，更新固件，清理灰尘'
  },
  {
    id: '2',
    assetId: 'NAS-002',
    assetName: 'Synology DS1821+',
    maintenanceType: '修复',
    priority: '高',
    status: '已完成',
    description: '硬盘3号槽故障',
    scheduledDate: '2023-06-22',
    completedDate: '2023-06-22',
    assignedTo: '李工程师',
    cost: '2300.00',
    notes: '更换了故障硬盘，使用西数红盘8TB替换'
  },
  {
    id: '3',
    assetId: 'SW-003',
    assetName: 'Cisco Catalyst 9300',
    maintenanceType: '固件更新',
    priority: '低',
    status: '已计划',
    description: '定期固件更新',
    scheduledDate: '2023-09-10',
    completedDate: null,
    assignedTo: '张工程师',
    cost: '0.00',
    notes: '更新到最新的安全固件版本'
  },
  {
    id: '4',
    assetId: 'PC-045',
    assetName: 'ThinkPad X1 Carbon',
    maintenanceType: '修复',
    priority: '中',
    status: '进行中',
    description: 'SSD性能下降',
    scheduledDate: '2023-07-28',
    completedDate: null,
    assignedTo: '赵工程师',
    cost: '1200.00',
    notes: '评估更换SSD的可能性'
  }
];

const MAINTENANCE_TYPES = [
  '定期维护', 
  '修复',
  '预防性维护',
  '固件更新',
  '软件更新',
  '硬件更换',
  '性能优化',
  '安全加固'
];

const PRIORITY_LEVELS = [
  '低',
  '中',
  '高',
  '紧急'
];

const STATUS_OPTIONS = [
  '已计划',
  '进行中',
  '已完成',
  '已取消',
  '已延期'
];

const getStatusColor = (status: string) => {
  switch (status) {
    case '已完成':
      return 'green.500';
    case '进行中':
      return 'blue.500';
    case '已计划':
      return 'purple.500';
    case '已取消':
      return 'red.500';
    case '已延期':
      return 'yellow.500';
    default:
      return 'gray.500';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case '已完成':
      return <CheckCircle weight="fill" />;
    case '进行中':
      return <ArrowClockwise weight="fill" />;
    case '已计划':
      return <CalendarCheck weight="fill" />;
    case '已取消':
      return <X weight="fill" />;
    case '已延期':
      return <ClockCounterClockwise weight="fill" />;
    default:
      return <Clock weight="fill" />;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case '低':
      return 'green.500';
    case '中':
      return 'blue.500';
    case '高':
      return 'orange.500';
    case '紧急':
      return 'red.500';
    default:
      return 'gray.500';
  }
};

const MaintenanceModule: React.FC = () => {
  const [records, setRecords] = useState<MaintenanceRecord[]>(MOCK_MAINTENANCE_RECORDS);
  const [newRecord, setNewRecord] = useState<Partial<MaintenanceRecord>>({
    assetId: '',
    assetName: '',
    maintenanceType: '定期维护',
    priority: '中',
    status: '已计划',
    description: '',
    scheduledDate: new Date().toISOString().split('T')[0],
    completedDate: null,
    assignedTo: '',
    cost: '0.00',
    notes: ''
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [viewingRecord, setViewingRecord] = useState<MaintenanceRecord | null>(null);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewRecord(prev => ({ ...prev, [name]: value }));
  };

  const handleAddRecord = () => {
    if (!newRecord.assetId || !newRecord.assetName || !newRecord.description || !newRecord.scheduledDate) {
      toast({
        title: '缺少必填字段',
        description: '请填写所有必填字段',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const recordToAdd: MaintenanceRecord = {
      id: editingId || Date.now().toString(),
      assetId: newRecord.assetId || '',
      assetName: newRecord.assetName || '',
      maintenanceType: newRecord.maintenanceType || '定期维护',
      priority: newRecord.priority || '中',
      status: newRecord.status || '已计划',
      description: newRecord.description || '',
      scheduledDate: newRecord.scheduledDate || new Date().toISOString().split('T')[0],
      completedDate: newRecord.completedDate || null,
      assignedTo: newRecord.assignedTo || '',
      cost: newRecord.cost || '0.00',
      notes: newRecord.notes || ''
    };

    if (editingId) {
      setRecords(prev => prev.map(record => 
        record.id === editingId ? recordToAdd : record
      ));
      setEditingId(null);
      toast({
        title: '维护记录已更新',
        status: 'success',
        duration: 2000,
      });
    } else {
      setRecords(prev => [...prev, recordToAdd]);
      toast({
        title: '维护记录已添加',
        status: 'success',
        duration: 2000,
      });
    }

    resetForm();
  };

  const resetForm = () => {
    setNewRecord({
      assetId: '',
      assetName: '',
      maintenanceType: '定期维护',
      priority: '中',
      status: '已计划',
      description: '',
      scheduledDate: new Date().toISOString().split('T')[0],
      completedDate: null,
      assignedTo: '',
      cost: '0.00',
      notes: ''
    });
    setEditingId(null);
  };

  const handleEdit = (record: MaintenanceRecord) => {
    setNewRecord(record);
    setEditingId(record.id);
  };

  const handleDelete = (id: string) => {
    setRecords(prev => prev.filter(record => record.id !== id));
    toast({
      title: '维护记录已删除',
      status: 'info',
      duration: 2000,
    });
  };

  const handleView = (record: MaintenanceRecord) => {
    setViewingRecord(record);
    onOpen();
  };

  const handleCancelEdit = () => {
    resetForm();
  };

  const handleMarkComplete = (id: string) => {
    setRecords(prev => prev.map(record => {
      if (record.id === id) {
        return {
          ...record,
          status: '已完成',
          completedDate: new Date().toISOString().split('T')[0]
        };
      }
      return record;
    }));
    
    toast({
      title: '维护记录已标记为完成',
      status: 'success',
      duration: 2000,
    });
  };

  return (
    <Box p={4}>
      <Heading size="lg" mb={6}>资产维护与维修管理</Heading>
      
      <Card mb={6}>
        <CardHeader>
          <Heading size="md">{editingId ? '更新维护记录' : '添加维护记录'}</Heading>
        </CardHeader>
        <CardBody>
          <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
            <VStack flex={1} spacing={4} align="flex-start">
              <FormControl isRequired>
                <FormLabel>资产ID</FormLabel>
                <Input 
                  name="assetId" 
                  value={newRecord.assetId} 
                  onChange={handleInputChange} 
                  placeholder="例如 SRV-001"
                />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>资产名称</FormLabel>
                <Input 
                  name="assetName" 
                  value={newRecord.assetName} 
                  onChange={handleInputChange} 
                  placeholder="例如 Dell PowerEdge R740"
                />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>维护类型</FormLabel>
                <Select 
                  name="maintenanceType" 
                  value={newRecord.maintenanceType} 
                  onChange={handleInputChange}
                >
                  {MAINTENANCE_TYPES.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </Select>
              </FormControl>
              <FormControl isRequired>
                <FormLabel>优先级</FormLabel>
                <Select 
                  name="priority" 
                  value={newRecord.priority} 
                  onChange={handleInputChange}
                >
                  {PRIORITY_LEVELS.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </Select>
              </FormControl>
            </VStack>
            <VStack flex={1} spacing={4} align="flex-start">
              <FormControl isRequired>
                <FormLabel>状态</FormLabel>
                <Select 
                  name="status" 
                  value={newRecord.status} 
                  onChange={handleInputChange}
                >
                  {STATUS_OPTIONS.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </Select>
              </FormControl>
              <FormControl isRequired>
                <FormLabel>计划日期</FormLabel>
                <Input 
                  type="date" 
                  name="scheduledDate" 
                  value={newRecord.scheduledDate} 
                  onChange={handleInputChange} 
                />
              </FormControl>
              {(newRecord.status === '已完成' || newRecord.completedDate) && (
                <FormControl>
                  <FormLabel>完成日期</FormLabel>
                  <Input 
                    type="date" 
                    name="completedDate" 
                    value={newRecord.completedDate || ''} 
                    onChange={handleInputChange} 
                  />
                </FormControl>
              )}
              <FormControl>
                <FormLabel>分配给</FormLabel>
                <Input 
                  name="assignedTo" 
                  value={newRecord.assignedTo} 
                  onChange={handleInputChange} 
                  placeholder="负责人"
                />
              </FormControl>
              <FormControl>
                <FormLabel>预计费用 (元)</FormLabel>
                <Input 
                  name="cost" 
                  value={newRecord.cost} 
                  onChange={handleInputChange} 
                  placeholder="0.00"
                />
              </FormControl>
            </VStack>
          </Flex>

          <FormControl mt={4} isRequired>
            <FormLabel>问题描述</FormLabel>
            <Textarea
              name="description"
              value={newRecord.description}
              onChange={handleInputChange}
              placeholder="详细描述维护或修复问题"
              minH="100px"
            />
          </FormControl>
          
          <FormControl mt={4}>
            <FormLabel>备注</FormLabel>
            <Textarea
              name="notes"
              value={newRecord.notes}
              onChange={handleInputChange}
              placeholder="额外信息..."
              minH="80px"
            />
          </FormControl>
          
          <Flex mt={6} justify="flex-end" gap={3}>
            {editingId && (
              <Button 
                leftIcon={<X />} 
                variant="outline" 
                onClick={handleCancelEdit}
              >
                取消
              </Button>
            )}
            <Button 
              leftIcon={editingId ? <PencilSimple /> : <Plus />} 
              colorScheme="blue" 
              onClick={handleAddRecord}
            >
              {editingId ? '更新记录' : '添加记录'}
            </Button>
          </Flex>
        </CardBody>
      </Card>

      <Card>
        <CardHeader>
          <Heading size="md">维护记录</Heading>
        </CardHeader>
        <CardBody>
          <Box overflowX="auto">
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>资产</Th>
                  <Th>维护类型</Th>
                  <Th>优先级</Th>
                  <Th>状态</Th>
                  <Th>计划日期</Th>
                  <Th>负责人</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {records.map(record => (
                  <Tr key={record.id}>
                    <Td>
                      <VStack align="flex-start" spacing={1}>
                        <Text fontWeight="bold">{record.assetId}</Text>
                        <Text fontSize="sm" color="gray.600">{record.assetName}</Text>
                      </VStack>
                    </Td>
                    <Td>
                      <HStack>
                        <Box color="blue.500">
                          <Wrench weight="fill" />
                        </Box>
                        <Text>{record.maintenanceType}</Text>
                      </HStack>
                    </Td>
                    <Td>
                      <Badge colorScheme={
                        record.priority === '低' ? 'green' :
                        record.priority === '中' ? 'blue' :
                        record.priority === '高' ? 'orange' : 'red'
                      }>
                        {record.priority}
                      </Badge>
                    </Td>
                    <Td>
                      <HStack>
                        <Box color={getStatusColor(record.status)}>
                          {getStatusIcon(record.status)}
                        </Box>
                        <Text>{record.status}</Text>
                      </HStack>
                    </Td>
                    <Td>{record.scheduledDate}</Td>
                    <Td>{record.assignedTo}</Td>
                    <Td>
                      <HStack spacing={2}>
                        <IconButton
                          aria-label="查看详情"
                          icon={<CircleWavyCheck />}
                          size="sm"
                          colorScheme="teal"
                          variant="ghost"
                          onClick={() => handleView(record)}
                        />
                        {record.status !== '已完成' && (
                          <IconButton
                            aria-label="标记为完成"
                            icon={<CheckCircle />}
                            size="sm"
                            colorScheme="green"
                            variant="ghost"
                            onClick={() => handleMarkComplete(record.id)}
                          />
                        )}
                        <IconButton
                          aria-label="编辑记录"
                          icon={<PencilSimple />}
                          size="sm"
                          colorScheme="blue"
                          variant="ghost"
                          onClick={() => handleEdit(record)}
                        />
                        <IconButton
                          aria-label="删除记录"
                          icon={<Trash />}
                          size="sm"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => handleDelete(record.id)}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        </CardBody>
      </Card>

      {/* 维护记录详情弹窗 */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>维护记录详情</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {viewingRecord && (
              <VStack spacing={4} align="stretch">
                <HStack justify="space-between">
                  <Box>
                    <Text fontWeight="bold" fontSize="lg">{viewingRecord.assetName}</Text>
                    <Text color="gray.600">ID: {viewingRecord.assetId}</Text>
                  </Box>
                  <Badge 
                    colorScheme={
                      viewingRecord.status === '已完成' ? 'green' :
                      viewingRecord.status === '进行中' ? 'blue' :
                      viewingRecord.status === '已计划' ? 'purple' :
                      viewingRecord.status === '已取消' ? 'red' : 'yellow'
                    }
                    fontSize="md"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    {viewingRecord.status}
                  </Badge>
                </HStack>
                
                <Divider />
                
                <HStack>
                  <VStack flex={1} align="flex-start">
                    <Text color="gray.500" fontSize="sm">维护类型</Text>
                    <Text>{viewingRecord.maintenanceType}</Text>
                  </VStack>
                  <VStack flex={1} align="flex-start">
                    <Text color="gray.500" fontSize="sm">优先级</Text>
                    <Badge colorScheme={getPriorityColor(viewingRecord.priority) === 'red.500' ? 'red' : 
                                      getPriorityColor(viewingRecord.priority) === 'orange.500' ? 'orange' :
                                      getPriorityColor(viewingRecord.priority) === 'blue.500' ? 'blue' : 'green'}>
                      {viewingRecord.priority}
                    </Badge>
                  </VStack>
                </HStack>
                
                <HStack>
                  <VStack flex={1} align="flex-start">
                    <Text color="gray.500" fontSize="sm">计划日期</Text>
                    <Text>{viewingRecord.scheduledDate}</Text>
                  </VStack>
                  <VStack flex={1} align="flex-start">
                    <Text color="gray.500" fontSize="sm">完成日期</Text>
                    <Text>{viewingRecord.completedDate || '未完成'}</Text>
                  </VStack>
                </HStack>
                
                <HStack>
                  <VStack flex={1} align="flex-start">
                    <Text color="gray.500" fontSize="sm">负责人</Text>
                    <Text>{viewingRecord.assignedTo || '未指派'}</Text>
                  </VStack>
                  <VStack flex={1} align="flex-start">
                    <Text color="gray.500" fontSize="sm">费用</Text>
                    <Text>¥ {viewingRecord.cost}</Text>
                  </VStack>
                </HStack>
                
                <Box>
                  <Text color="gray.500" fontSize="sm">问题描述</Text>
                  <Box p={3} bg="gray.50" borderRadius="md" mt={1}>
                    <Text>{viewingRecord.description}</Text>
                  </Box>
                </Box>
                
                {viewingRecord.notes && (
                  <Box>
                    <Text color="gray.500" fontSize="sm">备注</Text>
                    <Box p={3} bg="gray.50" borderRadius="md" mt={1}>
                      <Text>{viewingRecord.notes}</Text>
                    </Box>
                  </Box>
                )}
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" onClick={onClose}>关闭</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default MaintenanceModule; 