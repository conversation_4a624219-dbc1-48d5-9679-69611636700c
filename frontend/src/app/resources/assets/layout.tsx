'use client'

import React, { useState, ReactNode } from 'react'
import {
  Box,
  Flex,
  VStack,
  HStack,
  Text,
  Icon,
  Divider,
  useColorMode,
  Badge,
  Button,
  Tooltip,
  Collapse,
  useDisclosure
} from '@chakra-ui/react'
import { useRouter, usePathname } from 'next/navigation'
import NextLink from 'next/link'
import {
  Package,
  ClipboardText,
  Plus,
  FolderNotch,
  Tag,
  Clock,
  Truck,
  Users,
  Wrench,
  Recycle,
  Eye,
  MagnifyingGlass,
  ChartLine,
  Bell,
  Shield,
  ShieldCheck,
  FileText,
  Notepad,
  DotsThreeOutline,
  ChartPieSlice,
  Gauge,
  PuzzlePiece,
  ShareNetwork,
  DeviceMobile,
  Cloud,
  CaretDown,
  CaretRight,
  IconProps
} from '@phosphor-icons/react'

interface MenuItemProps {
  icon: React.FC<IconProps>;
  label: string;
  active?: boolean;
  href?: string;
  children?: ReactNode;
  badge?: string | null;
  onClick?: (() => void) | null;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, label, active = false, href = "#", children = null, badge = null, onClick = null }) => {
  const { colorMode } = useColorMode()
  const { isOpen, onToggle } = useDisclosure()
  const hasChildren = children && React.Children.count(children) > 0
  
  const itemStyles = {
    py: 2,
    px: 4,
    borderRadius: "md",
    color: colorMode === 'dark' ? 'whiteAlpha.800' : 'gray.700',
    bg: active ? (colorMode === 'dark' ? 'blue.900' : 'blue.50') : 'transparent',
    fontWeight: active ? "semibold" : "medium",
    transition: "all 0.2s",
    cursor: "pointer",
    _hover: {
      bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'blue.50',
      color: colorMode === 'dark' ? 'white' : 'blue.600',
    }
  }
  
  const MenuContent = (
    <Flex
      as={hasChildren ? 'div' : (NextLink as any)} 
      href={hasChildren ? undefined : href}
      align="center"
      justify="space-between"
      onClick={hasChildren ? onToggle : onClick || undefined}
      {...itemStyles}
    >
      <HStack spacing={3}>
        <Icon as={icon} boxSize={5} color={active ? "blue.500" : "inherit"} />
        <Text>{label}</Text>
      </HStack>
      
      {badge && (
        <Badge borderRadius="full" colorScheme="blue" px={2}>
          {badge}
        </Badge>
      )}
      
      {hasChildren && (
        <Icon as={isOpen ? CaretDown : CaretRight} boxSize={4} ml={2} />
      )}
    </Flex>
  )
  
  return (
    <Box width="full">
      {MenuContent}
      
      {hasChildren && (
        <Collapse in={isOpen} animateOpacity>
          <VStack 
            align="stretch" 
            pl={6} 
            mt={1} 
            spacing={1}
            borderLeft="1px solid"
            borderColor={colorMode === 'dark' ? 'whiteAlpha.200' : 'gray.200'}
            ml={4}
          >
            {children}
          </VStack>
        </Collapse>
      )}
    </Box>
  )
}

interface SubMenuItemProps {
  icon: React.FC<IconProps>;
  label: string;
  active?: boolean;
  href?: string;
  onClick?: (() => void) | null;
}

const SubMenuItem: React.FC<SubMenuItemProps> = ({ icon, label, active = false, href = "#", onClick = null }) => {
  const { colorMode } = useColorMode()
  
  return (
    <Flex
      as={NextLink}
      href={href}
      py={2}
      px={3}
      borderRadius="md"
      align="center"
      color={active ? (colorMode === 'dark' ? 'blue.300' : 'blue.600') : (colorMode === 'dark' ? 'whiteAlpha.700' : 'gray.600')}
      fontWeight={active ? "medium" : "normal"}
      fontSize="sm"
      _hover={{
        bg: colorMode === 'dark' ? 'whiteAlpha.100' : 'gray.50',
        color: colorMode === 'dark' ? 'blue.300' : 'blue.600',
      }}
      onClick={onClick || undefined}
    >
      <Icon as={icon} boxSize={4} mr={2} />
      <Text>{label}</Text>
    </Flex>
  )
}

interface AssetLayoutProps {
  children: ReactNode;
}

const AssetLayout: React.FC<AssetLayoutProps> = ({ children }) => {
  const { colorMode } = useColorMode()
  const router = useRouter()
  const pathname = usePathname()
  const isActive = (path: string) => pathname?.includes(path) ?? false
  
  return (
    <Flex minHeight="100vh">
      {/* 侧边菜单 */}
      <Box
        width="280px"
        bg={colorMode === 'dark' ? 'gray.900' : 'white'}
        borderRight="1px solid"
        borderColor={colorMode === 'dark' ? 'gray.800' : 'gray.100'}
        position="fixed"
        left={0}
        top={0}
        bottom={0}
        zIndex={10}
        overflowY="auto"
        maxHeight="100vh"
        css={{
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            width: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: colorMode === 'dark' ? '#333' : '#CBD5E0',
            borderRadius: '24px',
          },
        }}
      >
        <VStack align="stretch" spacing={6} py={4}>
          {/* 头部 LOGO 区域 */}
          <HStack px={6} py={2}>
            <Icon as={Package} boxSize={7} color="blue.500" />
            <Text fontSize="xl" fontWeight="bold">资产清单</Text>
            
            <Box ml="auto">
              <Button
                size="sm"
                borderRadius="full"
                colorScheme="blue"
                boxShadow="sm"
                _hover={{ transform: 'translateY(-1px)', boxShadow: 'md' }}
              >
                核心
              </Button>
            </Box>
          </HStack>
          
          <Divider borderColor={colorMode === 'dark' ? 'gray.800' : 'gray.100'} />
          
          {/* 菜单项 */}
          <VStack 
            align="stretch" 
            spacing={1} 
            px={3}
            flex="1"
            overflowY="auto"
          >
            <MenuItem 
              icon={ClipboardText} 
              label="资产登记与分类" 
              active={isActive('assets/register')}
              children={[
                <SubMenuItem key="register" icon={Plus} label="资产录入" active={isActive('assets/register/add')} href="/resources/assets/register/add" />,
                <SubMenuItem key="category" icon={FolderNotch} label="分类管理" active={isActive('assets/register/categories')} href="/resources/assets/register/categories" />,
                <SubMenuItem key="tags" icon={Tag} label="标签管理" active={isActive('assets/register/tags')} href="/resources/assets/register/tags" />
              ]}
            />
            
            <MenuItem 
              icon={Clock} 
              label="资产全生命周期跟踪" 
              active={isActive('assets/lifecycle')}
              href="/resources/assets/lifecycle"
              children={[
                <SubMenuItem key="events" icon={Clock} label="生命周期事件" active={isActive('assets/lifecycle')} href="/resources/assets/lifecycle" />,
                <SubMenuItem key="purchase" icon={Truck} label="采购管理" active={isActive('assets/lifecycle/purchase')} href="/resources/assets/lifecycle/purchase" />,
                <SubMenuItem key="assignment" icon={Users} label="领用与分配" active={isActive('assets/lifecycle/assignment')} href="/resources/assets/lifecycle/assignment" />,
                <SubMenuItem key="maintenance" icon={Wrench} label="维护与维修" active={isActive('assets/lifecycle/maintenance')} href="/resources/assets/lifecycle/maintenance" />,
                <SubMenuItem key="retirement" icon={Recycle} label="报废与回收" active={isActive('assets/lifecycle/retirement')} href="/resources/assets/lifecycle/retirement" />
              ]}
            />
            
            <MenuItem 
              icon={Eye} 
              label="实时监控与自动化" 
              active={isActive('assets/monitor')}
              children={[
                <SubMenuItem key="discovery" icon={MagnifyingGlass} label="网络设备发现" active={isActive('assets/monitor/discovery')} href="/resources/assets/monitor/discovery" />,
                <SubMenuItem key="status" icon={ChartLine} label="状态监控" active={isActive('assets/monitor/status')} href="/resources/assets/monitor/status" />,
                <SubMenuItem key="alerts" icon={Bell} label="自动化告警" active={isActive('assets/monitor/alerts')} href="/resources/assets/monitor/alerts" />
              ]}
            />
            
            <MenuItem 
              icon={Shield} 
              label="权限与流程管理" 
              active={isActive('assets/permissions')}
              children={[
                <SubMenuItem key="permissions" icon={ShieldCheck} label="权限管理" active={isActive('assets/permissions/roles')} href="/resources/assets/permissions/roles" />,
                <SubMenuItem key="workflow" icon={FileText} label="审批流程" active={isActive('assets/permissions/workflow')} href="/resources/assets/permissions/workflow" />,
                <SubMenuItem key="logs" icon={Notepad} label="操作日志" active={isActive('assets/permissions/logs')} href="/resources/assets/permissions/logs" />
              ]}
            />
            
            <MenuItem 
              icon={ChartPieSlice} 
              label="报表与分析" 
              active={isActive('assets/reports')}
              children={[
                <SubMenuItem key="custom" icon={DotsThreeOutline} label="自定义报表" active={isActive('assets/reports/custom')} href="/resources/assets/reports/custom" />,
                <SubMenuItem key="dashboard" icon={Gauge} label="可视化看板" active={isActive('assets/reports/dashboard')} href="/resources/assets/reports/dashboard" />,
                <SubMenuItem key="compliance" icon={ClipboardText} label="合规报告" active={isActive('assets/reports/compliance')} href="/resources/assets/reports/compliance" />
              ]}
            />
            
            <MenuItem 
              icon={PuzzlePiece} 
              label="集成与扩展" 
              active={isActive('assets/integration')}
              children={[
                <SubMenuItem key="systems" icon={ShareNetwork} label="第三方系统集成" active={isActive('assets/integration/systems')} href="/resources/assets/integration/systems" />,
                <SubMenuItem key="api" icon={Cloud} label="API支持" active={isActive('assets/integration/api')} href="/resources/assets/integration/api" />,
                <SubMenuItem key="mobile" icon={DeviceMobile} label="移动端支持" active={isActive('assets/integration/mobile')} href="/resources/assets/integration/mobile" />
              ]}
            />
          </VStack>
          
          <Box px={6} mt="auto">
            <Divider borderColor={colorMode === 'dark' ? 'gray.800' : 'gray.100'} mb={4} />
            
            <Tooltip label="帮助与文档" placement="top">
              <Button
                size="sm"
                variant="outline"
                borderRadius="md"
                width="full"
                leftIcon={<Icon as={FileText} />}
                borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
                _hover={{
                  bg: colorMode === 'dark' ? 'gray.800' : 'gray.50',
                }}
              >
                系统帮助
              </Button>
            </Tooltip>
          </Box>
        </VStack>
      </Box>
      
      {/* 主内容区 */}
      <Box ml="280px" width="calc(100% - 280px)" position="relative">
        {children}
      </Box>
    </Flex>
  )
}

export default AssetLayout 