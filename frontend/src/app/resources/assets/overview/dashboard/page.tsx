'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { 
  Box, 
  Heading, 
  Text, 
  Button, 
  VStack, 
  HStack,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Badge,
  Icon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Spacer
} from '@chakra-ui/react'
import {
  ArrowLeft,
  Package,
  Desktop,
  WifiHigh,
  Monitor,
  Printer,
  TrendUp,
  TrendDown,
  Warning,
  CheckCircle,
  Clock,
  Wrench
} from '@phosphor-icons/react'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'

export default function AssetDashboardPage() {
  const router = useRouter()

  // 获取资产统计数据
  const { data: stats, isLoading } = useQuery({
    queryKey: ['asset-stats'],
    queryFn: async () => {
      const response = await fetch('/api/assets/stats')
      if (!response.ok) {
        throw new Error('Failed to fetch asset stats')
      }
      return response.json()
    }
  })

  // 模拟数据
  const mockStats = {
    total: 1247,
    active: 1156,
    inactive: 45,
    maintenance: 32,
    retired: 14,
    categories: {
      servers: 234,
      network: 156,
      storage: 89,
      security: 67,
      printers: 45,
      others: 656
    },
    trends: {
      newThisMonth: 23,
      retiredThisMonth: 8,
      maintenanceThisMonth: 15
    },
    alerts: {
      warrantyExpiring: 12,
      maintenanceDue: 8,
      securityUpdates: 5
    }
  }

  const data = stats || mockStats

  return (
    <Box p={6} maxW="1400px" mx="auto">
      {/* 面包屑导航 */}
      <Breadcrumb mb={6}>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets">
            资产管理
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets/overview">
            资产概览
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <BreadcrumbLink>资产仪表板</BreadcrumbLink>
        </BreadcrumbItem>
      </Breadcrumb>

      {/* 页面头部 */}
      <VStack align="start" spacing={4} mb={8}>
        <HStack spacing={4} justify="space-between" w="full">
          <HStack spacing={4}>
            <Button
              leftIcon={<Icon as={ArrowLeft} />}
              variant="outline"
              onClick={() => router.back()}
            >
              返回
            </Button>
            <VStack align="start" spacing={1}>
              <Heading size="lg" fontWeight="bold" bgGradient="linear(to-r, blue.400, teal.400)" bgClip="text">
                资产仪表板
              </Heading>
              <Text color="gray.500">
                资产概览和统计信息，实时监控资产状态和趋势
              </Text>
            </VStack>
          </HStack>
          <Button
            colorScheme="blue"
            onClick={() => router.push('/resources/assets/register/add')}
          >
            新增资产
          </Button>
        </HStack>
      </VStack>

      {/* 统计卡片 */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>总资产数</StatLabel>
              <StatNumber color="blue.500">{data.total}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                本月新增 {data.trends.newThisMonth}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>在用资产</StatLabel>
              <StatNumber color="green.500">{data.active}</StatNumber>
              <StatHelpText>
                占比 {((data.active / data.total) * 100).toFixed(1)}%
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>维护中</StatLabel>
              <StatNumber color="orange.500">{data.maintenance}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                本月 {data.trends.maintenanceThisMonth} 次
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Stat>
              <StatLabel>已退役</StatLabel>
              <StatNumber color="gray.500">{data.retired}</StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                本月 {data.trends.retiredThisMonth}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* 分类统计和告警 */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} mb={8}>
        {/* 资产分类统计 */}
        <Card>
          <CardHeader>
            <Heading size="md">资产分类统计</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4}>
              <HStack w="full" justify="space-between">
                <HStack>
                  <Icon as={Desktop} color="blue.500" />
                  <Text>服务器</Text>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Text fontWeight="bold">{data.categories.servers}</Text>
                  <Progress value={(data.categories.servers / data.total) * 100} size="sm" w="60px" colorScheme="blue" />
                </VStack>
              </HStack>

              <HStack w="full" justify="space-between">
                <HStack>
                  <Icon as={WifiHigh} color="green.500" />
                  <Text>网络设备</Text>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Text fontWeight="bold">{data.categories.network}</Text>
                  <Progress value={(data.categories.network / data.total) * 100} size="sm" w="60px" colorScheme="green" />
                </VStack>
              </HStack>

              <HStack w="full" justify="space-between">
                <HStack>
                  <Icon as={Package} color="purple.500" />
                  <Text>存储设备</Text>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Text fontWeight="bold">{data.categories.storage}</Text>
                  <Progress value={(data.categories.storage / data.total) * 100} size="sm" w="60px" colorScheme="purple" />
                </VStack>
              </HStack>

              <HStack w="full" justify="space-between">
                <HStack>
                  <Icon as={Monitor} color="orange.500" />
                  <Text>安全设备</Text>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Text fontWeight="bold">{data.categories.security}</Text>
                  <Progress value={(data.categories.security / data.total) * 100} size="sm" w="60px" colorScheme="orange" />
                </VStack>
              </HStack>

              <HStack w="full" justify="space-between">
                <HStack>
                  <Icon as={Printer} color="pink.500" />
                  <Text>打印设备</Text>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Text fontWeight="bold">{data.categories.printers}</Text>
                  <Progress value={(data.categories.printers / data.total) * 100} size="sm" w="60px" colorScheme="pink" />
                </VStack>
              </HStack>

              <HStack w="full" justify="space-between">
                <HStack>
                  <Icon as={Package} color="gray.500" />
                  <Text>其他设备</Text>
                </HStack>
                <VStack align="end" spacing={0}>
                  <Text fontWeight="bold">{data.categories.others}</Text>
                  <Progress value={(data.categories.others / data.total) * 100} size="sm" w="60px" colorScheme="gray" />
                </VStack>
              </HStack>
            </VStack>
          </CardBody>
        </Card>

        {/* 告警信息 */}
        <Card>
          <CardHeader>
            <Heading size="md">告警信息</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4}>
              <HStack w="full" justify="space-between" p={3} bg="red.50" borderRadius="md" border="1px" borderColor="red.200">
                <HStack>
                  <Icon as={Warning} color="red.500" />
                  <Text>保修即将到期</Text>
                </HStack>
                <Badge colorScheme="red">{data.alerts.warrantyExpiring}</Badge>
              </HStack>

              <HStack w="full" justify="space-between" p={3} bg="orange.50" borderRadius="md" border="1px" borderColor="orange.200">
                <HStack>
                  <Icon as={Wrench} color="orange.500" />
                  <Text>待维护设备</Text>
                </HStack>
                <Badge colorScheme="orange">{data.alerts.maintenanceDue}</Badge>
              </HStack>

              <HStack w="full" justify="space-between" p={3} bg="yellow.50" borderRadius="md" border="1px" borderColor="yellow.200">
                <HStack>
                  <Icon as={CheckCircle} color="yellow.500" />
                  <Text>安全更新</Text>
                </HStack>
                <Badge colorScheme="yellow">{data.alerts.securityUpdates}</Badge>
              </HStack>

              <Button w="full" variant="outline" colorScheme="blue" size="sm">
                查看所有告警
              </Button>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <Heading size="md">快速操作</Heading>
        </CardHeader>
        <CardBody>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
            <Button
              leftIcon={<Icon as={Package} />}
              colorScheme="blue"
              variant="outline"
              onClick={() => router.push('/resources/assets/register/add')}
            >
              新增资产
            </Button>
            <Button
              leftIcon={<Icon as={WifiHigh} />}
              colorScheme="green"
              variant="outline"
              onClick={() => router.push('/resources/assets/overview/list')}
            >
              资产列表
            </Button>
            <Button
              leftIcon={<Icon as={Wrench} />}
              colorScheme="orange"
              variant="outline"
              onClick={() => router.push('/resources/assets/maintenance')}
            >
              维护管理
            </Button>
            <Button
              leftIcon={<Icon as={TrendUp} />}
              colorScheme="purple"
              variant="outline"
              onClick={() => router.push('/resources/assets/reports')}
            >
              报表分析
            </Button>
          </SimpleGrid>
        </CardBody>
      </Card>
    </Box>
  )
}
