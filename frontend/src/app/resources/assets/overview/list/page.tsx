'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Box, 
  Heading, 
  Text, 
  Button, 
  VStack, 
  HStack,
  useToast,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Icon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  Input,
  Select,
  InputGroup,
  InputLeftElement,
  Flex,
  Spacer,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Spinner,
  Center
} from '@chakra-ui/react'
import {
  ArrowLeft,
  Plus,
  MagnifyingGlass,
  DotsThreeOutline,
  PencilSimple,
  Trash,
  Eye,
  Package,
  Desktop,
  WifiHigh,
  Monitor
} from '@phosphor-icons/react'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'

interface Asset {
  id: number
  name: string
  code: string
  category: {
    id: number
    name: string
  }
  status: string
  location?: string
  manufacturer?: string
  model?: string
  serial_number?: string
  created_at: string
}

export default function AssetListPage() {
  const router = useRouter()
  const toast = useToast()
  const { isOpen, onOpen, onClose } = useDisclosure()
  
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null)

  // 获取资产列表
  const { data: assets = [], isLoading, refetch } = useQuery({
    queryKey: ['assets', searchTerm, statusFilter, categoryFilter],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)
      if (categoryFilter) params.append('category_id', categoryFilter)
      
      const response = await fetch(`/api/assets?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to fetch assets')
      }
      return response.json()
    }
  })

  // 获取资产分类
  const { data: categories = [] } = useQuery({
    queryKey: ['asset-categories'],
    queryFn: async () => {
      const response = await fetch('/api/asset-categories')
      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }
      return response.json()
    }
  })

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'green'
      case 'inactive':
        return 'gray'
      case 'maintenance':
        return 'orange'
      case 'retired':
        return 'red'
      default:
        return 'gray'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return '在用'
      case 'inactive':
        return '闲置'
      case 'maintenance':
        return '维护中'
      case 'retired':
        return '已退役'
      default:
        return status || '未知'
    }
  }

  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName?.toLowerCase()
    if (name?.includes('服务器') || name?.includes('server')) return Desktop
    if (name?.includes('网络') || name?.includes('network')) return WifiHigh
    if (name?.includes('监控') || name?.includes('monitor')) return Monitor
    return Package
  }

  const handleViewAsset = (asset: Asset) => {
    setSelectedAsset(asset)
    onOpen()
  }

  const handleEditAsset = (asset: Asset) => {
    router.push(`/resources/assets/edit/${asset.id}`)
  }

  const handleDeleteAsset = async (asset: Asset) => {
    if (confirm(`确定要删除资产 "${asset.name}" 吗？`)) {
      try {
        const response = await fetch(`/api/assets/${asset.id}`, {
          method: 'DELETE'
        })
        
        if (response.ok) {
          toast({
            title: '成功',
            description: '资产删除成功',
            status: 'success',
            duration: 3000,
            isClosable: true,
          })
          refetch()
        } else {
          throw new Error('Failed to delete asset')
        }
      } catch (error) {
        toast({
          title: '错误',
          description: '资产删除失败',
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      }
    }
  }

  return (
    <Box p={6} maxW="1400px" mx="auto">
      {/* 面包屑导航 */}
      <Breadcrumb mb={6}>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets">
            资产管理
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets/overview">
            资产概览
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <BreadcrumbLink>资产列表</BreadcrumbLink>
        </BreadcrumbItem>
      </Breadcrumb>

      {/* 页面头部 */}
      <VStack align="start" spacing={4} mb={8}>
        <HStack spacing={4} justify="space-between" w="full">
          <HStack spacing={4}>
            <Button
              leftIcon={<Icon as={ArrowLeft} />}
              variant="outline"
              onClick={() => router.back()}
            >
              返回
            </Button>
            <VStack align="start" spacing={1}>
              <Heading size="lg" fontWeight="bold" bgGradient="linear(to-r, blue.400, teal.400)" bgClip="text">
                资产列表
              </Heading>
              <Text color="gray.500">
                查看和管理所有IT资产，支持搜索、筛选和批量操作
              </Text>
            </VStack>
          </HStack>
          <Button
            leftIcon={<Icon as={Plus} />}
            colorScheme="blue"
            onClick={() => router.push('/resources/assets/register/add')}
          >
            新增资产
          </Button>
        </HStack>
      </VStack>

      {/* 搜索和筛选 */}
      <Box bg="white" p={4} borderRadius="lg" shadow="sm" mb={6}>
        <Flex gap={4} wrap="wrap">
          <InputGroup maxW="300px">
            <InputLeftElement>
              <Icon as={MagnifyingGlass} color="gray.400" />
            </InputLeftElement>
            <Input
              placeholder="搜索资产名称、编码或序列号..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
          
          <Select
            placeholder="选择状态"
            maxW="150px"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="active">在用</option>
            <option value="inactive">闲置</option>
            <option value="maintenance">维护中</option>
            <option value="retired">已退役</option>
          </Select>
          
          <Select
            placeholder="选择分类"
            maxW="200px"
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
          >
            {categories.map((category: any) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </Select>
          
          <Spacer />
          
          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm('')
              setStatusFilter('')
              setCategoryFilter('')
            }}
          >
            清除筛选
          </Button>
        </Flex>
      </Box>

      {/* 资产列表 */}
      <Box bg="white" borderRadius="lg" shadow="sm" overflow="hidden">
        {isLoading ? (
          <Center p={8}>
            <Spinner size="lg" />
          </Center>
        ) : (
          <Table variant="simple">
            <Thead bg="gray.50">
              <Tr>
                <Th>资产信息</Th>
                <Th>分类</Th>
                <Th>状态</Th>
                <Th>位置</Th>
                <Th>制造商</Th>
                <Th>型号</Th>
                <Th>序列号</Th>
                <Th>创建时间</Th>
                <Th>操作</Th>
              </Tr>
            </Thead>
            <Tbody>
              {assets.map((asset: Asset) => (
                <Tr key={asset.id} _hover={{ bg: 'gray.50' }}>
                  <Td>
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="medium">{asset.name}</Text>
                      <Text fontSize="sm" color="gray.500" fontFamily="mono">
                        {asset.code}
                      </Text>
                    </VStack>
                  </Td>
                  <Td>
                    <HStack>
                      <Icon as={getCategoryIcon(asset.category?.name)} color="blue.500" />
                      <Text>{asset.category?.name}</Text>
                    </HStack>
                  </Td>
                  <Td>
                    <Badge colorScheme={getStatusColor(asset.status)}>
                      {getStatusLabel(asset.status)}
                    </Badge>
                  </Td>
                  <Td>
                    <Text noOfLines={1} maxW="120px">
                      {asset.location || '-'}
                    </Text>
                  </Td>
                  <Td>
                    <Text noOfLines={1} maxW="100px">
                      {asset.manufacturer || '-'}
                    </Text>
                  </Td>
                  <Td>
                    <Text noOfLines={1} maxW="120px">
                      {asset.model || '-'}
                    </Text>
                  </Td>
                  <Td>
                    <Text fontSize="sm" fontFamily="mono" noOfLines={1} maxW="100px">
                      {asset.serial_number || '-'}
                    </Text>
                  </Td>
                  <Td>
                    <Text fontSize="sm">
                      {new Date(asset.created_at).toLocaleDateString('zh-CN')}
                    </Text>
                  </Td>
                  <Td>
                    <Menu>
                      <MenuButton
                        as={IconButton}
                        icon={<Icon as={DotsThreeOutline} />}
                        variant="outline"
                        size="sm"
                      />
                      <MenuList>
                        <MenuItem
                          icon={<Icon as={Eye} />}
                          onClick={() => handleViewAsset(asset)}
                        >
                          查看详情
                        </MenuItem>
                        <MenuItem
                          icon={<Icon as={PencilSimple} />}
                          onClick={() => handleEditAsset(asset)}
                        >
                          编辑
                        </MenuItem>
                        <MenuItem
                          icon={<Icon as={Trash} />}
                          color="red.500"
                          onClick={() => handleDeleteAsset(asset)}
                        >
                          删除
                        </MenuItem>
                      </MenuList>
                    </Menu>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        )}
        
        {!isLoading && assets.length === 0 && (
          <Center p={8}>
            <VStack spacing={4}>
              <Icon as={Package} size={48} color="gray.400" />
              <Text color="gray.500">暂无资产数据</Text>
              <Button
                colorScheme="blue"
                onClick={() => router.push('/resources/assets/register/add')}
              >
                添加第一个资产
              </Button>
            </VStack>
          </Center>
        )}
      </Box>

      {/* 资产详情模态框 */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>资产详情</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {selectedAsset && (
              <VStack spacing={4} align="stretch">
                <HStack justify="space-between">
                  <Text fontWeight="bold">资产名称:</Text>
                  <Text>{selectedAsset.name}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">资产编码:</Text>
                  <Text fontFamily="mono">{selectedAsset.code}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">分类:</Text>
                  <Text>{selectedAsset.category?.name}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">状态:</Text>
                  <Badge colorScheme={getStatusColor(selectedAsset.status)}>
                    {getStatusLabel(selectedAsset.status)}
                  </Badge>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">位置:</Text>
                  <Text>{selectedAsset.location || '-'}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">制造商:</Text>
                  <Text>{selectedAsset.manufacturer || '-'}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">型号:</Text>
                  <Text>{selectedAsset.model || '-'}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">序列号:</Text>
                  <Text fontFamily="mono">{selectedAsset.serial_number || '-'}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontWeight="bold">创建时间:</Text>
                  <Text>{new Date(selectedAsset.created_at).toLocaleString('zh-CN')}</Text>
                </HStack>
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  )
}
