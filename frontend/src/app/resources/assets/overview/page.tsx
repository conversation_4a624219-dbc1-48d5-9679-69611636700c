'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import {
  Box,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  VStack,
  HStack,
  Icon,
  Button,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  useColorModeValue
} from '@chakra-ui/react'
import {
  ArrowLeft,
  ChartPieSlice,
  ClipboardText,
  ListBullets,
  Wrench,
  ShoppingCart,
  FileText,
  Shield,
  Package,
  Gauge
} from '@phosphor-icons/react'
import Link from 'next/link'

interface FeatureCardProps {
  title: string
  description: string
  icon: React.ElementType
  color: string
  href: string
  onClick?: () => void
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  color,
  href,
  onClick
}) => {
  const router = useRouter()
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.600')

  const handleClick = () => {
    console.log('Card clicked:', title, 'href:', href)
    if (onClick) {
      onClick()
    } else {
      console.log('Navigating to:', href)
      router.push(href)
    }
  }

  return (
    <Card
      bg={bgColor}
      borderColor={borderColor}
      borderWidth="2px"
      cursor="pointer"
      transition="all 0.2s"
      _hover={{
        transform: 'translateY(-2px)',
        shadow: 'lg',
        borderColor: color
      }}
      onClick={handleClick}
    >
      <CardBody>
        <VStack spacing={4} align="start">
          <HStack spacing={3}>
            <Box
              p={3}
              borderRadius="lg"
              bg={`${color}.100`}
              color={`${color}.600`}
            >
              <Icon as={icon} boxSize={6} />
            </Box>
            <VStack align="start" spacing={1}>
              <Heading size="md" color={`${color}.600`}>
                {title}
              </Heading>
              <Text fontSize="sm" color="gray.500">
                {description}
              </Text>
            </VStack>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}

const AssetOverviewPage = () => {
  const router = useRouter()

  const features = [
    {
      title: '资产仪表盘',
      description: '查看资产统计和趋势可视化',
      icon: ChartPieSlice,
      color: 'blue',
      href: '/resources/assets/overview/dashboard'
    },
    {
      title: '资产分类',
      description: '管理资产分类和标签',
      icon: Gauge,
      color: 'teal'
    },
    {
      title: '资产登记',
      description: '添加新资产到系统',
      icon: ClipboardText,
      color: 'green',
      href: '/resources/assets/register'
    },
    {
      title: '设备列表',
      description: '查看和管理所有设备',
      icon: ListBullets,
      color: 'orange',
      href: '/resources/assets/overview/list'
    },
    {
      title: '维护管理',
      description: '设备维护和维修记录',
      icon: Wrench,
      color: 'purple',
      href: '/resources/assets/lifecycle/maintenance'
    },
    {
      title: '系统集成',
      description: '管理外部系统集成',
      icon: Package,
      color: 'orange'
    },
    {
      title: '安全审计',
      description: '查看资产操作日志和审计记录',
      icon: Shield,
      color: 'red'
    },
    {
      title: '资产采购',
      description: '管理资产采购流程',
      icon: ShoppingCart,
      color: 'blue'
    },
    {
      title: '资产盘点',
      description: '定期资产盘点与核查',
      icon: Package,
      color: 'teal'
    },
    {
      title: '权限管理',
      description: '用户与角色权限分配',
      icon: Shield,
      color: 'purple'
    },
    {
      title: '资产报废',
      description: '管理资产报废流程',
      icon: FileText,
      color: 'red'
    }
  ]

  return (
    <Box p={8}>
      {/* 面包屑导航 */}
      <Breadcrumb mb={6}>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources">
            资源管理
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} href="/resources/assets">
            资产管理
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <BreadcrumbLink>资产管理系统</BreadcrumbLink>
        </BreadcrumbItem>
      </Breadcrumb>

      {/* 页面头部 */}
      <VStack align="start" spacing={4} mb={8}>
        <HStack spacing={4} justify="space-between" w="full">
          <HStack spacing={4}>
            <Button
              leftIcon={<Icon as={ArrowLeft} />}
              variant="outline"
              onClick={() => router.back()}
            >
              返回
            </Button>
            <VStack align="start" spacing={1}>
              <Heading size="lg" fontWeight="bold" bgGradient="linear(to-r, blue.400, teal.400)" bgClip="text">
                资产管理系统
              </Heading>
              <Text color="gray.500">
                全面的资产生命周期管理平台
              </Text>
            </VStack>
          </HStack>
        </HStack>
      </VStack>

      {/* 功能卡片网格 */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
        {features.map((feature, index) => (
          <FeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
            color={feature.color}
            href={feature.href || '#'}
          />
        ))}
      </SimpleGrid>
    </Box>
  )
}

export default AssetOverviewPage
