import { NextResponse } from 'next/server'
import { Asset } from '@/types/asset'

// 模拟数据库查询函数
const query = async (sql: string, params?: any[]) => {
  console.log('模拟数据库查询:', sql, params)
  return [
    {
      id: '1',
      name: '测试资产',
      type: 'server',
      ipAddress: '***********',
      macAddress: '00:11:22:33:44:55',
      location: '机房A',
      description: '测试描述',
      monitoring_enabled: true,
      monitoring_type: 'snmp'
    }
  ]
}

export async function GET(request: Request) {
  try {
    // 从URL中获取ID
    const url = new URL(request.url)
    const id = url.pathname.split('/').pop() || '1'

    const asset = await query(
      `SELECT a.*,
              m.enabled as monitoring_enabled,
              m.type as monitoring_type
       FROM assets a
       LEFT JOIN monitored_devices m ON a.id = m.asset_id
       WHERE a.id = $1`,
      [id]
    )

    if (asset.length === 0) {
      return NextResponse.json(
        { error: '找不到该资产' },
        { status: 404 }
      )
    }

    return NextResponse.json(asset[0])
  } catch (err) {
    return NextResponse.json(
      { error: '获取资产信息失败' },
      { status: 500 }
    )
  }
}

export async function PUT(request: Request) {
  try {
    // 从URL中获取ID
    const url = new URL(request.url)
    const id = url.pathname.split('/').pop() || '1'

    const assetData: Asset = await request.json()

    // 更新资产基础信息
    await query(
      `UPDATE assets SET
        name = $1,
        type = $2,
        ip_address = $3,
        mac_address = $4,
        location = $5,
        description = $6,
        updated_at = NOW()
       WHERE id = $7`,
      [
        assetData.name,
        assetData.type,
        assetData.ipAddress,
        assetData.macAddress,
        assetData.location,
        assetData.description,
        id
      ]
    )

    // 更新监控配置
    if (assetData.monitoringConfig) {
      // 先删除旧的监控配置
      await query(
        `DELETE FROM monitored_devices WHERE asset_id = $1`,
        [id]
      )

      // 如果有新的监控配置，插入新的
      if (assetData.monitoringConfig.enabled && assetData.monitoringConfig.type !== 'none') {
        const config = assetData.monitoringConfig.config

        if (config && assetData.monitoringConfig.type === 'snmp') {
          const snmpConfig = config as any
          await query(
            `INSERT INTO monitored_devices
            (asset_id, host, type, community, oids, interval)
            VALUES ($1, $2, 'snmp', $3, $4, $5)`,
            [
              id,
              assetData.ipAddress,
              snmpConfig.community,
              snmpConfig.oids,
              snmpConfig.interval
            ]
          )
        } else if (config) {
          const sshConfig = config as any
          await query(
            `INSERT INTO monitored_devices
            (asset_id, host, type, username, password, commands, interval, port)
            VALUES ($1, $2, 'ssh', $3, $4, $5, $6, $7)`,
            [
              id,
              assetData.ipAddress,
              sshConfig.username,
              sshConfig.password,
              sshConfig.commands,
              sshConfig.interval,
              sshConfig.port || 22
            ]
          )
        }
      }
    }

    return NextResponse.json({ success: true })
  } catch (err) {
    return NextResponse.json(
      { error: '更新资产失败' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request) {
  try {
    // 从URL中获取ID
    const url = new URL(request.url)
    const id = url.pathname.split('/').pop() || '1'

    // 先删除监控配置
    await query(
      `DELETE FROM monitored_devices WHERE asset_id = $1`,
      [id]
    )

    // 再删除资产
    await query(
      `DELETE FROM assets WHERE id = $1`,
      [id]
    )

    return NextResponse.json({ success: true })
  } catch (err) {
    return NextResponse.json(
      { error: '删除资产失败' },
      { status: 500 }
    )
  }
}