import { NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://10.10.163.3:8001'

// 获取资产列表
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()

    const response = await fetch(`${BACKEND_URL}/api/assets${queryString ? `?${queryString}` : ''}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching assets:', error)
    return NextResponse.json(
      { error: '获取资产列表失败' },
      { status: 500 }
    )
  }
}

// 创建新资产
export async function POST(request: Request) {
  try {
    const assetData = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/assets`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(assetData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      return NextResponse.json(
        { error: errorData.detail || '创建资产失败' },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error creating asset:', error)
    return NextResponse.json(
      { error: '创建资产失败' },
      { status: 500 }
    )
  }
}