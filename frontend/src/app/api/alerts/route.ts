import { query } from '@/lib/db'
import { NextResponse } from 'next/server'
import { AlertRule, AlertLog } from '@/types/alert'

// 获取报警规则列表
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const deviceId = searchParams.get('deviceId')
  
  try {
    let queryStr = 'SELECT * FROM alert_rules'
    const params: any[] = []
    
    if (deviceId) {
      queryStr += ' WHERE device_id = $1'
      params.push(deviceId)
    }
    
    queryStr += ' ORDER BY created_at DESC'
    
    const rules = await query(queryStr, params)
    return NextResponse.json(rules)
  } catch (err) {
    return NextResponse.json(
      { error: '获取报警规则失败' },
      { status: 500 }
    )
  }
}

// 创建或更新报警规则
export async function POST(request: Request) {
  try {
    const ruleData: AlertRule = await request.json()
    
    if (ruleData.id) {
      // 更新现有规则
      await query(
        `UPDATE alert_rules SET
          name = $1,
          condition = $2,
          threshold = $3,
          severity = $4,
          is_active = $5,
          updated_at = NOW()
         WHERE id = $6`,
        [
          ruleData.name,
          ruleData.condition,
          ruleData.threshold,
          ruleData.severity,
          ruleData.isActive,
          ruleData.id
        ]
      )
    } else {
      // 创建新规则
      await query(
        `INSERT INTO alert_rules 
        (name, device_id, device_type, metric, condition, threshold, severity, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        [
          ruleData.name,
          ruleData.deviceId,
          ruleData.deviceType,
          ruleData.metric,
          ruleData.condition,
          ruleData.threshold,
          ruleData.severity,
          ruleData.isActive
        ]
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (err) {
    return NextResponse.json(
      { error: '保存报警规则失败' },
      { status: 500 }
    )
  }
}