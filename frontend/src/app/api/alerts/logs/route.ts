import { query } from '@/lib/db'
import { NextResponse } from 'next/server'
import { NotificationService } from '@/services/notificationService'

// 获取报警日志列表
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const deviceId = searchParams.get('deviceId')
  
  try {
    let queryStr = `
      SELECT al.*, ar.name as rule_name, ar.metric
      FROM alert_logs al
      JOIN alert_rules ar ON al.rule_id = ar.id
    `
    const params: any[] = []
    
    if (deviceId) {
      queryStr += ' WHERE ar.device_id = $1'
      params.push(deviceId)
    }
    
    queryStr += ' ORDER BY al.triggered_at DESC'
    
    const logs = await query(queryStr, params)
    return NextResponse.json(logs)
  } catch (err) {
    return NextResponse.json(
      { error: '获取报警日志失败' },
      { status: 500 }
    )
  }
}

// 创建新的报警日志
export async function POST(request: Request) {
  try {
    const data = await request.json()
    
    const result = await query(
      `INSERT INTO alert_logs 
      (rule_id, device_id, metric, value, severity, triggered_at, status) 
      VALUES ($1, $2, $3, $4, $5, NOW(), 'open')
      RETURNING id`,
      [
        data.ruleId,
        data.deviceId,
        data.metric,
        data.value,
        data.severity
      ]
    )
    
    // 获取设备信息
    const device = await query(
      `SELECT a.name as device_name, a.ip_address as device_ip, a.type as device_type,
              ar.name as rule_name, ar.threshold
       FROM assets a
       JOIN alert_rules ar ON ar.device_id = a.id
       WHERE ar.id = $1`,
      [data.ruleId]
    )
    
    const alertLog = {
      id: result[0].id,
      ...data,
      ruleName: device[0]?.rule_name,
      threshold: device[0]?.threshold,
      deviceName: device[0]?.device_name,
      deviceIp: device[0]?.device_ip,
      deviceType: device[0]?.device_type,
      triggeredAt: new Date(),
      status: 'open'
    }
    
    // 异步发送通知
    NotificationService.sendAlertNotification(alertLog)
    
    return NextResponse.json({ id: result[0].id })
  } catch (err) {
    return NextResponse.json(
      { error: '创建报警日志失败' },
      { status: 500 }
    )
  }
}