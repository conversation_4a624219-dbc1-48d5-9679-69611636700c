import { NextResponse } from 'next/server'

// 模拟数据库查询函数
const query = async (sql: string, params?: any[]) => {
  console.log('模拟数据库查询:', sql, params)
  return { rowCount: 1 }
}

// 将报警标记为已解决
export async function POST(request: Request) {
  try {
    // 在实际应用中，我们会从URL中获取ID
    // 这里简单模拟一下
    const id = '123'

    await query(
      `UPDATE alert_logs
       SET status = 'resolved', resolved_at = NOW()
       WHERE id = $1`,
      [id]
    )

    return NextResponse.json({ success: true })
  } catch (err) {
    return NextResponse.json(
      { error: '更新报警状态失败' },
      { status: 500 }
    )
  }
}