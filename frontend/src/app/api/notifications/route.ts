import { query } from '@/lib/db'
import { NextResponse } from 'next/server'
import { NotificationChannel } from '@/types/notification'

export async function GET() {
  try {
    const channels = await query('SELECT * FROM notification_channels ORDER BY created_at DESC')
    return NextResponse.json(channels)
  } catch (err) {
    return NextResponse.json(
      { error: '获取通知渠道失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const channelData: NotificationChannel = await request.json()
    
    const result = await query(
      `INSERT INTO notification_channels 
      (name, type, config, is_active)
      VALUES ($1, $2, $3, $4)
      RETURNING id`,
      [
        channelData.name,
        channelData.type,
        JSON.stringify(channelData.config),
        channelData.isActive
      ]
    )
    
    return NextResponse.json({ id: result[0].id })
  } catch (err) {
    return NextResponse.json(
      { error: '创建通知渠道失败' },
      { status: 500 }
    )
  }
}