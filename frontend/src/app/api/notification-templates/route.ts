import { query } from '@/lib/db'
import { NextResponse } from 'next/server'
import { NotificationTemplate } from '@/types/notificationTemplate'

export async function GET() {
  try {
    const templates = await query(
      'SELECT * FROM notification_templates ORDER BY is_default DESC, created_at DESC'
    )
    return NextResponse.json(templates)
  } catch (err) {
    return NextResponse.json(
      { error: '获取通知模板失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const templateData: NotificationTemplate = await request.json()
    
    // 如果设置为默认模板，先取消同类型的其他默认模板
    if (templateData.isDefault) {
      await query(
        `UPDATE notification_templates 
         SET is_default = false 
         WHERE type = $1`,
        [templateData.type]
      )
    }
    
    const result = await query(
      `INSERT INTO notification_templates 
      (name, type, subject, content, variables, is_default)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id`,
      [
        templateData.name,
        templateData.type,
        templateData.subject,
        templateData.content,
        JSON.stringify(templateData.variables),
        templateData.isDefault
      ]
    )
    
    return NextResponse.json({ id: result[0].id })
  } catch (err) {
    return NextResponse.json(
      { error: '创建通知模板失败' },
      { status: 500 }
    )
  }
}