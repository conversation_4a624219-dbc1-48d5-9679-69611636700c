import { NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://10.10.163.3:8001'

// 获取资产分类列表
export async function GET() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/asset-categories`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.status}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching asset categories:', error)
    return NextResponse.json(
      { error: '获取资产分类失败' },
      { status: 500 }
    )
  }
}

// 创建新的资产分类
export async function POST(request: Request) {
  try {
    const categoryData = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/asset-categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryData),
    })

    if (!response.ok) {
      const errorData = await response.json()
      return NextResponse.json(
        { error: errorData.detail || '创建资产分类失败' },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error creating asset category:', error)
    return NextResponse.json(
      { error: '创建资产分类失败' },
      { status: 500 }
    )
  }
}