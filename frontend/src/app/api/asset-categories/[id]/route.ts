import { NextResponse } from 'next/server'
import { query } from '@/lib/db'

// 获取特定资产分类
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const categoryId = params.id
    
    const result = await query(
      `SELECT id, name, code, description, level, parent_id, attributes, is_system, created_at, updated_at
       FROM asset_categories
       WHERE id = $1`,
      [categoryId]
    )
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: '找不到该资产分类' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(result[0])
  } catch (err) {
    console.error('获取资产分类失败:', err)
    return NextResponse.json(
      { error: '获取资产分类失败' },
      { status: 500 }
    )
  }
}

// 更新资产分类
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const categoryId = params.id
    const categoryData = await request.json()
    
    const result = await query(
      `UPDATE asset_categories SET
        name = $1,
        code = $2,
        description = $3,
        level = $4,
        parent_id = $5,
        attributes = $6,
        is_system = $7,
        updated_at = NOW()
       WHERE id = $8
       RETURNING *`,
      [
        categoryData.name,
        categoryData.code,
        categoryData.description,
        categoryData.level,
        categoryData.parent_id,
        JSON.stringify(categoryData.attributes || {}),
        categoryData.is_system,
        categoryId
      ]
    )
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: '找不到该资产分类' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(result[0])
  } catch (err) {
    console.error('更新资产分类失败:', err)
    return NextResponse.json(
      { error: '更新资产分类失败' },
      { status: 500 }
    )
  }
}

// 删除资产分类
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const categoryId = params.id
    
    // 检查是否有子分类
    const childrenResult = await query(
      'SELECT id FROM asset_categories WHERE parent_id = $1',
      [categoryId]
    )
    
    if (childrenResult.length > 0) {
      return NextResponse.json(
        { error: '无法删除分类，因为存在子分类' },
        { status: 400 }
      )
    }
    
    // 检查是否有资产使用此分类
    const assetsResult = await query(
      'SELECT id FROM assets WHERE category_id = $1',
      [categoryId]
    )
    
    if (assetsResult.length > 0) {
      return NextResponse.json(
        { error: '无法删除分类，因为有资产正在使用此分类' },
        { status: 400 }
      )
    }
    
    const result = await query(
      'DELETE FROM asset_categories WHERE id = $1 RETURNING id',
      [categoryId]
    )
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: '找不到该资产分类' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ message: '删除成功' })
  } catch (err) {
    console.error('删除资产分类失败:', err)
    return NextResponse.json(
      { error: '删除资产分类失败' },
      { status: 500 }
    )
  }
}