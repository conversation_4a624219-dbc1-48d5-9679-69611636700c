import { query } from '@/lib/db'
import { NextResponse } from 'next/server'

// 获取监控设备列表
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const assetId = searchParams.get('assetId')
  
  try {
    let queryStr = `
      SELECT m.*, a.name as asset_name, a.type as asset_type
      FROM monitored_devices m
      JOIN assets a ON m.asset_id = a.id
    `
    const params: any[] = []
    
    if (assetId) {
      queryStr += ' WHERE m.asset_id = $1'
      params.push(assetId)
    }
    
    queryStr += ' ORDER BY m.created_at DESC'
    
    const devices = await query(queryStr, params)
    return NextResponse.json(devices)
  } catch (err) {
    return NextResponse.json(
      { error: '获取监控设备失败' },
      { status: 500 }
    )
  }
}

// 获取监控数据
export async function POST(request: Request) {
  const { deviceId, type, limit = 100 } = await request.json()
  
  try {
    let data
    if (type === 'snmp') {
      data = await query(
        `SELECT * FROM snmp_data 
        WHERE device_id = $1 
        ORDER BY timestamp DESC 
        LIMIT $2`,
        [deviceId, limit]
      )
    } else {
      data = await query(
        `SELECT * FROM ssh_data 
        WHERE device_id = $1 
        ORDER BY timestamp DESC 
        LIMIT $2`,
        [deviceId, limit]
      )
    }
    
    return NextResponse.json(data)
  } catch (err) {
    return NextResponse.json(
      { error: '获取监控数据失败' },
      { status: 500 }
    )
  }
}