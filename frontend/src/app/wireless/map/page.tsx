'use client';

import { useEffect, useState } from 'react';
import { Box, Container, Heading } from '@chakra-ui/react';
import WirelessMapPage from '@/components/Wireless/WirelessMapPage';
import { useTranslation } from '@/i18n/client';

export default function WirelessMapRoutePage() {
  const [isClient, setIsClient] = useState(false);
  const { t } = useTranslation();

  // This ensures that client-side code only runs after hydration
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return (
    <Container maxW="container.xl" py={5}>
      <Heading mb={6}>{t('wireless.map')}</Heading>
      {isClient && (
        <Box h="calc(100vh - 200px)" w="100%">
          <WirelessMapPage />
        </Box>
      )}
    </Container>
  );
} 