'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Flex,
  Icon,
  Button,
  Divider,
  Badge,
  Progress,
  HStack,
  VStack,
  useColorModeValue,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Grid,
  GridItem
} from '@chakra-ui/react'
import {
  Lightning,
  ArrowRight,
  ArrowsClockwise,
  ChartLine,
  DotsThreeOutlineVertical,
  Warning,
  CheckCircle,
  Clock,
  Gauge,
  Plug,
  BatteryFull,
  Sliders,
  DeviceTablet
} from '@phosphor-icons/react'
import Link from 'next/link'

// UPS 数据类型
interface UPSData {
  id: string;
  name: string;
  status: 'online' | 'warning' | 'offline';
  batteryLevel: number;
  batteryTimeRemaining: number;
  load: number;
  inputVoltage: number;
  outputVoltage: number;
  temperature: number;
  lastUpdated: Date;
  location: string;
  model: string;
  maintenanceDate: string;
}

// 模拟 UPS 数据
const mockUPSData: UPSData[] = [
  {
    id: 'ups-01',
    name: 'UPS-01',
    status: 'online',
    batteryLevel: 100,
    batteryTimeRemaining: 240,
    load: 45,
    inputVoltage: 220,
    outputVoltage: 220,
    temperature: 25,
    lastUpdated: new Date(),
    location: '1楼机房',
    model: 'APC SRT5KXLI',
    maintenanceDate: '2025年12月31日'
  },
  {
    id: 'ups-02',
    name: 'UPS-02',
    status: 'online',
    batteryLevel: 98,
    batteryTimeRemaining: 230,
    load: 38,
    inputVoltage: 220,
    outputVoltage: 220,
    temperature: 26,
    lastUpdated: new Date(),
    location: '2楼机房',
    model: 'APC SRT5KXLI',
    maintenanceDate: '2025年12月20日'
  },
  {
    id: 'ups-03',
    name: 'UPS-03',
    status: 'warning',
    batteryLevel: 85,
    batteryTimeRemaining: 180,
    load: 72,
    inputVoltage: 215,
    outputVoltage: 220,
    temperature: 32,
    lastUpdated: new Date(),
    location: '3楼数据中心',
    model: 'APC SRT5KXLI',
    maintenanceDate: '2024年10月15日'
  },
  {
    id: 'ups-04',
    name: 'UPS-04',
    status: 'offline',
    batteryLevel: 0,
    batteryTimeRemaining: 0,
    load: 0,
    inputVoltage: 0,
    outputVoltage: 0,
    temperature: 0,
    lastUpdated: new Date(),
    location: '4楼弱电间',
    model: 'APC SRT3KXLI',
    maintenanceDate: '2024年08月10日'
  }
];

const UPSMonitoringPage = () => {
  const [upsData, setUpsData] = useState<UPSData[]>(mockUPSData);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // 刷新数据
  const refreshData = () => {
    setIsRefreshing(true);
    // 模拟数据刷新
    setTimeout(() => {
      // 更新数据，这里只是更新时间戳，实际应用中应该从API获取最新数据
      const updatedData = upsData.map(ups => ({
        ...ups,
        lastUpdated: new Date()
      }));
      setUpsData(updatedData);
      setLastRefresh(new Date());
      setIsRefreshing(false);
    }, 1000);
  };

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 5 * 60 * 1000); // 每5分钟刷新一次

    return () => clearInterval(interval);
  }, []);

  // 获取系统整体状态
  const getSystemStatus = () => {
    if (upsData.some(ups => ups.status === 'offline')) {
      return { status: 'error', text: '部分设备离线', color: 'red' };
    } else if (upsData.some(ups => ups.status === 'warning')) {
      return { status: 'warning', text: '部分设备警告', color: 'yellow' };
    } else {
      return { status: 'normal', text: '全部正常', color: 'green' };
    }
  };

  const systemStatus = getSystemStatus();
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Container maxW="container.xl" py={6}>
      {/* 页面标题和状态 */}
      <Box
        mb={8}
        p={6}
        borderRadius="xl"
        bg={cardBg}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          height="6px"
          bgGradient={`linear(to-r, ${systemStatus.color}.400, ${systemStatus.color}.500)`}
        />

        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>UPS监控</Heading>
            <Text color="gray.500" fontSize="md">监控UPS设备的电池状态和输出信息</Text>
          </Box>
          <Flex mt={{ base: 4, md: 0 }} align="center" gap={4}>
            <Badge
              colorScheme={systemStatus.color}
              fontSize="md"
              px={3}
              py={1}
              borderRadius="md"
            >
              {systemStatus.text}
            </Badge>
            <HStack>
              <Text fontSize="sm" color="gray.500">
                最后更新: {lastRefresh.toLocaleTimeString()}
              </Text>
              <HStack>
                <HStack spacing={2}>
                  <Button
                    as={Link}
                    href="/power-monitoring/ups/devices"
                    leftIcon={<DeviceTablet weight="bold" />}
                    colorScheme="orange"
                    size="md"
                    variant="outline"
                  >
                    设备管理
                  </Button>
                  <Button
                    as={Link}
                    href="/power-monitoring/ups/settings"
                    leftIcon={<Sliders weight="bold" />}
                    colorScheme="purple"
                    size="md"
                    variant="outline"
                  >
                    告警设置
                  </Button>
                </HStack>
                <Button
                  leftIcon={<ArrowsClockwise weight="bold" />}
                  colorScheme="blue"
                  size="md"
                  isLoading={isRefreshing}
                  loadingText="刷新中"
                  onClick={refreshData}
                >
                  刷新
                </Button>
              </HStack>
            </HStack>
          </Flex>
        </Flex>
      </Box>

      {/* UPS设备卡片 */}
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} mb={8}>
        {upsData.map((ups) => (
          <Card
            key={ups.id}
            borderRadius="lg"
            boxShadow="md"
            bg={cardBg}
            borderWidth="1px"
            borderColor={borderColor}
            overflow="hidden"
            transition="all 0.3s"
            _hover={{ transform: 'translateY(-5px)', boxShadow: 'lg' }}
          >
            <CardHeader
              py={4}
              px={6}
              bg={useColorModeValue('gray.50', 'gray.700')}
              borderBottomWidth="1px"
              borderBottomColor={borderColor}
            >
              <Flex justify="space-between" align="center">
                <Flex align="center">
                  <Icon
                    as={Lightning}
                    color={
                      ups.status === 'online' ? 'green.500' :
                      ups.status === 'warning' ? 'yellow.500' : 'red.500'
                    }
                    fontSize="xl"
                    mr={2}
                  />
                  <Heading size="md">{ups.name}</Heading>
                </Flex>
                <HStack>
                  <Badge
                    colorScheme={
                      ups.status === 'online' ? 'green' :
                      ups.status === 'warning' ? 'yellow' : 'red'
                    }
                    fontSize="sm"
                    px={2}
                    py={1}
                    borderRadius="full"
                  >
                    {ups.status === 'online' ? '在线' : ups.status === 'warning' ? '警告' : '离线'}
                  </Badge>
                  <Menu>
                    <MenuButton
                      as={Button}
                      variant="ghost"
                      size="sm"
                      icon={<DotsThreeOutlineVertical />}
                    >
                      <Icon as={DotsThreeOutlineVertical} />
                    </MenuButton>
                    <MenuList>
                      <MenuItem as={Link} href={`/power-monitoring/ups/${ups.id}`}>查看详情</MenuItem>
                      <MenuItem as={Link} href={`/power-monitoring/ups/${ups.id}/history`}>查看历史数据</MenuItem>
                      <MenuItem>导出报告</MenuItem>
                    </MenuList>
                  </Menu>
                </HStack>
              </Flex>
            </CardHeader>

            <CardBody p={6}>
              {ups.status !== 'offline' ? (
                <>
                  <Grid templateColumns="repeat(12, 1fr)" gap={4}>
                    {/* 电池容量 */}
                    <GridItem colSpan={{ base: 12, sm: 6, md: 4 }}>
                      <VStack align="start" spacing={1}>
                        <Flex align="center">
                          <Icon as={BatteryFull} color="green.500" mr={2} />
                          <Text fontWeight="medium">电池容量</Text>
                        </Flex>
                        <Text fontSize="2xl" fontWeight="bold">
                          {ups.batteryLevel}%
                        </Text>
                        <Progress
                          value={ups.batteryLevel}
                          colorScheme={ups.batteryLevel > 50 ? "green" : ups.batteryLevel > 20 ? "yellow" : "red"}
                          size="sm"
                          width="100%"
                          borderRadius="full"
                          mt={1}
                        />
                      </VStack>
                    </GridItem>

                    {/* 放电时间 */}
                    <GridItem colSpan={{ base: 12, sm: 6, md: 4 }}>
                      <VStack align="start" spacing={1}>
                        <Flex align="center">
                          <Icon as={Clock} color="blue.500" mr={2} />
                          <Text fontWeight="medium">放电时间</Text>
                        </Flex>
                        <Text fontSize="2xl" fontWeight="bold">
                          {ups.batteryTimeRemaining}分钟
                        </Text>
                        <Text fontSize="xs" color="gray.500">
                          满负载状态下
                        </Text>
                      </VStack>
                    </GridItem>

                    {/* 负载 */}
                    <GridItem colSpan={{ base: 12, sm: 6, md: 4 }}>
                      <VStack align="start" spacing={1}>
                        <Flex align="center">
                          <Icon as={Gauge} color={ups.load > 70 ? "orange.500" : "purple.500"} mr={2} />
                          <Text fontWeight="medium">负载</Text>
                        </Flex>
                        <Text fontSize="2xl" fontWeight="bold">
                          {ups.load}%
                        </Text>
                        <Progress
                          value={ups.load}
                          colorScheme={ups.load > 80 ? "red" : ups.load > 60 ? "orange" : "green"}
                          size="sm"
                          width="100%"
                          borderRadius="full"
                          mt={1}
                        />
                      </VStack>
                    </GridItem>
                  </Grid>

                  <Divider my={4} />

                  <Flex justify="space-between" wrap="wrap">
                    <HStack spacing={4} mb={{ base: 2, md: 0 }}>
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm" color="gray.500">输入电压</Text>
                        <Text fontWeight="medium">{ups.inputVoltage}V</Text>
                      </VStack>
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm" color="gray.500">输出电压</Text>
                        <Text fontWeight="medium">{ups.outputVoltage}V</Text>
                      </VStack>
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm" color="gray.500">温度</Text>
                        <Text fontWeight="medium">{ups.temperature}°C</Text>
                      </VStack>
                    </HStack>

                    <HStack>
                      <Button
                        as={Link}
                        href={`/power-monitoring/ups/${ups.id}/history`}
                        leftIcon={<ChartLine />}
                        colorScheme="teal"
                        size="sm"
                        variant="ghost"
                      >
                        历史数据
                      </Button>
                      <Button
                        as={Link}
                        href={`/power-monitoring/ups/${ups.id}`}
                        rightIcon={<ArrowRight />}
                        colorScheme="blue"
                        size="sm"
                        variant="outline"
                      >
                        详细信息
                      </Button>
                    </HStack>
                  </Flex>
                </>
              ) : (
                <Flex direction="column" align="center" justify="center" py={6}>
                  <Icon as={Warning} color="red.500" fontSize="4xl" mb={4} />
                  <Text fontSize="lg" fontWeight="medium" mb={2}>设备离线</Text>
                  <Text color="gray.500" textAlign="center" mb={4}>
                    无法获取设备数据，请检查设备连接状态
                  </Text>
                  <Button
                    as={Link}
                    href={`/power-monitoring/ups/${ups.id}`}
                    rightIcon={<ArrowRight />}
                    colorScheme="blue"
                    size="sm"
                  >
                    查看详情
                  </Button>
                </Flex>
              )}
            </CardBody>
          </Card>
        ))}
      </SimpleGrid>

      {/* 系统状态摘要 */}
      <Box
        p={6}
        borderRadius="xl"
        bg={cardBg}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading size="md" mb={4}>系统状态摘要</Heading>
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
          <Stat>
            <StatLabel>总设备数</StatLabel>
            <StatNumber>{upsData.length}</StatNumber>
            <StatHelpText>
              <Flex align="center">
                <Icon as={CheckCircle} color="green.500" mr={1} />
                <Text color="green.500">{upsData.filter(ups => ups.status === 'online').length} 台正常运行</Text>
              </Flex>
            </StatHelpText>
          </Stat>

          <Stat>
            <StatLabel>平均电池容量</StatLabel>
            <StatNumber>
              {Math.round(
                upsData
                  .filter(ups => ups.status !== 'offline')
                  .reduce((sum, ups) => sum + ups.batteryLevel, 0) /
                upsData.filter(ups => ups.status !== 'offline').length
              )}%
            </StatNumber>
            <StatHelpText>
              <Flex align="center">
                <Icon as={BatteryFull} color="green.500" mr={1} />
                <Text color="green.500">电池状态良好</Text>
              </Flex>
            </StatHelpText>
          </Stat>

          <Stat>
            <StatLabel>平均负载</StatLabel>
            <StatNumber>
              {Math.round(
                upsData
                  .filter(ups => ups.status !== 'offline')
                  .reduce((sum, ups) => sum + ups.load, 0) /
                upsData.filter(ups => ups.status !== 'offline').length
              )}%
            </StatNumber>
            <StatHelpText>
              <Flex align="center">
                <Icon as={Gauge} color="blue.500" mr={1} />
                <Text color="blue.500">负载正常</Text>
              </Flex>
            </StatHelpText>
          </Stat>
        </SimpleGrid>
      </Box>
    </Container>
  )
}

export default UPSMonitoringPage
