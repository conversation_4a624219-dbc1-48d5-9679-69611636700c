'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Icon,
  Button,
  HStack,
  VStack,
  CircularProgress,
  CircularProgressLabel,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Progress,
  Grid,
  GridItem,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue,
  Tooltip
} from '@chakra-ui/react'
import {
  Lightning,
  ArrowLeft,
  BatteryFull,
  Plug,
  Clock,
  Gauge,
  ArrowsClockwise,
  ChartLine,
  Warning,
  CheckCircle,
  Thermometer,
  Calendar,
  Info,
  Wrench,
  Buildings
} from '@phosphor-icons/react'
import Link from 'next/link'

// UPS 数据类型
interface UPSData {
  id: string;
  name: string;
  status: 'online' | 'warning' | 'offline';
  batteryLevel: number;
  batteryTimeRemaining: number;
  load: number;
  inputVoltage: number;
  outputVoltage: number;
  inputFrequency: number;
  outputFrequency: number;
  temperature: number;
  batteryVoltage: number;
  batteryGroups: number;
  singleGroupVoltage: number;
  ratedCapacity: number;
  ratedPower: number;
  runningDays: number;
  batteryHealth: number;
  lastTestDate: string;
  lastUpdated: Date;
  location: string;
  model: string;
  maintenanceDate: string;
  criticalLoad: number;
  nonCriticalLoad: number;
  events: {
    date: string;
    type: 'info' | 'warning' | 'error';
    message: string;
  }[];
}

// 模拟 UPS 数据
const mockUPSData: Record<string, UPSData> = {
  'ups-01': {
    id: 'ups-01',
    name: 'UPS-01',
    status: 'online',
    batteryLevel: 100,
    batteryTimeRemaining: 240,
    load: 45,
    inputVoltage: 220,
    outputVoltage: 220,
    inputFrequency: 50,
    outputFrequency: 50,
    temperature: 25,
    batteryVoltage: 240,
    batteryGroups: 4,
    singleGroupVoltage: 60,
    ratedCapacity: 5,
    ratedPower: 5,
    runningDays: 315,
    batteryHealth: 95,
    lastTestDate: '2023-12-15',
    lastUpdated: new Date(),
    location: '1楼机房',
    model: 'APC SRT5KXLI',
    maintenanceDate: '2025年12月31日',
    criticalLoad: 2.0,
    nonCriticalLoad: 0.5,
    events: [
      { date: '2023-12-15 10:30', type: 'info', message: '完成电池自检测试' },
      { date: '2023-11-20 08:15', type: 'info', message: '系统维护完成' },
      { date: '2023-10-05 14:22', type: 'warning', message: '市电波动检测' }
    ]
  },
  'ups-02': {
    id: 'ups-02',
    name: 'UPS-02',
    status: 'online',
    batteryLevel: 98,
    batteryTimeRemaining: 230,
    load: 38,
    inputVoltage: 220,
    outputVoltage: 220,
    inputFrequency: 50,
    outputFrequency: 50,
    temperature: 26,
    batteryVoltage: 235,
    batteryGroups: 4,
    singleGroupVoltage: 58.75,
    ratedCapacity: 5,
    ratedPower: 5,
    runningDays: 295,
    batteryHealth: 93,
    lastTestDate: '2023-12-10',
    lastUpdated: new Date(),
    location: '2楼机房',
    model: 'APC SRT5KXLI',
    maintenanceDate: '2025年12月20日',
    criticalLoad: 1.5,
    nonCriticalLoad: 0.4,
    events: [
      { date: '2023-12-10 11:45', type: 'info', message: '完成电池自检测试' },
      { date: '2023-11-15 09:30', type: 'info', message: '系统维护完成' },
      { date: '2023-09-28 16:10', type: 'warning', message: '短时间过载警告' }
    ]
  },
  'ups-03': {
    id: 'ups-03',
    name: 'UPS-03',
    status: 'warning',
    batteryLevel: 85,
    batteryTimeRemaining: 180,
    load: 72,
    inputVoltage: 215,
    outputVoltage: 220,
    inputFrequency: 49.8,
    outputFrequency: 50,
    temperature: 32,
    batteryVoltage: 230,
    batteryGroups: 4,
    singleGroupVoltage: 57.5,
    ratedCapacity: 5,
    ratedPower: 5,
    runningDays: 250,
    batteryHealth: 85,
    lastTestDate: '2023-11-20',
    lastUpdated: new Date(),
    location: '3楼数据中心',
    model: 'APC SRT5KXLI',
    maintenanceDate: '2024年10月15日',
    criticalLoad: 3.0,
    nonCriticalLoad: 0.6,
    events: [
      { date: '2023-12-18 08:30', type: 'warning', message: '电池温度过高警告' },
      { date: '2023-12-05 14:20', type: 'warning', message: '负载超过70%警告' },
      { date: '2023-11-20 10:15', type: 'info', message: '完成电池自检测试' }
    ]
  },
  'ups-04': {
    id: 'ups-04',
    name: 'UPS-04',
    status: 'offline',
    batteryLevel: 0,
    batteryTimeRemaining: 0,
    load: 0,
    inputVoltage: 0,
    outputVoltage: 0,
    inputFrequency: 0,
    outputFrequency: 0,
    temperature: 0,
    batteryVoltage: 0,
    batteryGroups: 2,
    singleGroupVoltage: 0,
    ratedCapacity: 3,
    ratedPower: 3,
    runningDays: 0,
    batteryHealth: 0,
    lastTestDate: '2023-10-15',
    lastUpdated: new Date(),
    location: '4楼弱电间',
    model: 'APC SRT3KXLI',
    maintenanceDate: '2024年08月10日',
    criticalLoad: 0,
    nonCriticalLoad: 0,
    events: [
      { date: '2023-12-20 09:15', type: 'error', message: '设备离线' },
      { date: '2023-12-19 23:45', type: 'warning', message: '电池电量低警告' },
      { date: '2023-12-19 20:30', type: 'warning', message: '市电断电' }
    ]
  }
};

const UPSDetailPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const [upsData, setUpsData] = useState<UPSData | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // 获取UPS数据
  useEffect(() => {
    // 模拟API调用
    if (mockUPSData[id]) {
      setUpsData(mockUPSData[id]);
    }
  }, [id]);

  // 刷新数据
  const refreshData = () => {
    setIsRefreshing(true);
    // 模拟数据刷新
    setTimeout(() => {
      if (mockUPSData[id]) {
        setUpsData({
          ...mockUPSData[id],
          lastUpdated: new Date()
        });
      }
      setLastRefresh(new Date());
      setIsRefreshing(false);
    }, 1000);
  };

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 5 * 60 * 1000); // 每5分钟刷新一次

    return () => clearInterval(interval);
  }, []);

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  if (!upsData) {
    return (
      <Container maxW="container.xl" py={6}>
        <Flex align="center" mb={6}>
          <Button
            as={Link}
            href="/power-monitoring/ups"
            leftIcon={<ArrowLeft />}
            variant="ghost"
            size="sm"
            mr={4}
          >
            返回UPS监控
          </Button>
          <Heading size="lg">UPS详情</Heading>
        </Flex>
        <Box p={10} textAlign="center">
          <Text>加载中...</Text>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={6}>
      {/* 页面标题和状态 */}
      <Box
        mb={8}
        p={6}
        borderRadius="xl"
        bg={cardBg}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          height="6px"
          bgGradient={`linear(to-r, ${
            upsData.status === 'online' ? 'green' :
            upsData.status === 'warning' ? 'yellow' : 'red'
          }.400, ${
            upsData.status === 'online' ? 'green' :
            upsData.status === 'warning' ? 'yellow' : 'red'
          }.500)`}
        />

        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Flex align="center">
            <Button
              as={Link}
              href="/power-monitoring/ups"
              leftIcon={<ArrowLeft />}
              variant="ghost"
              size="sm"
              mr={4}
            >
              返回
            </Button>
            <Box>
              <Heading size="lg" mb={1}>{upsData.name} 详细信息</Heading>
              <Flex align="center">
                <Badge
                  colorScheme={
                    upsData.status === 'online' ? 'green' :
                    upsData.status === 'warning' ? 'yellow' : 'red'
                  }
                  fontSize="sm"
                  px={2}
                  py={1}
                  borderRadius="full"
                  mr={2}
                >
                  {upsData.status === 'online' ? '在线' : upsData.status === 'warning' ? '警告' : '离线'}
                </Badge>
                <Text color="gray.500" fontSize="sm">
                  位置: {upsData.location}
                </Text>
              </Flex>
            </Box>
          </Flex>
          <Flex mt={{ base: 4, md: 0 }} align="center" gap={4}>
            <Text fontSize="sm" color="gray.500">
              最后更新: {lastRefresh.toLocaleTimeString()}
            </Text>
            <HStack>
              <Button
                as={Link}
                href={`/power-monitoring/ups/${id}/history`}
                leftIcon={<ChartLine weight="bold" />}
                colorScheme="teal"
                size="md"
                variant="outline"
              >
                历史数据
              </Button>
              <Button
                leftIcon={<ArrowsClockwise weight="bold" />}
                colorScheme="blue"
                size="md"
                isLoading={isRefreshing}
                loadingText="刷新中"
                onClick={refreshData}
              >
                刷新
              </Button>
            </HStack>
          </Flex>
        </Flex>
      </Box>

      {upsData.status !== 'offline' ? (
        <>
          {/* 核心指标 */}
          <SimpleGrid columns={{ base: 1, sm: 2, md: 5 }} spacing={6} mb={8}>
            {/* 电池剩余容量 */}
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex direction="column" align="center" justify="center" h="100%">
                  <Icon as={BatteryFull} color="green.500" fontSize="3xl" mb={2} />
                  <Text fontWeight="medium" mb={2}>电池剩余容量</Text>
                  <CircularProgress
                    value={upsData.batteryLevel}
                    color={upsData.batteryLevel > 50 ? "green.400" : upsData.batteryLevel > 20 ? "yellow.400" : "red.400"}
                    size="100px"
                  >
                    <CircularProgressLabel fontWeight="bold">{upsData.batteryLevel}%</CircularProgressLabel>
                  </CircularProgress>
                </Flex>
              </CardBody>
            </Card>

            {/* 剩余放电时间 */}
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex direction="column" align="center" justify="center" h="100%">
                  <Icon as={Clock} color="blue.500" fontSize="3xl" mb={2} />
                  <Text fontWeight="medium" mb={2}>剩余放电时间</Text>
                  <Text fontSize="2xl" fontWeight="bold" color="blue.500">{upsData.batteryTimeRemaining}分钟</Text>
                  <Text fontSize="sm" color="gray.500" mt={1}>满负载状态下</Text>
                </Flex>
              </CardBody>
            </Card>

            {/* 电池电压 */}
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex direction="column" align="center" justify="center" h="100%">
                  <Icon as={Lightning} color="orange.500" fontSize="3xl" mb={2} />
                  <Text fontWeight="medium" mb={2}>电池电压</Text>
                  <Text fontSize="2xl" fontWeight="bold" color="orange.500">{upsData.batteryVoltage}V</Text>
                  <Text fontSize="sm" color="gray.500" mt={1}>正常范围内</Text>
                </Flex>
              </CardBody>
            </Card>

            {/* 输出负载百分比 */}
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex direction="column" align="center" justify="center" h="100%">
                  <Icon as={Gauge} color="purple.500" fontSize="3xl" mb={2} />
                  <Text fontWeight="medium" mb={2}>输出负载百分比</Text>
                  <CircularProgress
                    value={upsData.load}
                    color={upsData.load > 80 ? "red.400" : upsData.load > 60 ? "orange.400" : "purple.400"}
                    size="100px"
                  >
                    <CircularProgressLabel fontWeight="bold">{upsData.load}%</CircularProgressLabel>
                  </CircularProgress>
                </Flex>
              </CardBody>
            </Card>

            {/* 输出有功功率 */}
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex direction="column" align="center" justify="center" h="100%">
                  <Icon as={Plug} color="cyan.500" fontSize="3xl" mb={2} />
                  <Text fontWeight="medium" mb={2}>输出有功功率</Text>
                  <Text fontSize="2xl" fontWeight="bold" color="cyan.500">
                    {(upsData.criticalLoad + upsData.nonCriticalLoad).toFixed(1)}kW
                  </Text>
                  <Text fontSize="sm" color="gray.500" mt={1}>最大容量: {upsData.ratedPower}kW</Text>
                </Flex>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* 详细信息标签页 */}
          <Tabs variant="soft-rounded" colorScheme="blue" mb={8}>
            <TabList mb={4}>
              <Tab>详细参数</Tab>
              <Tab>负载分布</Tab>
              <Tab>事件记录</Tab>
              <Tab>设备信息</Tab>
            </TabList>

            <TabPanels>
              {/* 详细参数标签页 */}
              <TabPanel px={0}>
                <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
                  <CardBody>
                    <Heading size="md" mb={4}>UPS 详细参数</Heading>
                    <Table size="sm" variant="simple">
                      <Tbody>
                        <Tr>
                          <Td fontWeight="medium">输入电压</Td>
                          <Td>{upsData.inputVoltage}V</Td>
                          <Td fontWeight="medium">输入频率</Td>
                          <Td>{upsData.inputFrequency}Hz</Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="medium">输出电压</Td>
                          <Td>{upsData.outputVoltage}V</Td>
                          <Td fontWeight="medium">输出频率</Td>
                          <Td>{upsData.outputFrequency}Hz</Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="medium">电池组数</Td>
                          <Td>{upsData.batteryGroups}组</Td>
                          <Td fontWeight="medium">单组电压</Td>
                          <Td>{upsData.singleGroupVoltage}V</Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="medium">额定容量</Td>
                          <Td>{upsData.ratedCapacity}kVA</Td>
                          <Td fontWeight="medium">额定功率</Td>
                          <Td>{upsData.ratedPower}kW</Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="medium">当前温度</Td>
                          <Td>{upsData.temperature}℃</Td>
                          <Td fontWeight="medium">运行时长</Td>
                          <Td>{upsData.runningDays}天</Td>
                        </Tr>
                        <Tr>
                          <Td fontWeight="medium">电池健康度</Td>
                          <Td>{upsData.batteryHealth}%</Td>
                          <Td fontWeight="medium">最后测试时间</Td>
                          <Td>{upsData.lastTestDate}</Td>
                        </Tr>
                      </Tbody>
                    </Table>
                  </CardBody>
                </Card>
              </TabPanel>

              {/* 负载分布标签页 */}
              <TabPanel px={0}>
                <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
                  <CardBody>
                    <Heading size="md" mb={4}>负载分布</Heading>
                    <Box mt={6}>
                      <Flex justify="space-between" mb={2}>
                        <Text>关键负载</Text>
                        <Text fontWeight="medium">
                          {upsData.criticalLoad.toFixed(1)}kW ({Math.round(upsData.criticalLoad / (upsData.criticalLoad + upsData.nonCriticalLoad) * 100)}%)
                        </Text>
                      </Flex>
                      <Progress
                        value={Math.round(upsData.criticalLoad / (upsData.criticalLoad + upsData.nonCriticalLoad) * 100)}
                        colorScheme="blue"
                        mb={4}
                      />

                      <Flex justify="space-between" mb={2}>
                        <Text>非关键负载</Text>
                        <Text fontWeight="medium">
                          {upsData.nonCriticalLoad.toFixed(1)}kW ({Math.round(upsData.nonCriticalLoad / (upsData.criticalLoad + upsData.nonCriticalLoad) * 100)}%)
                        </Text>
                      </Flex>
                      <Progress
                        value={Math.round(upsData.nonCriticalLoad / (upsData.criticalLoad + upsData.nonCriticalLoad) * 100)}
                        colorScheme="green"
                      />
                    </Box>
                  </CardBody>
                </Card>
              </TabPanel>

              {/* 事件记录标签页 */}
              <TabPanel px={0}>
                <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
                  <CardBody>
                    <Heading size="md" mb={4}>事件记录</Heading>
                    <Table size="sm" variant="simple">
                      <Thead>
                        <Tr>
                          <Th>时间</Th>
                          <Th>类型</Th>
                          <Th>描述</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {upsData.events.map((event, index) => (
                          <Tr key={index}>
                            <Td>{event.date}</Td>
                            <Td>
                              <Badge
                                colorScheme={
                                  event.type === 'info' ? 'blue' :
                                  event.type === 'warning' ? 'yellow' : 'red'
                                }
                              >
                                {event.type === 'info' ? '信息' : event.type === 'warning' ? '警告' : '错误'}
                              </Badge>
                            </Td>
                            <Td>{event.message}</Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </CardBody>
                </Card>
              </TabPanel>

              {/* 设备信息标签页 */}
              <TabPanel px={0}>
                <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
                  <CardBody>
                    <Heading size="md" mb={4}>设备信息</Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                      <Box>
                        <VStack align="start" spacing={4}>
                          <Flex align="center">
                            <Icon as={Info} color="blue.500" mr={2} />
                            <Text fontWeight="medium">品牌型号</Text>
                          </Flex>
                          <Text ml={6}>{upsData.model}</Text>

                          <Flex align="center">
                            <Icon as={Buildings} color="purple.500" mr={2} />
                            <Text fontWeight="medium">安装位置</Text>
                          </Flex>
                          <Text ml={6}>{upsData.location}</Text>
                        </VStack>
                      </Box>

                      <Box>
                        <VStack align="start" spacing={4}>
                          <Flex align="center">
                            <Icon as={Calendar} color="green.500" mr={2} />
                            <Text fontWeight="medium">维保期限</Text>
                          </Flex>
                          <Text ml={6}>{upsData.maintenanceDate}</Text>

                          <Flex align="center">
                            <Icon as={Wrench} color="orange.500" mr={2} />
                            <Text fontWeight="medium">最后维护时间</Text>
                          </Flex>
                          <Text ml={6}>{upsData.lastTestDate}</Text>
                        </VStack>
                      </Box>
                    </SimpleGrid>
                  </CardBody>
                </Card>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </>
      ) : (
        // 离线状态显示
        <Box
          p={10}
          borderRadius="lg"
          boxShadow="md"
          bg={cardBg}
          borderWidth="1px"
          borderColor={borderColor}
          textAlign="center"
        >
          <Icon as={Warning} color="red.500" fontSize="6xl" mb={4} />
          <Heading size="lg" mb={4}>设备离线</Heading>
          <Text color="gray.500" maxW="600px" mx="auto" mb={6}>
            无法获取设备数据，请检查设备连接状态或联系技术支持。
          </Text>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} maxW="600px" mx="auto">
            <Card>
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Text fontWeight="medium">设备信息</Text>
                  <Divider />
                  <Flex justify="space-between" w="100%">
                    <Text color="gray.500">品牌型号</Text>
                    <Text>{upsData.model}</Text>
                  </Flex>
                  <Flex justify="space-between" w="100%">
                    <Text color="gray.500">安装位置</Text>
                    <Text>{upsData.location}</Text>
                  </Flex>
                </VStack>
              </CardBody>
            </Card>

            <Card>
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Text fontWeight="medium">最近事件</Text>
                  <Divider />
                  {upsData.events.slice(0, 2).map((event, index) => (
                    <Flex key={index} justify="space-between" w="100%">
                      <Text color="gray.500">{event.date.split(' ')[0]}</Text>
                      <Badge
                        colorScheme={
                          event.type === 'info' ? 'blue' :
                          event.type === 'warning' ? 'yellow' : 'red'
                        }
                      >
                        {event.message}
                      </Badge>
                    </Flex>
                  ))}
                </VStack>
              </CardBody>
            </Card>
          </SimpleGrid>
        </Box>
      )}
    </Container>
  )
}

export default UPSDetailPage
