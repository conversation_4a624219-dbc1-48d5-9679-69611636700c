'use client'

import React, { useState, useEffect } from 'react'
import {
  Box, 
  Container, 
  Heading, 
  Text, 
  SimpleGrid, 
  Card, 
  CardBody,
  CardHeader,
  Flex, 
  Icon,
  Button,
  HStack,
  VStack,
  Divider,
  Badge,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react'
import { 
  Lightning, 
  ArrowLeft, 
  BatteryFull, 
  Plug, 
  Clock, 
  Gauge, 
  ArrowsClockwise,
  ChartLine,
  Thermometer,
} from '@phosphor-icons/react'
import Link from 'next/link'
import UPSHistoryChart from '@/components/UPS/UPSHistoryChart'

// UPS 数据类型
interface UPSData {
  id: string;
  name: string;
  status: 'online' | 'warning' | 'offline';
  batteryLevel: number;
  batteryTimeRemaining: number;
  load: number;
  inputVoltage: number;
  outputVoltage: number;
  temperature: number;
  lastUpdated: Date;
  location: string;
}

// 模拟 UPS 数据
const mockUPSData: Record<string, UPSData> = {
  'ups-01': {
    id: 'ups-01',
    name: 'UPS-01',
    status: 'online',
    batteryLevel: 100,
    batteryTimeRemaining: 240,
    load: 45,
    inputVoltage: 220,
    outputVoltage: 220,
    temperature: 25,
    lastUpdated: new Date(),
    location: '1楼机房',
  },
  'ups-02': {
    id: 'ups-02',
    name: 'UPS-02',
    status: 'online',
    batteryLevel: 98,
    batteryTimeRemaining: 230,
    load: 38,
    inputVoltage: 220,
    outputVoltage: 220,
    temperature: 26,
    lastUpdated: new Date(),
    location: '2楼机房',
  },
  'ups-03': {
    id: 'ups-03',
    name: 'UPS-03',
    status: 'warning',
    batteryLevel: 85,
    batteryTimeRemaining: 180,
    load: 72,
    inputVoltage: 215,
    outputVoltage: 220,
    temperature: 32,
    lastUpdated: new Date(),
    location: '3楼数据中心',
  },
  'ups-04': {
    id: 'ups-04',
    name: 'UPS-04',
    status: 'offline',
    batteryLevel: 0,
    batteryTimeRemaining: 0,
    load: 0,
    inputVoltage: 0,
    outputVoltage: 0,
    temperature: 0,
    lastUpdated: new Date(),
    location: '4楼弱电间',
  }
};

const UPSHistoryPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const [upsData, setUpsData] = useState<UPSData | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  
  // 获取UPS数据
  useEffect(() => {
    // 模拟API调用
    if (mockUPSData[id]) {
      setUpsData(mockUPSData[id]);
    }
  }, [id]);
  
  // 刷新数据
  const refreshData = () => {
    setIsRefreshing(true);
    // 模拟数据刷新
    setTimeout(() => {
      if (mockUPSData[id]) {
        setUpsData({
          ...mockUPSData[id],
          lastUpdated: new Date()
        });
      }
      setLastRefresh(new Date());
      setIsRefreshing(false);
    }, 1000);
  };
  
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  if (!upsData) {
    return (
      <Container maxW="container.xl" py={6}>
        <Flex align="center" mb={6}>
          <Button 
            as={Link}
            href="/power-monitoring/ups"
            leftIcon={<ArrowLeft />}
            variant="ghost"
            size="sm"
            mr={4}
          >
            返回UPS监控
          </Button>
          <Heading size="lg">UPS历史数据</Heading>
        </Flex>
        <Box p={10} textAlign="center">
          <Text>加载中...</Text>
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxW="container.xl" py={6}>
      {/* 页面标题和状态 */}
      <Box 
        mb={8} 
        p={6} 
        borderRadius="xl" 
        bg={cardBg} 
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box 
          position="absolute" 
          top={0} 
          left={0} 
          right={0} 
          height="6px" 
          bgGradient={`linear(to-r, blue.400, blue.500)`}
        />
        
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Flex align="center">
            <Button 
              as={Link}
              href={`/power-monitoring/ups/${id}`}
              leftIcon={<ArrowLeft />}
              variant="ghost"
              size="sm"
              mr={4}
            >
              返回详情
            </Button>
            <Box>
              <Heading size="lg" mb={1}>{upsData.name} 历史数据</Heading>
              <Flex align="center">
                <Badge 
                  colorScheme={
                    upsData.status === 'online' ? 'green' : 
                    upsData.status === 'warning' ? 'yellow' : 'red'
                  } 
                  fontSize="sm" 
                  px={2} 
                  py={1} 
                  borderRadius="full"
                  mr={2}
                >
                  {upsData.status === 'online' ? '在线' : upsData.status === 'warning' ? '警告' : '离线'}
                </Badge>
                <Text color="gray.500" fontSize="sm">
                  位置: {upsData.location}
                </Text>
              </Flex>
            </Box>
          </Flex>
          <Flex mt={{ base: 4, md: 0 }} align="center" gap={4}>
            <Text fontSize="sm" color="gray.500">
              最后更新: {lastRefresh.toLocaleTimeString()}
            </Text>
            <Button 
              leftIcon={<ArrowsClockwise weight="bold" />} 
              colorScheme="blue" 
              size="md"
              isLoading={isRefreshing}
              loadingText="刷新中"
              onClick={refreshData}
            >
              刷新
            </Button>
          </Flex>
        </Flex>
      </Box>

      {upsData.status !== 'offline' ? (
        <>
          {/* 当前状态摘要 */}
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6} mb={8}>
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex align="center" mb={2}>
                  <Icon as={BatteryFull} color="green.500" fontSize="xl" mr={2} />
                  <Text fontWeight="medium">电池容量</Text>
                </Flex>
                <Text fontSize="2xl" fontWeight="bold">{upsData.batteryLevel}%</Text>
              </CardBody>
            </Card>
            
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex align="center" mb={2}>
                  <Icon as={Gauge} color="purple.500" fontSize="xl" mr={2} />
                  <Text fontWeight="medium">负载</Text>
                </Flex>
                <Text fontSize="2xl" fontWeight="bold">{upsData.load}%</Text>
              </CardBody>
            </Card>
            
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex align="center" mb={2}>
                  <Icon as={Lightning} color="orange.500" fontSize="xl" mr={2} />
                  <Text fontWeight="medium">输入电压</Text>
                </Flex>
                <Text fontSize="2xl" fontWeight="bold">{upsData.inputVoltage}V</Text>
              </CardBody>
            </Card>
            
            <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex align="center" mb={2}>
                  <Icon as={Thermometer} color="red.500" fontSize="xl" mr={2} />
                  <Text fontWeight="medium">温度</Text>
                </Flex>
                <Text fontSize="2xl" fontWeight="bold">{upsData.temperature}°C</Text>
              </CardBody>
            </Card>
          </SimpleGrid>
          
          {/* 历史数据图表 */}
          <Tabs variant="soft-rounded" colorScheme="blue" mb={8}>
            <TabList mb={4}>
              <Tab>电池容量</Tab>
              <Tab>负载</Tab>
              <Tab>电压</Tab>
              <Tab>温度</Tab>
            </TabList>
            
            <TabPanels>
              <TabPanel px={0}>
                <UPSHistoryChart 
                  upsId={id}
                  metricType="battery"
                  title="电池容量"
                  color="green.500"
                  unit="%"
                  showAreaFill={true}
                />
              </TabPanel>
              
              <TabPanel px={0}>
                <UPSHistoryChart 
                  upsId={id}
                  metricType="load"
                  title="负载"
                  color="purple.500"
                  unit="%"
                  showAreaFill={true}
                />
              </TabPanel>
              
              <TabPanel px={0}>
                <UPSHistoryChart 
                  upsId={id}
                  metricType="voltage"
                  title="电压"
                  color="orange.500"
                  unit="V"
                  showAreaFill={false}
                />
              </TabPanel>
              
              <TabPanel px={0}>
                <UPSHistoryChart 
                  upsId={id}
                  metricType="temperature"
                  title="温度"
                  color="red.500"
                  unit="°C"
                  showAreaFill={false}
                />
              </TabPanel>
            </TabPanels>
          </Tabs>
          
          {/* 数据分析 */}
          <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor} mb={8}>
            <CardHeader>
              <Heading size="md">数据分析</Heading>
            </CardHeader>
            <CardBody>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <Box>
                  <Heading size="sm" mb={2}>电池状态分析</Heading>
                  <Text mb={4}>
                    电池容量保持在健康水平，过去30天内电池容量平均为{upsData.batteryLevel > 90 ? '95%' : '85%'}，
                    {upsData.batteryLevel > 90 ? '没有' : '有轻微'}衰减迹象。
                    建议继续定期进行电池自检测试，确保电池健康状态。
                  </Text>
                  
                  <Heading size="sm" mb={2}>负载分析</Heading>
                  <Text>
                    UPS负载在过去30天内平均为{upsData.load}%，
                    {upsData.load > 70 ? '接近' : '远低于'}额定容量。
                    {upsData.load > 70 
                      ? '建议考虑负载均衡或升级UPS设备，以确保系统稳定性。' 
                      : '当前负载水平适中，系统运行稳定。'}
                  </Text>
                </Box>
                
                <Box>
                  <Heading size="sm" mb={2}>电压稳定性分析</Heading>
                  <Text mb={4}>
                    输入电压在过去30天内保持稳定，波动范围在
                    {upsData.inputVoltage > 215 ? '±5V' : '±10V'}内。
                    {upsData.inputVoltage < 215 
                      ? '存在轻微的市电波动，但UPS正常调节输出电压。' 
                      : '市电供应稳定，UPS工作在最佳状态。'}
                  </Text>
                  
                  <Heading size="sm" mb={2}>温度分析</Heading>
                  <Text>
                    设备温度在过去30天内平均为{upsData.temperature}°C，
                    {upsData.temperature > 30 
                      ? '略高于理想温度范围。建议检查设备散热情况，确保环境温度适宜。' 
                      : '处于理想温度范围内，设备散热良好。'}
                  </Text>
                </Box>
              </SimpleGrid>
            </CardBody>
          </Card>
          
          {/* 导出选项 */}
          <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
            <CardHeader>
              <Heading size="md">数据导出</Heading>
            </CardHeader>
            <CardBody>
              <Text mb={4}>选择时间范围和数据类型导出历史数据报表</Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                <Button colorScheme="blue" leftIcon={<ChartLine />}>
                  导出24小时数据
                </Button>
                <Button colorScheme="blue" leftIcon={<ChartLine />}>
                  导出7天数据
                </Button>
                <Button colorScheme="blue" leftIcon={<ChartLine />}>
                  导出30天数据
                </Button>
              </SimpleGrid>
            </CardBody>
          </Card>
        </>
      ) : (
        // 离线状态显示
        <Box 
          p={10} 
          borderRadius="lg" 
          boxShadow="md" 
          bg={cardBg} 
          borderWidth="1px" 
          borderColor={borderColor}
          textAlign="center"
        >
          <Heading size="lg" mb={4}>设备离线</Heading>
          <Text color="gray.500" maxW="600px" mx="auto" mb={6}>
            设备当前处于离线状态，无法获取实时数据。您可以查看设备离线前的历史数据。
          </Text>
          <Button colorScheme="blue" as={Link} href={`/power-monitoring/ups/${id}`}>
            返回设备详情
          </Button>
        </Box>
      )}
    </Container>
  )
}

export default UPSHistoryPage
