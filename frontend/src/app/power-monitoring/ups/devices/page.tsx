'use client'

import React, { useState, useEffect } from 'react'
import {
  Box, 
  Container, 
  Heading, 
  Text, 
  SimpleGrid, 
  Card, 
  CardBody,
  CardHeader,
  CardFooter,
  Flex, 
  Icon,
  Button,
  HStack,
  VStack,
  Divider,
  Badge,
  useColorModeValue,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Switch,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  InputGroup,
  InputLeftElement,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Checkbox,
  Tag,
  TagLeftIcon,
  Tooltip,
  useBreakpointValue,
  IconButton
} from '@chakra-ui/react'
import { 
  Lightning, 
  ArrowLeft, 
  Plus,
  Pencil,
  Trash,
  Buildings,
  DeviceTablet,
  Barcode,
  Calendar,
  ClockClockwise,
  Check,
  Funnel,
  SlidersHorizontal
} from '@phosphor-icons/react'
import Link from 'next/link'

// UPS 设备类型
interface UPSDevice {
  id: string;
  name: string;
  model: string;
  serialNumber: string;
  location: string;
  installDate: string;
  maintenanceDate: string;
  capacity: number;
  batteryGroups: number;
  ipAddress: string;
  enabled: boolean;
}

// 模拟 UPS 设备数据
const initialUPSDevices: UPSDevice[] = [
  {
    id: 'ups-01',
    name: 'UPS-01',
    model: 'APC SRT5KXLI',
    serialNumber: 'SRT5K2021001',
    location: '1楼机房',
    installDate: '2021-06-15',
    maintenanceDate: '2025-12-31',
    capacity: 5,
    batteryGroups: 4,
    ipAddress: '*************',
    enabled: true
  },
  {
    id: 'ups-02',
    name: 'UPS-02',
    model: 'APC SRT5KXLI',
    serialNumber: 'SRT5K2021002',
    location: '2楼机房',
    installDate: '2021-07-20',
    maintenanceDate: '2025-12-20',
    capacity: 5,
    batteryGroups: 4,
    ipAddress: '*************',
    enabled: true
  },
  {
    id: 'ups-03',
    name: 'UPS-03',
    model: 'APC SRT5KXLI',
    serialNumber: 'SRT5K2021003',
    location: '3楼数据中心',
    installDate: '2021-08-10',
    maintenanceDate: '2024-10-15',
    capacity: 5,
    batteryGroups: 4,
    ipAddress: '*************',
    enabled: true
  },
  {
    id: 'ups-04',
    name: 'UPS-04',
    model: 'APC SRT3KXLI',
    serialNumber: 'SRT3K2021004',
    location: '4楼弱电间',
    installDate: '2021-09-05',
    maintenanceDate: '2024-08-10',
    capacity: 3,
    batteryGroups: 2,
    ipAddress: '*************',
    enabled: false
  }
];

// 新增：筛选组件
const DeviceFilterBar = ({ status, setStatus }: any) => (
  <HStack spacing={4} mb={4} align="center">
    <Tag size="lg" colorScheme="blue" variant="subtle" px={3} py={2} borderRadius="full">
      <TagLeftIcon as={Funnel} />筛选
    </Tag>
    <Select value={status} onChange={e => setStatus(e.target.value)} maxW="140px">
      <option value="all">全部状态</option>
      <option value="enabled">仅启用</option>
      <option value="disabled">仅禁用</option>
    </Select>
  </HStack>
);

// 新增：移动端卡片
const DeviceCard = ({ device, onEdit, onDelete, onToggle, selected, onSelect }: any) => (
  <Box borderWidth="1px" borderRadius="lg" p={4} mb={3} bg="white" boxShadow="sm">
    <Flex align="center" mb={2}>
      <Checkbox isChecked={selected} onChange={onSelect} mr={2} />
      <Icon as={Lightning} color="orange.500" boxSize={6} mr={2} />
      <Text fontWeight="bold" fontSize="lg">{device.name}</Text>
      <Badge ml={2} colorScheme={device.enabled ? 'green' : 'gray'}>{device.enabled ? '启用' : '禁用'}</Badge>
    </Flex>
    <Flex mb={2} gap={4} wrap="wrap">
      <Tag colorScheme="blue" variant="subtle">型号: {device.model}</Tag>
      <Tag colorScheme="gray" variant="subtle">位置: {device.location}</Tag>
      <Tag colorScheme="purple" variant="subtle">容量: {device.capacity}kVA</Tag>
      <Tag colorScheme="gray" variant="subtle">IP: {device.ipAddress}</Tag>
    </Flex>
    <Flex align="center" gap={2} mt={2}>
      <Tooltip label="编辑">
        <IconButton aria-label="编辑" icon={<Pencil />} size="sm" variant="ghost" colorScheme="blue" onClick={onEdit} />
      </Tooltip>
      <Tooltip label="删除">
        <IconButton aria-label="删除" icon={<Trash />} size="sm" variant="ghost" colorScheme="red" onClick={onDelete} />
      </Tooltip>
      <Switch isChecked={device.enabled} onChange={onToggle} colorScheme={device.enabled ? 'green' : 'gray'} size="md" ml={2} />
      <Text fontSize="sm" color={device.enabled ? 'green.600' : 'gray.500'} ml={1}>{device.enabled ? '已启用' : '已禁用'}</Text>
    </Flex>
  </Box>
);

const UPSDevicesPage = () => {
  const [upsDevices, setUpsDevices] = useState<UPSDevice[]>(initialUPSDevices);
  const [filteredDevices, setFilteredDevices] = useState<UPSDevice[]>(initialUPSDevices);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentDevice, setCurrentDevice] = useState<UPSDevice | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [deviceToDelete, setDeviceToDelete] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEnabled, setFilterEnabled] = useState<'all' | 'enabled' | 'disabled'>('all');
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  const toast = useToast();
  const isMobile = useBreakpointValue({ base: true, md: false });
  
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  useEffect(() => {
    let devices = [...upsDevices];
    if (searchTerm) {
      devices = devices.filter(device =>
        device.name.includes(searchTerm) ||
        device.model.includes(searchTerm) ||
        device.serialNumber.includes(searchTerm) ||
        device.location.includes(searchTerm) ||
        device.ipAddress.includes(searchTerm)
      );
    }
    if (filterEnabled !== 'all') {
      devices = devices.filter(d => d.enabled === (filterEnabled === 'enabled'));
    }
    setFilteredDevices(devices);
  }, [upsDevices, searchTerm, filterEnabled]);
  
  // 打开添加设备模态框
  const openAddModal = () => {
    setCurrentDevice({
      id: `ups-${Date.now()}`,
      name: '',
      model: '',
      serialNumber: '',
      location: '',
      installDate: new Date().toISOString().split('T')[0],
      maintenanceDate: '',
      capacity: 5,
      batteryGroups: 4,
      ipAddress: '',
      enabled: true
    });
    setIsAddModalOpen(true);
  };
  
  // 打开编辑设备模态框
  const openEditModal = (device: UPSDevice) => {
    setCurrentDevice({...device});
    setIsEditModalOpen(true);
  };
  
  // 确认删除设备
  const confirmDelete = (id: string) => {
    setDeviceToDelete(id);
    onOpen();
  };
  
  // 删除设备
  const deleteDevice = () => {
    setUpsDevices(upsDevices.filter(device => device.id !== deviceToDelete));
    onClose();
    
    toast({
      title: '设备已删除',
      description: '设备已成功从系统中移除',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };
  
  // 添加设备
  const addDevice = () => {
    if (currentDevice) {
      setUpsDevices([...upsDevices, currentDevice]);
      setIsAddModalOpen(false);
      
      toast({
        title: '设备已添加',
        description: `${currentDevice.name}已成功添加到系统`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };
  
  // 更新设备
  const updateDevice = () => {
    if (currentDevice) {
      setUpsDevices(upsDevices.map(device => 
        device.id === currentDevice.id ? currentDevice : device
      ));
      setIsEditModalOpen(false);
      
      toast({
        title: '设备已更新',
        description: `${currentDevice.name}信息已成功更新`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };
  
  // 批量删除
  const batchDelete = () => {
    setUpsDevices(upsDevices.filter(device => !selectedIds.includes(device.id)));
    setSelectedIds([]);
    toast({
      title: '批量删除成功',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };
  
  // 批量启用/禁用
  const batchToggleEnable = (enable: boolean) => {
    setUpsDevices(upsDevices.map(device =>
      selectedIds.includes(device.id) ? { ...device, enabled: enable } : device
    ));
    setSelectedIds([]);
    toast({
      title: enable ? '批量启用成功' : '批量禁用成功',
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };
  
  // 表单校验（示例：IP格式校验）
  const isValidIP = (ip: string) => /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/.test(ip);
  
  // 设备表单
  const DeviceForm = ({ isEdit = false }: { isEdit?: boolean }) => {
    if (!currentDevice) return null;
    
    return (
      <VStack align="start" spacing={4}>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
          <FormControl isRequired>
            <FormLabel>设备名称</FormLabel>
            <Input 
              value={currentDevice.name} 
              onChange={(e) => setCurrentDevice({...currentDevice, name: e.target.value})}
              placeholder="例如：UPS-01"
            />
          </FormControl>
          
          <FormControl isRequired>
            <FormLabel>设备型号</FormLabel>
            <Input 
              value={currentDevice.model} 
              onChange={(e) => setCurrentDevice({...currentDevice, model: e.target.value})}
              placeholder="例如：APC SRT5KXLI"
            />
          </FormControl>
        </SimpleGrid>
        
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
          <FormControl isRequired>
            <FormLabel>序列号</FormLabel>
            <Input 
              value={currentDevice.serialNumber} 
              onChange={(e) => setCurrentDevice({...currentDevice, serialNumber: e.target.value})}
              placeholder="例如：SRT5K2021001"
            />
          </FormControl>
          
          <FormControl isRequired>
            <FormLabel>安装位置</FormLabel>
            <Input 
              value={currentDevice.location} 
              onChange={(e) => setCurrentDevice({...currentDevice, location: e.target.value})}
              placeholder="例如：1楼机房"
            />
          </FormControl>
        </SimpleGrid>
        
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
          <FormControl isRequired>
            <FormLabel>安装日期</FormLabel>
            <Input 
              type="date"
              value={currentDevice.installDate} 
              onChange={(e) => setCurrentDevice({...currentDevice, installDate: e.target.value})}
            />
          </FormControl>
          
          <FormControl isRequired>
            <FormLabel>维保到期日</FormLabel>
            <Input 
              type="date"
              value={currentDevice.maintenanceDate} 
              onChange={(e) => setCurrentDevice({...currentDevice, maintenanceDate: e.target.value})}
            />
          </FormControl>
        </SimpleGrid>
        
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} width="100%">
          <FormControl isRequired>
            <FormLabel>额定容量 (kVA)</FormLabel>
            <NumberInput 
              value={currentDevice.capacity} 
              onChange={(valueString) => setCurrentDevice({...currentDevice, capacity: Number(valueString)})}
              min={1}
              max={100}
            >
              <NumberInputField />
              <NumberInputStepper>
                <NumberIncrementStepper />
                <NumberDecrementStepper />
              </NumberInputStepper>
            </NumberInput>
          </FormControl>
          
          <FormControl isRequired>
            <FormLabel>电池组数</FormLabel>
            <NumberInput 
              value={currentDevice.batteryGroups} 
              onChange={(valueString) => setCurrentDevice({...currentDevice, batteryGroups: Number(valueString)})}
              min={1}
              max={20}
            >
              <NumberInputField />
              <NumberInputStepper>
                <NumberIncrementStepper />
                <NumberDecrementStepper />
              </NumberInputStepper>
            </NumberInput>
          </FormControl>
        </SimpleGrid>
        
        <FormControl isRequired isInvalid={!isValidIP(currentDevice.ipAddress)}>
          <FormLabel>IP地址</FormLabel>
          <Input 
            value={currentDevice.ipAddress} 
            onChange={(e) => setCurrentDevice({...currentDevice, ipAddress: e.target.value})}
            placeholder="例如：*************"
          />
          {!isValidIP(currentDevice.ipAddress) && <FormHelperText color="red.500">请输入有效的IP地址</FormHelperText>}
        </FormControl>
        
        <FormControl display="flex" alignItems="center">
          <FormLabel htmlFor="device-enabled" mb="0">
            启用设备
          </FormLabel>
          <Switch 
            id="device-enabled" 
            colorScheme="green" 
            isChecked={currentDevice.enabled}
            onChange={(e) => setCurrentDevice({...currentDevice, enabled: e.target.checked})}
          />
        </FormControl>
      </VStack>
    );
  };
  
  return (
    <Container maxW="container.xl" py={6}>
      {/* 页面标题 */}
      <Box mb={8} p={6} borderRadius="xl" bg={cardBg} boxShadow="sm" borderWidth="1px" borderColor={borderColor} position="relative" overflow="hidden">
        <Box position="absolute" top={0} left={0} right={0} height="6px" bgGradient="linear(to-r, orange.400, red.500)" />
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Flex align="center">
            <Button as={Link} href="/power-monitoring/ups" leftIcon={<ArrowLeft />} variant="ghost" size="sm" mr={4}>返回</Button>
            <Box>
              <Heading size="lg" mb={1}>UPS设备管理</Heading>
              <Text color="gray.500">管理UPS设备信息和配置</Text>
            </Box>
          </Flex>
          <Button leftIcon={<Plus weight="bold" />} colorScheme="blue" size="md" mt={{ base: 4, md: 0 }} onClick={openAddModal} px={6} py={2} fontWeight="bold" borderRadius="xl">添加设备</Button>
        </Flex>
      </Box>
      {/* 搜索与筛选栏 */}
      <Box mb={4}>
        <Flex gap={2} direction={{ base: 'column', md: 'row' }} align={{ base: 'stretch', md: 'center' }}>
          <InputGroup maxW={{ base: '100%', md: '320px' }}>
            <InputLeftElement pointerEvents="none" color="gray.400">
              <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none"><path d="M11 4a7 7 0 105.196 11.933l3.933 3.933a1 1 0 001.414-1.414l-3.933-3.933A7 7 0 0011 4zm-5 7a5 5 0 1110 0 5 5 0 01-10 0z" fill="currentColor"/></svg>
            </InputLeftElement>
            <Input placeholder="搜索设备名称/型号/序列号/位置/IP" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} />
          </InputGroup>
          <DeviceFilterBar status={filterEnabled} setStatus={setFilterEnabled} />
          <Tooltip label="批量删除">
            <IconButton icon={<Trash />} colorScheme="red" variant="outline" onClick={batchDelete} isDisabled={selectedIds.length === 0} aria-label="批量删除" />
          </Tooltip>
          <Tooltip label="批量启用">
            <IconButton icon={<SlidersHorizontal />} colorScheme="green" variant="outline" onClick={() => batchToggleEnable(true)} isDisabled={selectedIds.length === 0} aria-label="批量启用" />
          </Tooltip>
          <Tooltip label="批量禁用">
            <IconButton icon={<SlidersHorizontal />} colorScheme="gray" variant="outline" onClick={() => batchToggleEnable(false)} isDisabled={selectedIds.length === 0} aria-label="批量禁用" />
          </Tooltip>
        </Flex>
      </Box>
      {/* 设备列表 */}
      <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor} mb={6}>
        <CardHeader>
          <Heading size="md">UPS设备列表</Heading>
        </CardHeader>
        <CardBody>
          {isMobile ? (
            <Box>
              {filteredDevices.map(device => (
                <DeviceCard
                  key={device.id}
                  device={device}
                  onEdit={() => openEditModal(device)}
                  onDelete={() => confirmDelete(device.id)}
                  onToggle={() => setUpsDevices(upsDevices.map(d => d.id === device.id ? { ...d, enabled: !d.enabled } : d))}
                  selected={selectedIds.includes(device.id)}
                  onSelect={(e: any) => setSelectedIds(e.target.checked ? [...selectedIds, device.id] : selectedIds.filter(id => id !== device.id))}
                />
              ))}
            </Box>
          ) : (
            <Table variant="simple">
              <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                <Tr>
                  <Th><Checkbox isChecked={selectedIds.length === filteredDevices.length && filteredDevices.length > 0} isIndeterminate={selectedIds.length > 0 && selectedIds.length < filteredDevices.length} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSelectedIds(e.target.checked ? filteredDevices.map(d => d.id) : [])} /></Th>
                  <Th>设备名称</Th>
                  <Th>型号</Th>
                  <Th>位置</Th>
                  <Th>容量</Th>
                  <Th>状态</Th>
                  <Th>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredDevices.map((device) => (
                  <Tr key={device.id} bg={selectedIds.includes(device.id) ? 'teal.50' : undefined}>
                    <Td><Checkbox isChecked={selectedIds.includes(device.id)} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSelectedIds(e.target.checked ? [...selectedIds, device.id] : selectedIds.filter(id => id !== device.id))} /></Td>
                    <Td><Flex align="center"><Icon as={Lightning} color="orange.500" mr={2} />{device.name}</Flex></Td>
                    <Td>{device.model}</Td>
                    <Td>{device.location}</Td>
                    <Td>{device.capacity} kVA</Td>
                    <Td><Badge colorScheme={device.enabled ? 'green' : 'gray'} variant="subtle" px={2} py={1} borderRadius="full">{device.enabled ? '启用' : '禁用'}</Badge></Td>
                    <Td>
                      <HStack spacing={1}>
                        <Tooltip label="编辑">
                          <IconButton aria-label="编辑" icon={<Pencil />} size="sm" variant="ghost" colorScheme="blue" onClick={() => openEditModal(device)} />
                        </Tooltip>
                        <Tooltip label="删除">
                          <IconButton aria-label="删除" icon={<Trash />} size="sm" variant="ghost" colorScheme="red" onClick={() => confirmDelete(device.id)} />
                        </Tooltip>
                        <Switch isChecked={device.enabled} onChange={() => setUpsDevices(upsDevices.map(d => d.id === device.id ? { ...d, enabled: !d.enabled } : d))} colorScheme={device.enabled ? 'green' : 'gray'} size="md" ml={2} />
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          )}
        </CardBody>
      </Card>
      
      {/* 设备详情 */}
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
        <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
          <CardHeader>
            <Heading size="md">设备概览</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="start" spacing={4}>
              <Flex align="center">
                <Icon as={DeviceTablet} color="blue.500" mr={2} />
                <Text fontWeight="medium">总设备数：{upsDevices.length}</Text>
              </Flex>
              <Flex align="center">
                <Icon as={Check} color="green.500" mr={2} />
                <Text fontWeight="medium">启用设备：{upsDevices.filter(d => d.enabled).length}</Text>
              </Flex>
              <Flex align="center">
                <Icon as={Lightning} color="orange.500" mr={2} />
                <Text fontWeight="medium">总容量：{upsDevices.reduce((sum, d) => sum + d.capacity, 0)} kVA</Text>
              </Flex>
              <Flex align="center">
                <Icon as={ClockClockwise} color="purple.500" mr={2} />
                <Text fontWeight="medium">
                  最近添加：
                  {upsDevices.length > 0 
                    ? upsDevices.sort((a, b) => new Date(b.installDate).getTime() - new Date(a.installDate).getTime())[0].name
                    : '无'
                  }
                </Text>
              </Flex>
            </VStack>
          </CardBody>
        </Card>
        
        <Card borderRadius="lg" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor}>
          <CardHeader>
            <Heading size="md">维保信息</Heading>
          </CardHeader>
          <CardBody>
            <Table size="sm" variant="simple">
              <Thead>
                <Tr>
                  <Th>设备名称</Th>
                  <Th>维保到期</Th>
                  <Th>状态</Th>
                </Tr>
              </Thead>
              <Tbody>
                {upsDevices
                  .sort((a, b) => new Date(a.maintenanceDate).getTime() - new Date(b.maintenanceDate).getTime())
                  .slice(0, 3)
                  .map((device) => {
                    const today = new Date();
                    const expiryDate = new Date(device.maintenanceDate);
                    const daysRemaining = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                    
                    let status = 'green';
                    if (daysRemaining < 0) {
                      status = 'red';
                    } else if (daysRemaining < 90) {
                      status = 'yellow';
                    }
                    
                    return (
                      <Tr key={device.id}>
                        <Td>{device.name}</Td>
                        <Td>{device.maintenanceDate}</Td>
                        <Td>
                          <Badge 
                            colorScheme={status}
                            variant="subtle"
                            px={2}
                            py={1}
                            borderRadius="full"
                          >
                            {daysRemaining < 0 
                              ? '已过期' 
                              : daysRemaining < 90 
                                ? '即将到期' 
                                : '正常'
                            }
                          </Badge>
                        </Td>
                      </Tr>
                    );
                  })
                }
              </Tbody>
            </Table>
            <Button 
              as={Link}
              href="/power-monitoring/ups/maintenance"
              variant="link" 
              colorScheme="blue" 
              size="sm"
              mt={4}
            >
              查看全部维保信息
            </Button>
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* 添加设备模态框 */}
      <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>添加UPS设备</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <DeviceForm />
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={addDevice}>
              添加
            </Button>
            <Button variant="ghost" onClick={() => setIsAddModalOpen(false)}>取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* 编辑设备模态框 */}
      <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>编辑UPS设备</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <DeviceForm isEdit={true} />
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={updateDevice}>
              保存
            </Button>
            <Button variant="ghost" onClick={() => setIsEditModalOpen(false)}>取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* 删除确认对话框 */}
      <AlertDialog
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              删除设备
            </AlertDialogHeader>

            <AlertDialogBody>
              确定要删除这个UPS设备吗？此操作无法撤销，并且会删除与该设备相关的所有数据。
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onClose}>
                取消
              </Button>
              <Button colorScheme="red" onClick={deleteDevice} ml={3}>
                删除
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Container>
  )
}

export default UPSDevicesPage
