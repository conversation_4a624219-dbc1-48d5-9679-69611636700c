'use client'

import React from 'react'
import {
  Box, 
  Container, 
  Heading, 
  Text, 
  SimpleGrid, 
  Card, 
  CardBody,
  Flex, 
  Icon,
  Button,
  HStack,
  VStack,
  CircularProgress,
  CircularProgressLabel,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Progress,
  Grid,
  GridItem
} from '@chakra-ui/react'
import { Lightning, ArrowLeft, BatteryFull, Plug, Clock, Gauge } from '@phosphor-icons/react'
import Link from 'next/link'

const UPS01DetailPage = () => {
  return (
    <Container maxW="container.xl" py={6}>
      <Flex align="center" mb={6}>
        <Button 
          as={Link}
          href="/power-monitoring/ups"
          leftIcon={<ArrowLeft />}
          variant="ghost"
          size="sm"
          mr={4}
        >
          返回UPS监控
        </Button>
        <Heading size="lg">UPS-01 详细信息</Heading>
        <Badge 
          colorScheme="green" 
          fontSize="sm" 
          ml={4}
          px={2}
          py={1}
        >
          运行中
        </Badge>
      </Flex>

      {/* 核心指标 */}
      <SimpleGrid columns={{ base: 1, md: 5 }} spacing={6} mb={8}>
        <Card>
          <CardBody>
            <Flex direction="column" align="center" justify="center" h="100%">
              <Icon as={BatteryFull} color="green.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>电池剩余容量</Text>
              <CircularProgress value={100} color="green.400" size="100px">
                <CircularProgressLabel fontWeight="bold">100%</CircularProgressLabel>
              </CircularProgress>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" align="center" justify="center" h="100%">
              <Icon as={Clock} color="blue.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>剩余放电时间</Text>
              <Text fontSize="2xl" fontWeight="bold" color="blue.500">240分钟</Text>
              <Text fontSize="sm" color="gray.500" mt={1}>满负载状态下</Text>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" align="center" justify="center" h="100%">
              <Icon as={Lightning} color="orange.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>电池电压</Text>
              <Text fontSize="2xl" fontWeight="bold" color="orange.500">240V</Text>
              <Text fontSize="sm" color="gray.500" mt={1}>正常范围内</Text>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" align="center" justify="center" h="100%">
              <Icon as={Gauge} color="purple.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>输出负载百分比</Text>
              <CircularProgress value={45} color="purple.400" size="100px">
                <CircularProgressLabel fontWeight="bold">45%</CircularProgressLabel>
              </CircularProgress>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" align="center" justify="center" h="100%">
              <Icon as={Plug} color="cyan.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>输出有功功率</Text>
              <Text fontSize="2xl" fontWeight="bold" color="cyan.500">2.5kW</Text>
              <Text fontSize="sm" color="gray.500" mt={1}>最大容量: 5kW</Text>
            </Flex>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* 详细数据表格 */}
      <Grid templateColumns="repeat(12, 1fr)" gap={6} mb={8}>
        <GridItem colSpan={{ base: 12, lg: 7 }}>
          <Card>
            <CardBody>
              <Heading size="md" mb={4}>UPS 详细参数</Heading>
              <Table size="sm" variant="simple">
                <Tbody>
                  <Tr>
                    <Td fontWeight="medium">输入电压</Td>
                    <Td>220V</Td>
                    <Td fontWeight="medium">输入频率</Td>
                    <Td>50Hz</Td>
                  </Tr>
                  <Tr>
                    <Td fontWeight="medium">输出电压</Td>
                    <Td>220V</Td>
                    <Td fontWeight="medium">输出频率</Td>
                    <Td>50Hz</Td>
                  </Tr>
                  <Tr>
                    <Td fontWeight="medium">电池组数</Td>
                    <Td>4组</Td>
                    <Td fontWeight="medium">单组电压</Td>
                    <Td>60V</Td>
                  </Tr>
                  <Tr>
                    <Td fontWeight="medium">额定容量</Td>
                    <Td>5kVA</Td>
                    <Td fontWeight="medium">额定功率</Td>
                    <Td>5kW</Td>
                  </Tr>
                  <Tr>
                    <Td fontWeight="medium">当前温度</Td>
                    <Td>25℃</Td>
                    <Td fontWeight="medium">运行时长</Td>
                    <Td>315天</Td>
                  </Tr>
                  <Tr>
                    <Td fontWeight="medium">电池健康度</Td>
                    <Td>95%</Td>
                    <Td fontWeight="medium">最后测试时间</Td>
                    <Td>2023-12-15</Td>
                  </Tr>
                </Tbody>
              </Table>
            </CardBody>
          </Card>
        </GridItem>

        <GridItem colSpan={{ base: 12, lg: 5 }}>
          <Card>
            <CardBody>
              <Heading size="md" mb={4}>负载分布</Heading>
              <Box mt={6}>
                <Flex justify="space-between" mb={2}>
                  <Text>关键负载</Text>
                  <Text fontWeight="medium">2.0kW (80%)</Text>
                </Flex>
                <Progress value={80} colorScheme="blue" mb={4} />
                
                <Flex justify="space-between" mb={2}>
                  <Text>非关键负载</Text>
                  <Text fontWeight="medium">0.5kW (20%)</Text>
                </Flex>
                <Progress value={20} colorScheme="green" />
              </Box>

              <Divider my={6} />

              <Heading size="md" mb={4}>设备信息</Heading>
              <VStack align="start" spacing={2}>
                <Flex justify="space-between" w="100%">
                  <Text color="gray.500">品牌型号</Text>
                  <Text fontWeight="medium">APC SRT5KXLI</Text>
                </Flex>
                <Flex justify="space-between" w="100%">
                  <Text color="gray.500">安装位置</Text>
                  <Text fontWeight="medium">1楼机房</Text>
                </Flex>
                <Flex justify="space-between" w="100%">
                  <Text color="gray.500">维保期限</Text>
                  <Text fontWeight="medium">2025年12月31日</Text>
                </Flex>
              </VStack>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>
    </Container>
  )
}

export default UPS01DetailPage 