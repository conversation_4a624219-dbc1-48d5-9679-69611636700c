'use client'

import React, { useState } from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  useColorModeValue,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Switch,
  Flex,
  Icon,
  HStack,
  Card,
  CardHeader,
  CardBody,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select as ChakraSelect,
  useDisclosure,
  FormErrorMessage,
  Stack,
  Tag,
  TagLabel,
  TagLeftIcon,
  Tooltip,
  useBreakpointValue,
  IconButton
} from '@chakra-ui/react'
import Link from 'next/link'
import {
  Lightning,
  BatteryFull,
  Thermometer,
  Gauge,
  Bell,
  Plus,
  Pencil,
  Trash,
  Funnel,
  SlidersHorizontal,
  ArrowLeft
} from '@phosphor-icons/react'

// 告警设置类型
interface AlertSetting {
  id: string;
  name: string;
  type: 'battery' | 'load' | 'voltage' | 'temperature';
  threshold: number;
  enabled: boolean;
  severity: 'low' | 'medium' | 'high';
}

// 示例告警设置数据
const initialAlertSettings: AlertSetting[] = [
  {
    id: '1',
    name: '电池电量低',
    type: 'battery',
    threshold: 20,
    enabled: true,
    severity: 'high'
  },
  {
    id: '2',
    name: '负载过高',
    type: 'load',
    threshold: 80,
    enabled: true,
    severity: 'medium'
  },
  {
    id: '3',
    name: '输入电压异常',
    type: 'voltage',
    threshold: 10,
    enabled: true,
    severity: 'medium'
  },
  {
    id: '4',
    name: '温度过高',
    type: 'temperature',
    threshold: 35,
    enabled: true,
    severity: 'high'
  }
];

// 辅助函数：获取告警类型图标
const getAlertTypeIcon = (type: string) => {
  switch (type) {
    case 'battery':
      return BatteryFull;
    case 'load':
      return Gauge;
    case 'voltage':
      return Lightning;
    case 'temperature':
      return Thermometer;
    default:
      return Bell;
  }
};

// 辅助函数：获取告警严重性颜色
const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'low':
      return 'blue';
    case 'medium':
      return 'yellow';
    case 'high':
      return 'red';
    default:
      return 'gray';
  }
};

// 新增：告警表单组件
const AlertForm = ({ isOpen, onClose, onSubmit, initialData }: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AlertSetting) => void;
  initialData?: AlertSetting;
}) => {
  const [form, setForm] = useState<AlertSetting>(initialData || {
    id: '',
    name: '',
    type: 'battery',
    threshold: 20,
    enabled: true,
    severity: 'medium',
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  React.useEffect(() => {
    if (isOpen) {
      setForm(initialData || {
        id: '',
        name: '',
        type: 'battery',
        threshold: 20,
        enabled: true,
        severity: 'medium',
      });
      setErrors({});
    }
  }, [isOpen, initialData]);

  const validate = () => {
    const err: { [key: string]: string } = {};
    if (!form.name.trim()) err.name = '告警名称不能为空';
    if (!form.type) err.type = '请选择类型';
    if (form.threshold === undefined || form.threshold === null || isNaN(Number(form.threshold))) err.threshold = '请输入阈值';
    if (!form.severity) err.severity = '请选择严重性';
    setErrors(err);
    return Object.keys(err).length === 0;
  };

  const handleSubmit = () => {
    if (validate()) {
      onSubmit({ ...form, id: form.id || `alert-${Date.now()}` });
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{initialData ? '编辑告警' : '添加告警'}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <FormControl isRequired isInvalid={!!errors.name} mb={3}>
            <FormLabel>告警名称</FormLabel>
            <Input value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} />
            <FormErrorMessage>{errors.name}</FormErrorMessage>
          </FormControl>
          <FormControl isRequired isInvalid={!!errors.type} mb={3}>
            <FormLabel>类型</FormLabel>
            <ChakraSelect value={form.type} onChange={e => setForm(f => ({ ...f, type: e.target.value as any }))}>
              <option value="battery">电池</option>
              <option value="load">负载</option>
              <option value="voltage">电压</option>
              <option value="temperature">温度</option>
            </ChakraSelect>
            <FormErrorMessage>{errors.type}</FormErrorMessage>
          </FormControl>
          <FormControl isRequired isInvalid={!!errors.threshold} mb={3}>
            <FormLabel>阈值</FormLabel>
            <Input type="number" value={form.threshold} onChange={e => setForm(f => ({ ...f, threshold: Number(e.target.value) }))} />
            <FormErrorMessage>{errors.threshold}</FormErrorMessage>
          </FormControl>
          <FormControl isRequired isInvalid={!!errors.severity} mb={3}>
            <FormLabel>严重性</FormLabel>
            <ChakraSelect value={form.severity} onChange={e => setForm(f => ({ ...f, severity: e.target.value as any }))}>
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
            </ChakraSelect>
            <FormErrorMessage>{errors.severity}</FormErrorMessage>
          </FormControl>
        </ModalBody>
        <ModalFooter>
          <Button onClick={onClose} mr={3}>取消</Button>
          <Button colorScheme="blue" onClick={handleSubmit}>{initialData ? '保存' : '添加'}</Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// 新增：筛选组件
const AlertFilterBar = ({ type, setType, severity, setSeverity, status, setStatus }: any) => {
  return (
    <Stack direction={{ base: 'column', md: 'row' }} spacing={4} mb={4} align="center">
      <Tag size="lg" colorScheme="blue" variant="subtle" px={3} py={2} borderRadius="full">
        <TagLeftIcon as={Funnel} />筛选
      </Tag>
      <ChakraSelect value={type} onChange={e => setType(e.target.value)} maxW="140px">
        <option value="all">全部类型</option>
        <option value="battery">电池</option>
        <option value="load">负载</option>
        <option value="voltage">电压</option>
        <option value="temperature">温度</option>
      </ChakraSelect>
      <ChakraSelect value={severity} onChange={e => setSeverity(e.target.value)} maxW="140px">
        <option value="all">全部严重性</option>
        <option value="high">高</option>
        <option value="medium">中</option>
        <option value="low">低</option>
      </ChakraSelect>
      <ChakraSelect value={status} onChange={e => setStatus(e.target.value)} maxW="140px">
        <option value="all">全部状态</option>
        <option value="enabled">启用</option>
        <option value="disabled">禁用</option>
      </ChakraSelect>
    </Stack>
  );
};

// 新增：移动端卡片
const AlertCard = ({ alert, onEdit, onDelete, onToggle }: any) => (
  <Box borderWidth="1px" borderRadius="lg" p={4} mb={3} bg="white" boxShadow="sm">
    <Flex align="center" mb={2}>
      <Icon as={getAlertTypeIcon(alert.type)} color={`${getSeverityColor(alert.severity)}.500`} boxSize={6} mr={2} />
      <Text fontWeight="bold" fontSize="lg">{alert.name}</Text>
      <Badge ml={2} colorScheme={getSeverityColor(alert.severity)}>{alert.severity === 'high' ? '高' : alert.severity === 'medium' ? '中' : '低'}</Badge>
    </Flex>
    <Flex mb={2} gap={4} wrap="wrap">
      <Tag colorScheme="blue" variant="subtle">{alert.type === 'battery' ? '电池' : alert.type === 'load' ? '负载' : alert.type === 'voltage' ? '电压' : '温度'}</Tag>
      <Tag colorScheme="gray" variant="subtle">阈值: {alert.threshold}{alert.type === 'temperature' ? '°C' : '%'}</Tag>
      <Tag colorScheme={alert.enabled ? 'green' : 'gray'}>{alert.enabled ? '启用' : '禁用'}</Tag>
    </Flex>
    <Flex align="center" gap={2} mt={2}>
      <Tooltip label="编辑">
        <IconButton aria-label="编辑" icon={<Pencil />} size="sm" variant="ghost" colorScheme="blue" onClick={onEdit} />
      </Tooltip>
      <Tooltip label="删除">
        <IconButton aria-label="删除" icon={<Trash />} size="sm" variant="ghost" colorScheme="red" onClick={onDelete} />
      </Tooltip>
      <Switch isChecked={alert.enabled} onChange={onToggle} colorScheme={alert.enabled ? 'green' : 'gray'} size="md" ml={2} />
      <Text fontSize="sm" color={alert.enabled ? 'green.600' : 'gray.500'} ml={1}>{alert.enabled ? '已启用' : '已禁用'}</Text>
    </Flex>
  </Box>
);

export default function UPSSettingsPage() {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const toast = useToast();

  // 状态
  const [alertSettings, setAlertSettings] = useState<AlertSetting[]>(initialAlertSettings);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAlert, setEditingAlert] = useState<AlertSetting | undefined>(undefined);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  // 新增筛选状态
  const [filterType, setFilterType] = useState('all');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const isMobile = useBreakpointValue({ base: true, md: false });

  // 过滤后的告警
  const filteredAlerts = alertSettings.filter(a =>
    (filterType === 'all' || a.type === filterType) &&
    (filterSeverity === 'all' || a.severity === filterSeverity) &&
    (filterStatus === 'all' || (filterStatus === 'enabled' ? a.enabled : !a.enabled))
  );

  // 新增/编辑
  const handleAdd = () => {
    setEditingAlert(undefined);
    setIsFormOpen(true);
  };
  const handleEdit = (alert: AlertSetting) => {
    setEditingAlert(alert);
    setIsFormOpen(true);
  };
  const handleFormSubmit = (data: AlertSetting) => {
    setAlertSettings(prev => {
      const exists = prev.find(a => a.id === data.id);
      if (exists) {
        return prev.map(a => a.id === data.id ? data : a);
      }
      return [...prev, data];
    });
    setIsFormOpen(false);
  };
  // 删除
  const handleDelete = (id: string) => {
    setDeleteId(id);
    onDeleteOpen();
  };
  const confirmDelete = () => {
    setAlertSettings(prev => prev.filter(a => a.id !== deleteId));
    setDeleteId(null);
    onDeleteClose();
  };

  return (
    <Box bg={bgColor} minH="100vh">
      <Container maxW="container.lg" py={6}>
        <Box mb={6} p={6} borderRadius="xl" bg={cardBg} boxShadow="md" borderWidth="1px" borderColor="gray.200">
          <Flex align="center" justify="space-between">
            <Flex align="center">
              <Button as={Link} href="/power-monitoring/ups" leftIcon={<ArrowLeft />} variant="ghost" size="sm" mr={4}>返回</Button>
              <Box>
                <Heading size="lg" mb={1}>UPS告警设置</Heading>
                <Text color="gray.500">配置UPS监控的告警阈值和通知设置</Text>
              </Box>
            </Flex>
            <Button leftIcon={<Plus />} colorScheme="blue" size="md" px={6} py={2} fontWeight="bold" borderRadius="xl" onClick={handleAdd}>
              添加告警
            </Button>
          </Flex>
        </Box>
        <Box mb={4}>
          <AlertFilterBar type={filterType} setType={setFilterType} severity={filterSeverity} setSeverity={setFilterSeverity} status={filterStatus} setStatus={setFilterStatus} />
        </Box>
        <Card borderRadius="xl" boxShadow="md" bg={cardBg} borderWidth="1px" borderColor={borderColor} overflow="hidden">
          <CardHeader pb={2}>
            <Flex justify="space-between" align="center">
              <Heading size="md" display="flex" alignItems="center">
                <Icon as={Bell} color="blue.500" mr={2} />
                告警列表
                <Badge ml={2} colorScheme="blue" borderRadius="full">{filteredAlerts.length}</Badge>
              </Heading>
            </Flex>
          </CardHeader>
          <CardBody>
            {isMobile ? (
              <Box>
                {filteredAlerts.map(alert => (
                  <AlertCard
                    key={alert.id}
                    alert={alert}
                    onEdit={() => handleEdit(alert)}
                    onDelete={() => handleDelete(alert.id)}
                    onToggle={() => {
                      setAlertSettings(alertSettings.map(item =>
                        item.id === alert.id ? { ...item, enabled: !item.enabled } : item
                      ));
                      toast({
                        title: `${alert.enabled ? '已禁用' : '已启用'}告警`,
                        description: `${alert.name}已${alert.enabled ? '禁用' : '启用'}`,
                        status: "success",
                        duration: 3000,
                        isClosable: true,
                      });
                    }}
                  />
                ))}
              </Box>
            ) : (
              <Table variant="simple">
                <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                  <Tr>
                    <Th>告警名称</Th>
                    <Th>类型</Th>
                    <Th>阈值</Th>
                    <Th>严重性</Th>
                    <Th>状态</Th>
                    <Th>操作</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {filteredAlerts.map((alert) => (
                    <Tr key={alert.id}>
                      <Td>
                        <Flex align="center">
                          <Icon as={getAlertTypeIcon(alert.type)} color={`${getSeverityColor(alert.severity)}.500`} boxSize={5} mr={2} />
                          <Text fontWeight="medium">{alert.name}</Text>
                        </Flex>
                      </Td>
                      <Td>
                        <Badge colorScheme={alert.type === 'battery' || alert.type === 'temperature' ? 'red' : alert.type === 'load' ? 'orange' : alert.type === 'voltage' ? 'yellow' : 'blue'} variant="subtle" px={2} py={1} borderRadius="full">
                          {alert.type === 'battery' ? '电池' : alert.type === 'load' ? '负载' : alert.type === 'voltage' ? '电压' : alert.type === 'temperature' ? '温度' : '未知'}
                        </Badge>
                      </Td>
                      <Td>
                        <Text fontWeight="medium">{alert.threshold}{alert.type === 'battery' || alert.type === 'load' ? '%' : alert.type === 'voltage' ? '%' : '°C'}</Text>
                      </Td>
                      <Td>
                        <Badge colorScheme={getSeverityColor(alert.severity)} variant="solid" px={2} py={1} borderRadius="full">
                          {alert.severity === 'low' ? '低' : alert.severity === 'medium' ? '中' : alert.severity === 'high' ? '高' : '未知'}
                        </Badge>
                      </Td>
                      <Td>
                        <Switch isChecked={alert.enabled} onChange={() => {
                          setAlertSettings(alertSettings.map(item =>
                            item.id === alert.id ? { ...item, enabled: !item.enabled } : item
                          ));
                          toast({
                            title: `${alert.enabled ? '已禁用' : '已启用'}告警`,
                            description: `${alert.name}已${alert.enabled ? '禁用' : '启用'}`,
                            status: "success",
                            duration: 3000,
                            isClosable: true,
                          });
                        }} colorScheme={alert.enabled ? 'green' : 'gray'} size="md" />
                        <Text as="span" fontSize="sm" color={alert.enabled ? 'green.600' : 'gray.500'} ml={2}>{alert.enabled ? '已启用' : '已禁用'}</Text>
                      </Td>
                      <Td>
                        <HStack spacing={1}>
                          <Tooltip label="编辑">
                            <IconButton aria-label="编辑" icon={<Pencil />} size="sm" variant="ghost" colorScheme="blue" onClick={() => handleEdit(alert)} />
                          </Tooltip>
                          <Tooltip label="删除">
                            <IconButton aria-label="删除" icon={<Trash />} size="sm" variant="ghost" colorScheme="red" onClick={() => handleDelete(alert.id)} />
                          </Tooltip>
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </CardBody>
        </Card>
        <AlertForm
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onSubmit={handleFormSubmit}
          initialData={editingAlert}
        />
        {/* 删除确认对话框 */}
        <Modal isOpen={isDeleteOpen} onClose={onDeleteClose} isCentered>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>删除告警</ModalHeader>
            <ModalCloseButton />
            <ModalBody>确定要删除该告警项吗？此操作无法撤销。</ModalBody>
            <ModalFooter>
              <Button onClick={onDeleteClose}>取消</Button>
              <Button colorScheme="red" ml={3} onClick={confirmDelete}>删除</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Container>
    </Box>
  );
}
