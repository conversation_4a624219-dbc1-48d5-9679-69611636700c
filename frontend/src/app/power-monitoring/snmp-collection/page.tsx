'use client'

import React, { useState, useEffect } from 'react'
import {
  getCollectionConfig,
  updateCollectionConfig,
  getDataStats,
  triggerDataCleanup,
  triggerDataCollection,
  SNMPCollectionConfig,
  DataStats
} from '@/services/snmpCollectionService'
import {
  Box,
  Heading,
  Text,
  Flex,
  Button,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useDisclosure,
  Spinner,
  Center,
  Select,
  Input,
  FormControl,
  FormLabel,
  Stack,
  HStack,
  Divider,
  useToast,
  Card,
  CardBody,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Progress,
  Switch,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
} from '@chakra-ui/react'
import {
  Plus,
  PencilSimple,
  Trash,
  MagnifyingGlass,
  ArrowsClockwise,
  Warning,
  Clock,
  Database,
  Gear,
  ChartLine,
} from '@phosphor-icons/react'

// SNMP采集配置页面
const SNMPCollectionPage: React.FC = () => {
  // 状态
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [dataStats, setDataStats] = useState<DataStats | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  // 采集频率配置
  const [collectionIntervals, setCollectionIntervals] = useState({
    environment: 60,
    ups: 60,
    mains: 60,
    network: 300,
    printer: 900,
    default: 300
  });

  // 数据保留配置
  const [dataRetention, setDataRetention] = useState({
    environment: 90,
    ups: 90,
    mains: 90,
    network: 30,
    printer: 30,
    custom: 30
  });

  // 启用状态
  const [enabledTypes, setEnabledTypes] = useState({
    environment: true,
    ups: true,
    mains: true,
    network: true,
    printer: true,
    custom: true
  });

  // Toast
  const toast = useToast();

  // 颜色
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const statBg = useColorModeValue('gray.50', 'gray.700');

  // 加载数据
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setDebugInfo(prev => [...prev, '开始加载SNMP采集管理页面数据...']);
      console.log('开始加载SNMP采集管理页面数据...');

      // 获取SNMP采集配置
      try {
        setDebugInfo(prev => [...prev, '正在获取SNMP采集配置...']);
        const config = await getCollectionConfig();
        setCollectionIntervals(config.collection_intervals);
        setDataRetention(config.data_retention);
        setEnabledTypes(config.enabled_types);
        setDebugInfo(prev => [...prev, 'SNMP采集配置加载成功']);
        console.log('SNMP采集配置加载成功');
      } catch (configErr: any) {
        const errMsg = `加载SNMP采集配置失败: ${configErr.message || '未知错误'}`;
        setDebugInfo(prev => [...prev, errMsg]);
        console.error(errMsg, configErr);
        // 继续执行，不中断整个加载过程
      }

      // 获取数据统计
      try {
        setDebugInfo(prev => [...prev, '正在获取SNMP数据统计...']);
        const stats = await getDataStats();
        setDataStats(stats);
        setDebugInfo(prev => [...prev, 'SNMP数据统计加载成功']);
        console.log('SNMP数据统计加载成功');
      } catch (statsErr: any) {
        const errMsg = `加载SNMP数据统计失败: ${statsErr.message || '未知错误'}`;
        setDebugInfo(prev => [...prev, errMsg]);
        console.error(errMsg, statsErr);
        // 继续执行，不中断整个加载过程
      }

      setIsLoading(false);
      setDebugInfo(prev => [...prev, '数据加载完成']);
    } catch (err: any) {
      const errMsg = `加载SNMP采集配置失败: ${err.message || '未知错误'}`;
      console.error(errMsg, err);
      setDebugInfo(prev => [...prev, errMsg]);
      setError('加载数据失败，请稍后重试');
      setIsLoading(false);
    }
  };

  // 保存配置
  const saveConfig = async () => {
    try {
      setIsSaving(true);

      // 构建配置对象
      const config: SNMPCollectionConfig = {
        collection_intervals: collectionIntervals,
        data_retention: dataRetention,
        enabled_types: enabledTypes
      };

      // 调用API保存配置
      await updateCollectionConfig(config);

      setIsSaving(false);
      toast({
        title: '保存成功',
        description: 'SNMP采集配置已成功保存',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

    } catch (err) {
      console.error('保存SNMP采集配置失败:', err);
      setIsSaving(false);
      toast({
        title: '保存失败',
        description: '无法保存SNMP采集配置，请稍后重试',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 手动触发数据清理
  const handleDataCleanup = async () => {
    try {
      const result = await triggerDataCleanup();
      toast({
        title: '操作成功',
        description: result.message,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      console.error('触发数据清理失败:', err);
      toast({
        title: '操作失败',
        description: '无法触发数据清理，请稍后重试',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 手动触发数据采集
  const handleDataCollection = async (deviceType: string) => {
    try {
      const result = await triggerDataCollection(deviceType);
      toast({
        title: '操作成功',
        description: result.message,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (err) {
      console.error('触发数据采集失败:', err);
      toast({
        title: '操作失败',
        description: '无法触发数据采集，请稍后重试',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 初始加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 处理采集频率变更
  const handleIntervalChange = (type: string, value: number) => {
    setCollectionIntervals({
      ...collectionIntervals,
      [type]: value
    });
  };

  // 处理数据保留变更
  const handleRetentionChange = (type: string, value: number) => {
    setDataRetention({
      ...dataRetention,
      [type]: value
    });
  };

  // 处理启用状态变更
  const handleEnabledChange = (type: string, enabled: boolean) => {
    setEnabledTypes({
      ...enabledTypes,
      [type]: enabled
    });
  };

  return (
    <Box maxW="container.xl" mx="auto" py={8} px={4}>
      {/* 页面标题区域 */}
      <Box
        mb={8}
        p={6}
        borderRadius="xl"
        bg={bgColor}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          height="6px"
          bgGradient="linear(to-r, teal.400, blue.500)"
        />

        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>SNMP采集服务配置</Heading>
            <Text color="gray.500" fontSize="md">配置SNMP数据采集频率和数据保留策略</Text>
          </Box>
          <Flex mt={{ base: 4, md: 0 }} align="center" gap={4}>
            <Button
              leftIcon={<Gear weight="bold" />}
              colorScheme="teal"
              size="md"
              isLoading={isSaving}
              loadingText="保存中"
              onClick={saveConfig}
            >
              保存配置
            </Button>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              variant="outline"
              colorScheme="blue"
              size="md"
              isLoading={isLoading}
              loadingText="刷新中"
              onClick={loadData}
            >
              刷新
            </Button>
          </Flex>
        </Flex>
      </Box>

      {/* 主要内容区域 */}
      {isLoading ? (
        <Center py={10}>
          <Spinner size="xl" color="teal.500" thickness="4px" />
        </Center>
      ) : error ? (
        <Box
          p={6}
          borderRadius="lg"
          bg="red.50"
          color="red.500"
          borderWidth="1px"
          borderColor="red.200"
          textAlign="center"
        >
          <Warning size={32} weight="bold" style={{ margin: '0 auto 16px' }} />
          <Heading size="md" mb={2}>{error}</Heading>
          <Button onClick={loadData} colorScheme="red" size="sm" mt={4}>
            重试
          </Button>
        </Box>
      ) : (
        <Box>
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} mb={8}>
            <Card borderRadius="xl" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex align="center" mb={4}>
                  <Box
                    bg={useColorModeValue('teal.50', 'teal.900')}
                    p={2}
                    borderRadius="md"
                    mr={3}
                  >
                    <Clock size={24} weight="fill" color={useColorModeValue('teal.500', 'teal.300')} />
                  </Box>
                  <Heading size="md">采集频率配置</Heading>
                </Flex>
                <Text fontSize="sm" color="gray.500" mb={4}>
                  设置不同类型设备的SNMP数据采集频率，单位为秒
                </Text>

                <Stack spacing={4}>
                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="env-interval" mb="0" fontWeight="medium">
                      环境监控设备
                    </FormLabel>
                    <Flex align="center">
                      <Switch
                        id="env-enabled"
                        colorScheme="teal"
                        mr={4}
                        isChecked={enabledTypes.environment}
                        onChange={(e) => handleEnabledChange('environment', e.target.checked)}
                      />
                      <NumberInput
                        id="env-interval"
                        min={10}
                        max={3600}
                        step={10}
                        value={collectionIntervals.environment}
                        onChange={(_, value) => handleIntervalChange('environment', value)}
                        isDisabled={!enabledTypes.environment}
                        maxW="120px"
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </Flex>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="ups-interval" mb="0" fontWeight="medium">
                      UPS设备
                    </FormLabel>
                    <Flex align="center">
                      <Switch
                        id="ups-enabled"
                        colorScheme="teal"
                        mr={4}
                        isChecked={enabledTypes.ups}
                        onChange={(e) => handleEnabledChange('ups', e.target.checked)}
                      />
                      <NumberInput
                        id="ups-interval"
                        min={10}
                        max={3600}
                        step={10}
                        value={collectionIntervals.ups}
                        onChange={(_, value) => handleIntervalChange('ups', value)}
                        isDisabled={!enabledTypes.ups}
                        maxW="120px"
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </Flex>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="mains-interval" mb="0" fontWeight="medium">
                      市电监控设备
                    </FormLabel>
                    <Flex align="center">
                      <Switch
                        id="mains-enabled"
                        colorScheme="teal"
                        mr={4}
                        isChecked={enabledTypes.mains}
                        onChange={(e) => handleEnabledChange('mains', e.target.checked)}
                      />
                      <NumberInput
                        id="mains-interval"
                        min={10}
                        max={3600}
                        step={10}
                        value={collectionIntervals.mains}
                        onChange={(_, value) => handleIntervalChange('mains', value)}
                        isDisabled={!enabledTypes.mains}
                        maxW="120px"
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </Flex>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="network-interval" mb="0" fontWeight="medium">
                      网络设备
                    </FormLabel>
                    <Flex align="center">
                      <Switch
                        id="network-enabled"
                        colorScheme="teal"
                        mr={4}
                        isChecked={enabledTypes.network}
                        onChange={(e) => handleEnabledChange('network', e.target.checked)}
                      />
                      <NumberInput
                        id="network-interval"
                        min={60}
                        max={3600}
                        step={60}
                        value={collectionIntervals.network}
                        onChange={(_, value) => handleIntervalChange('network', value)}
                        isDisabled={!enabledTypes.network}
                        maxW="120px"
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </Flex>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="printer-interval" mb="0" fontWeight="medium">
                      打印机设备
                    </FormLabel>
                    <Flex align="center">
                      <Switch
                        id="printer-enabled"
                        colorScheme="teal"
                        mr={4}
                        isChecked={enabledTypes.printer}
                        onChange={(e) => handleEnabledChange('printer', e.target.checked)}
                      />
                      <NumberInput
                        id="printer-interval"
                        min={300}
                        max={7200}
                        step={300}
                        value={collectionIntervals.printer}
                        onChange={(_, value) => handleIntervalChange('printer', value)}
                        isDisabled={!enabledTypes.printer}
                        maxW="120px"
                      >
                        <NumberInputField />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </Flex>
                  </FormControl>
                </Stack>
              </CardBody>
            </Card>

            <Card borderRadius="xl" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Flex align="center" mb={4}>
                  <Box
                    bg={useColorModeValue('blue.50', 'blue.900')}
                    p={2}
                    borderRadius="md"
                    mr={3}
                  >
                    <Database size={24} weight="fill" color={useColorModeValue('blue.500', 'blue.300')} />
                  </Box>
                  <Heading size="md">数据保留策略</Heading>
                </Flex>
                <Text fontSize="sm" color="gray.500" mb={4}>
                  设置不同类型数据的保留天数，超过保留期的数据将被自动清理
                </Text>

                <Stack spacing={4}>
                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="env-retention" mb="0" fontWeight="medium">
                      环境监控数据
                    </FormLabel>
                    <NumberInput
                      id="env-retention"
                      min={7}
                      max={365}
                      step={1}
                      value={dataRetention.environment}
                      onChange={(_, value) => handleRetentionChange('environment', value)}
                      maxW="120px"
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="ups-retention" mb="0" fontWeight="medium">
                      UPS数据
                    </FormLabel>
                    <NumberInput
                      id="ups-retention"
                      min={7}
                      max={365}
                      step={1}
                      value={dataRetention.ups}
                      onChange={(_, value) => handleRetentionChange('ups', value)}
                      maxW="120px"
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="mains-retention" mb="0" fontWeight="medium">
                      市电监控数据
                    </FormLabel>
                    <NumberInput
                      id="mains-retention"
                      min={7}
                      max={365}
                      step={1}
                      value={dataRetention.mains}
                      onChange={(_, value) => handleRetentionChange('mains', value)}
                      maxW="120px"
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="network-retention" mb="0" fontWeight="medium">
                      网络设备数据
                    </FormLabel>
                    <NumberInput
                      id="network-retention"
                      min={7}
                      max={365}
                      step={1}
                      value={dataRetention.network}
                      onChange={(_, value) => handleRetentionChange('network', value)}
                      maxW="120px"
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="printer-retention" mb="0" fontWeight="medium">
                      打印机数据
                    </FormLabel>
                    <NumberInput
                      id="printer-retention"
                      min={7}
                      max={365}
                      step={1}
                      value={dataRetention.printer}
                      onChange={(_, value) => handleRetentionChange('printer', value)}
                      maxW="120px"
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <FormLabel htmlFor="custom-retention" mb="0" fontWeight="medium">
                      自定义SNMP数据
                    </FormLabel>
                    <NumberInput
                      id="custom-retention"
                      min={7}
                      max={365}
                      step={1}
                      value={dataRetention.custom}
                      onChange={(_, value) => handleRetentionChange('custom', value)}
                      maxW="120px"
                    >
                      <NumberInputField />
                      <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                      </NumberInputStepper>
                    </NumberInput>
                  </FormControl>
                </Stack>
              </CardBody>
            </Card>
          </SimpleGrid>

          <Card borderRadius="xl" boxShadow="md" borderWidth="1px" borderColor={borderColor} mb={8}>
            <CardBody>
              <Flex align="center" mb={4}>
                <Box
                  bg={useColorModeValue('purple.50', 'purple.900')}
                  p={2}
                  borderRadius="md"
                  mr={3}
                >
                  <ChartLine size={24} weight="fill" color={useColorModeValue('purple.500', 'purple.300')} />
                </Box>
                <Heading size="md">数据统计</Heading>
              </Flex>

              <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
                <Stat
                  bg={statBg}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <StatLabel fontWeight="medium">环境监控数据</StatLabel>
                  <StatNumber>{dataStats?.environment_count.toLocaleString() || '0'}</StatNumber>
                  <StatHelpText>最近30天</StatHelpText>
                </Stat>

                <Stat
                  bg={statBg}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <StatLabel fontWeight="medium">UPS数据</StatLabel>
                  <StatNumber>{dataStats?.ups_count.toLocaleString() || '0'}</StatNumber>
                  <StatHelpText>最近30天</StatHelpText>
                </Stat>

                <Stat
                  bg={statBg}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <StatLabel fontWeight="medium">网络设备数据</StatLabel>
                  <StatNumber>{dataStats?.network_count.toLocaleString() || '0'}</StatNumber>
                  <StatHelpText>最近30天</StatHelpText>
                </Stat>

                <Stat
                  bg={statBg}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <StatLabel fontWeight="medium">自定义SNMP数据</StatLabel>
                  <StatNumber>{dataStats?.custom_count.toLocaleString() || '0'}</StatNumber>
                  <StatHelpText>最近30天</StatHelpText>
                </Stat>
              </SimpleGrid>

              <Flex mt={4} justify="flex-end">
                <Button
                  leftIcon={<Database weight="bold" />}
                  colorScheme="red"
                  variant="outline"
                  size="sm"
                  onClick={handleDataCleanup}
                  mr={2}
                >
                  清理过期数据
                </Button>
                <Button
                  leftIcon={<ArrowsClockwise weight="bold" />}
                  colorScheme="blue"
                  variant="outline"
                  size="sm"
                  onClick={() => handleDataCollection('all')}
                >
                  立即采集所有数据
                </Button>
              </Flex>
            </CardBody>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default SNMPCollectionPage;
