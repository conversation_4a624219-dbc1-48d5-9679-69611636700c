'use client'

import React from 'react'
import {
  Box, 
  Container, 
  Heading, 
  Text, 
  SimpleGrid, 
  Card, 
  CardBody, 
  Stat, 
  StatLabel, 
  StatNumber, 
  Flex, 
  Icon, 
  Button,
  Divider,
  Badge,
  CircularProgress,
  CircularProgressLabel,
  Table,
  Tbody,
  Tr,
  Th,
  Td,
  Grid,
  GridItem,
  Progress
} from '@chakra-ui/react'
import { Lightning, Gauge, Thermometer, PlugsConnected, ChartLine } from '@phosphor-icons/react'

const MainsPowerMonitoringPage = () => {
  return (
    <Container maxW="container.xl" py={6}>
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading size="lg">市电监控</Heading>
          <Text color="gray.500" mt={1}>监控设备用电负载及总功率</Text>
        </Box>
        <Badge 
          colorScheme="green" 
          fontSize="md" 
          px={3} 
          py={1} 
          borderRadius="md"
        >
          正常供电
        </Badge>
      </Flex>

      {/* 市电综合信息 */}
      <Card mb={8}>
        <CardBody>
          <Heading size="md" mb={4}>市电综合信息</Heading>
          <Grid templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }} gap={6}>
            <GridItem>
              <Stat>
                <StatLabel>当前电压</StatLabel>
                <StatNumber>380V</StatNumber>
                <Text fontSize="sm" color="gray.500">三相四线</Text>
              </Stat>
            </GridItem>
            <GridItem>
              <Stat>
                <StatLabel>频率</StatLabel>
                <StatNumber>50Hz</StatNumber>
                <Text fontSize="sm" color="gray.500">稳定状态</Text>
              </Stat>
            </GridItem>
            <GridItem>
              <Stat>
                <StatLabel>供电状态</StatLabel>
                <StatNumber color="green.500">正常</StatNumber>
                <Text fontSize="sm" color="gray.500">持续供电中</Text>
              </Stat>
            </GridItem>
          </Grid>
        </CardBody>
      </Card>

      {/* 空调市电监控 */}
      <Heading size="md" mb={4}>空调市电</Heading>
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} mb={8}>
        <Card>
          <CardBody>
            <Flex direction="column" align="center">
              <Icon as={Lightning} color="blue.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>总功率</Text>
              <CircularProgress value={64} color="blue.400" size="120px" thickness="8px">
                <CircularProgressLabel>
                  <Text fontWeight="bold" fontSize="xl">12.8kW</Text>
                </CircularProgressLabel>
              </CircularProgress>
              <Text fontSize="sm" color="gray.500" mt={2}>额定容量: 20kW</Text>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" h="100%">
              <Flex align="center" mb={4}>
                <Icon as={Thermometer} color="blue.500" mr={2} />
                <Text fontWeight="medium">相位电流</Text>
              </Flex>
              <Box mb={3}>
                <Flex justify="space-between" mb={1}>
                  <Text fontSize="sm">A相</Text>
                  <Text fontSize="sm" fontWeight="medium">19.5A</Text>
                </Flex>
                <Progress value={65} colorScheme="blue" size="sm" mb={2} />
              </Box>
              <Box mb={3}>
                <Flex justify="space-between" mb={1}>
                  <Text fontSize="sm">B相</Text>
                  <Text fontSize="sm" fontWeight="medium">20.1A</Text>
                </Flex>
                <Progress value={67} colorScheme="blue" size="sm" mb={2} />
              </Box>
              <Box mb={3}>
                <Flex justify="space-between" mb={1}>
                  <Text fontSize="sm">C相</Text>
                  <Text fontSize="sm" fontWeight="medium">19.8A</Text>
                </Flex>
                <Progress value={66} colorScheme="blue" size="sm" mb={2} />
              </Box>
              <Divider my={3} />
              <Box>
                <Text fontSize="sm" fontWeight="medium">三相平衡状态: <Badge colorScheme="green">良好</Badge></Text>
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" h="100%">
              <Flex align="center" mb={4}>
                <Icon as={ChartLine} color="blue.500" mr={2} />
                <Text fontWeight="medium">使用情况</Text>
              </Flex>
              <Box flex="1">
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontSize="sm" color="gray.500">今日用电量</Text>
                    <Text fontWeight="medium">96 kWh</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">昨日用电量</Text>
                    <Text fontWeight="medium">102 kWh</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">本月用电量</Text>
                    <Text fontWeight="medium">2,356 kWh</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">功率因数</Text>
                    <Text fontWeight="medium">0.92</Text>
                  </Box>
                </SimpleGrid>
                <Divider my={4} />
                <Box mt={2}>
                  <Text fontSize="sm" color="gray.500">设备状态</Text>
                  <Flex align="center" mt={1}>
                    <Badge colorScheme="green" mr={2}>正常运行</Badge>
                    <Text fontSize="sm" color="gray.500">连续运行时间: 15天</Text>
                  </Flex>
                </Box>
              </Box>
            </Flex>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* UPS市电监控 */}
      <Heading size="md" mb={4}>UPS市电</Heading>
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} mb={8}>
        <Card>
          <CardBody>
            <Flex direction="column" align="center">
              <Icon as={Lightning} color="purple.500" fontSize="3xl" mb={2} />
              <Text fontWeight="medium" mb={2}>总功率</Text>
              <CircularProgress value={38} color="purple.400" size="120px" thickness="8px">
                <CircularProgressLabel>
                  <Text fontWeight="bold" fontSize="xl">5.7kW</Text>
                </CircularProgressLabel>
              </CircularProgress>
              <Text fontSize="sm" color="gray.500" mt={2}>额定容量: 15kW</Text>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" h="100%">
              <Flex align="center" mb={4}>
                <Icon as={PlugsConnected} color="purple.500" mr={2} />
                <Text fontWeight="medium">相位电流</Text>
              </Flex>
              <Box mb={3}>
                <Flex justify="space-between" mb={1}>
                  <Text fontSize="sm">A相</Text>
                  <Text fontSize="sm" fontWeight="medium">8.7A</Text>
                </Flex>
                <Progress value={58} colorScheme="purple" size="sm" mb={2} />
              </Box>
              <Box mb={3}>
                <Flex justify="space-between" mb={1}>
                  <Text fontSize="sm">B相</Text>
                  <Text fontSize="sm" fontWeight="medium">8.5A</Text>
                </Flex>
                <Progress value={57} colorScheme="purple" size="sm" mb={2} />
              </Box>
              <Box mb={3}>
                <Flex justify="space-between" mb={1}>
                  <Text fontSize="sm">C相</Text>
                  <Text fontSize="sm" fontWeight="medium">9.0A</Text>
                </Flex>
                <Progress value={60} colorScheme="purple" size="sm" mb={2} />
              </Box>
              <Divider my={3} />
              <Box>
                <Text fontSize="sm" fontWeight="medium">三相平衡状态: <Badge colorScheme="green">良好</Badge></Text>
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <Flex direction="column" h="100%">
              <Flex align="center" mb={4}>
                <Icon as={ChartLine} color="purple.500" mr={2} />
                <Text fontWeight="medium">使用情况</Text>
              </Flex>
              <Box flex="1">
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontSize="sm" color="gray.500">今日用电量</Text>
                    <Text fontWeight="medium">46 kWh</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">昨日用电量</Text>
                    <Text fontWeight="medium">48 kWh</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">本月用电量</Text>
                    <Text fontWeight="medium">1,182 kWh</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">功率因数</Text>
                    <Text fontWeight="medium">0.98</Text>
                  </Box>
                </SimpleGrid>
                <Divider my={4} />
                <Box mt={2}>
                  <Text fontSize="sm" color="gray.500">供电形式</Text>
                  <Flex align="center" mt={1}>
                    <Badge colorScheme="green" mr={2}>市电供电</Badge>
                    <Text fontSize="sm" color="gray.500">旁路正常</Text>
                  </Flex>
                </Box>
              </Box>
            </Flex>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* 总用电统计 */}
      <Card>
        <CardBody>
          <Heading size="md" mb={4}>系统状态</Heading>
          <Text>市电供电系统运行正常，电压稳定，负载均衡。</Text>
          <Divider my={4} />
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            <GridItem>
              <Stat>
                <StatLabel>电能质量</StatLabel>
                <StatNumber>优</StatNumber>
                <Text fontSize="sm" color="gray.500">电压稳定，波动小</Text>
              </Stat>
            </GridItem>
            <GridItem>
              <Stat>
                <StatLabel>功率因数</StatLabel>
                <StatNumber>0.95</StatNumber>
                <Text fontSize="sm" color="gray.500">正常范围</Text>
              </Stat>
            </GridItem>
            <GridItem>
              <Stat>
                <StatLabel>供电可靠性</StatLabel>
                <StatNumber>99.99%</StatNumber>
                <Text fontSize="sm" color="gray.500">本年度累计</Text>
              </Stat>
            </GridItem>
          </Grid>
        </CardBody>
      </Card>
    </Container>
  )
}

export default MainsPowerMonitoringPage 