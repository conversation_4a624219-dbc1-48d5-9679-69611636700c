'use client'

import React, { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button as ShadcnButton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Table as ShadcnTable,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table"
import { Button } from "@/components/Button"
import { Modal, ModalBody, ModalFooter } from "@/components/Modal"
import { Table, Thead, Tbody, Tr, Th, Td } from "@/components/Table"

export default function ShadcnDemoPage() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleLoadingClick = () => {
    setIsLoading(true)
    setTimeout(() => setIsLoading(false), 2000)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Shadcn/UI 组件演示</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* 数据分析报告卡片 */}
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardTitle>数据分析报告</CardTitle>
            <CardDescription className="text-blue-100">2024年第二季度</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <p className="text-gray-600 mb-4">
              本季度数据分析显示，用户参与度提升了23%，转化率达到历史新高。详细数据请查看完整报告。
            </p>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">89%</div>
                <div className="text-sm text-gray-500">完成率</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">12.4k</div>
                <div className="text-sm text-gray-500">数据点</div>
              </div>
            </div>

            <div className="flex gap-2 mb-4">
              <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                分析
              </span>
              <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                报告
              </span>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-gray-200 mr-2"></div>
              <div className="text-sm font-medium">张经理</div>
            </div>
            <ShadcnButton>查看详情</ShadcnButton>
          </CardFooter>
        </Card>

        {/* 项目进度更新卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>项目进度更新</CardTitle>
            <CardDescription>系统升级项目</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              系统升级项目已完成75%，预计下周完成全部开发工作。测试团队已准备就绪。
            </p>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="border p-4 rounded-lg">
                <div className="text-2xl font-bold text-[#16a34a]">75%</div>
                <div className="text-sm text-gray-500">进度</div>
              </div>
              <div className="border p-4 rounded-lg">
                <div className="text-2xl font-bold text-orange-500">8</div>
                <div className="text-sm text-gray-500">待办任务</div>
              </div>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
              <div className="bg-[#16a34a] h-2.5 rounded-full" style={{ width: '75%' }}></div>
            </div>

            <div className="flex gap-2">
              <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                项目
              </span>
              <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                进度
              </span>
            </div>
          </CardContent>
          <CardFooter>
            <ShadcnButton variant="outline" className="mr-2">查看任务</ShadcnButton>
            <ShadcnButton variant="komodo">更新进度</ShadcnButton>
          </CardFooter>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>原生 Shadcn/UI 按钮</CardTitle>
            <CardDescription>使用 Shadcn/UI 的原生按钮组件</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 mb-4">
              <ShadcnButton variant="default">默认按钮</ShadcnButton>
              <ShadcnButton variant="secondary">次要按钮</ShadcnButton>
              <ShadcnButton variant="outline">轮廓按钮</ShadcnButton>
              <ShadcnButton variant="ghost">幽灵按钮</ShadcnButton>
              <ShadcnButton variant="link">链接按钮</ShadcnButton>
              <ShadcnButton variant="destructive">危险按钮</ShadcnButton>
              <ShadcnButton variant="komodo">Komodo按钮</ShadcnButton>
            </div>

            <div className="flex flex-wrap gap-4 items-center">
              <ShadcnButton size="sm">小按钮</ShadcnButton>
              <ShadcnButton size="default">默认按钮</ShadcnButton>
              <ShadcnButton size="lg">大按钮</ShadcnButton>
              <ShadcnButton size="icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                  <path d="M5 12h14"></path>
                  <path d="M12 5v14"></path>
                </svg>
              </ShadcnButton>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>兼容层按钮</CardTitle>
            <CardDescription>使用兼容NextUI和Chakra UI API的按钮组件</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 mb-4">
              <Button color="default">Default</Button>
              <Button color="primary">Primary</Button>
              <Button color="secondary">Secondary</Button>
              <Button color="success">Success</Button>
              <Button color="danger">Danger</Button>
            </div>

            <div className="flex flex-wrap gap-4 mb-4">
              <Button variant="solid">Solid</Button>
              <Button variant="flat">Flat</Button>
              <Button variant="bordered">Bordered</Button>
              <Button variant="faded">Faded</Button>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button
                leftIcon={
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                  </svg>
                }
              >
                左侧图标
              </Button>

              <Button
                rightIcon={
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                  </svg>
                }
              >
                右侧图标
              </Button>

              <Button
                isLoading={isLoading}
                loadingText="加载中"
                onClick={handleLoadingClick}
              >
                点击加载
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>表单控件</CardTitle>
          <CardDescription>Shadcn/UI 的表单控件</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid w-full max-w-sm items-center gap-1.5 mb-4">
            <Label htmlFor="email">邮箱</Label>
            <Input type="email" id="email" placeholder="请输入邮箱地址" />
          </div>

          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="password">密码</Label>
            <Input type="password" id="password" placeholder="请输入密码" />
          </div>
        </CardContent>
        <CardFooter>
          <ShadcnButton>提交</ShadcnButton>
        </CardFooter>
      </Card>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>对话框</CardTitle>
          <CardDescription>Shadcn/UI 的对话框组件</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            点击下面的按钮打开对话框。这个对话框使用了兼容层组件，API与NextUI和Chakra UI的Modal组件兼容。
          </p>

          <Button onClick={() => setIsModalOpen(true)}>
            打开对话框
          </Button>

          <Modal
            isOpen={isModalOpen}
            onOpenChange={setIsModalOpen}
            title="对话框标题"
          >
            <ModalBody>
              <p className="text-gray-600">
                这是一个使用Shadcn/UI Dialog组件实现的对话框，但API与NextUI和Chakra UI的Modal组件兼容。
              </p>

              <div className="mt-4">
                <Label htmlFor="modal-input">输入内容</Label>
                <Input id="modal-input" className="mt-1" />
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="outline" onClick={() => setIsModalOpen(false)} className="mr-2">
                取消
              </Button>
              <Button onClick={() => setIsModalOpen(false)}>
                确认
              </Button>
            </ModalFooter>
          </Modal>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>原生表格</CardTitle>
            <CardDescription>Shadcn/UI 的原生表格组件</CardDescription>
          </CardHeader>
          <CardContent>
            <ShadcnTable>
              <TableHeader>
                <TableRow>
                  <TableHead>名称</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">服务器 A</TableCell>
                  <TableCell>
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      正常
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <ShadcnButton variant="outline" size="sm">查看</ShadcnButton>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">服务器 B</TableCell>
                  <TableCell>
                    <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                      警告
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <ShadcnButton variant="outline" size="sm">查看</ShadcnButton>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">服务器 C</TableCell>
                  <TableCell>
                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                      错误
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <ShadcnButton variant="outline" size="sm">查看</ShadcnButton>
                  </TableCell>
                </TableRow>
              </TableBody>
            </ShadcnTable>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>兼容层表格</CardTitle>
            <CardDescription>兼容NextUI和Chakra UI API的表格组件</CardDescription>
          </CardHeader>
          <CardContent>
            <Table variant="striped" size="md" isHeaderSticky>
              <Thead>
                <Tr>
                  <Th>名称</Th>
                  <Th>状态</Th>
                  <Th align="end">操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                <Tr>
                  <Td className="font-medium">服务器 A</Td>
                  <Td>
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                      正常
                    </span>
                  </Td>
                  <Td align="end">
                    <Button variant="bordered" size="sm">查看</Button>
                  </Td>
                </Tr>
                <Tr>
                  <Td className="font-medium">服务器 B</Td>
                  <Td>
                    <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                      警告
                    </span>
                  </Td>
                  <Td align="end">
                    <Button variant="bordered" size="sm">查看</Button>
                  </Td>
                </Tr>
                <Tr>
                  <Td className="font-medium">服务器 C</Td>
                  <Td>
                    <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                      错误
                    </span>
                  </Td>
                  <Td align="end">
                    <Button variant="bordered" size="sm">查看</Button>
                  </Td>
                </Tr>
              </Tbody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
