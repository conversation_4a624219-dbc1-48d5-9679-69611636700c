"use client"

import React, { useEffect, useState, useRef } from "react";
import {
  Box,
  Button,
  Flex,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Checkbox,
  VStack,
  HStack,
  useToast,
  Spinner,
  CheckboxGroup,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Input as ChakraInput,
  useColorMode,
  useBreakpointValue,
  useColorModeValue,
  Badge,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Text,
} from "@chakra-ui/react";
import {
  AddIcon,
  EditIcon,
  DeleteIcon,
  ViewIcon,
  MoonIcon,
  SunIcon,
  CheckIcon,
  RepeatIcon,
  ChevronDownIcon
} from "@chakra-ui/icons";
import axios from "axios";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd';
import { Global } from '@emotion/react';
import { useSwipeable } from 'react-swipeable';

// 导入新组件
import MobileInspectionItem from '../../../components/mobile/MobileInspectionItem';
import MobileTemplateItem from '../../../components/mobile/MobileTemplateItem';
import CustomDashboard from '../../../components/Dashboard/CustomDashboard';
import AIAnalysisPanel from '../../../components/inspection/AIAnalysisPanel';
import NotificationCenter from '../../../components/notification/NotificationCenter';

// 导入状态管理
import { useTemplateStore } from '../../../store/templateStore';
import { sendMockNotification } from '../../../services/notificationService';

interface InspectionItem {
  item: string;
  check_command: string;
  expected_result: string;
  auto_fix: boolean;
  fix_command?: string;
  remark: string;
}

interface InspectionTemplate {
  id?: number;
  name: string;
  description?: string;
  device_types: string[];
  content: InspectionItem[];
  created_at?: string;
  updated_at?: string;
}

const DEVICE_TYPES = ["Huawei", "Linux", "Windows"];

const defaultItem: InspectionItem = {
  item: "",
  check_command: "",
  expected_result: "",
  auto_fix: false,
  fix_command: "",
  remark: "",
};

// 华为网络设备常用巡检项
const HUAWEI_DEFAULT_ITEMS: InspectionItem[] = [
  {
    item: "端口状态",
    check_command: "display interface brief",
    expected_result: "up",
    auto_fix: false,
    fix_command: "",
    remark: "检查所有端口是否正常up"
  },
  {
    item: "CPU利用率",
    check_command: "display cpu-usage",
    expected_result: "< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "CPU利用率应低于80%"
  },
  {
    item: "内存利用率",
    check_command: "display memory-usage",
    expected_result: "< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "内存利用率应低于80%"
  },
  {
    item: "设备温度",
    check_command: "display environment",
    expected_result: "正常范围",
    auto_fix: false,
    fix_command: "",
    remark: "温度应在设备允许范围内"
  },
  {
    item: "电源状态",
    check_command: "display power",
    expected_result: "正常",
    auto_fix: false,
    fix_command: "",
    remark: "所有电源模块工作正常"
  },
  {
    item: "风扇状态",
    check_command: "display fan",
    expected_result: "正常",
    auto_fix: false,
    fix_command: "",
    remark: "所有风扇工作正常"
  },
  {
    item: "配置变更",
    check_command: "display configuration record",
    expected_result: "无异常变更",
    auto_fix: false,
    fix_command: "",
    remark: "近期无异常配置变更"
  },
  {
    item: "日志告警",
    check_command: "display logbuffer",
    expected_result: "无严重告警",
    auto_fix: false,
    fix_command: "",
    remark: "日志中无严重级别告警"
  },
  {
    item: "NTP同步状态",
    check_command: "display ntp status",
    expected_result: "已同步",
    auto_fix: false,
    fix_command: "ntp-service enable",
    remark: "NTP时间同步正常"
  },
  {
    item: "路由表健康",
    check_command: "display ip routing-table",
    expected_result: "无异常路由",
    auto_fix: false,
    fix_command: "",
    remark: "路由表无异常项"
  }
];

// Linux网络设备常用巡检项
const LINUX_DEFAULT_ITEMS: InspectionItem[] = [
  {
    item: "端口状态",
    check_command: "ip link show",
    expected_result: "state UP",
    auto_fix: false,
    fix_command: "",
    remark: "所有网卡应为UP"
  },
  {
    item: "CPU利用率",
    check_command: "top -bn1 | grep 'Cpu(s)'",
    expected_result: "< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "CPU利用率应低于80%"
  },
  {
    item: "内存利用率",
    check_command: "free -m",
    expected_result: "< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "内存利用率应低于80%"
  },
  {
    item: "磁盘空间",
    check_command: "df -h",
    expected_result: "使用率< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "所有分区使用率低于80%"
  },
  {
    item: "系统负载",
    check_command: "uptime",
    expected_result: "load average < 2",
    auto_fix: false,
    fix_command: "",
    remark: "系统负载应低于2"
  },
  {
    item: "时间同步",
    check_command: "timedatectl status",
    expected_result: "NTP synchronized: yes",
    auto_fix: false,
    fix_command: "timedatectl set-ntp true",
    remark: "NTP时间同步正常"
  },
  {
    item: "日志告警",
    check_command: "dmesg | grep -i error",
    expected_result: "无严重错误",
    auto_fix: false,
    fix_command: "",
    remark: "系统日志无严重错误"
  }
];

// Windows设备常用巡检项
const WINDOWS_DEFAULT_ITEMS: InspectionItem[] = [
  {
    item: "网卡状态",
    check_command: "Get-NetAdapter | Select Name, Status",
    expected_result: "Up",
    auto_fix: false,
    fix_command: "",
    remark: "所有网卡应为Up"
  },
  {
    item: "CPU利用率",
    check_command: "Get-Counter '\Processor(_Total)\% Processor Time'",
    expected_result: "< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "CPU利用率应低于80%"
  },
  {
    item: "内存利用率",
    check_command: "Get-Counter '\Memory\% Committed Bytes In Use'",
    expected_result: "< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "内存利用率应低于80%"
  },
  {
    item: "磁盘空间",
    check_command: "Get-PSDrive -PSProvider FileSystem",
    expected_result: "使用率< 80%",
    auto_fix: false,
    fix_command: "",
    remark: "所有分区使用率低于80%"
  },
  {
    item: "系统事件日志",
    check_command: "Get-EventLog -LogName System -EntryType Error -Newest 10",
    expected_result: "无严重错误",
    auto_fix: false,
    fix_command: "",
    remark: "系统日志无严重错误"
  },
  {
    item: "时间同步",
    check_command: "w32tm /query /status",
    expected_result: "同步",
    auto_fix: false,
    fix_command: "w32tm /resync",
    remark: "时间同步正常"
  }
];

// 设备类型切换时自动填充默认检查项
const getDefaultItemsByType = (type: string): InspectionItem[] => {
  if (type === 'Huawei') return [...HUAWEI_DEFAULT_ITEMS];
  if (type === 'Linux') return [...LINUX_DEFAULT_ITEMS];
  if (type === 'Windows') return [...WINDOWS_DEFAULT_ITEMS];
  return [{ ...defaultItem }];
};

const TemplateForm = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: InspectionTemplate) => void;
  initialData?: InspectionTemplate;
}) => {
  const [form, setForm] = useState<InspectionTemplate>(
    initialData || {
      name: "",
      description: "",
      device_types: [DEVICE_TYPES[0]],
      content: [...HUAWEI_DEFAULT_ITEMS],
    }
  );
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [importOpen, setImportOpen] = useState(false);
  const [importText, setImportText] = useState("");
  const importTextareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen) {
      setForm(
        initialData
          ? {
              ...initialData,
              device_types: Array.isArray((initialData as any).device_types)
                ? (initialData as any).device_types
                : (initialData as any).device_type
                  ? [(initialData as any).device_type]
                  : [DEVICE_TYPES[0]],
            }
          : {
              name: "",
              description: "",
              device_types: [DEVICE_TYPES[0]],
              content: [...HUAWEI_DEFAULT_ITEMS],
            }
      );
      setErrors({});
    }
  }, [isOpen, initialData]);

  const handleItemChange = (idx: number, key: keyof InspectionItem, value: any) => {
    setForm((prev) => {
      const content = [...prev.content];
      content[idx] = { ...content[idx], [key]: value };
      // 清空fix_command如果auto_fix为false
      if (key === "auto_fix" && !value) {
        content[idx].fix_command = "";
      }
      return { ...prev, content };
    });
  };

  const addItem = () => {
    setForm((prev) => ({ ...prev, content: [...prev.content, { ...defaultItem }] }));
  };

  const removeItem = (idx: number) => {
    setForm((prev) => ({ ...prev, content: prev.content.filter((_, i) => i !== idx) }));
  };

  const validate = () => {
    const err: { [key: string]: string } = {};
    if (!form.name.trim()) err.name = "模板名称不能为空";
    if (!form.device_types || form.device_types.length === 0) err.device_types = "请选择设备类型";
    if (!form.content.length) err.content = "至少需要一个检查项";
    form.content.forEach((item, idx) => {
      if (!item.item.trim()) err[`item_${idx}`] = "检查内容不能为空";
      if (!item.check_command.trim()) err[`check_command_${idx}`] = "检查命令不能为空";
      if (!item.expected_result.trim()) err[`expected_result_${idx}`] = "预期结果不能为空";
      if (item.auto_fix && !item.fix_command?.trim()) err[`fix_command_${idx}`] = "修复命令不能为空";
    });
    setErrors(err);
    return Object.keys(err).length === 0;
  };

  const handleSubmit = () => {
    if (validate()) {
      onSubmit(form);
    }
  };

  // 批量导入逻辑
  const handleImport = () => setImportOpen(true);
  const handleImportConfirm = () => {
    // 解析CSV文本，每行为一项，字段顺序：检查内容,检查命令,预期结果,允许自动修复,修复命令,备注
    const lines = importText.split(/\r?\n/).filter(l => l.trim());
    const items = lines.map(line => {
      const [item, check_command, expected_result, auto_fix, fix_command, remark] = line.split(",");
      return {
        item: item?.trim() || "",
        check_command: check_command?.trim() || "",
        expected_result: expected_result?.trim() || "",
        auto_fix: auto_fix?.trim() === "是" || auto_fix?.trim().toLowerCase() === "true",
        fix_command: fix_command?.trim() || "",
        remark: remark?.trim() || "",
      };
    });
    setForm(f => ({ ...f, content: items.length ? items : f.content }));
    setImportOpen(false);
    setImportText("");
  };
  const handleImportCancel = () => {
    setImportOpen(false);
    setImportText("");
  };

  // 导出为CSV
  const handleExport = () => {
    const header = ["检查内容","检查命令","预期结果","允许自动修复","修复命令","备注"];
    const rows = form.content.map(item => [
      item.item,
      item.check_command,
      item.expected_result,
      item.auto_fix ? "是" : "否",
      item.fix_command || "",
      item.remark || ""
    ]);
    const csv = [header, ...rows].map(r => r.map((v: string) => `"${(v||"").replace(/"/g,'""')}"`).join(",")).join("\r\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${form.name || "inspection-template"}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 拖拽排序逻辑
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    const newContent = Array.from(form.content);
    const [removed] = newContent.splice(result.source.index, 1);
    newContent.splice(result.destination.index, 0, removed);
    setForm(f => ({ ...f, content: newContent }));
  };

  // 设备类型切换时自动填充默认检查项（仅新建时生效）
  const handleDeviceTypesChange = (val: string[]) => {
    setForm(f => {
      if (!initialData && val.length === 1) {
        return { ...f, device_types: val, content: getDefaultItemsByType(val[0]) };
      }
      return { ...f, device_types: val };
    });
  };

  return (
    <>
      {/* 全局样式，修正z-index分层，防止遮挡 */}
      <Global
        styles={`
          .chakra-modal__overlay {
            pointer-events: auto;
            z-index: 1000;
          }
          .chakra-modal__content-container {
            z-index: 1001;
          }
        `}
      />
      <Modal isOpen={isOpen} onClose={onClose} size="full" motionPreset="none">
        <ModalOverlay bg="rgba(0,0,0,0.35)" backdropFilter="blur(2px)" />
        <ModalContent width="100vw" borderRadius={0} boxShadow="lg" p={0} m={0}>
          <ModalHeader pl={4} pr={4} pt={6} pb={2} bg="white" borderBottomWidth={1} borderColor="gray.200">
            <Flex align="center" justify="space-between" w="100%">
              <Heading size="md">编辑巡检模板</Heading>
              <ModalCloseButton position="static" />
            </Flex>
          </ModalHeader>
          <ModalBody maxHeight="calc(100vh - 120px)" overflowY="auto" p={{ base: 2, md: 8 }} bg="gray.50">
            <VStack spacing={8} align="stretch">
              {/* 基础信息分组 */}
              <Box bg="white" borderRadius="xl" boxShadow="md" p={{ base: 4, md: 8 }} borderWidth={1} borderColor="gray.100">
                <Flex direction={{ base: 'column', md: 'row' }} gap={6} align="start">
                  <FormControl isRequired isInvalid={!!errors.name} flex={1} mb={{ base: 4, md: 0 }}>
                    <FormLabel fontWeight="bold" color="gray.800">模板名称</FormLabel>
                    <Input value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} variant="outline" bg="white" borderColor="teal.500" borderWidth={2} borderRadius="lg" fontSize="lg" color="gray.900" _placeholder={{ color: 'gray.400' }} _focus={{ borderColor: 'teal.600', boxShadow: '0 0 0 2px #319795' }} />
                    {errors.name && <Box color="red.500" fontSize="sm">{errors.name}</Box>}
                  </FormControl>
                  <FormControl flex={2} mb={{ base: 4, md: 0 }}>
                    <FormLabel fontWeight="extrabold" color="gray.800">描述</FormLabel>
                    <Textarea value={form.description} onChange={e => setForm(f => ({ ...f, description: e.target.value }))} rows={2} variant="outline" bg="white" borderColor="gray.400" borderWidth={2} borderRadius="md" fontSize="lg" color="gray.900" boxShadow="0 2px 8px #0001" _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px #319795' }} />
                  </FormControl>
                  <FormControl isRequired isInvalid={!!errors.device_types} flex={1}>
                    <FormLabel fontWeight="extrabold" color="gray.800">设备类型</FormLabel>
                    <CheckboxGroup value={form.device_types} onChange={handleDeviceTypesChange}>
                      <HStack>
                        {DEVICE_TYPES.map(type => (
                          <Checkbox key={type} value={type}>{type}</Checkbox>
                        ))}
                      </HStack>
                    </CheckboxGroup>
                    {errors.device_types && <Box color="red.500" fontSize="sm">{errors.device_types}</Box>}
                  </FormControl>
                </Flex>
              </Box>
              {/* 检查项分组 */}
              <Box bg="white" borderRadius="xl" boxShadow="md" p={{ base: 4, md: 8 }} borderWidth={1} borderColor="gray.100">
                <Flex justify="space-between" align="center" mb={4}>
                  <Heading size="md">检查项</Heading>
                  <HStack>
                    <Button size="sm" onClick={handleImport}>批量导入</Button>
                    <Button size="sm" onClick={handleExport}>导出为CSV</Button>
                  </HStack>
                </Flex>
                {errors.content && <Box color="red.500" fontSize="sm" mb={2}>{errors.content}</Box>}
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="inspection-items-droppable">
                    {(provided) => (
                      <VStack spacing={4} align="stretch" ref={provided.innerRef} {...provided.droppableProps}>
                        {form.content.map((item, idx) => (
                          <Draggable key={idx} draggableId={`inspection-item-${idx}`} index={idx}>
                            {(provided, snapshot) => (
                              <Box
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                bg={snapshot.isDragging ? 'teal.50' : 'gray.50'}
                                borderWidth="1px"
                                borderRadius="lg"
                                p={6}
                                position="relative"
                                boxShadow={snapshot.isDragging ? 'lg' : 'sm'}
                                mb={2}
                              >
                                <Flex align="start" justify="space-between" direction={{ base: 'column', md: 'row' }} gap={6}>
                                  <Box flex={1} minW={0}>
                                    <FormControl isRequired isInvalid={!!errors[`item_${idx}`]} mb={2}>
                                      <FormLabel fontWeight="extrabold" color="gray.800">检查内容</FormLabel>
                                      <Input value={item.item} onChange={e => handleItemChange(idx, "item", e.target.value)} variant="outline" bg="white" borderColor="gray.400" borderWidth={2} borderRadius="md" fontSize="lg" color="gray.900" boxShadow="0 2px 8px #0001" _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px #319795' }} />
                                      {errors[`item_${idx}`] && <Box color="red.500" fontSize="sm">{errors[`item_${idx}`]}</Box>}
                                    </FormControl>
                                    <FormControl isRequired isInvalid={!!errors[`check_command_${idx}`]} mb={2}>
                                      <FormLabel fontWeight="extrabold" color="gray.800">检查命令</FormLabel>
                                      <Input value={item.check_command} onChange={e => handleItemChange(idx, "check_command", e.target.value)} variant="outline" bg="white" borderColor="gray.400" borderWidth={2} borderRadius="md" fontSize="lg" color="gray.900" boxShadow="0 2px 8px #0001" _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px #319795' }} />
                                      {errors[`check_command_${idx}`] && <Box color="red.500" fontSize="sm">{errors[`check_command_${idx}`]}</Box>}
                                    </FormControl>
                                    <FormControl isRequired isInvalid={!!errors[`expected_result_${idx}`]} mb={2}>
                                      <FormLabel fontWeight="extrabold" color="gray.800">预期结果</FormLabel>
                                      <Input value={item.expected_result} onChange={e => handleItemChange(idx, "expected_result", e.target.value)} variant="outline" bg="white" borderColor="gray.400" borderWidth={2} borderRadius="md" fontSize="lg" color="gray.900" boxShadow="0 2px 8px #0001" _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px #319795' }} />
                                      {errors[`expected_result_${idx}`] && <Box color="red.500" fontSize="sm">{errors[`expected_result_${idx}`]}</Box>}
                                    </FormControl>
                                  </Box>
                                  <Box flex={1} minW={0}>
                                    <HStack mb={2} align="center" width="100%">
                                      <Box flex="none">
                                        <Checkbox isChecked={item.auto_fix} onChange={e => handleItemChange(idx, "auto_fix", e.target.checked)} whiteSpace="nowrap">
                                          允许自动修复
                                        </Checkbox>
                                      </Box>
                                      <FormControl isInvalid={!!errors[`fix_command_${idx}`]} isDisabled={!item.auto_fix} mb={0} flex={1}>
                                        <FormLabel fontWeight="extrabold" color="gray.800">修复命令</FormLabel>
                                        <Textarea value={item.fix_command || ""} onChange={e => handleItemChange(idx, "fix_command", e.target.value)} disabled={!item.auto_fix} rows={2} width="100%" variant="outline" bg="white" borderColor="gray.400" borderWidth={2} borderRadius="md" fontSize="lg" color="gray.900" boxShadow="0 2px 8px #0001" _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px #319795' }} />
                                        {errors[`fix_command_${idx}`] && <Box color="red.500" fontSize="sm">{errors[`fix_command_${idx}`]}</Box>}
                                      </FormControl>
                                    </HStack>
                                    <FormControl mb={2} width="100%">
                                      <FormLabel fontWeight="extrabold" color="gray.800">备注</FormLabel>
                                      <Textarea value={item.remark || ""} onChange={e => handleItemChange(idx, "remark", e.target.value)} rows={2} width="100%" variant="outline" bg="white" borderColor="gray.400" borderWidth={2} borderRadius="md" fontSize="lg" color="gray.900" boxShadow="0 2px 8px #0001" _focus={{ borderColor: 'teal.500', boxShadow: '0 0 0 2px #319795' }} />
                                    </FormControl>
                                    <Flex justify="flex-end" mt={2}>
                                      {form.content.length > 1 && (
                                        <IconButton aria-label="删除检查项" icon={<DeleteIcon />} size="sm" colorScheme="red" variant="ghost" onClick={() => removeItem(idx)} />
                                      )}
                                    </Flex>
                                  </Box>
                                </Flex>
                              </Box>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </VStack>
                    )}
                  </Droppable>
                </DragDropContext>
                <Flex justify="center" mt={6}>
                  <Button leftIcon={<AddIcon />} onClick={addItem} size="md" colorScheme="teal" variant="outline">添加检查项</Button>
                </Flex>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter bg="white" borderTopWidth={1} borderColor="gray.100" position="sticky" bottom={0} zIndex={1001} py={4} px={8} justifyContent="center">
            <Button onClick={onClose} mr={6} size="lg" variant="outline">取消</Button>
            <Button colorScheme="teal" onClick={handleSubmit} size="lg">{initialData ? "保存修改" : "创建模板"}</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 批量导入弹窗 */}
      <Modal
        isOpen={importOpen}
        onClose={handleImportCancel}
        size="xl"
        isCentered
        motionPreset="scale"
      >
        <ModalOverlay
          bg="rgba(0,0,0,0.7)"
          backdropFilter="blur(6px)"
          display="flex"
          alignItems="center"
          justifyContent="center"
        />
        <ModalContent
          maxW={{base: "95vw", md: "650px"}}
          width="100%"
          margin="auto"
          borderRadius="xl"
          boxShadow="2xl"
          bg="white"
          position="relative"
          zIndex="2000"
          overflow="visible"
        >
          <ModalHeader
            textAlign="center"
            fontSize="2xl"
            fontWeight="bold"
            py={4}
            borderBottomWidth={1}
            borderColor="gray.200"
          >
            批量导入检查项
          </ModalHeader>
          <ModalBody
            px={{base:4,md:8}}
            py={4}
            display="flex"
            flexDirection="column"
            alignItems="center"
            bg="white"
          >
            <Tabs
              variant="enclosed"
              colorScheme="teal"
              align="center"
              isFitted
              width="100%"
            >
              <TabList mb={4} gap={4} px={4}>
                <Tab fontSize="lg" fontWeight="bold" borderRadius="xl" px={8} py={2}>文本粘贴</Tab>
                <Tab fontSize="lg" fontWeight="bold" borderRadius="xl" px={8} py={2}>文件上传（Excel/CSV）</Tab>
              </TabList>
              <TabPanels>
                <TabPanel px={0}>
                  <Box mb={4} bg="gray.50" borderRadius="md" p={4} display="flex" alignItems="flex-start" gap={3}>
                    <Box color="teal.500" fontSize="2xl" mt={1}>
                      <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none"><path d="M12 2a10 10 0 100 20 10 10 0 000-20zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" fill="currentColor"/></svg>
                    </Box>
                    <Box color="gray.700" fontSize="md">
                      <b>导入说明：</b><br />
                      1. 每行一条检查项，字段顺序：<b>检查内容, 检查命令, 预期结果, 允许自动修复(是/否), 修复命令, 备注</b><br />
                      2. 允许自动修复请填写"是"或"否"<br />
                      3. 示例：<br />
                      <Box as="pre" bg="gray.100" borderRadius="sm" p={2} mt={1} fontSize="sm" color="gray.800">端口状态,display interface brief,up,否,,检查端口是否正常</Box>
                    </Box>
                  </Box>
                  <Flex justifyContent="center" mt={2} width="100%">
                    <Textarea
                      ref={importTextareaRef}
                      value={importText}
                      onChange={e => setImportText(e.target.value)}
                      rows={14}
                      fontSize="xl"
                      p={6}
                      minH="240px"
                      maxW="600px"
                      width="100%"
                      placeholder="请粘贴或输入CSV内容，每行一条检查项，格式如上所示..."
                      variant="outline"
                      bg="gray.50"
                      borderColor="teal.500"
                      borderWidth={2}
                      borderRadius="lg"
                      color="gray.900"
                      boxShadow="0 2px 12px #31979522"
                      _placeholder={{ color: 'gray.400' }}
                      _focus={{ borderColor: 'teal.600', boxShadow: '0 0 0 2px #319795' }}
                    />
                  </Flex>
                </TabPanel>
                <TabPanel px={0}>
                  <Box mb={4} bg="gray.50" borderRadius="md" p={4} display="flex" alignItems="flex-start" gap={3}>
                    <Box color="teal.500" fontSize="2xl" mt={1}>
                      <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none"><path d="M12 2a10 10 0 100 20 10 10 0 000-20zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" fill="currentColor"/></svg>
                    </Box>
                    <Box color="gray.700" fontSize="md">
                      <b>导入说明：</b><br />
                      支持上传 <b>.csv、.xls、.xlsx</b> 文件，文件内容需符合检查项字段顺序。<br />
                      1. 字段顺序：检查内容, 检查命令, 预期结果, 允许自动修复(是/否), 修复命令, 备注<br />
                      2. 示例文件可在模板导出后参考。
                    </Box>
                  </Box>
                  <Flex direction="column" align="center" justify="center" minH="220px">
                    <ChakraInput
                      type="file"
                      accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      width="320px"
                      size="lg"
                      p={3}
                      borderRadius="md"
                      borderColor="teal.500"
                      borderWidth={2}
                      bg="gray.50"
                      mb={4}
                      onChange={e => {
                        // 这里只做文件名和简单预览，实际解析可后续补充
                        const file = e.target.files?.[0];
                        if (file) {
                          setImportText(file.name);
                        }
                      }}
                    />
                    {importText && (
                      <Box color="teal.600" fontWeight="bold" fontSize="md">已选择文件：{importText}</Box>
                    )}
                  </Flex>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </ModalBody>
          <ModalFooter
            pb={6}
            pt={2}
            justifyContent="center"
            bg="gray.50"
            borderBottomRadius="xl"
            borderTopWidth={1}
            borderColor="gray.200"
            position="sticky"
            bottom={0}
            zIndex={10}
            boxShadow="0 -2px 12px #0001"
          >
            <Button
              onClick={handleImportCancel}
              size="lg"
              variant="outline"
              colorScheme="gray"
              borderRadius="2xl"
              fontWeight="bold"
              px={10}
              mr={6}
              boxShadow="sm"
            >
              取消
            </Button>
            <Button
              colorScheme="teal"
              onClick={handleImportConfirm}
              size="lg"
              fontWeight="extrabold"
              borderRadius="2xl"
              px={12}
              boxShadow="md"
              leftIcon={<svg width="1.2em" height="1.2em" viewBox="0 0 24 24" fill="none"><path d="M12 16V4m0 12l-4-4m4 4l4-4M4 20h16" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>}
            >
              导入
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

const ExecuteModal = ({ isOpen, onClose, template, onExecuted }: {
  isOpen: boolean;
  onClose: () => void;
  template?: InspectionTemplate;
  onExecuted?: (result: any) => void;
}) => {
  const [ip, setIp] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const toast = useToast();

  const handleExecute = async () => {
    setLoading(true);
    setResult(null);
    try {
      const res = await axios.post("/api/inspection-execute", {
        template_id: template?.id,
        device: { ip, username, password },
      });
      setResult(res.data);
      onExecuted && onExecuted(res.data);
    } catch (e: any) {
      toast({ title: "执行失败", description: e?.response?.data?.error || e.message, status: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setIp(""); setUsername(""); setPassword(""); setResult(null);
    }
  }, [isOpen]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="4xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>执行巡检 - {template?.name}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <FormControl isRequired>
              <FormLabel>设备IP</FormLabel>
              <Input value={ip} onChange={e => setIp(e.target.value)} />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>用户名</FormLabel>
              <Input value={username} onChange={e => setUsername(e.target.value)} />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>密码</FormLabel>
              <Input type="password" value={password} onChange={e => setPassword(e.target.value)} />
            </FormControl>
            <Button colorScheme="teal" onClick={handleExecute} isLoading={loading} isDisabled={!ip || !username || !password}>
              开始巡检
            </Button>
            {result && (
              <Box mt={4}>
                <Heading size="sm" mb={2}>执行结果</Heading>
                <Table size="sm" variant="striped">
                  <Thead>
                    <Tr>
                      <Th>检查内容</Th>
                      <Th>检查命令</Th>
                      <Th>结果</Th>
                      <Th>通过</Th>
                      <Th>自动修复</Th>
                      <Th>修复命令</Th>
                      <Th>修复结果</Th>
                      <Th>备注</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {result.results.map((r: any, idx: number) => (
                      <Tr key={idx}>
                        <Td>{r.item}</Td>
                        <Td>{r.check_command}</Td>
                        <Td>{r.check_result || r.check_error}</Td>
                        <Td color={r.passed ? 'green.600' : 'red.600'}>{r.passed ? '✔' : '✘'}</Td>
                        <Td>{r.auto_fix ? '是' : '否'}</Td>
                        <Td>{r.fix_command || '-'}</Td>
                        <Td>{r.fixed ? (r.fix_result || '已修复') : (r.fix_error || '-')}</Td>
                        <Td>{r.remark || '-'}</Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            )}
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button onClick={onClose}>关闭</Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// ClientOnly 组件，防止 SSR/CSR hydration 不一致
function ClientOnly({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);
  useEffect(() => setMounted(true), []);
  if (!mounted) return null;
  return <>{children}</>;
}

const InspectionTemplatePage = () => {
  // 使用Zustand状态管理
  const {
    templates,
    loading,
    fetchTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    oneClickInspect,
    inspectionResult,
    aiAnalysis,
    inspectStep,
    inspectStepMsg
  } = useTemplateStore();

  // 本地UI状态
  const [editing, setEditing] = useState<InspectionTemplate | undefined>();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [execTpl, setExecTpl] = useState<InspectionTemplate | undefined>();
  const { isOpen: execOpen, onOpen: execOnOpen, onClose: execOnClose } = useDisclosure();
  const toast = useToast();
  const [reportModal, setReportModal] = useState<{visible: boolean, report?: any, aiAnalysis?: string}>({visible: false});
  const [localInspectStep, setLocalInspectStep] = useState<string>('idle');

  // 移动端检测
  const isMobile = useBreakpointValue({ base: true, md: false }) || false;

  // 颜色模式
  const { colorMode } = useColorMode();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 设置巡检步骤（使用本地状态代替store中的状态）
  const setInspectStep = (step: string) => {
    setLocalInspectStep(step);
  };

  // 首次加载时获取模板
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // 监听巡检状态变化
  useEffect(() => {
    if (localInspectStep === 'done' && inspectionResult) {
      setReportModal({
        visible: true,
        report: inspectionResult,
        aiAnalysis
      });
    }
  }, [localInspectStep, inspectionResult, aiAnalysis]);

  const handleCreate = () => {
    setEditing(undefined);
    onOpen();
  };

  const handleEdit = (tpl: InspectionTemplate) => {
    setEditing(tpl);
    onOpen();
  };

  const handleDelete = async (id?: number) => {
    if (!id) return;
    if (!window.confirm("确定要删除该模板吗？")) return;
    await deleteTemplate(id);
  };

  const handleOneClickInspect = async (tpl: InspectionTemplate) => {
    if (!tpl.id) return;
    await oneClickInspect(tpl.id);
  };

  // 执行巡检函数
  const executeInspection = async (templateId: number, params: any) => {
    try {
      // 这里应该是实际的API调用
      // 模拟巡检结果
      return {
        template_id: templateId,
        device_ip: params.ip,
        timestamp: new Date().toISOString(),
        results: [
          {
            item: "CPU利用率",
            check_command: "top -bn1 | grep 'Cpu(s)'",
            expected_result: "< 80%",
            check_result: "CPU利用率: 92.5%",
            passed: false,
            auto_fix: true,
            fix_command: "kill -9 $(ps aux | sort -nrk 3,3 | head -n 3 | awk '{print $2}')",
            fixed: false,
            remark: "CPU利用率过高"
          },
          {
            item: "磁盘空间",
            check_command: "df -h",
            expected_result: "使用率< 80%",
            check_result: "/ 使用率: 85%",
            passed: false,
            auto_fix: true,
            fix_command: "find /tmp -type f -atime +7 -delete",
            fixed: false,
            remark: "根分区空间不足"
          },
          {
            item: "内存利用率",
            check_command: "free -m",
            expected_result: "< 80%",
            check_result: "内存使用率: 65%",
            passed: true,
            auto_fix: false,
            remark: "内存使用正常"
          },
          {
            item: "时间同步",
            check_command: "timedatectl status",
            expected_result: "NTP synchronized: yes",
            check_result: "NTP synchronized: no",
            passed: false,
            auto_fix: true,
            fix_command: "timedatectl set-ntp true",
            fixed: false,
            remark: "NTP时间同步异常"
          },
          {
            item: "系统负载",
            check_command: "uptime",
            expected_result: "load average < 2",
            check_result: "load average: 0.52, 0.58, 0.59",
            passed: true,
            auto_fix: false,
            remark: "系统负载正常"
          }
        ]
      };
    } catch (error) {
      console.error("执行巡检失败", error);
      throw error;
    }
  };

  // 一键修复功能
  const handleOneClickFix = async (tpl: InspectionTemplate) => {
    if (!tpl.id) return;

    try {
      // 先执行巡检
      const result = await executeInspection(tpl.id, {
        ip: "localhost",
        username: "admin",
        password: "admin"
      });

      if (!result) {
        toast({
          title: "巡检失败",
          description: "无法获取巡检结果",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // 设置状态为修复中
      setInspectStep('fixing');

      // 模拟修复过程
      setTimeout(async () => {
        try {
          // 过滤出需要修复的项目
          const itemsToFix = result.results.filter((item: any) => !item.passed && item.auto_fix);

          // 如果没有需要修复的项目
          if (itemsToFix.length === 0) {
            toast({
              title: "无需修复",
              description: "没有发现需要自动修复的项目",
              status: "info",
              duration: 5000,
              isClosable: true,
            });
            setInspectStep('idle');
            return;
          }

          // 模拟修复过程
          const fixedResults = result.results.map((item: any) => {
            if (!item.passed && item.auto_fix) {
              return {
                ...item,
                fixed: true,
                fix_result: "已自动修复"
              };
            }
            return item;
          });

          // 更新结果
          const fixedReport = {
            ...result,
            results: fixedResults
          };

          // 设置状态为分析中
          setInspectStep('analyzing');

          // 模拟AI分析
          setTimeout(() => {
            // 生成AI分析结果
            const aiAnalysisText = `系统检测到 ${itemsToFix.length} 个问题并已自动修复。主要问题包括：${itemsToFix.map((i: any) => i.item).join('、')}。建议定期执行巡检以保持系统稳定性。`;

            // 设置状态为完成
            setInspectStep('done');

            // 显示报告
            setReportModal({
              visible: true,
              report: fixedReport,
              aiAnalysis: aiAnalysisText
            });

            // 发送通知
            try {
              const { sendMockNotification } = require('../../../services/notificationService');
              sendMockNotification(
                '修复完成',
                `已自动修复 ${itemsToFix.length} 个问题`,
                'success'
              );
            } catch (e) {
              console.log('通知发送失败', e);
            }

            // 重置状态
            setTimeout(() => setInspectStep('idle'), 2000);
          }, 1500);
        } catch (error) {
          console.error("修复失败", error);
          setInspectStep('error');
          toast({
            title: "修复失败",
            description: "执行自动修复时发生错误",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
      }, 1500);
    } catch (error) {
      console.error("一键修复失败", error);
      toast({
        title: "修复失败",
        description: "执行修复时发生错误",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // 报告导出
  const handleExportReport = () => {
    if (!reportModal.report) return;
    const header = ["检查内容","检查命令","结果","通过","自动修复","修复命令","修复结果","备注"];
    const rows = reportModal.report.results.map((r: any) => [
      r.item, r.check_command, r.check_result || r.check_error, r.passed ? "✔" : "✘",
      r.auto_fix ? "是" : "否", r.fix_command || "", r.fixed ? (r.fix_result || "已修复") : (r.fix_error || "-"), r.remark || ""
    ]);
    const csv = [header, ...rows].map(r => r.map((v: string) => `"${(v||"").replace(/"/g,'""')}"`).join(",")).join("\r\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `inspection-report-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleTemplateSubmit = async (data: InspectionTemplate) => {
    let success;
    if (editing?.id) {
      success = await updateTemplate({...data, id: editing.id});
    } else {
      success = await createTemplate(data);
    }

    if (success) {
      onClose();
    }
  };

  return (
    <ClientOnly>
      <Box p={{ base: 4, md: 8 }} bg={bgColor}>
        <Flex
          justify="space-between"
          align="center"
          mb={6}
          direction={{ base: 'column', md: 'row' }}
          gap={{ base: 4, md: 0 }}
        >
          <Flex align="center" width={{ base: '100%', md: 'auto' }}>
            <Heading size="md">巡检模板管理</Heading>
            <Flex ml={4}>
              <NotificationCenter onNavigate={(url) => window.location.href = url} />
            </Flex>
          </Flex>

          <Flex gap={2} width={{ base: '100%', md: 'auto' }}>
            <Button
              colorScheme="teal"
              leftIcon={<AddIcon />}
              onClick={handleCreate}
              width={{ base: '100%', md: 'auto' }}
            >
              新建模板
            </Button>
          </Flex>
        </Flex>

        {/* 移动端显示自定义仪表盘 */}
        {isMobile && (
          <Box mb={6}>
            <CustomDashboard />
          </Box>
        )}

        <Box mb={6} />

        {loading ? (
          <Flex justify="center" align="center" h="200px"><Spinner color="teal.500" size="xl" /></Flex>
        ) : isMobile ? (
          // 移动端卡片视图
          <VStack spacing={4} align="stretch">
            {templates.map((tpl: InspectionTemplate) => (
              <MobileTemplateItem
                key={tpl.id}
                template={tpl}
                onEdit={() => handleEdit(tpl)}
                onDelete={() => handleDelete(tpl.id)}
                onExecute={() => { setExecTpl(tpl); execOnOpen(); }}
                onInspect={() => handleOneClickInspect(tpl)}
                onFix={() => handleOneClickFix(tpl)}
              />
            ))}
          </VStack>
        ) : (
          // 桌面端表格视图
          <Table
            variant="simple"
            bg={bgColor}
            borderRadius="md"
            boxShadow="sm"
            width="100%"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Thead>
              <Tr>
                <Th px={4} width="18%">模板名称</Th>
                <Th px={4} width="16%">设备类型</Th>
                <Th px={4} width="28%">描述</Th>
                <Th px={4} width="10%">检查项数</Th>
                <Th px={4} width="18%">创建时间</Th>
                <Th px={4} width="10%">操作</Th>
              </Tr>
            </Thead>
            <Box h={3} />
            <Tbody>
              {templates.map((tpl: InspectionTemplate) => (
                <Tr key={tpl.id}>
                  <Td px={4} width="18%">{tpl.name}</Td>
                  <Td px={4} width="16%">{Array.isArray(tpl.device_types) ? tpl.device_types.join(', ') : ''}</Td>
                  <Td px={4} width="28%">{tpl.description}</Td>
                  <Td px={4} width="10%">{tpl.content?.length || 0}</Td>
                  <Td px={4} width="18%">{tpl.created_at ? new Date(tpl.created_at).toLocaleString() : ""}</Td>
                  <Td px={4} width="10%">
                    <HStack spacing={1}>
                      <Tooltip label="编辑模板">
                        <IconButton aria-label="编辑" icon={<EditIcon />} size="sm" onClick={() => handleEdit(tpl)} />
                      </Tooltip>
                      <Tooltip label="删除模板">
                        <IconButton aria-label="删除" icon={<DeleteIcon />} size="sm" colorScheme="red" onClick={() => handleDelete(tpl.id)} />
                      </Tooltip>
                      <Tooltip label="执行巡检">
                        <IconButton aria-label="执行" icon={<ViewIcon />} size="sm" colorScheme="blue" onClick={() => { setExecTpl(tpl); execOnOpen(); }} />
                      </Tooltip>
                      <Menu>
                        <MenuButton as={Button} size="sm" colorScheme="teal" rightIcon={<ChevronDownIcon />}>
                          操作
                        </MenuButton>
                        <MenuList>
                          <MenuItem icon={<CheckIcon />} onClick={() => handleOneClickInspect(tpl)}>一键巡检</MenuItem>
                          <MenuItem icon={<RepeatIcon />} onClick={() => handleOneClickFix(tpl)}>一键修复</MenuItem>
                        </MenuList>
                      </Menu>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        )}
        <TemplateForm
          isOpen={isOpen}
          onClose={onClose}
          onSubmit={handleTemplateSubmit}
          initialData={editing}
        />
        <ExecuteModal
          isOpen={execOpen}
          onClose={execOnClose}
          template={execTpl}
        />
        <Modal isOpen={reportModal.visible} onClose={() => setReportModal({visible: false})} size="5xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>巡检报告</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              {reportModal.report && (
                <>
                  <Tabs isFitted variant="enclosed" colorScheme="teal" mb={4}>
                    <TabList>
                      <Tab>巡检结果</Tab>
                      <Tab>AI增强分析</Tab>
                    </TabList>
                    <TabPanels>
                      <TabPanel p={0} pt={4}>
                        <Flex justify="space-between" align="center" mb={4}>
                          <Heading size="sm">巡检结果</Heading>
                          <Button size="sm" colorScheme="teal" onClick={handleExportReport} leftIcon={<AddIcon />}>导出CSV</Button>
                        </Flex>
                        <Box overflowX="auto">
                          <Table size="sm" variant="striped" borderWidth="1px" borderColor={borderColor}>
                            <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                              <Tr>
                                <Th>检查内容</Th>
                                <Th>检查命令</Th>
                                <Th>结果</Th>
                                <Th>通过</Th>
                                <Th>自动修复</Th>
                                <Th>修复命令</Th>
                                <Th>修复结果</Th>
                                <Th>备注</Th>
                              </Tr>
                            </Thead>
                            <Tbody>
                              {reportModal.report.results.map((r: any, idx: number) => (
                                <Tr
                                  key={idx}
                                  bg={r.passed
                                    ? useColorModeValue("green.50", "green.900")
                                    : r.fixed
                                      ? useColorModeValue("blue.50", "blue.900")
                                      : useColorModeValue("red.50", "red.900")
                                  }
                                >
                                  <Td>{r.item}</Td>
                                  <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.check_command}</pre></Td>
                                  <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.check_result || r.check_error}</pre></Td>
                                  <Td>{r.passed ? "✔" : "✘"}</Td>
                                  <Td>{r.auto_fix ? "是" : "否"}</Td>
                                  <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.fix_command || "-"}</pre></Td>
                                  <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.fixed ? (r.fix_result || "已修复") : (r.fix_error || "-")}</pre></Td>
                                  <Td>{r.remark || "-"}</Td>
                                </Tr>
                              ))}
                            </Tbody>
                          </Table>
                        </Box>
                      </TabPanel>
                      <TabPanel p={0} pt={4}>
                        <AIAnalysisPanel
                          inspectionResults={reportModal.report}
                          basicAnalysis={reportModal.aiAnalysis}
                        />
                      </TabPanel>
                    </TabPanels>
                  </Tabs>
                </>
              )}
            </ModalBody>
            <ModalFooter>
              <Button onClick={() => setReportModal({visible: false})}>关闭</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
        <Modal isOpen={localInspectStep !== 'idle' && localInspectStep !== 'done'} onClose={() => setInspectStep('idle')} isCentered>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>自动化巡检流程</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Box textAlign="center" py={6}>
                {localInspectStep === 'inspecting' && <><Spinner size="xl" color="teal.500" mb={4}/><Box fontWeight="bold">正在执行巡检...</Box></>}
                {localInspectStep === 'fixing' && <><Spinner size="xl" color="orange.400" mb={4}/><Box fontWeight="bold">正在自动修复异常项...</Box></>}
                {localInspectStep === 'analyzing' && <><Spinner size="xl" color="blue.400" mb={4}/><Box fontWeight="bold">正在AI智能分析...</Box></>}
                {localInspectStep === 'error' && <Box color="red.500" fontWeight="bold">巡检过程中发生错误</Box>}
              </Box>
            </ModalBody>
          </ModalContent>
        </Modal>
      </Box>
    </ClientOnly>
  );
};

export default InspectionTemplatePage;
