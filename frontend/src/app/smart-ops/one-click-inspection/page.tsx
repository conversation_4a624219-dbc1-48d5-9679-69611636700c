"use client"

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Text,
  VStack,
  HStack,
  Select,
  Input,
  FormControl,
  FormLabel,
  useToast,
  useColorModeValue,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Divider,
  SimpleGrid,
  Badge,
  Spinner,
  Progress,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Tooltip,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon
} from '@chakra-ui/react';
import { CheckIcon, WarningIcon, RepeatIcon, DownloadIcon, InfoIcon } from '@chakra-ui/icons';
import axios from 'axios';
import { useTemplateStore, InspectionTemplate } from '../../../store/templateStore';
import AIAnalysisPanel from '../../../components/inspection/AIAnalysisPanel';
import { sendMockNotification } from '../../../services/notificationService';

// 设备类型
interface Device {
  id: string;
  name: string;
  ip: string;
  type: string;
  status: 'online' | 'offline' | 'warning';
  lastInspection?: string;
}

// 巡检状态
type InspectionStatus = 'idle' | 'selecting' | 'inspecting' | 'fixing' | 'analyzing' | 'done' | 'error';

// 一键巡检页面
const OneClickInspectionPage = () => {
  const toast = useToast();
  const { 
    templates, 
    fetchTemplates, 
    loading: templatesLoading,
    oneClickInspect,
    inspectionResult,
    aiAnalysis,
    inspectStep,
    inspectStepMsg
  } = useTemplateStore();
  
  // 本地状态
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [devices, setDevices] = useState<Device[]>([]);
  const [loadingDevices, setLoadingDevices] = useState(false);
  const [inspectionStatus, setInspectionStatus] = useState<InspectionStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [reportModal, setReportModal] = useState<{visible: boolean, report?: any, aiAnalysis?: string}>({visible: false});
  
  // 颜色模式
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  // 模拟设备数据
  const mockDevices: Device[] = [
    { id: '1', name: 'Server-001', ip: '*************', type: 'Linux', status: 'online', lastInspection: '2023-05-15' },
    { id: '2', name: 'Server-002', ip: '*************', type: 'Linux', status: 'online', lastInspection: '2023-05-10' },
    { id: '3', name: 'Router-001', ip: '***********', type: 'Huawei', status: 'online', lastInspection: '2023-05-12' },
    { id: '4', name: 'Switch-001', ip: '***********', type: 'Huawei', status: 'warning', lastInspection: '2023-05-01' },
    { id: '5', name: 'Server-003', ip: '*************', type: 'Windows', status: 'offline', lastInspection: '2023-04-28' },
    { id: '6', name: 'Server-004', ip: '*************', type: 'Linux', status: 'online', lastInspection: '2023-05-14' },
  ];
  
  // 加载模板和设备
  useEffect(() => {
    fetchTemplates();
    
    // 模拟加载设备
    setLoadingDevices(true);
    setTimeout(() => {
      setDevices(mockDevices);
      setLoadingDevices(false);
    }, 1000);
  }, [fetchTemplates]);
  
  // 监听巡检状态变化
  useEffect(() => {
    if (inspectStep === 'inspecting') {
      setInspectionStatus('inspecting');
      setProgress(25);
    } else if (inspectStep === 'fixing') {
      setInspectionStatus('fixing');
      setProgress(50);
    } else if (inspectStep === 'analyzing') {
      setInspectionStatus('analyzing');
      setProgress(75);
    } else if (inspectStep === 'done') {
      setInspectionStatus('done');
      setProgress(100);
      
      // 显示报告
      setReportModal({ 
        visible: true, 
        report: inspectionResult, 
        aiAnalysis 
      });
      
      // 发送通知
      sendMockNotification(
        '巡检完成',
        `设备巡检已完成，共发现 ${inspectionResult?.results?.filter((r: any) => !r.passed).length || 0} 个问题`,
        'success'
      );
    } else if (inspectStep === 'error') {
      setInspectionStatus('error');
      setProgress(0);
      
      // 发送通知
      sendMockNotification(
        '巡检失败',
        '设备巡检过程中发生错误，请检查设备连接状态',
        'error'
      );
    }
  }, [inspectStep, inspectionResult, aiAnalysis]);
  
  // 选择设备
  const handleSelectDevice = (deviceId: string) => {
    if (selectedDevices.includes(deviceId)) {
      setSelectedDevices(prev => prev.filter(id => id !== deviceId));
    } else {
      setSelectedDevices(prev => [...prev, deviceId]);
    }
  };
  
  // 选择全部设备
  const handleSelectAllDevices = () => {
    if (selectedDevices.length === devices.length) {
      setSelectedDevices([]);
    } else {
      setSelectedDevices(devices.map(d => d.id));
    }
  };
  
  // 选择模板
  const handleSelectTemplate = (templateId: number | null) => {
    setSelectedTemplate(templateId);
  };
  
  // 开始巡检
  const handleStartInspection = async () => {
    if (!selectedTemplate) {
      toast({
        title: '请选择巡检模板',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    if (selectedDevices.length === 0) {
      toast({
        title: '请选择至少一个设备',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    // 开始巡检
    setInspectionStatus('selecting');
    
    try {
      // 调用一键巡检
      await oneClickInspect(selectedTemplate);
    } catch (error) {
      console.error('巡检失败:', error);
      setInspectionStatus('error');
      toast({
        title: '巡检失败',
        description: '执行巡检时发生错误',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };
  
  // 导出报告
  const handleExportReport = () => {
    if (!reportModal.report) return;
    
    const header = ["检查内容","检查命令","结果","通过","自动修复","修复命令","修复结果","备注"];
    const rows = reportModal.report.results.map((r: any) => [
      r.item, r.check_command, r.check_result || r.check_error, r.passed ? "✔" : "✘",
      r.auto_fix ? "是" : "否", r.fix_command || "", r.fixed ? (r.fix_result || "已修复") : (r.fix_error || "-"), r.remark || ""
    ]);
    const csv = [header, ...rows].map(r => r.map((v: string) => `"${(v||"").replace(/"/g,'""')}"`).join(",")).join("\r\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `inspection-report-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  return (
    <Container maxW="container.xl" p={4}>
      <Heading size="lg" mb={6}>一键巡检</Heading>
      
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* 左侧：设备和模板选择 */}
        <Card bg={bgColor} borderWidth="1px" borderColor={borderColor}>
          <CardHeader>
            <Heading size="md">选择巡检对象</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={6} align="stretch">
              {/* 模板选择 */}
              <Box>
                <FormControl>
                  <FormLabel fontWeight="bold">选择巡检模板</FormLabel>
                  <Select 
                    placeholder="选择巡检模板" 
                    value={selectedTemplate || ''} 
                    onChange={(e) => handleSelectTemplate(e.target.value ? Number(e.target.value) : null)}
                    isDisabled={inspectionStatus !== 'idle'}
                  >
                    {templates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name} ({template.device_types.join(', ')})
                      </option>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              
              {/* 设备选择 */}
              <Box>
                <Flex justify="space-between" align="center" mb={2}>
                  <FormLabel fontWeight="bold" mb={0}>选择设备</FormLabel>
                  <Button 
                    size="xs" 
                    onClick={handleSelectAllDevices}
                    isDisabled={inspectionStatus !== 'idle'}
                  >
                    {selectedDevices.length === devices.length ? '取消全选' : '全选'}
                  </Button>
                </Flex>
                
                {loadingDevices ? (
                  <Flex justify="center" py={8}>
                    <Spinner />
                  </Flex>
                ) : (
                  <VStack spacing={2} align="stretch" maxH="300px" overflowY="auto">
                    {devices.map((device) => (
                      <Flex 
                        key={device.id}
                        p={2}
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={borderColor}
                        justify="space-between"
                        align="center"
                        bg={selectedDevices.includes(device.id) ? 'teal.50' : 'transparent'}
                        cursor={inspectionStatus === 'idle' ? 'pointer' : 'default'}
                        onClick={() => inspectionStatus === 'idle' && handleSelectDevice(device.id)}
                        opacity={device.status === 'offline' ? 0.6 : 1}
                      >
                        <HStack>
                          <Box 
                            w="10px" 
                            h="10px" 
                            borderRadius="full" 
                            bg={device.status === 'online' ? 'green.500' : device.status === 'warning' ? 'orange.500' : 'red.500'} 
                          />
                          <VStack spacing={0} align="start">
                            <Text fontWeight="medium">{device.name}</Text>
                            <Text fontSize="xs" color="gray.500">{device.ip} ({device.type})</Text>
                          </VStack>
                        </HStack>
                        <HStack>
                          {device.lastInspection && (
                            <Text fontSize="xs" color="gray.500">
                              上次巡检: {device.lastInspection}
                            </Text>
                          )}
                          <Checkbox 
                            isChecked={selectedDevices.includes(device.id)} 
                            isDisabled={device.status === 'offline' || inspectionStatus !== 'idle'}
                            onChange={(e) => e.stopPropagation()}
                          />
                        </HStack>
                      </Flex>
                    ))}
                  </VStack>
                )}
              </Box>
            </VStack>
          </CardBody>
          <CardFooter>
            <Button 
              colorScheme="teal" 
              onClick={handleStartInspection}
              isLoading={inspectionStatus !== 'idle' && inspectionStatus !== 'done' && inspectionStatus !== 'error'}
              loadingText={
                inspectionStatus === 'inspecting' ? '正在巡检...' :
                inspectionStatus === 'fixing' ? '正在修复...' :
                inspectionStatus === 'analyzing' ? '正在分析...' :
                '正在处理...'
              }
              isDisabled={!selectedTemplate || selectedDevices.length === 0 || inspectionStatus !== 'idle'}
              width="full"
            >
              开始一键巡检
            </Button>
          </CardFooter>
        </Card>
        
        {/* 右侧：巡检状态和结果 */}
        <Card bg={bgColor} borderWidth="1px" borderColor={borderColor}>
          <CardHeader>
            <Heading size="md">巡检状态</Heading>
          </CardHeader>
          <CardBody>
            {inspectionStatus === 'idle' ? (
              <Flex direction="column" justify="center" align="center" h="300px" color="gray.500">
                <InfoIcon boxSize={10} mb={4} />
                <Text>请选择巡检模板和设备，然后点击"开始一键巡检"</Text>
              </Flex>
            ) : (
              <VStack spacing={6} align="stretch">
                {/* 进度条 */}
                <Box>
                  <Flex justify="space-between" mb={2}>
                    <Text fontWeight="bold">巡检进度</Text>
                    <Text>{progress}%</Text>
                  </Flex>
                  <Progress 
                    value={progress} 
                    colorScheme={
                      inspectionStatus === 'error' ? 'red' :
                      inspectionStatus === 'done' ? 'green' : 'teal'
                    }
                    size="md"
                    borderRadius="md"
                  />
                </Box>
                
                {/* 状态信息 */}
                <Box>
                  <Text fontWeight="bold" mb={2}>当前状态</Text>
                  <Flex 
                    p={4} 
                    bg={
                      inspectionStatus === 'error' ? 'red.50' :
                      inspectionStatus === 'done' ? 'green.50' : 'blue.50'
                    } 
                    borderRadius="md"
                    align="center"
                  >
                    {inspectionStatus === 'inspecting' && <Spinner size="sm" mr={3} color="blue.500" />}
                    {inspectionStatus === 'fixing' && <Spinner size="sm" mr={3} color="orange.500" />}
                    {inspectionStatus === 'analyzing' && <Spinner size="sm" mr={3} color="purple.500" />}
                    {inspectionStatus === 'done' && <CheckIcon mr={3} color="green.500" />}
                    {inspectionStatus === 'error' && <WarningIcon mr={3} color="red.500" />}
                    <Text>
                      {inspectionStatus === 'selecting' && '准备巡检...'}
                      {inspectionStatus === 'inspecting' && '正在执行巡检...'}
                      {inspectionStatus === 'fixing' && '正在自动修复异常项...'}
                      {inspectionStatus === 'analyzing' && '正在AI智能分析...'}
                      {inspectionStatus === 'done' && '巡检完成！'}
                      {inspectionStatus === 'error' && '巡检过程中发生错误'}
                    </Text>
                  </Flex>
                </Box>
                
                {/* 巡检结果摘要 */}
                {inspectionStatus === 'done' && inspectionResult && (
                  <Box>
                    <Text fontWeight="bold" mb={2}>巡检结果摘要</Text>
                    <SimpleGrid columns={3} spacing={4}>
                      <Stat 
                        title="检查项" 
                        value={inspectionResult.results.length} 
                        color="blue.500" 
                      />
                      <Stat 
                        title="通过项" 
                        value={inspectionResult.results.filter((r: any) => r.passed).length} 
                        color="green.500" 
                      />
                      <Stat 
                        title="异常项" 
                        value={inspectionResult.results.filter((r: any) => !r.passed).length} 
                        color="red.500" 
                      />
                    </SimpleGrid>
                  </Box>
                )}
                
                {/* 操作按钮 */}
                <Flex justify="space-between" mt={4}>
                  <Button 
                    leftIcon={<RepeatIcon />}
                    onClick={() => setInspectionStatus('idle')}
                    isDisabled={inspectionStatus !== 'done' && inspectionStatus !== 'error'}
                  >
                    重新巡检
                  </Button>
                  
                  <Button 
                    colorScheme="teal"
                    leftIcon={<DownloadIcon />}
                    onClick={() => setReportModal({ visible: true, report: inspectionResult, aiAnalysis })}
                    isDisabled={inspectionStatus !== 'done'}
                  >
                    查看详细报告
                  </Button>
                </Flex>
              </VStack>
            )}
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* 报告模态框 */}
      <Modal isOpen={reportModal.visible} onClose={() => setReportModal({visible: false})} size="5xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>巡检报告</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {reportModal.report && (
              <>
                <Tabs isFitted variant="enclosed" colorScheme="teal" mb={4}>
                  <TabList>
                    <Tab>巡检结果</Tab>
                    <Tab>AI增强分析</Tab>
                  </TabList>
                  <TabPanels>
                    <TabPanel p={0} pt={4}>
                      <Flex justify="space-between" align="center" mb={4}>
                        <Heading size="sm">巡检结果</Heading>
                        <Button size="sm" colorScheme="teal" onClick={handleExportReport} leftIcon={<DownloadIcon />}>导出CSV</Button>
                      </Flex>
                      <Box overflowX="auto">
                        <Table size="sm" variant="striped" borderWidth="1px" borderColor={borderColor}>
                          <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                            <Tr>
                              <Th>检查内容</Th>
                              <Th>检查命令</Th>
                              <Th>结果</Th>
                              <Th>通过</Th>
                              <Th>自动修复</Th>
                              <Th>修复命令</Th>
                              <Th>修复结果</Th>
                              <Th>备注</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {reportModal.report.results.map((r: any, idx: number) => (
                              <Tr 
                                key={idx} 
                                bg={r.passed 
                                  ? useColorModeValue("green.50", "green.900") 
                                  : r.fixed 
                                    ? useColorModeValue("blue.50", "blue.900") 
                                    : useColorModeValue("red.50", "red.900")
                                }
                              >
                                <Td>{r.item}</Td>
                                <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.check_command}</pre></Td>
                                <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.check_result || r.check_error}</pre></Td>
                                <Td>{r.passed ? "✔" : "✘"}</Td>
                                <Td>{r.auto_fix ? "是" : "否"}</Td>
                                <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.fix_command || "-"}</pre></Td>
                                <Td><pre style={{whiteSpace: 'pre-wrap'}}>{r.fixed ? (r.fix_result || "已修复") : (r.fix_error || "-")}</pre></Td>
                                <Td>{r.remark || "-"}</Td>
                              </Tr>
                            ))}
                          </Tbody>
                        </Table>
                      </Box>
                    </TabPanel>
                    <TabPanel p={0} pt={4}>
                      <AIAnalysisPanel 
                        inspectionResults={reportModal.report} 
                        basicAnalysis={reportModal.aiAnalysis} 
                      />
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={() => setReportModal({visible: false})}>关闭</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  );
};

// 统计卡片组件
interface StatProps {
  title: string;
  value: number;
  color: string;
}

const Stat: React.FC<StatProps> = ({ title, value, color }) => {
  return (
    <Box p={4} borderRadius="md" borderWidth="1px" borderColor={color} bg={`${color}10`}>
      <Text fontSize="sm" color="gray.600">{title}</Text>
      <Text fontSize="2xl" fontWeight="bold" color={color}>{value}</Text>
    </Box>
  );
};

// 添加缺失的Checkbox组件
const Checkbox = ({ isChecked, isDisabled, onChange }: { isChecked: boolean, isDisabled?: boolean, onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void }) => {
  return (
    <Box 
      as="input" 
      type="checkbox" 
      checked={isChecked} 
      disabled={isDisabled}
      onChange={onChange}
      cursor={isDisabled ? 'not-allowed' : 'pointer'}
    />
  );
};

export default OneClickInspectionPage;
