'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  HStack,
  VStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Tooltip,
  Divider,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Progress,
  Tag,
  TagLabel,
  TagLeftIcon,
  Switch,
  Select,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
} from '@chakra-ui/react'
import {
  MagnifyingGlass,
  Plus,
  Gear,
  ArrowsClockwise,
  Warning,
  CheckCircle,
  XCircle,
  Buildings,
  MapPin,
  Pencil,
  ClockCounterClockwise,
  Article,
  Wrench,
  ChartLine,
  WifiHigh,
  WifiMedium,
  Gauge,
  CaretDown,
  DotsThreeVertical,
  Trash,
  Desktop,
  Broadcast,
  ArrowsDownUp,
  ArrowUp,
  ArrowDown,
  Clock,
  Calendar,
  Funnel,
} from '@phosphor-icons/react'
import axios from 'axios'

// 设备状态类型定义
interface Device {
  id: string
  name: string
  device_type: string
  model: string
  serial_number: string
  ip_address?: string
  mac_address?: string
  location?: string
  status: 'active' | 'inactive' | 'maintenance' | 'retired'
  components: Component[]
  last_updated: string
}

// 组件状态类型定义
interface Component {
  id: string
  name: string
  component_type: string
  model: string
  serial_number: string
  status: 'installed' | 'faulty' | 'retired' | 'maintenance'
  health: 'healthy' | 'warning' | 'critical'
  last_checked: string
}

export default function DeviceStatusPage() {
  const [devices, setDevices] = useState<Device[]>([])
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  const toast = useToast()
  const bgColor = useColorModeValue('white', 'gray.800')
  const borderColor = useColorModeValue('gray.200', 'gray.700')

  // 获取设备数据
  const fetchDevices = async () => {
    setIsLoading(true)
    try {
      const response = await axios.get('/api/device_monitor/api/devices/status')
      const devices = response.data.devices.map((device: any) => ({
        ...device,
        last_updated: device.updated_at,
        components: device.components.map((comp: any) => ({
          ...comp,
          last_checked: comp.updated_at || comp.created_at
        }))
      }))
      setDevices(devices)
      setFilteredDevices(devices)
    } catch (error) {
      toast({
        title: "获取设备数据失败",
        description: "无法从服务器获取设备状态数据",
        status: "error",
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 过滤设备
  useEffect(() => {
    let filtered = devices

    // 按状态过滤
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(device => device.status === selectedStatus)
    }

    // 按搜索词过滤
    if (searchQuery) {
      filtered = filtered.filter(device =>
        device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.serial_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (device.ip_address && device.ip_address.includes(searchQuery)))
    }

    setFilteredDevices(filtered)
  }, [searchQuery, selectedStatus, devices])

  // 初始化加载数据
  useEffect(() => {
    fetchDevices()
  }, [])

  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green'
      case 'inactive':
        return 'gray'
      case 'maintenance':
        return 'orange'
      case 'retired':
        return 'red'
      default:
        return 'gray'
    }
  }

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '运行中'
      case 'inactive':
        return '离线'
      case 'maintenance':
        return '维护中'
      case 'retired':
        return '已退役'
      default:
        return status
    }
  }

  // 获取健康状态颜色
  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy':
        return 'green'
      case 'warning':
        return 'yellow'
      case 'critical':
        return 'red'
      default:
        return 'gray'
    }
  }

  return (
    <Box p={6}>
      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>设备状态监控</Heading>
            <Text color="gray.500" fontSize="md">监控设备及其组件的健康状态</Text>
          </Box>
          <HStack spacing={4} mt={{ base: 4, md: 0 }}>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="blue"
              variant="outline"
              onClick={fetchDevices}
              isLoading={isLoading}
            >
              刷新状态
            </Button>
          </HStack>
        </Flex>
      </Box>

      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <InputGroup maxW={{ base: '100%', md: '300px' }}>
            <InputLeftElement pointerEvents="none">
              <Box color="gray.500">
                <MagnifyingGlass size={20} />
              </Box>
            </InputLeftElement>
            <Input
              placeholder="搜索设备..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              pl="40px"
            />
          </InputGroup>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">所有状态</option>
            <option value="active">运行中</option>
            <option value="inactive">离线</option>
            <option value="maintenance">维护中</option>
            <option value="retired">已退役</option>
          </Select>
        </Flex>
      </Box>

      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* 设备列表 */}
        <Box>
          <Heading size="md" mb={3}>设备列表</Heading>
          <Box
            borderWidth="1px"
            borderRadius="xl"
            overflow="hidden"
            bg={bgColor}
            borderColor={borderColor}
            boxShadow="md"
            height="600px"
            overflowY="auto"
          >
            <Table variant="simple">
              <Thead bg={useColorModeValue('gray.50', 'gray.700')} position="sticky" top={0} zIndex={1}>
                <Tr>
                  <Th py={4}>设备名称</Th>
                  <Th py={4}>状态</Th>
                  <Th py={4}>型号</Th>
                  <Th py={4}>序列号</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredDevices.length > 0 ? (
                  filteredDevices.map(device => (
                    <Tr
                      key={device.id}
                      _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                      cursor="pointer"
                      onClick={() => setSelectedDevice(device)}
                      bg={selectedDevice?.id === device.id ? useColorModeValue('blue.50', 'blue.900') : undefined}
                    >
                      <Td>
                        <Text fontWeight="medium">{device.name}</Text>
                        <Text fontSize="xs" color="gray.500">{device.device_type}</Text>
                      </Td>
                      <Td>
                        <Badge
                          colorScheme={getStatusColor(device.status)}
                          px={2}
                          py={1}
                          borderRadius="full"
                          variant="subtle"
                        >
                          {getStatusText(device.status)}
                        </Badge>
                      </Td>
                      <Td>{device.model}</Td>
                      <Td>{device.serial_number}</Td>
                    </Tr>
                  ))
                ) : (
                  <Tr>
                    <Td colSpan={4} textAlign="center" py={8}>
                      <VStack spacing={3}>
                        <Box
                          p={3}
                          borderRadius="full"
                          bg={useColorModeValue('gray.100', 'gray.700')}
                        >
                          <MagnifyingGlass size={24} weight="duotone" />
                        </Box>
                        <Text>没有找到匹配的设备</Text>
                        <Text fontSize="sm" color="gray.500">
                          尝试使用不同的搜索条件或清除筛选器
                        </Text>
                      </VStack>
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </Box>
        </Box>

        {/* 设备详情和组件状态 */}
        <Box>
          {selectedDevice ? (
            <Box>
              <Heading size="md" mb={3}>设备详情</Heading>
              <Box
                borderWidth="1px"
                borderRadius="xl"
                overflow="hidden"
                bg={bgColor}
                borderColor={borderColor}
                boxShadow="md"
                p={4}
                mb={4}
              >
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontSize="sm" color="gray.500">设备名称</Text>
                    <Text fontWeight="medium">{selectedDevice.name}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">设备类型</Text>
                    <Text fontWeight="medium">{selectedDevice.device_type}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">型号</Text>
                    <Text fontWeight="medium">{selectedDevice.model}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">序列号</Text>
                    <Text fontWeight="medium">{selectedDevice.serial_number}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">IP地址</Text>
                    <Text fontWeight="medium">{selectedDevice.ip_address || '-'}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.500">最后更新时间</Text>
                    <Text fontWeight="medium">
                      {new Date(selectedDevice.last_updated).toLocaleString()}
                    </Text>
                  </Box>
                </SimpleGrid>
              </Box>

              <Heading size="md" mb={3}>组件状态</Heading>
              <Box
                borderWidth="1px"
                borderRadius="xl"
                overflow="hidden"
                bg={bgColor}
                borderColor={borderColor}
                boxShadow="md"
                height="400px"
                overflowY="auto"
              >
                <Table variant="simple">
                  <Thead bg={useColorModeValue('gray.50', 'gray.700')} position="sticky" top={0} zIndex={1}>
                    <Tr>
                      <Th py={3}>组件名称</Th>
                      <Th py={3}>类型</Th>
                      <Th py={3}>状态</Th>
                      <Th py={3}>健康状态</Th>
                      <Th py={3}>最后检查</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {selectedDevice.components.length > 0 ? (
                      selectedDevice.components.map(component => (
                        <Tr key={component.id}>
                          <Td>{component.name}</Td>
                          <Td>{component.component_type}</Td>
                          <Td>
                            <Badge
                              colorScheme={getStatusColor(component.status)}
                              px={2}
                              py={1}
                              borderRadius="full"
                              variant="subtle"
                            >
                              {component.status}
                            </Badge>
                          </Td>
                          <Td>
                            <Badge
                              colorScheme={getHealthColor(component.health)}
                              px={2}
                              py={1}
                              borderRadius="full"
                              variant="subtle"
                            >
                              {component.health}
                            </Badge>
                          </Td>
                          <Td>
                            {new Date(component.last_checked).toLocaleString()}
                          </Td>
                        </Tr>
                      ))
                    ) : (
                      <Tr>
                        <Td colSpan={5} textAlign="center" py={4}>
                          <Text>没有组件记录</Text>
                        </Td>
                      </Tr>
                    )}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          ) : (
            <Box
              borderWidth="1px"
              borderRadius="xl"
              overflow="hidden"
              bg={bgColor}
              borderColor={borderColor}
              boxShadow="md"
              height="600px"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <VStack spacing={4}>
                <Box
                  p={4}
                  borderRadius="full"
                  bg={useColorModeValue('gray.100', 'gray.700')}
                >
                  <Desktop size={40} weight="duotone" />
                </Box>
                <Text fontSize="lg" fontWeight="medium">选择一个设备查看详情</Text>
                <Text color="gray.500" textAlign="center" maxW="400px">
                  从左侧列表中选择一个设备，查看其详细信息和组件状态
                </Text>
              </VStack>
            </Box>
          )}
        </Box>
      </SimpleGrid>
    </Box>
  )
}
