'use client'
import React, { useEffect, useState } from 'react'
import { Card, Table, Button, Tag, Row, Col, Select, Input, Modal, message, Form } from 'antd'
import { useAuth } from '@/context/AuthProvider'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useRouter } from 'next/navigation'

const { Option } = Select
const PAGE_SIZE = 10

const PermissionsPage = () => {
  const { access } = useAuth()
  const router = useRouter()
  const [users, setUsers] = useState<any[]>([])
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [groups, setGroups] = useState<any[]>([])
  const [permissions, setPermissions] = useState<any[]>([])
  const [editingUser, setEditingUser] = useState<any>(null)
  const [modalVisible, setModalVisible] = useState(false)
  const [resetPwdModal, setResetPwdModal] = useState(false)
  const [resetPwd, setResetPwd] = useState('')
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [newUser, setNewUser] = useState<any>({
    username: '', password: '', name: '', email: '', groups: [], user_permissions: [], is_active: true
  })

  // 获取用户分页
  const fetchUsers = async (p = 1) => {
    setLoading(true)
    const res = await fetch(`/api/accounts/users/?page=${p}&page_size=${PAGE_SIZE}`, {
      headers: { Authorization: `Bearer ${access}` }
    })
    const data = await res.json()
    setUsers(data.results)
    setTotal(data.count)
    setLoading(false)
  }

  // 获取所有角色
  const fetchGroups = async () => {
    const res = await fetch('/api/accounts/groups/', {
      headers: { Authorization: `Bearer ${access}` }
    })
    setGroups(await res.json())
  }

  // 获取所有权限点
  const fetchPermissions = async () => {
    const res = await fetch('/api/accounts/permissions/', {
      headers: { Authorization: `Bearer ${access}` }
    })
    setPermissions(await res.json())
  }

  useEffect(() => {
    if (access) {
      fetchUsers(page)
      fetchGroups()
      fetchPermissions()
    }
    // eslint-disable-next-line
  }, [access, page])

  // 新建用户
  const handleCreate = async () => {
    const res = await fetch('/api/accounts/users/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${access}` },
      body: JSON.stringify({
        username: newUser.username,
        password: newUser.password,
        email: newUser.email,
        first_name: newUser.name,
        groups: newUser.groups,
        user_permissions: newUser.user_permissions,
        is_active: newUser.is_active,
      })
    })
    if (res.ok) {
      message.success('新建用户成功')
      setCreateModalVisible(false)
      setNewUser({ username: '', password: '', name: '', email: '', groups: [], user_permissions: [], is_active: true })
      fetchUsers(page)
    } else {
      const data = await res.json()
      message.error(data.error || '新建失败')
    }
  }

  // 编辑用户
  const handleSave = async () => {
    const res = await fetch(`/api/accounts/users/${editingUser.id}/`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${access}` },
      body: JSON.stringify({
        email: editingUser.email,
        first_name: editingUser.name,
        groups: editingUser.groups,
        user_permissions: editingUser.user_permissions,
        is_active: editingUser.is_active,
      })
    })
    if (res.ok) {
      message.success('保存成功')
      setModalVisible(false)
      fetchUsers(page)
    } else {
      const data = await res.json()
      message.error(data.error || '保存失败')
    }
  }

  // 删除用户
  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认删除该用户？',
      onOk: async () => {
        await fetch(`/api/accounts/users/${id}/`, {
          method: 'DELETE',
          headers: { Authorization: `Bearer ${access}` }
        })
        message.success('删除成功')
        fetchUsers(page)
      }
    })
  }

  // 重置密码
  const handleResetPwd = async () => {
    const res = await fetch(`/api/accounts/users/${editingUser.id}/set_password/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${access}` },
      body: JSON.stringify({ password: resetPwd })
    })
    if (res.ok) {
      message.success('密码已重置')
      setResetPwdModal(false)
      setResetPwd('')
    } else {
      message.error('重置失败')
    }
  }

  // 分配角色
  const handleSetGroups = async (groups: string[]) => {
    await fetch(`/api/accounts/users/${editingUser.id}/set_groups/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${access}` },
      body: JSON.stringify({ groups })
    })
    setEditingUser((u: any) => ({ ...u, groups }))
    message.success('角色已更新')
  }

  // 分配权限点
  const handleSetPermissions = async (perms: string[]) => {
    await fetch(`/api/accounts/users/${editingUser.id}/set_permissions/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${access}` },
      body: JSON.stringify({ permissions: perms })
    })
    setEditingUser((u: any) => ({ ...u, user_permissions: perms }))
    message.success('权限已更新')
  }

  const columns = [
    { title: '用户名', dataIndex: 'username', key: 'username' },
    { title: '姓名', dataIndex: 'first_name', key: 'first_name' },
    { title: '邮箱', dataIndex: 'email', key: 'email' },
    { title: '角色', dataIndex: 'groups', key: 'groups', render: (groups: string[]) => groups?.map(g => <Tag key={g}>{g}</Tag>) },
    { title: '权限点', dataIndex: 'user_permissions', key: 'user_permissions', render: (perms: string[]) => perms?.map(p => <Tag key={p}>{p}</Tag>) },
    { title: '状态', dataIndex: 'is_active', key: 'is_active', render: (v: boolean) => v ? <Tag color="green">启用</Tag> : <Tag color="red">停用</Tag> },
    {
      title: '操作', key: 'action', render: (_: any, record: any) => (
        <>
          <Button type="link" onClick={() => { setEditingUser({ ...record, name: record.first_name }); setModalVisible(true) }}>编辑</Button>
          <Button type="link" onClick={() => { setEditingUser(record); setResetPwdModal(true) }}>重置密码</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </>
      )
    }
  ]

  return (
    <Card 
      title={
        <span>
          <Button
            icon={<ArrowLeftOutlined />}
            type="link"
            onClick={() => router.push('/assets')}
            style={{ marginRight: 8, padding: 0 }}
          >
            返回
          </Button>
          权限管理
        </span>
      }
      extra={<Button type="primary" onClick={() => setCreateModalVisible(true)}>新建用户</Button>}
    >
      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={loading}
        pagination={{
          total,
          pageSize: PAGE_SIZE,
          current: page,
          onChange: setPage
        }}
      />

      {/* 新建用户弹窗 */}
      <Modal
        open={createModalVisible}
        title="新建用户"
        onCancel={() => setCreateModalVisible(false)}
        onOk={handleCreate}
        okText="创建"
      >
        <Form layout="vertical">
          <Form.Item label="用户名" required>
            <Input value={newUser.username} onChange={e => setNewUser((u: any) => ({ ...u, username: e.target.value }))} />
          </Form.Item>
          <Form.Item label="密码" required>
            <Input.Password value={newUser.password} onChange={e => setNewUser((u: any) => ({ ...u, password: e.target.value }))} />
          </Form.Item>
          <Form.Item label="姓名">
            <Input value={newUser.name} onChange={e => setNewUser((u: any) => ({ ...u, name: e.target.value }))} />
          </Form.Item>
          <Form.Item label="邮箱">
            <Input value={newUser.email} onChange={e => setNewUser((u: any) => ({ ...u, email: e.target.value }))} />
          </Form.Item>
          <Form.Item label="角色">
            <Select
              mode="multiple"
              value={newUser.groups}
              onChange={v => setNewUser((u: any) => ({ ...u, groups: v }))}
              style={{ width: '100%' }}
            >
              {groups.map(g => <Option key={g.name} value={g.name}>{g.name}</Option>)}
            </Select>
          </Form.Item>
          <Form.Item label="权限点">
            <Select
              mode="multiple"
              value={newUser.user_permissions}
              onChange={v => setNewUser((u: any) => ({ ...u, user_permissions: v }))}
              style={{ width: '100%' }}
            >
              {permissions.map(p => <Option key={p.codename} value={p.codename}>{p.name}</Option>)}
            </Select>
          </Form.Item>
          <Form.Item label="状态">
            <Select value={newUser.is_active ? 'active' : 'inactive'} onChange={v => setNewUser((u: any) => ({ ...u, is_active: v === 'active' }))}>
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑用户弹窗 */}
      <Modal
        open={modalVisible}
        title="编辑用户"
        onCancel={() => setModalVisible(false)}
        onOk={handleSave}
        okText="保存"
      >
        {editingUser && (
          <Form layout="vertical">
            <Form.Item label="用户名">
              <Input value={editingUser.username} disabled />
            </Form.Item>
            <Form.Item label="姓名">
              <Input value={editingUser.name} onChange={e => setEditingUser((u: any) => ({ ...u, name: e.target.value }))} />
            </Form.Item>
            <Form.Item label="邮箱">
              <Input value={editingUser.email} onChange={e => setEditingUser((u: any) => ({ ...u, email: e.target.value }))} />
            </Form.Item>
            <Form.Item label="角色">
              <Select
                mode="multiple"
                value={editingUser.groups}
                onChange={handleSetGroups}
                style={{ width: '100%' }}
              >
                {groups.map(g => <Option key={g.name} value={g.name}>{g.name}</Option>)}
              </Select>
            </Form.Item>
            <Form.Item label="权限点">
              <Select
                mode="multiple"
                value={editingUser.user_permissions}
                onChange={handleSetPermissions}
                style={{ width: '100%' }}
              >
                {permissions.map(p => <Option key={p.codename} value={p.codename}>{p.name}</Option>)}
              </Select>
            </Form.Item>
            <Form.Item label="状态">
              <Select value={editingUser.is_active ? 'active' : 'inactive'} onChange={v => setEditingUser((u: any) => ({ ...u, is_active: v === 'active' }))}>
                <Option value="active">启用</Option>
                <Option value="inactive">停用</Option>
              </Select>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 重置密码弹窗 */}
      <Modal
        open={resetPwdModal}
        title="重置密码"
        onCancel={() => setResetPwdModal(false)}
        onOk={handleResetPwd}
        okText="重置"
      >
        <Input.Password value={resetPwd} onChange={e => setResetPwd(e.target.value)} placeholder="请输入新密码" />
      </Modal>
    </Card>
  )
}

export default PermissionsPage 