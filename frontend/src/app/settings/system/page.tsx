'use client'

import React, { useState, useRef } from 'react'
import {
  Box,
  VStack,
  Heading,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Input,
  Button,
  Text,
  Divider,
  HStack,
  useToast,
  Image,
  Flex,
  useColorMode,
  FormHelperText,
  IconButton,
  Tooltip,
} from '@chakra-ui/react'
import { useTranslation } from '@/contexts/LanguageContext'
import { ArrowLeft, Upload, Trash, Check } from '@phosphor-icons/react'
import { useRouter } from 'next/navigation'

export default function SystemSettingsPage() {
  const { colorMode } = useColorMode()
  const { t } = useTranslation()
  const toast = useToast()
  const router = useRouter()
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 系统设置状态
  const [systemName, setSystemName] = useState('资产管理系统')
  const [companyName, setCompanyName] = useState('燃石科技')

  // Logo上传状态
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>('/logo.png') // 默认logo
  const [isUploading, setIsUploading] = useState(false)
  const [uploadSuccess, setUploadSuccess] = useState(false)

  // 处理Logo文件选择
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.match('image.*')) {
      toast({
        title: '文件类型错误',
        description: '请选择图片文件（JPG, PNG, SVG等）',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: '文件过大',
        description: '图片大小不能超过2MB',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    setLogoFile(file)

    // 创建预览URL
    const reader = new FileReader()
    reader.onloadend = () => {
      setLogoPreview(reader.result as string)
    }
    reader.readAsDataURL(file)
  }

  // 触发文件选择对话框
  const handleClickUpload = () => {
    fileInputRef.current?.click()
  }

  // 清除已选择的Logo
  const handleClearLogo = () => {
    setLogoFile(null)
    setLogoPreview('/logo.png') // 恢复默认logo
    setUploadSuccess(false)

    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 上传Logo
  const handleUploadLogo = async () => {
    if (!logoFile) return

    setIsUploading(true)

    // 模拟上传过程
    setTimeout(() => {
      setIsUploading(false)
      setUploadSuccess(true)

      toast({
        title: 'Logo上传成功',
        description: 'Logo已成功更新',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
    }, 1500)

    // 实际项目中，这里应该是一个API调用
    // const formData = new FormData()
    // formData.append('logo', logoFile)
    //
    // try {
    //   const response = await fetch('/api/settings/logo', {
    //     method: 'POST',
    //     body: formData,
    //   })
    //
    //   if (response.ok) {
    //     setUploadSuccess(true)
    //     toast({
    //       title: 'Logo上传成功',
    //       description: 'Logo已成功更新',
    //       status: 'success',
    //       duration: 3000,
    //       isClosable: true,
    //     })
    //   } else {
    //     throw new Error('上传失败')
    //   }
    // } catch (error) {
    //   toast({
    //     title: '上传失败',
    //     description: '请稍后重试',
    //     status: 'error',
    //     duration: 3000,
    //     isClosable: true,
    //   })
    // } finally {
    //   setIsUploading(false)
    // }
  }

  // 保存系统设置
  const handleSaveSettings = () => {
    // 模拟保存设置
    toast({
      title: '设置已保存',
      description: '系统设置已成功更新',
      status: 'success',
      duration: 3000,
      isClosable: true,
    })

    // 实际项目中，这里应该是一个API调用
    // const settings = {
    //   systemName,
    //   companyName,
    // }
    //
    // fetch('/api/settings/system', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify(settings),
    // })
  }

  return (
    <VStack spacing={8} align="stretch" w="100%" p={4}>
      <HStack>
        <Button
          leftIcon={<ArrowLeft weight="bold" />}
          variant="ghost"
          onClick={() => router.push('/settings')}
        >
          返回
        </Button>
        <Heading size="lg">LOGO 设置</Heading>
      </HStack>

      <Card>
        <CardBody>
          <VStack spacing={6} align="stretch">
            <Box>
              <Heading size="md" mb={4}>基本设置</Heading>
              <FormControl mb={4}>
                <FormLabel>系统名称</FormLabel>
                <Input
                  value={systemName}
                  onChange={(e) => setSystemName(e.target.value)}
                  maxW="400px"
                />
              </FormControl>

              <FormControl>
                <FormLabel>公司名称</FormLabel>
                <Input
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  maxW="400px"
                />
              </FormControl>
            </Box>

            <Divider />

            <Box>
              <Heading size="md" mb={4}>系统Logo</Heading>
              <FormControl>
                <FormLabel>上传Logo</FormLabel>
                <FormHelperText mb={3}>
                  推荐尺寸: 200x50 像素，支持PNG、JPG、SVG格式，大小不超过2MB
                </FormHelperText>

                <Flex direction="column" align="flex-start">
                  {/* Logo预览 */}
                  <Box
                    borderWidth="1px"
                    borderRadius="md"
                    borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
                    p={4}
                    mb={4}
                    bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                    minW="300px"
                    minH="100px"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    {logoPreview && (
                      <Image
                        src={logoPreview}
                        alt="Logo预览"
                        maxH="80px"
                        objectFit="contain"
                      />
                    )}
                  </Box>

                  {/* 上传按钮组 */}
                  <HStack spacing={2}>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      ref={fileInputRef}
                      display="none"
                    />

                    <Button
                      leftIcon={<Upload weight="bold" />}
                      onClick={handleClickUpload}
                      isDisabled={isUploading}
                    >
                      选择Logo
                    </Button>

                    {logoFile && (
                      <>
                        <Button
                          colorScheme="blue"
                          leftIcon={uploadSuccess ? <Check weight="bold" /> : undefined}
                          onClick={handleUploadLogo}
                          isLoading={isUploading}
                          isDisabled={uploadSuccess}
                        >
                          {uploadSuccess ? '已上传' : '上传Logo'}
                        </Button>

                        <Tooltip label="清除选择的Logo">
                          <IconButton
                            aria-label="清除Logo"
                            icon={<Trash weight="bold" />}
                            variant="outline"
                            colorScheme="red"
                            onClick={handleClearLogo}
                            isDisabled={isUploading}
                          />
                        </Tooltip>
                      </>
                    )}
                  </HStack>
                </Flex>
              </FormControl>
            </Box>

            <Divider />

            <Flex justify="flex-end">
              <Button colorScheme="blue" onClick={handleSaveSettings}>
                保存设置
              </Button>
            </Flex>
          </VStack>
        </CardBody>
      </Card>
    </VStack>
  )
}
