'use client'

import React from 'react'
import {
  Box,
  VStack,
  Heading,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Switch,
  Select,
  Text,
  Divider,
  Icon,
  HStack,
  useToast,
  Button,
} from '@chakra-ui/react'
import { useRouter } from 'next/navigation'
import { useTranslation, Language } from '@/contexts/LanguageContext'
import { useTheme } from '@/contexts/ThemeContext'
import { Sun, Moon, Gear } from '@phosphor-icons/react'

export default function SettingsPage() {
  const { isDarkMode, toggleTheme } = useTheme()
  const { t, setLanguage, currentLanguage } = useTranslation()
  const toast = useToast()
  const router = useRouter()

  const handleThemeChange = () => {
    toggleTheme()
    toast({
      title: isDarkMode ? t('light_mode_enabled') : t('dark_mode_enabled'),
      status: 'success',
      duration: 2000,
      isClosable: true,
      position: 'top',
    })
  }

  const navigateToSystemSettings = () => {
    router.push('/settings/system')
  }

  return (
    <VStack spacing={8} align="stretch" w="100%" p={4}>
      <Heading size="lg">{t('settings')}</Heading>

      <Card mb={6}>
        <CardBody>
          <VStack spacing={6} align="stretch">
            <Box>
              <Heading size="md" mb={4}>{t('language')}</Heading>
              <FormControl>
                <FormLabel>{t('select.language')}</FormLabel>
                <Select
                  value={currentLanguage}
                  onChange={(e) => setLanguage(e.target.value as Language)}
                  maxW="300px"
                  icon={<></>}
                  sx={{
                    '& > option': {
                      bg: isDarkMode ? 'gray.800' : 'white',
                      color: isDarkMode ? 'white' : 'gray.800',
                    },
                    '&::after': {
                      borderTopColor: isDarkMode ? 'gray.400' : 'gray.600',
                    }
                  }}
                >
                  <option value="zh-CN">中文</option>
                  <option value="en-US">English</option>
                </Select>
              </FormControl>
            </Box>

            <Divider />

            <Box>
              <Heading size="md" mb={4}>{t('theme')}</Heading>
              <FormControl>
                <HStack justify="space-between" align="center" spacing={4}>
                  <HStack spacing={3}>
                    <FormLabel htmlFor="dark-mode-switch" mb="0">
                      {t('dark.mode')}
                    </FormLabel>
                    <Icon
                      as={isDarkMode ? Moon : Sun}
                      color={isDarkMode ? 'yellow.200' : 'yellow.500'}
                      boxSize={5}
                      transition="all 0.3s ease-in-out"
                      transform={isDarkMode ? 'rotate(0deg)' : 'rotate(90deg)'}
                    />
                  </HStack>
                  <Switch
                    id="dark-mode-switch"
                    isChecked={isDarkMode}
                    onChange={handleThemeChange}
                    colorScheme="green"
                    sx={{
                      '& .chakra-switch__track': {
                        transition: 'all 0.3s ease-in-out',
                      },
                      '& .chakra-switch__thumb': {
                        transition: 'transform 0.3s ease-in-out',
                      }
                    }}
                  />
                </HStack>
                <Text mt={2} fontSize="sm" color="gray.500" transition="color 0.3s ease-in-out">
                  {isDarkMode ? t('dark.mode.enabled') : t('dark.mode.disabled')}
                </Text>
              </FormControl>
            </Box>
          </VStack>
        </CardBody>
      </Card>

      <Card>
        <CardBody>
          <VStack spacing={6} align="stretch">
            <Box>
              <Heading size="md" mb={4}>LOGO 设置</Heading>
              <HStack spacing={4} align="center">
                <Icon as={Gear} boxSize={5} color={isDarkMode ? 'blue.300' : 'blue.500'} />
                <Text>配置系统名称、Logo和其他系统级设置</Text>
                <Button
                  colorScheme="blue"
                  size="sm"
                  ml="auto"
                  onClick={navigateToSystemSettings}
                >
                  管理 LOGO 设置
                </Button>
              </HStack>
            </Box>
          </VStack>
        </CardBody>
      </Card>
    </VStack>
  )
}