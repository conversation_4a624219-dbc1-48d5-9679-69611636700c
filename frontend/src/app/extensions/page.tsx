'use client'

import React, { useState, useEffect } from 'react'
import {
  Container,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  useToast,
  Button,
  Input,
  Box,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  FormControl,
  FormLabel,
  Card,
  CardBody,
  Alert,
  AlertIcon,
} from '@chakra-ui/react'
import { FiEdit2, FiTrash2, FiSave, FiPlus } from 'react-icons/fi'
import { useTranslation } from '@/contexts/LanguageContext'

interface Extension {
  cabinetId: string
  extensionNumber: string
  socketNumber: string
  user: string
}

const ExtensionsPage = () => {
  const { t } = useTranslation()
  const toast = useToast()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [extensions, setExtensions] = useState<Extension[]>([])
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editForm, setEditForm] = useState<Extension>({
    cabinetId: '',
    extensionNumber: '',
    socketNumber: '',
    user: '',
  })

  // 从本地存储加载数据
  useEffect(() => {
    const savedExtensions = localStorage.getItem('phoneExtensions')
    if (savedExtensions) {
      setExtensions(JSON.parse(savedExtensions))
    }
  }, [])

  // 保存到本地存储
  const saveToLocalStorage = (data: Extension[]) => {
    localStorage.setItem('phoneExtensions', JSON.stringify(data))
  }

  // 处理编辑
  const handleEdit = (extension: Extension) => {
    setEditForm(extension)
    setEditingId(extension.cabinetId)
  }

  // 处理保存
  const handleSave = (extension: Extension) => {
    const newExtensions = extensions.map((ext) =>
      ext.cabinetId === extension.cabinetId ? extension : ext
    )
    setExtensions(newExtensions)
    saveToLocalStorage(newExtensions)
    setEditingId(null)
    toast({
      title: t('save.success'),
      status: 'success',
      duration: 2000,
    })
  }

  // 处理删除
  const handleDelete = (cabinetId: string) => {
    const newExtensions = extensions.filter((ext) => ext.cabinetId !== cabinetId)
    setExtensions(newExtensions)
    saveToLocalStorage(newExtensions)
    toast({
      title: t('delete.success'),
      status: 'success',
      duration: 2000,
    })
  }

  // 处理添加
  const handleAdd = () => {
    if (!editForm.cabinetId || !editForm.extensionNumber) {
      toast({
        title: t('required.fields'),
        status: 'error',
        duration: 2000,
      })
      return
    }

    const newExtensions = [...extensions, editForm]
    setExtensions(newExtensions)
    saveToLocalStorage(newExtensions)
    setEditForm({
      cabinetId: '',
      extensionNumber: '',
      socketNumber: '',
      user: '',
    })
    onClose()
    toast({
      title: t('add.success'),
      status: 'success',
      duration: 2000,
    })
  }

  return (
    <Container maxW="container.xl" p={4}>
      <Box mb={6} display="flex" justifyContent="space-between" alignItems="center">
        <Heading size="lg">{t('extensions.management')}</Heading>
        <Button 
          leftIcon={<FiPlus />} 
          colorScheme="blue" 
          onClick={onOpen}
          size="md"
          shadow="sm"
        >
          {t('add.extension')}
        </Button>
      </Box>

      <Card variant="outline" shadow="sm">
        <CardBody>
          {extensions.length === 0 ? (
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              暂无分机号数据，请点击右上角添加按钮新增
            </Alert>
          ) : (
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th width="20%">{t('cabinet.id')}</Th>
                  <Th width="20%">{t('extension.number')}</Th>
                  <Th width="20%">{t('socket.number')}</Th>
                  <Th width="25%">{t('user')}</Th>
                  <Th width="15%">{t('actions')}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {extensions.map((extension) => (
                  <Tr key={extension.cabinetId}>
                    <Td>{extension.cabinetId}</Td>
                    <Td>
                      {editingId === extension.cabinetId ? (
                        <Input
                          size="sm"
                          value={editForm.extensionNumber}
                          onChange={(e) =>
                            setEditForm({ ...editForm, extensionNumber: e.target.value })
                          }
                        />
                      ) : (
                        extension.extensionNumber
                      )}
                    </Td>
                    <Td>
                      {editingId === extension.cabinetId ? (
                        <Input
                          size="sm"
                          value={editForm.socketNumber}
                          onChange={(e) =>
                            setEditForm({ ...editForm, socketNumber: e.target.value })
                          }
                        />
                      ) : (
                        extension.socketNumber
                      )}
                    </Td>
                    <Td>
                      {editingId === extension.cabinetId ? (
                        <Input
                          size="sm"
                          value={editForm.user}
                          onChange={(e) => setEditForm({ ...editForm, user: e.target.value })}
                        />
                      ) : (
                        extension.user
                      )}
                    </Td>
                    <Td>
                      {editingId === extension.cabinetId ? (
                        <IconButton
                          aria-label="Save"
                          icon={<FiSave />}
                          colorScheme="green"
                          size="sm"
                          mr={2}
                          onClick={() => handleSave(editForm)}
                        />
                      ) : (
                        <IconButton
                          aria-label="Edit"
                          icon={<FiEdit2 />}
                          colorScheme="blue"
                          size="sm"
                          mr={2}
                          onClick={() => handleEdit(extension)}
                        />
                      )}
                      <IconButton
                        aria-label="Delete"
                        icon={<FiTrash2 />}
                        colorScheme="red"
                        size="sm"
                        onClick={() => handleDelete(extension.cabinetId)}
                      />
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* 添加分机号码弹窗 */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t('add.extension')}</ModalHeader>
          <ModalBody>
            <FormControl mb={4}>
              <FormLabel>{t('cabinet.id')}</FormLabel>
              <Input
                placeholder="请输入机柜编号"
                value={editForm.cabinetId}
                onChange={(e) => setEditForm({ ...editForm, cabinetId: e.target.value })}
              />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>{t('extension.number')}</FormLabel>
              <Input
                placeholder="请输入分机号"
                value={editForm.extensionNumber}
                onChange={(e) =>
                  setEditForm({ ...editForm, extensionNumber: e.target.value })
                }
              />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>{t('socket.number')}</FormLabel>
              <Input
                placeholder="请输入插座号"
                value={editForm.socketNumber}
                onChange={(e) => setEditForm({ ...editForm, socketNumber: e.target.value })}
              />
            </FormControl>
            <FormControl>
              <FormLabel>{t('user')}</FormLabel>
              <Input
                placeholder="请输入使用人"
                value={editForm.user}
                onChange={(e) => setEditForm({ ...editForm, user: e.target.value })}
              />
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={handleAdd}>
              {t('add')}
            </Button>
            <Button variant="ghost" onClick={onClose}>{t('cancel')}</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  )
}

export default ExtensionsPage 