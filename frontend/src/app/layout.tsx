import type { Metadata } from 'next';
import RootLayoutClient from '../components/RootLayoutClient';
import '../globals.css';

export const metadata: Metadata = {
  title: '燃石医学 | Burning Rock',
  description: '燃石医学网络管理平台',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-icon.png'
  }
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <body>
        <RootLayoutClient>
          {children}
        </RootLayoutClient>
      </body>
    </html>
  );
}