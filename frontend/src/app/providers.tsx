'use client'

import { useState } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from 'next-themes'
import { ChakraProvider } from '@chakra-ui/react'
import { LanguageProvider } from '../contexts/LanguageContext'
import { chakraTheme } from '../theme/index'

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient())

  return (
    <ChakraProvider theme={chakraTheme}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <LanguageProvider>
          <QueryClientProvider client={queryClient}>
            {children}
          </QueryClientProvider>
        </LanguageProvider>
      </ThemeProvider>
    </ChakraProvider>
  )
}