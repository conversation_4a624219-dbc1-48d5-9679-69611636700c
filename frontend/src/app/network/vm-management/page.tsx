'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  HStack,
  VStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Tooltip,
  Divider,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Progress,
  Tag,
  TagLabel,
  TagLeftIcon,
  Switch,
} from '@chakra-ui/react'
import CustomSelect from '@/components/CustomSelect'
import {
  MagnifyingGlass,
  Plus,
  Play,
  Stop,
  Trash,
  Gear,
  ArrowsClockwise,
  Desktop,
  CloudArrowUp,
  Database,
  HardDrives,
  Cpu,
  Lightning,
  Warning,
} from '@phosphor-icons/react'

// 虚拟机类型定义
interface VirtualMachine {
  id: string
  name: string
  status: 'running' | 'stopped' | 'error' | 'suspended'
  type: 'proxmox' | 'esxi'
  host: string
  os: string
  cpu: number
  memory: number
  disk: number
  ip: string
  createdAt: string
  lastBackup?: string
}

// 虚拟机主机类型定义
interface VMHost {
  id: string
  name: string
  type: 'proxmox' | 'esxi'
  address: string
  status: 'online' | 'offline' | 'warning'
  version: string
  totalCpu: number
  usedCpu: number
  totalMemory: number
  usedMemory: number
  totalStorage: number
  usedStorage: number
  vms: number
}

// 模拟数据 - 虚拟机主机
const mockHosts: VMHost[] = [
  {
    id: '1',
    name: 'Proxmox-Node1',
    type: 'proxmox',
    address: '**********',
    status: 'online',
    version: '8.0.4',
    totalCpu: 32,
    usedCpu: 18,
    totalMemory: 128,
    usedMemory: 64,
    totalStorage: 2048,
    usedStorage: 1024,
    vms: 12
  },
  {
    id: '2',
    name: 'ESXi-Host1',
    type: 'esxi',
    address: '**********',
    status: 'online',
    version: '7.0.3',
    totalCpu: 64,
    usedCpu: 32,
    totalMemory: 256,
    usedMemory: 128,
    totalStorage: 4096,
    usedStorage: 2048,
    vms: 24
  }
];

// 模拟数据 - 虚拟机
const mockVMs: VirtualMachine[] = [
  {
    id: '101',
    name: 'web-server-1',
    status: 'running',
    type: 'proxmox',
    host: 'Proxmox-Node1',
    os: 'Ubuntu 22.04 LTS',
    cpu: 4,
    memory: 8,
    disk: 100,
    ip: '*************',
    createdAt: '2023-01-15',
    lastBackup: '2023-05-10'
  },
  {
    id: '102',
    name: 'db-server-1',
    status: 'running',
    type: 'proxmox',
    host: 'Proxmox-Node1',
    os: 'CentOS 8',
    cpu: 8,
    memory: 16,
    disk: 500,
    ip: '*************',
    createdAt: '2023-02-20',
    lastBackup: '2023-05-12'
  },
  {
    id: '201',
    name: 'app-server-1',
    status: 'stopped',
    type: 'esxi',
    host: 'ESXi-Host1',
    os: 'Windows Server 2022',
    cpu: 8,
    memory: 32,
    disk: 500,
    ip: '*************',
    createdAt: '2023-03-05'
  }
];

export default function VMManagementPage() {
  const [hosts, setHosts] = useState<VMHost[]>(mockHosts);
  const [vms, setVMs] = useState<VirtualMachine[]>(mockVMs);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredVMs, setFilteredVMs] = useState<VirtualMachine[]>(mockVMs);
  const [selectedHost, setSelectedHost] = useState<string>('all');
  const [selectedVM, setSelectedVM] = useState<VirtualMachine | null>(null);

  const { isOpen: isAddHostOpen, onOpen: onAddHostOpen, onClose: onAddHostClose } = useDisclosure();
  const { isOpen: isAddVMOpen, onOpen: onAddVMOpen, onClose: onAddVMClose } = useDisclosure();
  const { isOpen: isVMDetailsOpen, onOpen: onVMDetailsOpen, onClose: onVMDetailsClose } = useDisclosure();

  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const statBg = useColorModeValue('gray.50', 'gray.700');

  // 过滤虚拟机
  useEffect(() => {
    let filtered = vms;

    // 按主机过滤
    if (selectedHost !== 'all') {
      filtered = filtered.filter(vm => vm.host === selectedHost);
    }

    // 按搜索词过滤
    if (searchQuery) {
      filtered = filtered.filter(vm =>
        vm.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vm.ip.includes(searchQuery) ||
        vm.os.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredVMs(filtered);
  }, [searchQuery, selectedHost, vms]);

  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
      case 'online':
        return 'green';
      case 'stopped':
      case 'offline':
        return 'gray';
      case 'suspended':
      case 'warning':
        return 'yellow';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'running':
        return '运行中';
      case 'stopped':
        return '已停止';
      case 'suspended':
        return '已挂起';
      case 'error':
        return '错误';
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'warning':
        return '警告';
      default:
        return status;
    }
  };

  // 处理虚拟机操作
  const handleVMAction = (action: string, vm: VirtualMachine) => {
    let message = '';
    let newStatus = vm.status;

    switch (action) {
      case 'start':
        message = `启动虚拟机 ${vm.name}`;
        newStatus = 'running';
        break;
      case 'stop':
        message = `停止虚拟机 ${vm.name}`;
        newStatus = 'stopped';
        break;
      case 'restart':
        message = `重启虚拟机 ${vm.name}`;
        newStatus = 'running';
        break;
      case 'delete':
        message = `删除虚拟机 ${vm.name}`;
        // 从列表中移除
        setVMs(vms.filter(v => v.id !== vm.id));
        toast({
          title: "操作成功",
          description: message,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        return;
    }

    // 更新状态
    setVMs(vms.map(v => v.id === vm.id ? {...v, status: newStatus as any} : v));

    toast({
      title: "操作成功",
      description: message,
      status: "success",
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Box p={6}>
      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>虚拟化平台管理</Heading>
            <Text color="gray.500" fontSize="md">管理 Proxmox VE 和 VMware ESXi 虚拟化平台及其虚拟机</Text>
          </Box>
          <HStack spacing={4} mt={{ base: 4, md: 0 }}>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="teal"
              onClick={onAddVMOpen}
            >
              创建虚拟机
            </Button>
            <Button
              leftIcon={<HardDrives weight="bold" />}
              variant="outline"
              colorScheme="blue"
              onClick={onAddHostOpen}
            >
              添加虚拟化平台
            </Button>
          </HStack>
        </Flex>
      </Box>

      <Tabs variant="enclosed" colorScheme="teal">
        <TabList mb={4} borderBottom="2px solid" borderColor="teal.500">
          <Tab
            fontSize="md"
            fontWeight="bold"
            mx={2}
            _selected={{
              color: "teal.500",
              bg: useColorModeValue("teal.50", "teal.900"),
              borderColor: "teal.500",
              borderBottomColor: "transparent",
              mb: "-2px"
            }}
          >
            <Box as="span" display="flex" alignItems="center">
              <Desktop size={18} weight="fill" style={{ marginRight: '8px' }} />
              虚拟机
            </Box>
          </Tab>
          <Tab
            fontSize="md"
            fontWeight="bold"
            mx={2}
            _selected={{
              color: "blue.500",
              bg: useColorModeValue("blue.50", "blue.900"),
              borderColor: "blue.500",
              borderBottomColor: "transparent",
              mb: "-2px"
            }}
          >
            <Box as="span" display="flex" alignItems="center">
              <HardDrives size={18} weight="fill" style={{ marginRight: '8px' }} />
              虚拟化平台
            </Box>
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0}>
            <Box mb={6}>
              <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
                <InputGroup maxW={{ base: '100%', md: '300px' }}>
                  <InputLeftElement pointerEvents="none">
                    <Box color="gray.500">
                      <MagnifyingGlass size={20} />
                    </Box>
                  </InputLeftElement>
                  <Input
                    placeholder="搜索虚拟机..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    pl="40px"
                  />
                </InputGroup>
                <CustomSelect
                  maxW={{ base: '100%', md: '200px' }}
                  value={selectedHost}
                  onChange={(e) => setSelectedHost(e.target.value)}
                >
                  <option value="all" style={{ padding: '10px' }}>所有主机</option>
                  {hosts.map(host => (
                    <option key={host.id} value={host.name} style={{ padding: '10px' }}>{host.name}</option>
                  ))}
                </CustomSelect>
              </Flex>
            </Box>

            <Box mb={4}>
              <Heading size="md" mb={3}>虚拟机列表</Heading>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} mb={6}>
                <Stat
                  bg={useColorModeValue('green.50', 'green.900')}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={useColorModeValue('green.100', 'green.700')}
                >
                  <StatLabel fontWeight="medium" color={useColorModeValue('green.600', 'green.200')}>运行中</StatLabel>
                  <StatNumber fontSize="3xl">{vms.filter(vm => vm.status === 'running').length}</StatNumber>
                  <StatHelpText>
                    <Text fontSize="xs" color={useColorModeValue('green.600', 'green.200')}>
                      {Math.round(vms.filter(vm => vm.status === 'running').length / vms.length * 100)}% 的虚拟机
                    </Text>
                  </StatHelpText>
                </Stat>

                <Stat
                  bg={useColorModeValue('gray.50', 'gray.800')}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={useColorModeValue('gray.200', 'gray.700')}
                >
                  <StatLabel fontWeight="medium" color={useColorModeValue('gray.600', 'gray.300')}>已停止</StatLabel>
                  <StatNumber fontSize="3xl">{vms.filter(vm => vm.status === 'stopped').length}</StatNumber>
                  <StatHelpText>
                    <Text fontSize="xs" color={useColorModeValue('gray.600', 'gray.300')}>
                      {Math.round(vms.filter(vm => vm.status === 'stopped').length / vms.length * 100)}% 的虚拟机
                    </Text>
                  </StatHelpText>
                </Stat>

                <Stat
                  bg={useColorModeValue('red.50', 'red.900')}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={useColorModeValue('red.100', 'red.700')}
                >
                  <StatLabel fontWeight="medium" color={useColorModeValue('red.600', 'red.200')}>异常状态</StatLabel>
                  <StatNumber fontSize="3xl">{vms.filter(vm => vm.status === 'error' || vm.status === 'suspended').length}</StatNumber>
                  <StatHelpText>
                    <Text fontSize="xs" color={useColorModeValue('red.600', 'red.200')}>
                      {Math.round(vms.filter(vm => vm.status === 'error' || vm.status === 'suspended').length / vms.length * 100)}% 的虚拟机
                    </Text>
                  </StatHelpText>
                </Stat>
              </SimpleGrid>

              <Box
                borderWidth="1px"
                borderRadius="xl"
                overflow="hidden"
                bg={bgColor}
                borderColor={borderColor}
                boxShadow="md"
              >
                <Table variant="simple">
                  <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                    <Tr>
                      <Th py={4} width="20%">名称</Th>
                      <Th py={4} width="12%">状态</Th>
                      <Th py={4} width="12%">类型</Th>
                      <Th py={4} width="12%">主机</Th>
                      <Th py={4} width="12%">IP地址</Th>
                      <Th py={4} width="12%">资源</Th>
                      <Th py={4} width="20%">操作</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredVMs.length > 0 ? (
                      filteredVMs.map(vm => (
                        <Tr
                          key={vm.id}
                          _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                          cursor="pointer"
                          onClick={() => {
                            setSelectedVM(vm);
                            onVMDetailsOpen();
                          }}
                        >
                          <Td>
                            <Flex align="center">
                              <Box
                                bg={useColorModeValue(
                                  vm.type === 'proxmox' ? 'orange.50' : 'blue.50',
                                  vm.type === 'proxmox' ? 'orange.900' : 'blue.900'
                                )}
                                p={1.5}
                                borderRadius="md"
                                mr={3}
                              >
                                <Desktop
                                  size={18}
                                  weight="fill"
                                  color={useColorModeValue(
                                    vm.type === 'proxmox' ? 'orange.500' : 'blue.500',
                                    vm.type === 'proxmox' ? 'orange.300' : 'blue.300'
                                  )}
                                />
                              </Box>
                              <Text fontWeight="medium">{vm.name}</Text>
                            </Flex>
                          </Td>
                          <Td>
                            <Badge
                              colorScheme={getStatusColor(vm.status)}
                              px={2}
                              py={1}
                              borderRadius="full"
                              variant="subtle"
                            >
                              {getStatusText(vm.status)}
                            </Badge>
                          </Td>
                          <Td>{vm.type === 'proxmox' ? 'Proxmox VE' : 'VMware ESXi'}</Td>
                          <Td>{vm.host}</Td>
                          <Td>
                            <Text fontFamily="mono">{vm.ip}</Text>
                          </Td>
                          <Td>
                            <HStack spacing={2}>
                              <Tag size="sm" variant="subtle" colorScheme="blue" borderRadius="full">
                                <TagLeftIcon boxSize="12px" as={Cpu} />
                                <TagLabel>{vm.cpu} vCPU</TagLabel>
                              </Tag>
                              <Tag size="sm" variant="subtle" colorScheme="green" borderRadius="full">
                                <TagLabel>{vm.memory} GB</TagLabel>
                              </Tag>
                            </HStack>
                          </Td>
                          <Td onClick={(e) => e.stopPropagation()}>
                            <HStack spacing={1}>
                              {vm.status === 'stopped' ? (
                                <Tooltip label="启动">
                                  <IconButton
                                    icon={<Play weight="bold" />}
                                    aria-label="启动"
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="green"
                                    onClick={() => handleVMAction('start', vm)}
                                  />
                                </Tooltip>
                              ) : (
                                <Tooltip label="停止">
                                  <IconButton
                                    icon={<Stop weight="bold" />}
                                    aria-label="停止"
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="red"
                                    onClick={() => handleVMAction('stop', vm)}
                                  />
                                </Tooltip>
                              )}
                              <Tooltip label="重启">
                                <IconButton
                                  icon={<ArrowsClockwise weight="bold" />}
                                  aria-label="重启"
                                  size="sm"
                                  variant="ghost"
                                  colorScheme="orange"
                                  onClick={() => handleVMAction('restart', vm)}
                                />
                              </Tooltip>
                              <Tooltip label="删除">
                                <IconButton
                                  icon={<Trash weight="bold" />}
                                  aria-label="删除"
                                  size="sm"
                                  variant="ghost"
                                  colorScheme="red"
                                  onClick={() => handleVMAction('delete', vm)}
                                />
                              </Tooltip>
                            </HStack>
                          </Td>
                        </Tr>
                      ))
                    ) : (
                      <Tr>
                        <Td colSpan={7} textAlign="center" py={8}>
                          <VStack spacing={3}>
                            <Box
                              p={3}
                              borderRadius="full"
                              bg={useColorModeValue('gray.100', 'gray.700')}
                            >
                              <MagnifyingGlass size={24} weight="duotone" />
                            </Box>
                            <Text>没有找到匹配的虚拟机</Text>
                            <Text fontSize="sm" color="gray.500">
                              尝试使用不同的搜索条件或清除筛选器
                            </Text>
                          </VStack>
                        </Td>
                      </Tr>
                    )}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>

          <TabPanel px={0}>
            <Box mb={4}>
              <Heading size="md" mb={3}>虚拟化平台状态概览</Heading>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                <Stat
                  bg={useColorModeValue('teal.50', 'teal.900')}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={useColorModeValue('teal.100', 'teal.700')}
                >
                  <StatLabel fontWeight="medium" color={useColorModeValue('teal.600', 'teal.200')}>总主机数</StatLabel>
                  <StatNumber fontSize="3xl">{hosts.length}</StatNumber>
                  <StatHelpText>
                    <HStack>
                      <Box w="10px" h="10px" borderRadius="full" bg="green.400" />
                      <Text fontSize="xs">{hosts.filter(h => h.status === 'online').length} 在线</Text>
                    </HStack>
                  </StatHelpText>
                </Stat>

                <Stat
                  bg={useColorModeValue('blue.50', 'blue.900')}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={useColorModeValue('blue.100', 'blue.700')}
                >
                  <StatLabel fontWeight="medium" color={useColorModeValue('blue.600', 'blue.200')}>总虚拟机数</StatLabel>
                  <StatNumber fontSize="3xl">{hosts.reduce((sum, host) => sum + host.vms, 0)}</StatNumber>
                  <StatHelpText>
                    <HStack>
                      <Box w="10px" h="10px" borderRadius="full" bg="green.400" />
                      <Text fontSize="xs">{vms.filter(vm => vm.status === 'running').length} 运行中</Text>
                    </HStack>
                  </StatHelpText>
                </Stat>

                <Stat
                  bg={useColorModeValue('purple.50', 'purple.900')}
                  p={4}
                  borderRadius="lg"
                  boxShadow="sm"
                  borderWidth="1px"
                  borderColor={useColorModeValue('purple.100', 'purple.700')}
                >
                  <StatLabel fontWeight="medium" color={useColorModeValue('purple.600', 'purple.200')}>总资源使用</StatLabel>
                  <Flex align="center" justify="space-between">
                    <Box>
                      <Text fontSize="sm" fontWeight="medium">CPU</Text>
                      <Text fontSize="lg" fontWeight="bold">
                        {Math.round(hosts.reduce((sum, host) => sum + host.usedCpu, 0) / hosts.reduce((sum, host) => sum + host.totalCpu, 0) * 100)}%
                      </Text>
                    </Box>
                    <Box>
                      <Text fontSize="sm" fontWeight="medium">内存</Text>
                      <Text fontSize="lg" fontWeight="bold">
                        {Math.round(hosts.reduce((sum, host) => sum + host.usedMemory, 0) / hosts.reduce((sum, host) => sum + host.totalMemory, 0) * 100)}%
                      </Text>
                    </Box>
                  </Flex>
                </Stat>
              </SimpleGrid>
            </Box>

            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
              {hosts.map(host => (
                <Card
                  key={host.id}
                  borderColor={borderColor}
                  boxShadow="md"
                  borderRadius="xl"
                  _hover={{
                    transform: 'translateY(-2px)',
                    boxShadow: 'lg',
                    borderColor: host.status === 'online' ? 'green.300' : host.status === 'warning' ? 'yellow.300' : 'red.300'
                  }}
                  transition="all 0.2s"
                  overflow="hidden"
                >
                  <Box
                    h="4px"
                    bg={host.status === 'online' ? 'green.400' : host.status === 'warning' ? 'yellow.400' : 'red.400'}
                    w="100%"
                  />
                  <CardBody p={5}>
                    <Flex justify="space-between" align="flex-start" mb={4}>
                      <Box>
                        <Flex align="center" mb={2}>
                          <Box
                            bg={useColorModeValue(
                              host.type === 'proxmox' ? 'orange.50' : 'blue.50',
                              host.type === 'proxmox' ? 'orange.900' : 'blue.900'
                            )}
                            p={2}
                            borderRadius="md"
                            mr={3}
                          >
                            <HardDrives
                              size={24}
                              weight="fill"
                              color={useColorModeValue(
                                host.type === 'proxmox' ? 'orange.500' : 'blue.500',
                                host.type === 'proxmox' ? 'orange.300' : 'blue.300'
                              )}
                            />
                          </Box>
                          <Box>
                            <Heading size="md">{host.name}</Heading>
                            <Flex align="center" mt={1}>
                              <Badge
                                colorScheme={getStatusColor(host.status)}
                                variant="subtle"
                                px={2}
                                py={0.5}
                                borderRadius="full"
                              >
                                {getStatusText(host.status)}
                              </Badge>
                              <Text fontSize="sm" color="gray.500" ml={2}>
                                {host.type === 'proxmox' ? 'Proxmox VE' : 'VMware ESXi'} {host.version}
                              </Text>
                            </Flex>
                          </Box>
                        </Flex>
                        <Text fontSize="sm" color="gray.500" mt={1}>{host.address}</Text>
                      </Box>
                      <HStack>
                        <Tooltip label="编辑">
                          <IconButton
                            icon={<Gear weight="bold" />}
                            aria-label="编辑"
                            size="sm"
                            variant="ghost"
                            colorScheme="blue"
                          />
                        </Tooltip>
                        <Tooltip label="刷新">
                          <IconButton
                            icon={<ArrowsClockwise weight="bold" />}
                            aria-label="刷新"
                            size="sm"
                            variant="ghost"
                            colorScheme="green"
                          />
                        </Tooltip>
                      </HStack>
                    </Flex>

                    <SimpleGrid columns={2} spacing={4} mb={4}>
                      <Stat
                        bg={statBg}
                        p={3}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor={borderColor}
                      >
                        <StatLabel fontSize="xs" fontWeight="medium">虚拟机</StatLabel>
                        <Flex align="baseline">
                          <StatNumber fontSize="2xl">{host.vms}</StatNumber>
                          <Text fontSize="sm" color="gray.500" ml={1}>台</Text>
                        </Flex>
                      </Stat>
                      <Stat
                        bg={statBg}
                        p={3}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor={borderColor}
                      >
                        <StatLabel fontSize="xs" fontWeight="medium">CPU 使用率</StatLabel>
                        <Flex align="baseline">
                          <StatNumber fontSize="2xl">{Math.round((host.usedCpu / host.totalCpu) * 100)}</StatNumber>
                          <Text fontSize="sm" color="gray.500" ml={1}>%</Text>
                        </Flex>
                        <StatHelpText fontSize="xs" mb={0}>{host.usedCpu} / {host.totalCpu} 核</StatHelpText>
                      </Stat>
                    </SimpleGrid>

                    <VStack spacing={4} align="stretch">
                      <Box>
                        <Flex justify="space-between" mb={1}>
                          <Text fontSize="sm" fontWeight="medium">内存</Text>
                          <Text fontSize="sm">{host.usedMemory} / {host.totalMemory} GB</Text>
                        </Flex>
                        <Progress
                          value={(host.usedMemory / host.totalMemory) * 100}
                          colorScheme={(host.usedMemory / host.totalMemory) > 0.8 ? "red" : "blue"}
                          size="sm"
                          borderRadius="full"
                          hasStripe
                          isAnimated={host.status === 'online'}
                        />
                      </Box>
                      <Box>
                        <Flex justify="space-between" mb={1}>
                          <Text fontSize="sm" fontWeight="medium">存储</Text>
                          <Text fontSize="sm">{host.usedStorage} / {host.totalStorage} GB</Text>
                        </Flex>
                        <Progress
                          value={(host.usedStorage / host.totalStorage) * 100}
                          colorScheme={(host.usedStorage / host.totalStorage) > 0.8 ? "red" : "green"}
                          size="sm"
                          borderRadius="full"
                          hasStripe
                          isAnimated={host.status === 'online'}
                        />
                      </Box>
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* 添加虚拟化平台模态框 */}
      <Modal
        isOpen={isAddHostOpen}
        onClose={onAddHostClose}
        isCentered
        size="md"
        motionPreset="slideInBottom"
        scrollBehavior="inside"
        blockScrollOnMount={false}
      >
        <ModalOverlay backdropFilter="blur(5px)" bg="blackAlpha.700" />
        <ModalContent
          boxShadow="xl"
          borderRadius="xl"
          maxH="calc(100vh - 120px)"
          maxW="480px"
          w="95%"
          mx="auto"
          my="auto"
          mt="60px"
        >
          <ModalHeader bg={useColorModeValue('gray.50', 'gray.800')} borderTopRadius="xl" py={3}>
            <Flex align="center">
              <Box
                bg={useColorModeValue('blue.50', 'blue.900')}
                p={1.5}
                borderRadius="md"
                mr={2}
              >
                <HardDrives size={18} weight="fill" color={useColorModeValue('blue.500', 'blue.300')} />
              </Box>
              <Text fontSize="md" fontWeight="bold">添加虚拟化平台</Text>
            </Flex>
          </ModalHeader>
          <ModalCloseButton top={3} />
          <ModalBody py={3}>
            <Box mb={4} bg={useColorModeValue('blue.50', 'blue.900')} p={3} borderRadius="md">
              <Flex align="center" mb={1}>
                <Box color={useColorModeValue('blue.500', 'blue.200')} mr={2}>
                  <Lightning size={16} weight="fill" />
                </Box>
                <Text fontWeight="medium" fontSize="sm" color={useColorModeValue('blue.600', 'blue.200')}>连接说明</Text>
              </Flex>
              <Text fontSize="xs" color={useColorModeValue('blue.600', 'blue.200')}>
                添加虚拟化平台需要管理员权限和API访问凭据。请确保您有权限访问该平台，并且平台API服务已启用。
              </Text>
            </Box>

            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel fontWeight="medium" fontSize="sm" mb={1}>平台类型</FormLabel>
                <CustomSelect
                  placeholder="选择虚拟化平台类型"
                  size="sm"
                >
                  <option value="proxmox" style={{ padding: '6px' }}>Proxmox VE</option>
                  <option value="esxi" style={{ padding: '6px' }}>VMware ESXi</option>
                </CustomSelect>
                <Text fontSize="xs" color="gray.500" mt={1}>选择要添加的虚拟化平台类型</Text>
              </FormControl>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontWeight="medium" fontSize="sm" mb={1}>主机名称</FormLabel>
                  <Input placeholder="输入主机名称" size="sm" />
                  <Text fontSize="xs" color="gray.500" mt={1}>为此平台设置一个易于识别的名称</Text>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel fontWeight="medium" fontSize="sm" mb={1}>IP地址/主机名</FormLabel>
                  <Input placeholder="例如: *************" size="sm" />
                  <Text fontSize="xs" color="gray.500" mt={1}>输入平台的IP地址或主机名</Text>
                </FormControl>
              </SimpleGrid>

              <Divider my={1} />
              <Heading size="xs" mb={1} fontSize="sm">连接设置</Heading>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontWeight="medium" fontSize="sm" mb={1}>API 端口</FormLabel>
                  <Input placeholder="例如: 8006" defaultValue="8006" size="sm" />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    {`Proxmox VE 默认为 8006，ESXi 默认为 443`}
                  </Text>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel fontWeight="medium" fontSize="sm" mb={1}>连接协议</FormLabel>
                  <CustomSelect defaultValue="https" size="sm">
                    <option value="https" style={{ padding: '6px' }}>HTTPS</option>
                    <option value="http" style={{ padding: '6px' }}>HTTP (不安全)</option>
                  </CustomSelect>
                  <Text fontSize="xs" color="gray.500" mt={1}>建议使用HTTPS以确保安全连接</Text>
                </FormControl>
              </SimpleGrid>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <FormControl isRequired>
                  <FormLabel fontWeight="medium" fontSize="sm" mb={1}>用户名</FormLabel>
                  <Input placeholder="例如: root@pam" defaultValue="root@pam" size="sm" />
                  <Text fontSize="xs" color="gray.500" mt={1}>
                    {`Proxmox VE 格式为 user@realm，ESXi 为用户名`}
                  </Text>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel fontWeight="medium" fontSize="sm" mb={1}>API Token/密码</FormLabel>
                  <Input placeholder="输入API Token或密码" type="password" size="sm" />
                  <Text fontSize="xs" color="gray.500" mt={1}>输入API Token或管理员密码</Text>
                </FormControl>
              </SimpleGrid>

              <FormControl>
                <Flex align="center">
                  <Box as="div" mr={2}>
                    <input
                      type="checkbox"
                      id="verify-ssl"
                      defaultChecked
                      style={{
                        width: '1rem',
                        height: '1rem',
                        accentColor: 'var(--chakra-colors-blue-500)'
                      }}
                    />
                  </Box>
                  <FormLabel htmlFor="verify-ssl" mb="0" fontSize="sm">验证SSL证书</FormLabel>
                </Flex>
                <Text fontSize="xs" color="gray.500" mt={1}>如果使用自签名证书，可以关闭此选项</Text>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter bg={useColorModeValue('gray.50', 'gray.800')} borderBottomRadius="xl" py={3}>
            <Button colorScheme="blue" mr={3} size="sm" leftIcon={<Plus size={16} weight="bold" />}>
              添加平台
            </Button>
            <Button variant="outline" onClick={onAddHostClose} size="sm">取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 创建虚拟机模态框 */}
      <Modal
        isOpen={isAddVMOpen}
        onClose={onAddVMClose}
        isCentered
        size="md"
        motionPreset="slideInBottom"
        scrollBehavior="inside"
        blockScrollOnMount={false}
      >
        <ModalOverlay backdropFilter="blur(5px)" bg="blackAlpha.700" />
        <ModalContent
          boxShadow="xl"
          borderRadius="xl"
          maxH="calc(100vh - 120px)"
          maxW="480px"
          w="95%"
          mx="auto"
          my="auto"
          mt="60px"
        >
          <ModalHeader bg={useColorModeValue('gray.50', 'gray.800')} borderTopRadius="xl" py={3}>
            <Flex align="center">
              <Box
                bg={useColorModeValue('teal.50', 'teal.900')}
                p={1.5}
                borderRadius="md"
                mr={2}
              >
                <Desktop size={18} weight="fill" color={useColorModeValue('teal.500', 'teal.300')} />
              </Box>
              <Text fontSize="md" fontWeight="bold">创建新虚拟机</Text>
            </Flex>
          </ModalHeader>
          <ModalCloseButton top={3} />

          <Tabs isFitted variant="enclosed" colorScheme="teal" mx={3} mt={2} size="sm">
            <TabList>
              <Tab fontWeight="medium" fontSize="sm" py={2}>基本信息</Tab>
              <Tab fontWeight="medium" fontSize="sm" py={2}>资源配置</Tab>
              <Tab fontWeight="medium" fontSize="sm" py={2}>网络设置</Tab>
            </TabList>

            <TabPanels>
              {/* 基本信息面板 */}
              <TabPanel px={0} pt={3}>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel fontWeight="medium" fontSize="sm" mb={1}>虚拟化平台</FormLabel>
                    <CustomSelect placeholder="选择虚拟化平台" size="sm">
                      <option value="proxmox" style={{ padding: '6px' }}>Proxmox VE</option>
                      <option value="esxi" style={{ padding: '6px' }}>VMware ESXi</option>
                    </CustomSelect>
                    <Text fontSize="xs" color="gray.500" mt={1}>选择要使用的虚拟化平台类型</Text>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel fontWeight="medium" fontSize="sm" mb={1}>主机</FormLabel>
                    <CustomSelect placeholder="选择主机" size="sm">
                      {hosts.map(host => (
                        <option key={host.id} value={host.id} style={{ padding: '6px' }}>
                          {host.name} ({host.type === 'proxmox' ? 'Proxmox VE' : 'VMware ESXi'})
                        </option>
                      ))}
                    </CustomSelect>
                    <Text fontSize="xs" color="gray.500" mt={1}>选择要部署虚拟机的物理主机</Text>
                  </FormControl>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    <FormControl isRequired>
                      <FormLabel fontWeight="medium" fontSize="sm" mb={1}>虚拟机名称</FormLabel>
                      <Input placeholder="输入虚拟机名称" size="sm" />
                      <Text fontSize="xs" color="gray.500" mt={1}>使用有意义的名称</Text>
                    </FormControl>

                    <FormControl>
                      <FormLabel fontWeight="medium" fontSize="sm" mb={1}>描述</FormLabel>
                      <Input placeholder="可选描述" size="sm" />
                      <Text fontSize="xs" color="gray.500" mt={1}>添加描述以便于管理（可选）</Text>
                    </FormControl>
                  </SimpleGrid>

                  <FormControl isRequired>
                    <FormLabel fontWeight="medium" fontSize="sm" mb={1}>操作系统</FormLabel>
                    <CustomSelect placeholder="选择操作系统" size="sm">
                      <option value="ubuntu" style={{ padding: '6px' }}>Ubuntu 22.04 LTS</option>
                      <option value="centos" style={{ padding: '6px' }}>CentOS 8</option>
                      <option value="windows" style={{ padding: '6px' }}>Windows Server 2022</option>
                      <option value="debian" style={{ padding: '6px' }}>Debian 11</option>
                    </CustomSelect>
                    <Text fontSize="xs" color="gray.500" mt={1}>选择虚拟机的操作系统类型</Text>
                  </FormControl>
                </VStack>
              </TabPanel>

              {/* 资源配置面板 */}
              <TabPanel px={0} pt={3}>
                <VStack spacing={4} align="stretch">
                  <Box>
                    <FormControl isRequired>
                      <FormLabel fontWeight="medium" fontSize="sm" mb={1}>CPU (核心)</FormLabel>
                      <Flex align="center">
                        <CustomSelect defaultValue="2" size="sm" maxW="150px">
                          <option value="1" style={{ padding: '6px' }}>1 核</option>
                          <option value="2" style={{ padding: '6px' }}>2 核</option>
                          <option value="4" style={{ padding: '6px' }}>4 核</option>
                          <option value="8" style={{ padding: '6px' }}>8 核</option>
                          <option value="16" style={{ padding: '6px' }}>16 核</option>
                        </CustomSelect>
                        <Box ml={4} flex="1">
                          <Progress value={25} size="sm" colorScheme="blue" borderRadius="full" />
                          <Flex justify="space-between" mt={1}>
                            <Text fontSize="xs" color="gray.500">已分配: 8/32 核</Text>
                            <Text fontSize="xs" color="gray.500">主机: Proxmox-Node1</Text>
                          </Flex>
                        </Box>
                      </Flex>
                    </FormControl>
                  </Box>

                  <Box>
                    <FormControl isRequired>
                      <FormLabel fontWeight="medium" fontSize="sm" mb={1}>内存 (GB)</FormLabel>
                      <Flex align="center">
                        <CustomSelect defaultValue="4" size="sm" maxW="150px">
                          <option value="1" style={{ padding: '6px' }}>1 GB</option>
                          <option value="2" style={{ padding: '6px' }}>2 GB</option>
                          <option value="4" style={{ padding: '6px' }}>4 GB</option>
                          <option value="8" style={{ padding: '6px' }}>8 GB</option>
                          <option value="16" style={{ padding: '6px' }}>16 GB</option>
                          <option value="32" style={{ padding: '6px' }}>32 GB</option>
                        </CustomSelect>
                        <Box ml={4} flex="1">
                          <Progress value={50} size="sm" colorScheme="green" borderRadius="full" />
                          <Flex justify="space-between" mt={1}>
                            <Text fontSize="xs" color="gray.500">已分配: 64/128 GB</Text>
                            <Text fontSize="xs" color="gray.500">主机: Proxmox-Node1</Text>
                          </Flex>
                        </Box>
                      </Flex>
                    </FormControl>
                  </Box>

                  <Box>
                    <FormControl isRequired>
                      <FormLabel fontWeight="medium" fontSize="sm" mb={1}>磁盘 (GB)</FormLabel>
                      <Flex align="center">
                        <CustomSelect defaultValue="50" size="sm" maxW="150px">
                          <option value="20" style={{ padding: '6px' }}>20 GB</option>
                          <option value="50" style={{ padding: '6px' }}>50 GB</option>
                          <option value="100" style={{ padding: '6px' }}>100 GB</option>
                          <option value="200" style={{ padding: '6px' }}>200 GB</option>
                          <option value="500" style={{ padding: '6px' }}>500 GB</option>
                        </CustomSelect>
                        <Box ml={4} flex="1">
                          <Progress value={40} size="sm" colorScheme="purple" borderRadius="full" />
                          <Flex justify="space-between" mt={1}>
                            <Text fontSize="xs" color="gray.500">已分配: 1024/2048 GB</Text>
                            <Text fontSize="xs" color="gray.500">主机: Proxmox-Node1</Text>
                          </Flex>
                        </Box>
                      </Flex>
                    </FormControl>
                  </Box>
                </VStack>
              </TabPanel>

              {/* 网络设置面板 */}
              <TabPanel px={0} pt={3}>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel fontWeight="medium" fontSize="sm" mb={1}>网络配置方式</FormLabel>
                    <CustomSelect defaultValue="dhcp" size="sm">
                      <option value="dhcp" style={{ padding: '6px' }}>DHCP (自动获取IP)</option>
                      <option value="static" style={{ padding: '6px' }}>静态IP</option>
                    </CustomSelect>
                    <Text fontSize="xs" color="gray.500" mt={1}>选择虚拟机的网络配置方式</Text>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontWeight="medium" fontSize="sm" mb={1}>网络接口</FormLabel>
                    <CustomSelect defaultValue="default" size="sm">
                      <option value="default" style={{ padding: '6px' }}>默认桥接网络</option>
                      <option value="vmbr0" style={{ padding: '6px' }}>vmbr0 (***********/24)</option>
                      <option value="vmbr1" style={{ padding: '6px' }}>vmbr1 (**********/24)</option>
                    </CustomSelect>
                    <Text fontSize="xs" color="gray.500" mt={1}>选择要连接的网络接口</Text>
                  </FormControl>

                  <Box bg={useColorModeValue('gray.50', 'gray.700')} p={3} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                    <Heading size="xs" mb={2} fontSize="sm">高级网络选项</Heading>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                      <FormControl>
                        <FormLabel fontSize="sm">MAC 地址</FormLabel>
                        <Input placeholder="自动生成" size="sm" />
                      </FormControl>
                      <FormControl>
                        <FormLabel fontSize="sm">VLAN 标签</FormLabel>
                        <Input placeholder="无" size="sm" />
                      </FormControl>
                    </SimpleGrid>
                  </Box>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>

          <ModalFooter bg={useColorModeValue('gray.50', 'gray.800')} borderBottomRadius="xl" py={3}>
            <Button colorScheme="teal" mr={3} size="sm" leftIcon={<Plus size={16} weight="bold" />}>
              创建虚拟机
            </Button>
            <Button variant="outline" onClick={onAddVMClose} size="sm">取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 虚拟机详情模态框 */}
      <Modal
        isOpen={isVMDetailsOpen}
        onClose={onVMDetailsClose}
        isCentered
        size="lg"
        motionPreset="slideInBottom"
        scrollBehavior="inside"
      >
        <ModalOverlay backdropFilter="blur(5px)" bg="blackAlpha.700" />
        <ModalContent
          boxShadow="xl"
          borderRadius="xl"
          maxH="calc(100vh - 80px)"
          maxW="700px"
          w="95%"
          mx="auto"
          my="auto"
        >
          {selectedVM && (
            <>
              <ModalHeader bg={useColorModeValue('gray.50', 'gray.800')} borderTopRadius="xl" py={3}>
                <Flex align="center" justify="space-between">
                  <Flex align="center">
                    <Box
                      bg={useColorModeValue(
                        selectedVM.type === 'proxmox' ? 'orange.50' : 'blue.50',
                        selectedVM.type === 'proxmox' ? 'orange.900' : 'blue.900'
                      )}
                      p={1.5}
                      borderRadius="md"
                      mr={2}
                    >
                      <Desktop
                        size={18}
                        weight="fill"
                        color={useColorModeValue(
                          selectedVM.type === 'proxmox' ? 'orange.500' : 'blue.500',
                          selectedVM.type === 'proxmox' ? 'orange.300' : 'blue.300'
                        )}
                      />
                    </Box>
                    <Text fontSize="md" fontWeight="bold">{selectedVM.name}</Text>
                  </Flex>
                  <Badge colorScheme={getStatusColor(selectedVM.status)} fontSize="xs" px={2} py={0.5}>
                    {getStatusText(selectedVM.status)}
                  </Badge>
                </Flex>
              </ModalHeader>
              <ModalCloseButton top={3} />
              <ModalBody py={3}>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                  <Box>
                    <Heading size="xs" mb={2} fontSize="sm">基本信息</Heading>
                    <VStack align="stretch" spacing={2}>
                      <Flex justify="space-between">
                        <Text fontWeight="medium" color="gray.500" fontSize="xs">ID</Text>
                        <Text fontSize="xs">{selectedVM.id}</Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text fontWeight="medium" color="gray.500" fontSize="xs">主机</Text>
                        <Text fontSize="xs">{selectedVM.host}</Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text fontWeight="medium" color="gray.500" fontSize="xs">类型</Text>
                        <Text fontSize="xs">{selectedVM.type === 'proxmox' ? 'Proxmox VE' : 'VMware ESXi'}</Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text fontWeight="medium" color="gray.500" fontSize="xs">操作系统</Text>
                        <Text fontSize="xs">{selectedVM.os}</Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text fontWeight="medium" color="gray.500" fontSize="xs">IP地址</Text>
                        <Text fontSize="xs" fontFamily="mono">{selectedVM.ip}</Text>
                      </Flex>
                      <Flex justify="space-between">
                        <Text fontWeight="medium" color="gray.500" fontSize="xs">创建时间</Text>
                        <Text fontSize="xs">{selectedVM.createdAt}</Text>
                      </Flex>
                      {selectedVM.lastBackup && (
                        <Flex justify="space-between">
                          <Text fontWeight="medium" color="gray.500" fontSize="xs">最后备份</Text>
                          <Text fontSize="xs">{selectedVM.lastBackup}</Text>
                        </Flex>
                      )}
                    </VStack>
                  </Box>

                  <Box>
                    <Heading size="xs" mb={2} fontSize="sm">资源配置</Heading>
                    <VStack align="stretch" spacing={3}>
                      <Box>
                        <Flex justify="space-between" mb={0.5}>
                          <Text fontSize="xs" fontWeight="medium">CPU</Text>
                          <Text fontSize="xs">{selectedVM.cpu} vCPU</Text>
                        </Flex>
                        <Progress value={70} colorScheme="blue" size="xs" borderRadius="full" />
                      </Box>
                      <Box>
                        <Flex justify="space-between" mb={0.5}>
                          <Text fontSize="xs" fontWeight="medium">内存</Text>
                          <Text fontSize="xs">{selectedVM.memory} GB</Text>
                        </Flex>
                        <Progress value={60} colorScheme="green" size="xs" borderRadius="full" />
                      </Box>
                      <Box>
                        <Flex justify="space-between" mb={0.5}>
                          <Text fontSize="xs" fontWeight="medium">磁盘</Text>
                          <Text fontSize="xs">{selectedVM.disk} GB</Text>
                        </Flex>
                        <Progress value={40} colorScheme="purple" size="xs" borderRadius="full" />
                      </Box>
                    </VStack>

                    <Divider my={3} />

                    <Heading size="xs" mb={2} fontSize="sm">性能监控</Heading>
                    <SimpleGrid columns={2} spacing={3}>
                      <Stat bg={statBg} p={2} borderRadius="md">
                        <StatLabel fontSize="xs">CPU 使用率</StatLabel>
                        <StatNumber fontSize="sm">32%</StatNumber>
                      </Stat>
                      <Stat bg={statBg} p={2} borderRadius="md">
                        <StatLabel fontSize="xs">内存使用率</StatLabel>
                        <StatNumber fontSize="sm">45%</StatNumber>
                      </Stat>
                    </SimpleGrid>
                  </Box>
                </SimpleGrid>
              </ModalBody>
              <ModalFooter bg={useColorModeValue('gray.50', 'gray.800')} borderBottomRadius="xl" py={3}>
                {selectedVM.status === 'stopped' ? (
                  <Button
                    colorScheme="green"
                    leftIcon={<Play size={14} weight="bold" />}
                    mr={2}
                    size="sm"
                    onClick={() => {
                      handleVMAction('start', selectedVM);
                      onVMDetailsClose();
                    }}
                  >
                    启动
                  </Button>
                ) : (
                  <Button
                    colorScheme="red"
                    leftIcon={<Stop size={14} weight="bold" />}
                    mr={2}
                    size="sm"
                    onClick={() => {
                      handleVMAction('stop', selectedVM);
                      onVMDetailsClose();
                    }}
                  >
                    停止
                  </Button>
                )}
                <Button
                  colorScheme="orange"
                  leftIcon={<ArrowsClockwise size={14} weight="bold" />}
                  mr={2}
                  size="sm"
                  onClick={() => {
                    handleVMAction('restart', selectedVM);
                    onVMDetailsClose();
                  }}
                >
                  重启
                </Button>
                <Button variant="outline" onClick={onVMDetailsClose} size="sm">关闭</Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </Box>
  )
}
