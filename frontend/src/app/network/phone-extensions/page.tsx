'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Input,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  useDisclosure,
  useToast,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Tooltip,
  SimpleGrid,
  HStack,
  Spinner,
  Center,
  useColorMode,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Stack,
  FormControl,
  FormLabel,
  Divider
} from '@chakra-ui/react';
import {
  MagnifyingGlass,
  DotsThree,
  Plus,
  Download,
  PencilSimple,
  Trash,
} from '@phosphor-icons/react';
import * as phoneExtensionService from '@/services/phoneExtensionService';
import { PhoneExtension, CreatePhoneExtensionDTO } from '@/services/phoneExtensionService';

// 修改模拟数据以匹配后端的接口定义
const mockExtensions: PhoneExtension[] = [
  {
    id: '1',
    extension_number: '8001',
    name: '张三',
    department: '研发部',
    type: 'internal',
    status: 'active',
    notes: '12楼A区'
  },
  {
    id: '2',
    extension_number: '8002',
    name: '李四',
    department: '市场部',
    type: 'internal',
    status: 'active',
    notes: '11楼B区'
  },
  {
    id: '3',
    extension_number: '8003',
    name: '王五',
    department: '财务部',
    type: 'internal',
    status: 'inactive',
    notes: '11楼A区'
  }
];

interface CabinetExtension { number: number; socket?: string; user?: string; }
interface CabinetGroup { name: string; extensions: CabinetExtension[]; }

const professionalCabinets: CabinetGroup[] = [
  // ...（同前，省略，保持原有结构）
];

function CabinetOverview() {
  return (
    <Box mb={10} overflowX="auto">
      <Text fontSize="2xl" fontWeight="bold" mb={6} color="komodo.green">分机柜分布总览</Text>
      <Table size="sm" variant="simple" border="1px solid #e2e8f0">
        <Thead>
          <Tr>
            <Th rowSpan={3} textAlign="center">分机柜</Th>
            {Array.from({ length: 30 }, (_, i) => (
              <Th key={i} textAlign="center">{i + 1}</Th>
            ))}
          </Tr>
        </Thead>
        <Tbody>
          {professionalCabinets.map((cabinet: CabinetGroup) => (
            <React.Fragment key={cabinet.name}>
              <Tr>
                <Td rowSpan={3} textAlign="center" fontWeight="bold" verticalAlign="middle">{cabinet.name}</Td>
                <Td textAlign="center" fontWeight="bold">分机号</Td>
                {cabinet.extensions.map((ext: CabinetExtension, i: number) => (
                  <Td key={i} textAlign="center">{ext.number}</Td>
                ))}
              </Tr>
              <Tr>
                <Td textAlign="center" fontWeight="bold">插座号</Td>
                {cabinet.extensions.map((ext: CabinetExtension, i: number) => (
                  <Td key={i} textAlign="center">{ext.socket || '-'}</Td>
                ))}
              </Tr>
              <Tr>
                <Td textAlign="center" fontWeight="bold">使用人</Td>
                {cabinet.extensions.map((ext: CabinetExtension, i: number) => (
                  <Td key={i} textAlign="center">{ext.user || '-'}</Td>
                ))}
              </Tr>
            </React.Fragment>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
}

interface ExtensionListProps {
  extensions: PhoneExtension[];
  loading: boolean;
  onEdit: (ext: PhoneExtension) => void;
  onDelete: (id: string) => void;
  onSearch: () => void;
  searchQuery: string;
  setSearchQuery: (v: string) => void;
  onExport: () => void;
  onAdd: () => void;
  selectedType: string;
  setSelectedType: (v: string) => void;
}

function ExtensionList({
  extensions, loading, onEdit, onDelete, onSearch, searchQuery, setSearchQuery, onExport, onAdd, selectedType, setSelectedType
}: ExtensionListProps) {
  return (
    <Box borderRadius="xl" bg={useColorModeValue("white", "gray.800")} boxShadow="md" p={6} mb={8}>
      <Flex mb={4} gap={2} wrap="wrap" align="center">
        <HStack flex={1}>
          <Input
            placeholder="搜索分机号、姓名或部门..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && onSearch()}
            size="md"
            borderRadius="lg"
            bg={useColorModeValue("gray.50", "gray.700")}
          />
          <Button leftIcon={<MagnifyingGlass />} onClick={onSearch} colorScheme="blue" size="md" borderRadius="lg">搜索</Button>
        </HStack>
        <Select w={{ base: "full", md: "150px" }} value={selectedType} onChange={e => setSelectedType(e.target.value)} size="md" borderRadius="lg">
          <option value="all">所有类型</option>
          <option value="internal">内部</option>
          <option value="external">外部</option>
        </Select>
        <Button leftIcon={<Download />} onClick={onExport} colorScheme="green" size="md" borderRadius="lg">导出数据</Button>
        <Button leftIcon={<Plus />} onClick={onAdd} colorScheme="blue" size="md" borderRadius="lg">添加分机</Button>
      </Flex>
      <Box overflowX="auto">
        <Table variant="simple" size="md">
          <Thead position="sticky" top={0} zIndex={1} bg={useColorModeValue("white", "gray.800")}> 
            <Tr>
              <Th>分机号</Th>
              <Th>姓名</Th>
              <Th>部门</Th>
              <Th>位置</Th>
              <Th>类型</Th>
              <Th>状态</Th>
              <Th textAlign="right">操作</Th>
            </Tr>
          </Thead>
          <Tbody>
            {extensions.length === 0 && !loading && (
              <Tr>
                <Td colSpan={7} textAlign="center" color="gray.400">暂无分机数据</Td>
              </Tr>
            )}
            {extensions.map((ext) => (
              <Tr key={ext.id} _hover={{ bg: useColorModeValue("gray.50", "gray.700") }}>
                <Td>{ext.extension_number}</Td>
                <Td>{ext.name}</Td>
                <Td>{ext.department}</Td>
                <Td>{ext.notes}</Td>
                <Td>
                  <Badge colorScheme={ext.type === "internal" ? "blue" : "green"}>
                    {ext.type === "internal" ? "内部" : "外部"}
                  </Badge>
                </Td>
                <Td>
                  <Badge colorScheme={ext.status === "active" ? "green" : "red"}>
                    {ext.status === "active" ? "在用" : "停用"}
                  </Badge>
                </Td>
                <Td textAlign="right">
                  <Tooltip label="编辑">
                    <IconButton icon={<PencilSimple />} size="sm" variant="ghost" onClick={() => onEdit(ext)} aria-label="编辑" borderRadius="full" />
                  </Tooltip>
                  <Tooltip label="删除">
                    <IconButton icon={<Trash />} size="sm" variant="ghost" colorScheme="red" onClick={() => onDelete(ext.id)} aria-label="删除" borderRadius="full" />
                  </Tooltip>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
        {loading && <Text mt={4} textAlign="center" color="gray.400">加载中...</Text>}
      </Box>
    </Box>
  );
}

export default function PhoneExtensionsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  // 搜索分机
  const handleSearch = async () => {
    // 这里可以保留原有搜索逻辑或仅做提示
    toast({
      title: '搜索功能',
      description: `你搜索了：${searchQuery}`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Container maxW="container.xl" py={8}>
      <Box mb={8}>
        <Text color="gray.600">管理和维护公司内部电话分机信息</Text>
      </Box>
      <Flex mb={4} gap={2} wrap="wrap" align="center">
        <HStack flex={1}>
          <Input
            placeholder="搜索分机号、姓名或部门..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            size="md"
            borderRadius="lg"
            bg={useColorModeValue('gray.50', 'gray.700')}
          />
          <Button leftIcon={<MagnifyingGlass />} onClick={handleSearch} colorScheme="blue" size="md" borderRadius="lg">搜索</Button>
        </HStack>
      </Flex>
    </Container>
  );
} 