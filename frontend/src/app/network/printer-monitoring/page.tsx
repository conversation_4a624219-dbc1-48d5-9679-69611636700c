'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  HStack,
  VStack,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  useColorModeValue,
  useToast,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  IconButton,
  Tooltip,
  Progress,
  Divider,
} from '@chakra-ui/react'
import {
  MagnifyingGlass,
  Printer,
  ArrowsClockwise,
  Warning,
  ChartLine,
  Drop,
  FileText,
} from '@phosphor-icons/react'
import dynamic from 'next/dynamic'

// 动态导入Chart.js相关组件，避免SSR问题
const Line = dynamic(
  () => import('react-chartjs-2').then((mod) => mod.Line),
  { ssr: false }
)

// 在客户端组件中初始化Chart.js
const initChartJS = () => {
  if (typeof window !== 'undefined') {
    import('chart.js').then(({ Chart, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend }) => {
      Chart.register(
        CategoryScale,
        LinearScale,
        PointElement,
        LineElement,
        Title,
        Tooltip,
        Legend
      )
    })
  }
}

// 打印机类型定义
interface PrinterDevice {
  id: string
  device_id: string
  name: string
  model: string
  manufacturer: string
  location: string
  floor: string
  ip_address: string
  mac_address: string
  status: 'online' | 'offline' | 'warning' | 'error'
  serial_number: string
  firmware_version: string
  last_seen: string
}

// 打印机数据类型定义
interface PrinterData {
  id: number
  device_id: string
  timestamp: string
  status: string
  toner_black: number
  toner_cyan: number
  toner_magenta: number
  toner_yellow: number
  pages_total: number
  pages_since_last: number
  error_state: string
  warning_state: string
}

// 模拟数据 - 打印机
const mockPrinters: PrinterDevice[] = [
  {
    id: '1',
    device_id: 'printer-01',
    name: '打印机-1F-01',
    model: 'HP LaserJet Pro M402dn',
    manufacturer: 'HP',
    location: '1楼办公区',
    floor: '1F',
    ip_address: '*************',
    mac_address: 'AA:BB:CC:11:22:33',
    status: 'online',
    serial_number: 'SN12345678',
    firmware_version: '2.145.1',
    last_seen: '2023-06-15T14:30:00Z',
  },
  {
    id: '2',
    device_id: 'printer-02',
    name: '打印机-2F-01',
    model: 'HP Color LaserJet Pro MFP M479fdw',
    manufacturer: 'HP',
    location: '2楼办公区',
    floor: '2F',
    ip_address: '*************',
    mac_address: 'AA:BB:CC:11:22:34',
    status: 'warning',
    serial_number: 'SN12345679',
    firmware_version: '2.145.1',
    last_seen: '2023-06-15T14:30:00Z',
  },
  {
    id: '3',
    device_id: 'printer-03',
    name: '打印机-3F-01',
    model: 'Canon imageCLASS MF743Cdw',
    manufacturer: 'Canon',
    location: '3楼会议室',
    floor: '3F',
    ip_address: '*************',
    mac_address: 'AA:BB:CC:11:22:35',
    status: 'error',
    serial_number: 'SN12345680',
    firmware_version: '1.05',
    last_seen: '2023-06-15T14:30:00Z',
  },
  {
    id: '4',
    device_id: 'printer-04',
    name: '打印机-1F-02',
    model: 'Epson WorkForce Pro WF-C5790',
    manufacturer: 'Epson',
    location: '1楼前台',
    floor: '1F',
    ip_address: '*************',
    mac_address: 'AA:BB:CC:11:22:36',
    status: 'offline',
    serial_number: 'SN12345681',
    firmware_version: '3.12.1',
    last_seen: '2023-06-10T14:30:00Z',
  },
];

// 模拟数据 - 打印机数据
const mockPrinterData: PrinterData[] = [
  {
    id: 1,
    device_id: 'printer-01',
    timestamp: '2023-06-15T14:30:00Z',
    status: 'online',
    toner_black: 75,
    toner_cyan: 0,
    toner_magenta: 0,
    toner_yellow: 0,
    pages_total: 12500,
    pages_since_last: 150,
    error_state: '',
    warning_state: '',
  },
  {
    id: 2,
    device_id: 'printer-02',
    timestamp: '2023-06-15T14:30:00Z',
    status: 'warning',
    toner_black: 15,
    toner_cyan: 45,
    toner_magenta: 60,
    toner_yellow: 30,
    pages_total: 8750,
    pages_since_last: 85,
    error_state: '',
    warning_state: 'LOW_TONER_BLACK',
  },
  {
    id: 3,
    device_id: 'printer-03',
    timestamp: '2023-06-15T14:30:00Z',
    status: 'error',
    toner_black: 50,
    toner_cyan: 40,
    toner_magenta: 35,
    toner_yellow: 45,
    pages_total: 5200,
    pages_since_last: 0,
    error_state: 'PAPER_JAM',
    warning_state: '',
  },
];

export default function PrinterMonitoringPage() {
  const [printers, setPrinters] = useState<PrinterDevice[]>(mockPrinters);
  const [printerData, setPrinterData] = useState<PrinterData[]>(mockPrinterData);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFloor, setSelectedFloor] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedPrinter, setSelectedPrinter] = useState<PrinterDevice | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');

  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 初始化Chart.js
  useEffect(() => {
    initChartJS()
  }, []);

  // 过滤打印机
  const filteredPrinters = printers.filter(printer => {
    // 按楼层过滤
    if (selectedFloor !== 'all' && printer.floor !== selectedFloor) {
      return false;
    }

    // 按状态过滤
    if (selectedStatus !== 'all' && printer.status !== selectedStatus) {
      return false;
    }

    // 按搜索词过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        printer.name.toLowerCase().includes(query) ||
        printer.ip_address.includes(query) ||
        printer.location.toLowerCase().includes(query) ||
        printer.model.toLowerCase().includes(query) ||
        printer.manufacturer.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // 获取打印机数据
  const getPrinterData = (deviceId: string) => {
    return printerData.find(data => data.device_id === deviceId);
  };

  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green';
      case 'offline':
        return 'gray';
      case 'warning':
        return 'yellow';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'warning':
        return '警告';
      case 'error':
        return '错误';
      default:
        return status;
    }
  };

  // 获取错误状态文本
  const getErrorStateText = (errorState: string) => {
    switch (errorState) {
      case 'PAPER_JAM':
        return '卡纸';
      case 'OUT_OF_PAPER':
        return '缺纸';
      case 'COVER_OPEN':
        return '盖子打开';
      case 'TONER_EMPTY':
        return '墨粉耗尽';
      case 'HARDWARE_ERROR':
        return '硬件错误';
      default:
        return errorState || '无';
    }
  };

  // 获取警告状态文本
  const getWarningStateText = (warningState: string) => {
    switch (warningState) {
      case 'LOW_TONER_BLACK':
        return '黑色墨粉不足';
      case 'LOW_TONER_CYAN':
        return '青色墨粉不足';
      case 'LOW_TONER_MAGENTA':
        return '品红色墨粉不足';
      case 'LOW_TONER_YELLOW':
        return '黄色墨粉不足';
      case 'LOW_PAPER':
        return '纸张不足';
      case 'SERVICE_NEEDED':
        return '需要维护';
      default:
        return warningState || '无';
    }
  };

  // 刷新数据
  const refreshData = () => {
    setIsLoading(true);

    // 模拟API调用
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "数据已刷新",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    }, 1000);
  };

  // 生成图表数据
  const generateChartData = (deviceId: string, dataType: string) => {
    // 这里应该从API获取历史数据，现在使用模拟数据
    const labels = Array.from({ length: 30 }, (_, i) => `${i+1}日`);

    let data: number[] = [];
    let label = '';
    let borderColor = '';

    switch (dataType) {
      case 'pages':
        data = Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 10);
        label = '每日打印页数';
        borderColor = 'rgb(75, 192, 192)';
        break;
      case 'toner_black':
        // 模拟墨粉消耗，从100%逐渐减少
        data = Array.from({ length: 30 }, (_, i) => Math.max(0, 100 - i * 3 - Math.floor(Math.random() * 5)));
        label = '黑色墨粉剩余 (%)';
        borderColor = 'rgb(0, 0, 0)';
        break;
      case 'toner_color':
        // 为彩色打印机生成三种颜色的墨粉数据
        return {
          labels,
          datasets: [
            {
              label: '青色墨粉 (%)',
              data: Array.from({ length: 30 }, (_, i) => Math.max(0, 100 - i * 2 - Math.floor(Math.random() * 5))),
              borderColor: 'rgb(0, 191, 255)',
              backgroundColor: 'rgba(0, 191, 255, 0.2)',
              tension: 0.3,
            },
            {
              label: '品红色墨粉 (%)',
              data: Array.from({ length: 30 }, (_, i) => Math.max(0, 100 - i * 2.5 - Math.floor(Math.random() * 5))),
              borderColor: 'rgb(255, 0, 255)',
              backgroundColor: 'rgba(255, 0, 255, 0.2)',
              tension: 0.3,
            },
            {
              label: '黄色墨粉 (%)',
              data: Array.from({ length: 30 }, (_, i) => Math.max(0, 100 - i * 1.8 - Math.floor(Math.random() * 5))),
              borderColor: 'rgb(255, 255, 0)',
              backgroundColor: 'rgba(255, 255, 0, 0.2)',
              tension: 0.3,
            },
          ],
        };
    }

    return {
      labels,
      datasets: [
        {
          label,
          data,
          borderColor,
          backgroundColor: borderColor.replace('rgb', 'rgba').replace(')', ', 0.2)'),
          tension: 0.3,
        },
      ],
    };
  };

  return (
    <div className="printer-monitoring-container">
      <Box p={6}>
        <Box mb={6}>
          <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
            <Box>
              <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>打印机监控</Heading>
              <Text color="gray.500" fontSize="md">监控和管理网络打印机</Text>
            </Box>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="teal"
              isLoading={isLoading}
              loadingText="刷新中"
              onClick={refreshData}
              mt={{ base: 4, md: 0 }}
            >
              刷新数据
            </Button>
          </Flex>
        </Box>

      <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6} mb={6}>
        <Stat
          bg={useColorModeValue('teal.50', 'teal.900')}
          p={4}
          borderRadius="lg"
          boxShadow="sm"
          borderWidth="1px"
          borderColor={useColorModeValue('teal.100', 'teal.700')}
        >
          <StatLabel fontWeight="medium" color={useColorModeValue('teal.600', 'teal.200')}>总打印机</StatLabel>
          <StatNumber fontSize="3xl">{printers.length}</StatNumber>
          <StatHelpText>
            <HStack>
              <Box w="10px" h="10px" borderRadius="full" bg="green.400" />
              <Text fontSize="xs">{printers.filter(p => p.status === 'online').length} 在线</Text>
            </HStack>
          </StatHelpText>
        </Stat>

        <Stat
          bg={useColorModeValue('blue.50', 'blue.900')}
          p={4}
          borderRadius="lg"
          boxShadow="sm"
          borderWidth="1px"
          borderColor={useColorModeValue('blue.100', 'blue.700')}
        >
          <StatLabel fontWeight="medium" color={useColorModeValue('blue.600', 'blue.200')}>总打印页数</StatLabel>
          <StatNumber fontSize="3xl">
            {printerData.reduce((sum, data) => sum + data.pages_total, 0).toLocaleString()}
          </StatNumber>
          <StatHelpText>
            <Text fontSize="xs" color={useColorModeValue('blue.600', 'blue.200')}>
              本月: {printerData.reduce((sum, data) => sum + data.pages_since_last, 0).toLocaleString()} 页
            </Text>
          </StatHelpText>
        </Stat>

        <Stat
          bg={useColorModeValue('yellow.50', 'yellow.900')}
          p={4}
          borderRadius="lg"
          boxShadow="sm"
          borderWidth="1px"
          borderColor={useColorModeValue('yellow.100', 'yellow.700')}
        >
          <StatLabel fontWeight="medium" color={useColorModeValue('yellow.600', 'yellow.200')}>警告设备</StatLabel>
          <StatNumber fontSize="3xl">{printers.filter(p => p.status === 'warning').length}</StatNumber>
          <StatHelpText>
            <Text fontSize="xs" color={useColorModeValue('yellow.600', 'yellow.200')}>
              需要更换墨粉
            </Text>
          </StatHelpText>
        </Stat>

        <Stat
          bg={useColorModeValue('red.50', 'red.900')}
          p={4}
          borderRadius="lg"
          boxShadow="sm"
          borderWidth="1px"
          borderColor={useColorModeValue('red.100', 'red.700')}
        >
          <StatLabel fontWeight="medium" color={useColorModeValue('red.600', 'red.200')}>错误设备</StatLabel>
          <StatNumber fontSize="3xl">{printers.filter(p => p.status === 'error').length}</StatNumber>
          <StatHelpText>
            <Text fontSize="xs" color={useColorModeValue('red.600', 'red.200')}>
              {printers.filter(p => p.status === 'offline').length} 离线设备
            </Text>
          </StatHelpText>
        </Stat>
      </SimpleGrid>

      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <InputGroup maxW={{ base: '100%', md: '300px' }}>
            <InputLeftElement pointerEvents="none">
              <Box color="gray.500">
                <MagnifyingGlass size={20} />
              </Box>
            </InputLeftElement>
            <Input
              placeholder="搜索打印机..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              pl="40px"
            />
          </InputGroup>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedFloor}
            onChange={(e) => setSelectedFloor(e.target.value)}
          >
            <option value="all">所有楼层</option>
            <option value="1F">1楼</option>
            <option value="2F">2楼</option>
            <option value="3F">3楼</option>
          </Select>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">所有状态</option>
            <option value="online">在线</option>
            <option value="offline">离线</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
          </Select>
        </Flex>
      </Box>

      {selectedPrinter ? (
        <Box mb={6}>
          <Flex justify="space-between" align="center" mb={4}>
            <Heading size="md">打印机详情: {selectedPrinter.name}</Heading>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setSelectedPrinter(null)}
            >
              返回列表
            </Button>
          </Flex>

          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} mb={6}>
            <Card borderRadius="lg" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Heading size="sm" mb={4}>基本信息</Heading>
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">设备ID</Text>
                    <Text>{selectedPrinter.device_id}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">状态</Text>
                    <Badge colorScheme={getStatusColor(selectedPrinter.status)}>
                      {getStatusText(selectedPrinter.status)}
                    </Badge>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">型号</Text>
                    <Text>{selectedPrinter.model}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">制造商</Text>
                    <Text>{selectedPrinter.manufacturer}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">位置</Text>
                    <Text>{selectedPrinter.location}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">楼层</Text>
                    <Text>{selectedPrinter.floor}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">IP地址</Text>
                    <Text>{selectedPrinter.ip_address}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">MAC地址</Text>
                    <Text>{selectedPrinter.mac_address}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">序列号</Text>
                    <Text>{selectedPrinter.serial_number}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="bold" color="gray.500">固件版本</Text>
                    <Text>{selectedPrinter.firmware_version}</Text>
                  </Box>
                </SimpleGrid>
              </CardBody>
            </Card>

            <Card borderRadius="lg" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
              <CardBody>
                <Heading size="sm" mb={4}>状态信息</Heading>
                {getPrinterData(selectedPrinter.device_id) ? (
                  <VStack spacing={4} align="stretch">
                    {getPrinterData(selectedPrinter.device_id)?.error_state && (
                      <Box
                        p={3}
                        bg={useColorModeValue('red.50', 'red.900')}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor={useColorModeValue('red.100', 'red.700')}
                      >
                        <Flex align="center">
                          <Warning size={20} weight="fill" color="red" style={{ marginRight: '8px' }} />
                          <Text fontWeight="medium" color={useColorModeValue('red.600', 'red.200')}>
                            错误: {getErrorStateText(getPrinterData(selectedPrinter.device_id)?.error_state || '')}
                          </Text>
                        </Flex>
                      </Box>
                    )}

                    {getPrinterData(selectedPrinter.device_id)?.warning_state && (
                      <Box
                        p={3}
                        bg={useColorModeValue('yellow.50', 'yellow.900')}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor={useColorModeValue('yellow.100', 'yellow.700')}
                      >
                        <Flex align="center">
                          <Warning size={20} weight="fill" color="orange" style={{ marginRight: '8px' }} />
                          <Text fontWeight="medium" color={useColorModeValue('yellow.600', 'yellow.200')}>
                            警告: {getWarningStateText(getPrinterData(selectedPrinter.device_id)?.warning_state || '')}
                          </Text>
                        </Flex>
                      </Box>
                    )}

                    <Box>
                      <Text fontSize="sm" fontWeight="bold" color="gray.500" mb={2}>墨粉状态</Text>
                      {getPrinterData(selectedPrinter.device_id)?.toner_black !== undefined && (
                        <Box mb={3}>
                          <Flex justify="space-between" mb={1}>
                            <Text fontSize="sm">黑色墨粉</Text>
                            <Text fontSize="sm">{getPrinterData(selectedPrinter.device_id)?.toner_black}%</Text>
                          </Flex>
                          <Progress
                            value={getPrinterData(selectedPrinter.device_id)?.toner_black || 0}
                            colorScheme={(getPrinterData(selectedPrinter.device_id)?.toner_black || 0) < 20 ? "red" : "blue"}
                            size="sm"
                            borderRadius="full"
                          />
                        </Box>
                      )}

                      {getPrinterData(selectedPrinter.device_id)?.toner_cyan !== undefined &&
                       (getPrinterData(selectedPrinter.device_id)?.toner_cyan || 0) > 0 && (
                        <Box mb={3}>
                          <Flex justify="space-between" mb={1}>
                            <Text fontSize="sm">青色墨粉</Text>
                            <Text fontSize="sm">{getPrinterData(selectedPrinter.device_id)?.toner_cyan}%</Text>
                          </Flex>
                          <Progress
                            value={getPrinterData(selectedPrinter.device_id)?.toner_cyan || 0}
                            colorScheme={(getPrinterData(selectedPrinter.device_id)?.toner_cyan || 0) < 20 ? "red" : "cyan"}
                            size="sm"
                            borderRadius="full"
                          />
                        </Box>
                      )}

                      {getPrinterData(selectedPrinter.device_id)?.toner_magenta !== undefined &&
                       (getPrinterData(selectedPrinter.device_id)?.toner_magenta || 0) > 0 && (
                        <Box mb={3}>
                          <Flex justify="space-between" mb={1}>
                            <Text fontSize="sm">品红色墨粉</Text>
                            <Text fontSize="sm">{getPrinterData(selectedPrinter.device_id)?.toner_magenta}%</Text>
                          </Flex>
                          <Progress
                            value={getPrinterData(selectedPrinter.device_id)?.toner_magenta || 0}
                            colorScheme={(getPrinterData(selectedPrinter.device_id)?.toner_magenta || 0) < 20 ? "red" : "pink"}
                            size="sm"
                            borderRadius="full"
                          />
                        </Box>
                      )}

                      {getPrinterData(selectedPrinter.device_id)?.toner_yellow !== undefined &&
                       (getPrinterData(selectedPrinter.device_id)?.toner_yellow || 0) > 0 && (
                        <Box mb={3}>
                          <Flex justify="space-between" mb={1}>
                            <Text fontSize="sm">黄色墨粉</Text>
                            <Text fontSize="sm">{getPrinterData(selectedPrinter.device_id)?.toner_yellow}%</Text>
                          </Flex>
                          <Progress
                            value={getPrinterData(selectedPrinter.device_id)?.toner_yellow || 0}
                            colorScheme={(getPrinterData(selectedPrinter.device_id)?.toner_yellow || 0) < 20 ? "red" : "yellow"}
                            size="sm"
                            borderRadius="full"
                          />
                        </Box>
                      )}
                    </Box>

                    <Divider />

                    <SimpleGrid columns={2} spacing={4}>
                      <Box>
                        <Text fontSize="sm" fontWeight="bold" color="gray.500">总打印页数</Text>
                        <Text>{getPrinterData(selectedPrinter.device_id)?.pages_total.toLocaleString()} 页</Text>
                      </Box>
                      <Box>
                        <Text fontSize="sm" fontWeight="bold" color="gray.500">本月打印页数</Text>
                        <Text>{getPrinterData(selectedPrinter.device_id)?.pages_since_last.toLocaleString()} 页</Text>
                      </Box>
                    </SimpleGrid>
                  </VStack>
                ) : (
                  <Text>无状态数据</Text>
                )}
              </CardBody>
            </Card>
          </SimpleGrid>

          <Tabs variant="enclosed" colorScheme="teal">
            <TabList>
              <Tab>历史趋势</Tab>
            </TabList>
            <TabPanels>
              <TabPanel px={0}>
                <HStack spacing={4} mb={4}>
                  <Select
                    maxW="200px"
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                  >
                    <option value="7d">最近7天</option>
                    <option value="30d">最近30天</option>
                    <option value="90d">最近90天</option>
                  </Select>
                </HStack>
                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                  <Card borderRadius="lg" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
                    <CardBody>
                      <Heading size="sm" mb={4}>每日打印页数</Heading>
                      <Box h="300px">
                        <Line
                          data={generateChartData(selectedPrinter.device_id, 'pages')}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                              y: {
                                beginAtZero: true
                              }
                            }
                          }}
                        />
                      </Box>
                    </CardBody>
                  </Card>

                  {selectedPrinter.model.toLowerCase().includes('color') ? (
                    <Card borderRadius="lg" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
                      <CardBody>
                        <Heading size="sm" mb={4}>彩色墨粉剩余量</Heading>
                        <Box h="300px">
                          <Line
                            data={generateChartData(selectedPrinter.device_id, 'toner_color')}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              scales: {
                                y: {
                                  beginAtZero: true,
                                  max: 100
                                }
                              }
                            }}
                          />
                        </Box>
                      </CardBody>
                    </Card>
                  ) : (
                    <Card borderRadius="lg" boxShadow="md" borderWidth="1px" borderColor={borderColor}>
                      <CardBody>
                        <Heading size="sm" mb={4}>黑色墨粉剩余量</Heading>
                        <Box h="300px">
                          <Line
                            data={generateChartData(selectedPrinter.device_id, 'toner_black')}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              scales: {
                                y: {
                                  beginAtZero: true,
                                  max: 100
                                }
                              }
                            }}
                          />
                        </Box>
                      </CardBody>
                    </Card>
                  )}
                </SimpleGrid>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>
      ) : (
        <Box
          borderWidth="1px"
          borderRadius="xl"
          overflow="hidden"
          bg={bgColor}
          borderColor={borderColor}
          boxShadow="md"
        >
          <Table variant="simple">
            <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
              <Tr>
                <Th py={4}>名称</Th>
                <Th py={4}>状态</Th>
                <Th py={4}>位置</Th>
                <Th py={4}>型号</Th>
                <Th py={4}>墨粉</Th>
                <Th py={4}>页数</Th>
                <Th py={4}>操作</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredPrinters.length > 0 ? (
                filteredPrinters.map(printer => (
                  <Tr
                    key={printer.id}
                    _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                    cursor="pointer"
                    onClick={() => setSelectedPrinter(printer)}
                  >
                    <Td>
                      <Flex align="center">
                        <Box
                          bg={useColorModeValue('blue.50', 'blue.900')}
                          p={1.5}
                          borderRadius="md"
                          mr={3}
                        >
                          <Printer
                            size={18}
                            weight="fill"
                            color={useColorModeValue('blue.500', 'blue.300')}
                          />
                        </Box>
                        <Text fontWeight="medium">{printer.name}</Text>
                      </Flex>
                    </Td>
                    <Td>
                      <Badge
                        colorScheme={getStatusColor(printer.status)}
                        px={2}
                        py={1}
                        borderRadius="full"
                        variant="subtle"
                      >
                        {getStatusText(printer.status)}
                      </Badge>
                    </Td>
                    <Td>{printer.location}</Td>
                    <Td>
                      <Text noOfLines={1}>{printer.model}</Text>
                    </Td>
                    <Td>
                      {printer.status !== 'offline' ? (
                        <Flex align="center">
                          <Drop size={16} style={{ marginRight: '4px' }} />
                          <Text>
                            {getPrinterData(printer.device_id)?.toner_black || 0}%
                          </Text>
                        </Flex>
                      ) : (
                        <Text color="gray.500">-</Text>
                      )}
                    </Td>
                    <Td>
                      {printer.status !== 'offline' ? (
                        <Flex align="center">
                          <FileText size={16} style={{ marginRight: '4px' }} />
                          <Text>
                            {getPrinterData(printer.device_id)?.pages_total.toLocaleString() || 0}
                          </Text>
                        </Flex>
                      ) : (
                        <Text color="gray.500">-</Text>
                      )}
                    </Td>
                    <Td onClick={(e) => e.stopPropagation()}>
                      <HStack spacing={1}>
                        <Tooltip label="查看详情">
                          <IconButton
                            icon={<MagnifyingGlass weight="bold" />}
                            aria-label="查看详情"
                            size="sm"
                            variant="ghost"
                            colorScheme="blue"
                            onClick={() => setSelectedPrinter(printer)}
                          />
                        </Tooltip>
                        <Tooltip label="刷新">
                          <IconButton
                            icon={<ArrowsClockwise weight="bold" />}
                            aria-label="刷新"
                            size="sm"
                            variant="ghost"
                            colorScheme="green"
                            onClick={(e) => {
                              e.stopPropagation();
                              toast({
                                title: `正在刷新 ${printer.name}`,
                                status: "info",
                                duration: 2000,
                                isClosable: true,
                              });
                            }}
                          />
                        </Tooltip>
                      </HStack>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={7} textAlign="center" py={8}>
                    <VStack spacing={3}>
                      <Box
                        p={3}
                        borderRadius="full"
                        bg={useColorModeValue('gray.100', 'gray.700')}
                      >
                        <MagnifyingGlass size={24} weight="duotone" />
                      </Box>
                      <Text>没有找到匹配的打印机</Text>
                      <Text fontSize="sm" color="gray.500">
                        尝试使用不同的搜索条件或清除筛选器
                      </Text>
                    </VStack>
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      )}
      </Box>
    </div>
  );
}
