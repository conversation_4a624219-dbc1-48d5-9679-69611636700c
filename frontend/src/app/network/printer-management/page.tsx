'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  HStack,
  VStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Tooltip,
  Divider,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Progress,
  Tag,
  TagLabel,
  TagLeftIcon,
  Switch,
  Select,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react'
import CustomSelect from '@/components/CustomSelect'
import {
  MagnifyingGlass,
  Plus,
  Printer,
  Trash,
  Gear,
  ArrowsClockwise,
  Drop,
  Warning,
  CheckCircle,
  XCircle,
  Buildings,
  MapPin,
  Pencil,
  ClockCounterClockwise,
  Article,
  Wrench,
} from '@phosphor-icons/react'
import axios from 'axios'

// 打印机类型定义
interface Printer {
  id: number
  name: string
  model: string
  serial_number?: string
  manufacturer?: string
  location?: string
  department?: string
  ip_address?: string
  mac_address?: string
  printer_type: string
  status: 'online' | 'offline' | 'warning' | 'error' | 'maintenance'
  snmp_enabled: boolean
  is_color: boolean
  is_duplex: boolean
  toner_black_level?: number
  toner_cyan_level?: number
  toner_magenta_level?: number
  toner_yellow_level?: number
  total_pages?: number
  color_pages?: number
  mono_pages?: number
  error_message?: string
  last_seen?: string
}

// 耗材类型定义
interface Consumable {
  id: number
  printer_id: number
  consumable_type: string
  model_number?: string
  serial_number?: string
  manufacturer?: string
  level?: number
  capacity?: number
  pages_printed?: number
  install_date?: string
  expiry_date?: string
}

// 维护记录类型定义
interface MaintenanceRecord {
  id: number
  printer_id: number
  maintenance_type: string
  description?: string
  performed_by: string
  cost?: number
  parts_replaced?: any
  maintenance_date: string
  next_maintenance_date?: string
}

// 模拟数据 - 打印机
const mockPrinters: Printer[] = [
  {
    id: 1,
    name: 'HP LaserJet Pro M404dn',
    model: 'M404dn',
    serial_number: 'VNB3R12345',
    manufacturer: 'HP',
    location: '研发部',
    department: '研发部',
    ip_address: '*************',
    mac_address: '00:11:22:33:44:55',
    printer_type: 'laser',
    status: 'online',
    snmp_enabled: true,
    is_color: false,
    is_duplex: true,
    toner_black_level: 75,
    total_pages: 12500,
    last_seen: '2023-05-15T10:30:00Z'
  },
  {
    id: 2,
    name: 'Epson WorkForce Pro WF-C5790',
    model: 'WF-C5790',
    serial_number: 'X28G7654321',
    manufacturer: 'Epson',
    location: '财务部',
    department: '财务部',
    ip_address: '*************',
    mac_address: 'AA:BB:CC:DD:EE:FF',
    printer_type: 'inkjet',
    status: 'warning',
    snmp_enabled: true,
    is_color: true,
    is_duplex: true,
    toner_black_level: 15,
    toner_cyan_level: 45,
    toner_magenta_level: 60,
    toner_yellow_level: 30,
    total_pages: 8750,
    error_message: '墨水不足',
    last_seen: '2023-05-15T11:15:00Z'
  },
  {
    id: 3,
    name: 'Brother HL-L8360CDW',
    model: 'HL-L8360CDW',
    serial_number: 'U1P987654321',
    manufacturer: 'Brother',
    location: '市场部',
    department: '市场部',
    ip_address: '*************',
    mac_address: '11:22:33:44:55:66',
    printer_type: 'laser',
    status: 'offline',
    snmp_enabled: true,
    is_color: true,
    is_duplex: true,
    toner_black_level: 50,
    toner_cyan_level: 60,
    toner_magenta_level: 55,
    toner_yellow_level: 65,
    total_pages: 15200,
    last_seen: '2023-05-14T16:45:00Z'
  }
];

// 模拟数据 - 耗材
const mockConsumables: Consumable[] = [
  {
    id: 1,
    printer_id: 1,
    consumable_type: 'toner_black',
    model_number: 'CF258A',
    manufacturer: 'HP',
    level: 75,
    capacity: 3000,
    pages_printed: 750,
    install_date: '2023-03-10T00:00:00Z'
  },
  {
    id: 2,
    printer_id: 2,
    consumable_type: 'ink_black',
    model_number: 'T902XL',
    manufacturer: 'Epson',
    level: 15,
    capacity: 2500,
    pages_printed: 2125,
    install_date: '2023-02-15T00:00:00Z'
  },
  {
    id: 3,
    printer_id: 2,
    consumable_type: 'ink_cyan',
    model_number: 'T902XL',
    manufacturer: 'Epson',
    level: 45,
    capacity: 1800,
    pages_printed: 990,
    install_date: '2023-02-15T00:00:00Z'
  }
];

// 模拟数据 - 维护记录
const mockMaintenanceRecords: MaintenanceRecord[] = [
  {
    id: 1,
    printer_id: 1,
    maintenance_type: 'consumable_replacement',
    description: '更换黑色墨粉盒',
    performed_by: '张工',
    cost: 450,
    maintenance_date: '2023-03-10T00:00:00Z'
  },
  {
    id: 2,
    printer_id: 2,
    maintenance_type: 'cleaning',
    description: '清洁打印头',
    performed_by: '李工',
    maintenance_date: '2023-04-05T00:00:00Z'
  }
];

export default function PrinterManagementPage() {
  const [printers, setPrinters] = useState<Printer[]>(mockPrinters);
  const [consumables, setConsumables] = useState<Consumable[]>(mockConsumables);
  const [maintenanceRecords, setMaintenanceRecords] = useState<MaintenanceRecord[]>(mockMaintenanceRecords);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPrinters, setFilteredPrinters] = useState<Printer[]>(mockPrinters);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedPrinter, setSelectedPrinter] = useState<Printer | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { isOpen: isAddPrinterOpen, onOpen: onAddPrinterOpen, onClose: onAddPrinterClose } = useDisclosure();
  const { isOpen: isPrinterDetailsOpen, onOpen: onPrinterDetailsOpen, onClose: onPrinterDetailsClose } = useDisclosure();
  const { isOpen: isAddConsumableOpen, onOpen: onAddConsumableOpen, onClose: onAddConsumableClose } = useDisclosure();
  const { isOpen: isAddMaintenanceOpen, onOpen: onAddMaintenanceOpen, onClose: onAddMaintenanceClose } = useDisclosure();

  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const statBg = useColorModeValue('gray.50', 'gray.700');

  // 过滤打印机
  useEffect(() => {
    let filtered = printers;

    // 按状态过滤
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(printer => printer.status === selectedStatus);
    }

    // 按类型过滤
    if (selectedType !== 'all') {
      filtered = filtered.filter(printer => printer.printer_type === selectedType);
    }

    // 按搜索词过滤
    if (searchQuery) {
      filtered = filtered.filter(printer =>
        printer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        printer.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
        printer.location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        printer.department?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        printer.ip_address?.includes(searchQuery)
      );
    }

    setFilteredPrinters(filtered);
  }, [searchQuery, selectedStatus, selectedType, printers]);

  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green';
      case 'offline':
        return 'gray';
      case 'warning':
        return 'yellow';
      case 'error':
        return 'red';
      case 'maintenance':
        return 'blue';
      default:
        return 'gray';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'warning':
        return '警告';
      case 'error':
        return '错误';
      case 'maintenance':
        return '维护中';
      default:
        return status;
    }
  };

  // 获取打印机类型显示文本
  const getPrinterTypeText = (type: string) => {
    switch (type) {
      case 'laser':
        return '激光打印机';
      case 'inkjet':
        return '喷墨打印机';
      case 'dot_matrix':
        return '针式打印机';
      case 'thermal':
        return '热敏打印机';
      case 'multifunction':
        return '多功能一体机';
      case 'plotter':
        return '绘图仪';
      case 'label':
        return '标签打印机';
      case 'other':
        return '其他';
      default:
        return type;
    }
  };

  // 处理打印机操作
  const handlePrinterAction = (action: string, printer: Printer) => {
    let message = '';

    switch (action) {
      case 'refresh':
        message = `刷新打印机 ${printer.name} 状态`;
        // 这里应该调用API获取最新状态
        toast({
          title: "操作成功",
          description: message,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        break;
      case 'delete':
        message = `删除打印机 ${printer.name}`;
        // 从列表中移除
        setPrinters(printers.filter(p => p.id !== printer.id));
        toast({
          title: "操作成功",
          description: message,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        break;
    }
  };

  // 获取打印机的耗材
  const getPrinterConsumables = (printerId: number) => {
    return consumables.filter(c => c.printer_id === printerId);
  };

  // 获取打印机的维护记录
  const getPrinterMaintenanceRecords = (printerId: number) => {
    return maintenanceRecords.filter(m => m.printer_id === printerId);
  };

  // 刷新所有打印机状态
  const refreshAllPrinters = () => {
    setIsLoading(true);
    // 这里应该调用API获取所有打印机的最新状态
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "刷新成功",
        description: "所有打印机状态已更新",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    }, 1500);
  };

  return (
    <Box p={6}>
      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>打印机管理</Heading>
            <Text color="gray.500" fontSize="md">管理和监控网络打印机状态、耗材和维护</Text>
          </Box>
          <HStack spacing={4} mt={{ base: 4, md: 0 }}>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="blue"
              variant="outline"
              onClick={refreshAllPrinters}
              isLoading={isLoading}
            >
              刷新状态
            </Button>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="teal"
              onClick={onAddPrinterOpen}
            >
              添加打印机
            </Button>
          </HStack>
        </Flex>
      </Box>

      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <InputGroup maxW={{ base: '100%', md: '300px' }}>
            <InputLeftElement pointerEvents="none">
              <Box color="gray.500">
                <MagnifyingGlass size={20} />
              </Box>
            </InputLeftElement>
            <Input
              placeholder="搜索打印机..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              pl="40px"
            />
          </InputGroup>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">所有状态</option>
            <option value="online">在线</option>
            <option value="offline">离线</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
            <option value="maintenance">维护中</option>
          </Select>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
          >
            <option value="all">所有类型</option>
            <option value="laser">激光打印机</option>
            <option value="inkjet">喷墨打印机</option>
            <option value="dot_matrix">针式打印机</option>
            <option value="thermal">热敏打印机</option>
            <option value="multifunction">多功能一体机</option>
            <option value="plotter">绘图仪</option>
            <option value="label">标签打印机</option>
            <option value="other">其他</option>
          </Select>
        </Flex>
      </Box>

      <Box mb={4}>
        <Heading size="md" mb={3}>打印机状态概览</Heading>
        <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4} mb={6}>
          <Stat
            bg={useColorModeValue('green.50', 'green.900')}
            p={4}
            borderRadius="lg"
            boxShadow="sm"
            borderWidth="1px"
            borderColor={useColorModeValue('green.100', 'green.700')}
          >
            <StatLabel fontWeight="medium" color={useColorModeValue('green.600', 'green.200')}>在线</StatLabel>
            <StatNumber fontSize="3xl">{printers.filter(printer => printer.status === 'online').length}</StatNumber>
            <StatHelpText>
              <Text fontSize="xs" color={useColorModeValue('green.600', 'green.200')}>
                {Math.round(printers.filter(printer => printer.status === 'online').length / printers.length * 100)}% 的打印机
              </Text>
            </StatHelpText>
          </Stat>

          <Stat
            bg={useColorModeValue('yellow.50', 'yellow.900')}
            p={4}
            borderRadius="lg"
            boxShadow="sm"
            borderWidth="1px"
            borderColor={useColorModeValue('yellow.100', 'yellow.700')}
          >
            <StatLabel fontWeight="medium" color={useColorModeValue('yellow.600', 'yellow.200')}>警告</StatLabel>
            <StatNumber fontSize="3xl">{printers.filter(printer => printer.status === 'warning').length}</StatNumber>
            <StatHelpText>
              <Text fontSize="xs" color={useColorModeValue('yellow.600', 'yellow.200')}>
                需要关注
              </Text>
            </StatHelpText>
          </Stat>

          <Stat
            bg={useColorModeValue('red.50', 'red.900')}
            p={4}
            borderRadius="lg"
            boxShadow="sm"
            borderWidth="1px"
            borderColor={useColorModeValue('red.100', 'red.700')}
          >
            <StatLabel fontWeight="medium" color={useColorModeValue('red.600', 'red.200')}>错误</StatLabel>
            <StatNumber fontSize="3xl">{printers.filter(printer => printer.status === 'error').length}</StatNumber>
            <StatHelpText>
              <Text fontSize="xs" color={useColorModeValue('red.600', 'red.200')}>
                需要处理
              </Text>
            </StatHelpText>
          </Stat>

          <Stat
            bg={useColorModeValue('gray.50', 'gray.800')}
            p={4}
            borderRadius="lg"
            boxShadow="sm"
            borderWidth="1px"
            borderColor={useColorModeValue('gray.200', 'gray.700')}
          >
            <StatLabel fontWeight="medium" color={useColorModeValue('gray.600', 'gray.300')}>离线</StatLabel>
            <StatNumber fontSize="3xl">{printers.filter(printer => printer.status === 'offline').length}</StatNumber>
            <StatHelpText>
              <Text fontSize="xs" color={useColorModeValue('gray.600', 'gray.300')}>
                无法连接
              </Text>
            </StatHelpText>
          </Stat>
        </SimpleGrid>

        <Box
          borderWidth="1px"
          borderRadius="xl"
          overflow="hidden"
          bg={bgColor}
          borderColor={borderColor}
          boxShadow="md"
        >
          <Table variant="simple">
            <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
              <Tr>
                <Th py={4} width="20%">打印机名称</Th>
                <Th py={4} width="12%">状态</Th>
                <Th py={4} width="12%">类型</Th>
                <Th py={4} width="12%">位置</Th>
                <Th py={4} width="12%">IP地址</Th>
                <Th py={4} width="12%">墨盒/碳粉</Th>
                <Th py={4} width="20%">操作</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredPrinters.length > 0 ? (
                filteredPrinters.map(printer => (
                  <Tr
                    key={printer.id}
                    _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                    cursor="pointer"
                    onClick={() => {
                      setSelectedPrinter(printer);
                      onPrinterDetailsOpen();
                    }}
                  >
                    <Td>
                      <Flex align="center">
                        <Box
                          bg={useColorModeValue(
                            printer.is_color ? 'blue.50' : 'gray.50',
                            printer.is_color ? 'blue.900' : 'gray.700'
                          )}
                          p={1.5}
                          borderRadius="md"
                          mr={3}
                        >
                          <Printer
                            size={18}
                            weight="fill"
                            color={useColorModeValue(
                              printer.is_color ? 'blue.500' : 'gray.500',
                              printer.is_color ? 'blue.300' : 'gray.300'
                            )}
                          />
                        </Box>
                        <Box>
                          <Text fontWeight="medium">{printer.name}</Text>
                          <Text fontSize="xs" color="gray.500">{printer.model}</Text>
                        </Box>
                      </Flex>
                    </Td>
                    <Td>
                      <Badge
                        colorScheme={getStatusColor(printer.status)}
                        px={2}
                        py={1}
                        borderRadius="full"
                        variant="subtle"
                      >
                        {getStatusText(printer.status)}
                      </Badge>
                    </Td>
                    <Td>{getPrinterTypeText(printer.printer_type)}</Td>
                    <Td>{printer.location || '-'}</Td>
                    <Td>
                      <Text fontFamily="mono">{printer.ip_address || '-'}</Text>
                    </Td>
                    <Td>
                      {printer.toner_black_level !== undefined && (
                        <Tooltip label={`黑色: ${printer.toner_black_level}%`}>
                          <Box>
                            <Progress
                              value={printer.toner_black_level}
                              size="sm"
                              colorScheme={printer.toner_black_level < 20 ? 'red' : 'blue'}
                              borderRadius="full"
                            />
                          </Box>
                        </Tooltip>
                      )}
                    </Td>
                    <Td onClick={(e) => e.stopPropagation()}>
                      <HStack spacing={1}>
                        <Tooltip label="刷新状态">
                          <IconButton
                            icon={<ArrowsClockwise weight="bold" />}
                            aria-label="刷新状态"
                            size="sm"
                            variant="ghost"
                            colorScheme="blue"
                            onClick={() => handlePrinterAction('refresh', printer)}
                          />
                        </Tooltip>
                        <Tooltip label="编辑">
                          <IconButton
                            icon={<Pencil weight="bold" />}
                            aria-label="编辑"
                            size="sm"
                            variant="ghost"
                            colorScheme="green"
                          />
                        </Tooltip>
                        <Tooltip label="删除">
                          <IconButton
                            icon={<Trash weight="bold" />}
                            aria-label="删除"
                            size="sm"
                            variant="ghost"
                            colorScheme="red"
                            onClick={() => handlePrinterAction('delete', printer)}
                          />
                        </Tooltip>
                      </HStack>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={7} textAlign="center" py={8}>
                    <VStack spacing={3}>
                      <Box
                        p={3}
                        borderRadius="full"
                        bg={useColorModeValue('gray.100', 'gray.700')}
                      >
                        <MagnifyingGlass size={24} weight="duotone" />
                      </Box>
                      <Text>没有找到匹配的打印机</Text>
                      <Text fontSize="sm" color="gray.500">
                        尝试使用不同的搜索条件或清除筛选器
                      </Text>
                    </VStack>
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </Box>

      {/* 打印机详情模态框 */}
      {selectedPrinter && (
        <Modal isOpen={isPrinterDetailsOpen} onClose={onPrinterDetailsClose} size="4xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>
              <Flex align="center">
                <Box
                  bg={useColorModeValue(
                    selectedPrinter.is_color ? 'blue.50' : 'gray.50',
                    selectedPrinter.is_color ? 'blue.900' : 'gray.700'
                  )}
                  p={2}
                  borderRadius="md"
                  mr={3}
                >
                  <Printer
                    size={20}
                    weight="fill"
                    color={useColorModeValue(
                      selectedPrinter.is_color ? 'blue.500' : 'gray.500',
                      selectedPrinter.is_color ? 'blue.300' : 'gray.300'
                    )}
                  />
                </Box>
                <Box>
                  <Text>{selectedPrinter.name}</Text>
                  <Text fontSize="sm" color="gray.500">{selectedPrinter.model}</Text>
                </Box>
              </Flex>
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <Tabs colorScheme="teal" variant="enclosed">
                <TabList>
                  <Tab>基本信息</Tab>
                  <Tab>耗材</Tab>
                  <Tab>维护记录</Tab>
                </TabList>
                <TabPanels>
                  {/* 基本信息面板 */}
                  <TabPanel>
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                      <Box>
                        <Card mb={4}>
                          <CardBody>
                            <Heading size="sm" mb={4}>打印机状态</Heading>
                            <Flex align="center" mb={3}>
                              <Badge
                                colorScheme={getStatusColor(selectedPrinter.status)}
                                px={2}
                                py={1}
                                borderRadius="full"
                                variant="subtle"
                                mr={2}
                              >
                                {getStatusText(selectedPrinter.status)}
                              </Badge>
                              {selectedPrinter.error_message && (
                                <Text fontSize="sm" color="red.500">
                                  {selectedPrinter.error_message}
                                </Text>
                              )}
                            </Flex>
                            <Text fontSize="sm" color="gray.500">
                              最后在线时间: {selectedPrinter.last_seen ? new Date(selectedPrinter.last_seen).toLocaleString() : '未知'}
                            </Text>
                          </CardBody>
                        </Card>

                        <Card mb={4}>
                          <CardBody>
                            <Heading size="sm" mb={4}>打印机信息</Heading>
                            <SimpleGrid columns={2} spacing={4}>
                              <Box>
                                <Text fontSize="sm" color="gray.500">型号</Text>
                                <Text>{selectedPrinter.model}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">序列号</Text>
                                <Text>{selectedPrinter.serial_number || '-'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">制造商</Text>
                                <Text>{selectedPrinter.manufacturer || '-'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">类型</Text>
                                <Text>{getPrinterTypeText(selectedPrinter.printer_type)}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">彩色打印</Text>
                                <Text>{selectedPrinter.is_color ? '支持' : '不支持'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">双面打印</Text>
                                <Text>{selectedPrinter.is_duplex ? '支持' : '不支持'}</Text>
                              </Box>
                            </SimpleGrid>
                          </CardBody>
                        </Card>
                      </Box>

                      <Box>
                        <Card mb={4}>
                          <CardBody>
                            <Heading size="sm" mb={4}>网络信息</Heading>
                            <SimpleGrid columns={2} spacing={4}>
                              <Box>
                                <Text fontSize="sm" color="gray.500">IP地址</Text>
                                <Text fontFamily="mono">{selectedPrinter.ip_address || '-'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">MAC地址</Text>
                                <Text fontFamily="mono">{selectedPrinter.mac_address || '-'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">SNMP</Text>
                                <Text>{selectedPrinter.snmp_enabled ? '已启用' : '已禁用'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">位置</Text>
                                <Text>{selectedPrinter.location || '-'}</Text>
                              </Box>
                              <Box>
                                <Text fontSize="sm" color="gray.500">部门</Text>
                                <Text>{selectedPrinter.department || '-'}</Text>
                              </Box>
                            </SimpleGrid>
                          </CardBody>
                        </Card>

                        <Card>
                          <CardBody>
                            <Heading size="sm" mb={4}>使用统计</Heading>
                            <SimpleGrid columns={2} spacing={4}>
                              <Box>
                                <Text fontSize="sm" color="gray.500">总打印页数</Text>
                                <Text>{selectedPrinter.total_pages?.toLocaleString() || '-'}</Text>
                              </Box>
                              {selectedPrinter.is_color && (
                                <>
                                  <Box>
                                    <Text fontSize="sm" color="gray.500">彩色打印页数</Text>
                                    <Text>{selectedPrinter.color_pages?.toLocaleString() || '-'}</Text>
                                  </Box>
                                  <Box>
                                    <Text fontSize="sm" color="gray.500">黑白打印页数</Text>
                                    <Text>{selectedPrinter.mono_pages?.toLocaleString() || '-'}</Text>
                                  </Box>
                                </>
                              )}
                            </SimpleGrid>
                          </CardBody>
                        </Card>
                      </Box>
                    </SimpleGrid>

                    <Card mt={4}>
                      <CardBody>
                        <Heading size="sm" mb={4}>耗材状态</Heading>
                        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
                          {selectedPrinter.toner_black_level !== undefined && (
                            <Box>
                              <Flex justify="space-between" mb={1}>
                                <Text fontSize="sm">黑色墨粉</Text>
                                <Text fontSize="sm" fontWeight="bold">{selectedPrinter.toner_black_level}%</Text>
                              </Flex>
                              <Progress
                                value={selectedPrinter.toner_black_level}
                                size="lg"
                                colorScheme={selectedPrinter.toner_black_level < 20 ? 'red' : 'blue'}
                                borderRadius="full"
                              />
                            </Box>
                          )}
                          {selectedPrinter.toner_cyan_level !== undefined && (
                            <Box>
                              <Flex justify="space-between" mb={1}>
                                <Text fontSize="sm">青色墨粉</Text>
                                <Text fontSize="sm" fontWeight="bold">{selectedPrinter.toner_cyan_level}%</Text>
                              </Flex>
                              <Progress
                                value={selectedPrinter.toner_cyan_level}
                                size="lg"
                                colorScheme={selectedPrinter.toner_cyan_level < 20 ? 'red' : 'cyan'}
                                borderRadius="full"
                              />
                            </Box>
                          )}
                          {selectedPrinter.toner_magenta_level !== undefined && (
                            <Box>
                              <Flex justify="space-between" mb={1}>
                                <Text fontSize="sm">品红色墨粉</Text>
                                <Text fontSize="sm" fontWeight="bold">{selectedPrinter.toner_magenta_level}%</Text>
                              </Flex>
                              <Progress
                                value={selectedPrinter.toner_magenta_level}
                                size="lg"
                                colorScheme={selectedPrinter.toner_magenta_level < 20 ? 'red' : 'pink'}
                                borderRadius="full"
                              />
                            </Box>
                          )}
                          {selectedPrinter.toner_yellow_level !== undefined && (
                            <Box>
                              <Flex justify="space-between" mb={1}>
                                <Text fontSize="sm">黄色墨粉</Text>
                                <Text fontSize="sm" fontWeight="bold">{selectedPrinter.toner_yellow_level}%</Text>
                              </Flex>
                              <Progress
                                value={selectedPrinter.toner_yellow_level}
                                size="lg"
                                colorScheme={selectedPrinter.toner_yellow_level < 20 ? 'red' : 'yellow'}
                                borderRadius="full"
                              />
                            </Box>
                          )}
                        </SimpleGrid>
                      </CardBody>
                    </Card>
                  </TabPanel>

                  {/* 耗材面板 */}
                  <TabPanel>
                    <Flex justify="space-between" mb={4}>
                      <Heading size="md">耗材管理</Heading>
                      <Button
                        leftIcon={<Plus weight="bold" />}
                        colorScheme="teal"
                        size="sm"
                        onClick={onAddConsumableOpen}
                      >
                        添加耗材
                      </Button>
                    </Flex>

                    <Box
                      borderWidth="1px"
                      borderRadius="lg"
                      overflow="hidden"
                      bg={bgColor}
                      borderColor={borderColor}
                    >
                      <Table variant="simple">
                        <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                          <Tr>
                            <Th>耗材类型</Th>
                            <Th>型号</Th>
                            <Th>剩余量</Th>
                            <Th>安装日期</Th>
                            <Th>操作</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {getPrinterConsumables(selectedPrinter.id).length > 0 ? (
                            getPrinterConsumables(selectedPrinter.id).map(consumable => (
                              <Tr key={consumable.id}>
                                <Td>
                                  <Flex align="center">
                                    <Box
                                      bg={useColorModeValue('blue.50', 'blue.900')}
                                      p={1.5}
                                      borderRadius="md"
                                      mr={3}
                                    >
                                      <Drop size={16} weight="fill" />
                                    </Box>
                                    <Text>
                                      {consumable.consumable_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    </Text>
                                  </Flex>
                                </Td>
                                <Td>{consumable.model_number || '-'}</Td>
                                <Td>
                                  {consumable.level !== undefined ? (
                                    <Flex align="center">
                                      <Text mr={2}>{consumable.level}%</Text>
                                      <Progress
                                        value={consumable.level}
                                        size="sm"
                                        width="100px"
                                        colorScheme={consumable.level < 20 ? 'red' : 'blue'}
                                        borderRadius="full"
                                      />
                                    </Flex>
                                  ) : '-'}
                                </Td>
                                <Td>
                                  {consumable.install_date ? new Date(consumable.install_date).toLocaleDateString() : '-'}
                                </Td>
                                <Td>
                                  <HStack spacing={1}>
                                    <IconButton
                                      icon={<Pencil weight="bold" />}
                                      aria-label="编辑"
                                      size="sm"
                                      variant="ghost"
                                      colorScheme="blue"
                                    />
                                    <IconButton
                                      icon={<Trash weight="bold" />}
                                      aria-label="删除"
                                      size="sm"
                                      variant="ghost"
                                      colorScheme="red"
                                    />
                                  </HStack>
                                </Td>
                              </Tr>
                            ))
                          ) : (
                            <Tr>
                              <Td colSpan={5} textAlign="center" py={4}>
                                <Text>没有耗材记录</Text>
                              </Td>
                            </Tr>
                          )}
                        </Tbody>
                      </Table>
                    </Box>
                  </TabPanel>

                  {/* 维护记录面板 */}
                  <TabPanel>
                    <Flex justify="space-between" mb={4}>
                      <Heading size="md">维护记录</Heading>
                      <Button
                        leftIcon={<Plus weight="bold" />}
                        colorScheme="teal"
                        size="sm"
                        onClick={onAddMaintenanceOpen}
                      >
                        添加维护记录
                      </Button>
                    </Flex>

                    <Box
                      borderWidth="1px"
                      borderRadius="lg"
                      overflow="hidden"
                      bg={bgColor}
                      borderColor={borderColor}
                    >
                      <Table variant="simple">
                        <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                          <Tr>
                            <Th>维护类型</Th>
                            <Th>执行人</Th>
                            <Th>维护日期</Th>
                            <Th>描述</Th>
                            <Th>操作</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {getPrinterMaintenanceRecords(selectedPrinter.id).length > 0 ? (
                            getPrinterMaintenanceRecords(selectedPrinter.id).map(record => (
                              <Tr key={record.id}>
                                <Td>
                                  <Flex align="center">
                                    <Box
                                      bg={useColorModeValue('purple.50', 'purple.900')}
                                      p={1.5}
                                      borderRadius="md"
                                      mr={3}
                                    >
                                      <Wrench size={16} weight="fill" />
                                    </Box>
                                    <Text>
                                      {record.maintenance_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    </Text>
                                  </Flex>
                                </Td>
                                <Td>{record.performed_by}</Td>
                                <Td>
                                  {new Date(record.maintenance_date).toLocaleDateString()}
                                </Td>
                                <Td>{record.description || '-'}</Td>
                                <Td>
                                  <IconButton
                                    icon={<Trash weight="bold" />}
                                    aria-label="删除"
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="red"
                                  />
                                </Td>
                              </Tr>
                            ))
                          ) : (
                            <Tr>
                              <Td colSpan={5} textAlign="center" py={4}>
                                <Text>没有维护记录</Text>
                              </Td>
                            </Tr>
                          )}
                        </Tbody>
                      </Table>
                    </Box>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            </ModalBody>
            <ModalFooter>
              <Button onClick={onPrinterDetailsClose}>关闭</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* 添加打印机模态框 */}
      <Modal isOpen={isAddPrinterOpen} onClose={onAddPrinterClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>添加新打印机</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
              <FormControl isRequired>
                <FormLabel>打印机名称</FormLabel>
                <Input placeholder="输入打印机名称" />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>型号</FormLabel>
                <Input placeholder="输入打印机型号" />
              </FormControl>
              <FormControl>
                <FormLabel>序列号</FormLabel>
                <Input placeholder="输入序列号" />
              </FormControl>
              <FormControl>
                <FormLabel>制造商</FormLabel>
                <Input placeholder="输入制造商" />
              </FormControl>
              <FormControl isRequired>
                <FormLabel>打印机类型</FormLabel>
                <Select placeholder="选择打印机类型">
                  <option value="laser">激光打印机</option>
                  <option value="inkjet">喷墨打印机</option>
                  <option value="dot_matrix">针式打印机</option>
                  <option value="thermal">热敏打印机</option>
                  <option value="multifunction">多功能一体机</option>
                  <option value="plotter">绘图仪</option>
                  <option value="label">标签打印机</option>
                  <option value="other">其他</option>
                </Select>
              </FormControl>
              <FormControl>
                <FormLabel>IP地址</FormLabel>
                <Input placeholder="输入IP地址" />
              </FormControl>
              <FormControl>
                <FormLabel>MAC地址</FormLabel>
                <Input placeholder="输入MAC地址" />
              </FormControl>
              <FormControl>
                <FormLabel>位置</FormLabel>
                <Input placeholder="输入位置" />
              </FormControl>
              <FormControl>
                <FormLabel>部门</FormLabel>
                <Input placeholder="输入部门" />
              </FormControl>
              <FormControl>
                <FormLabel>SNMP启用</FormLabel>
                <Switch defaultChecked />
              </FormControl>
              <FormControl>
                <FormLabel>彩色打印</FormLabel>
                <Switch />
              </FormControl>
              <FormControl>
                <FormLabel>双面打印</FormLabel>
                <Switch />
              </FormControl>
            </SimpleGrid>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="teal" mr={3}>保存</Button>
            <Button onClick={onAddPrinterClose}>取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 添加耗材模态框 */}
      <Modal isOpen={isAddConsumableOpen} onClose={onAddConsumableClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>添加耗材</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <FormControl isRequired mb={4}>
              <FormLabel>耗材类型</FormLabel>
              <Select placeholder="选择耗材类型">
                <option value="toner_black">黑色墨粉</option>
                <option value="toner_cyan">青色墨粉</option>
                <option value="toner_magenta">品红色墨粉</option>
                <option value="toner_yellow">黄色墨粉</option>
                <option value="ink_black">黑色墨水</option>
                <option value="ink_cyan">青色墨水</option>
                <option value="ink_magenta">品红色墨水</option>
                <option value="ink_yellow">黄色墨水</option>
                <option value="drum">硒鼓</option>
                <option value="maintenance_kit">维护套件</option>
                <option value="waste_toner">废粉盒</option>
                <option value="fuser">定影器</option>
                <option value="transfer_belt">转印带</option>
                <option value="other">其他</option>
              </Select>
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>型号</FormLabel>
              <Input placeholder="输入型号" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>序列号</FormLabel>
              <Input placeholder="输入序列号" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>制造商</FormLabel>
              <Input placeholder="输入制造商" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>剩余量 (%)</FormLabel>
              <Input type="number" placeholder="输入剩余百分比" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>容量 (页数)</FormLabel>
              <Input type="number" placeholder="输入容量" />
            </FormControl>
            <FormControl>
              <FormLabel>安装日期</FormLabel>
              <Input type="date" />
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="teal" mr={3}>保存</Button>
            <Button onClick={onAddConsumableClose}>取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 添加维护记录模态框 */}
      <Modal isOpen={isAddMaintenanceOpen} onClose={onAddMaintenanceClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>添加维护记录</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <FormControl isRequired mb={4}>
              <FormLabel>维护类型</FormLabel>
              <Select placeholder="选择维护类型">
                <option value="scheduled">定期维护</option>
                <option value="repair">维修</option>
                <option value="consumable_replacement">更换耗材</option>
                <option value="cleaning">清洁</option>
                <option value="calibration">校准</option>
                <option value="other">其他</option>
              </Select>
            </FormControl>
            <FormControl isRequired mb={4}>
              <FormLabel>执行人</FormLabel>
              <Input placeholder="输入执行人" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>描述</FormLabel>
              <Input placeholder="输入维护描述" />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>维护日期</FormLabel>
              <Input type="date" defaultValue={new Date().toISOString().split('T')[0]} />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>费用</FormLabel>
              <Input type="number" placeholder="输入维护费用" />
            </FormControl>
            <FormControl>
              <FormLabel>下次维护日期</FormLabel>
              <Input type="date" />
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="teal" mr={3}>保存</Button>
            <Button onClick={onAddMaintenanceClose}>取消</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}