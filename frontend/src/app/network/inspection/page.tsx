'use client'
import { useState, useEffect } from 'react'
import { Card, Table, Button, message, Modal, Form, Input, Select } from 'antd'
import api from '../../../api'

interface Template {
  id: number
  name: string
  device_type: string
  content: string
  check_items: Array<{
    name: string
    threshold: string
  }>
}

interface Task {
  id: number
  template: number
  status: string
  created_at: string
}

const { Option } = Select

export default function InspectionPage() {
  const [templates, setTemplates] = useState([])
  const [tasks, setTasks] = useState([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchTemplates()
    fetchTasks()
  }, [])

  const fetchTemplates = async () => {
    try {
      const res = await api.get('/inspection/templates/')
      setTemplates(res.data)
    } catch (err) {
      message.error('获取模板失败')
    }
  }

  const fetchTasks = async () => {
    try {
      const res = await api.get('/inspection/tasks/')
      setTasks(res.data)
    } catch (err) {
      message.error('获取任务失败')
    }
  }

  const handleCreateTemplate = async (values: Omit<Template, 'id'>) => {
    try {
      await api.post('/inspection/templates/', values)
      message.success('创建成功')
      setIsModalVisible(false)
      form.resetFields()
      fetchTemplates()
    } catch (err) {
      message.error('创建失败')
    }
  }

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '设备类型',
      dataIndex: 'device_type',
      key: 'device_type',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Template) => (
        <Button type="link" onClick={() => {}}>
          创建任务
        </Button>
      ),
    },
  ]

  return (
    <div className="p-4">
      <Card title="巡检模板" className="mb-4">
        <Button 
          type="primary" 
          onClick={() => setIsModalVisible(true)}
          className="mb-4"
        >
          新建模板
        </Button>
        <Table 
          columns={columns} 
          dataSource={templates} 
          rowKey="id"
        />
      </Card>

      <Modal
        title="新建巡检模板"
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleCreateTemplate}>
          <Form.Item name="name" label="模板名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="device_type" label="设备类型" rules={[{ required: true }]}>
            <Select>
              <Option value="server">服务器</Option>
              <Option value="network">网络设备</Option>
              <Option value="storage">存储设备</Option>
            </Select>
          </Form.Item>
          <Form.Item name="content" label="描述">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
