'use client'

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Input,
  Text,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  HStack,
  Icon,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Badge,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Divider,
  useToast,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Checkbox,
  VStack,
  Progress,
  Textarea,
  Center,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  InputGroup,
  InputRightElement,
} from '@chakra-ui/react';
import {
  MagnifyingGlass,
  DotsThree,
  Plus,
  Download,
  PencilSimple,
  Trash,
  CloudArrowUp,
  Clock,
  Calendar,
  ArrowClockwise,
  CheckCircle,
  Warning,
  X,
  Gear,
  ArrowsClockwise,
  CaretRight,
  CaretLeft,
  FileText,
  FilePlus,
  Copy,
  FloppyDisk,
  ArrowsIn,
  ArrowsOut,
} from '@phosphor-icons/react';

// 定义设备类型
interface Device {
  id: string;
  name: string;
  ip: string;
  type: string;
  model: string;
  status: 'online' | 'offline' | 'warning';
  lastConfig?: string;
}

// 定义配置文件类型
interface ConfigFile {
  id: string;
  deviceId: string;
  deviceName: string;
  timestamp: string;
  size: string;
  version: string;
  status: 'active' | 'archived' | 'modified';
  filename: string;
}

// 模拟设备数据
const mockDevices: Device[] = [
  { id: '1', name: 'Core-Switch-01', ip: '***********', type: 'Huawei', model: 'S6720-30C-EI-24S-AC', status: 'online', lastConfig: '2023-05-15 14:30' },
  { id: '2', name: 'Core-Switch-02', ip: '***********', type: 'Huawei', model: 'S6720-30C-EI-24S-AC', status: 'online', lastConfig: '2023-05-15 14:35' },
  { id: '3', name: 'Access-Switch-01', ip: '************', type: 'Huawei', model: 'S5700-28C-EI', status: 'online', lastConfig: '2023-05-14 10:15' },
  { id: '4', name: 'Access-Switch-02', ip: '***********1', type: 'Huawei', model: 'S5700-28C-EI', status: 'warning', lastConfig: '2023-05-10 09:45' },
  { id: '5', name: 'Router-01', ip: '*************', type: 'Huawei', model: 'AR2220E', status: 'online', lastConfig: '2023-05-15 15:00' },
  { id: '6', name: 'Router-02', ip: '*************', type: 'Huawei', model: 'AR2220E', status: 'offline' },
];

// 模拟配置文件数据
const mockConfigFiles: ConfigFile[] = [
  { id: '1', deviceId: '1', deviceName: 'Core-Switch-01', timestamp: '2023-05-15 14:30', size: '45 KB', version: 'v1.0', status: 'active', filename: 'Core-Switch-01_20230515_143000.cfg' },
  { id: '2', deviceId: '1', deviceName: 'Core-Switch-01', timestamp: '2023-05-14 10:30', size: '44 KB', version: 'v0.9', status: 'archived', filename: 'Core-Switch-01_20230514_103000.cfg' },
  { id: '3', deviceId: '1', deviceName: 'Core-Switch-01', timestamp: '2023-05-13 09:15', size: '44 KB', version: 'v0.8', status: 'archived', filename: 'Core-Switch-01_20230513_091500.cfg' },
  { id: '4', deviceId: '2', deviceName: 'Core-Switch-02', timestamp: '2023-05-15 14:35', size: '44 KB', version: 'v1.0', status: 'active', filename: 'Core-Switch-02_20230515_143500.cfg' },
  { id: '5', deviceId: '2', deviceName: 'Core-Switch-02', timestamp: '2023-05-14 11:20', size: '43 KB', version: 'v0.9', status: 'archived', filename: 'Core-Switch-02_20230514_112000.cfg' },
  { id: '6', deviceId: '3', deviceName: 'Access-Switch-01', timestamp: '2023-05-14 10:15', size: '32 KB', version: 'v1.0', status: 'active', filename: 'Access-Switch-01_20230514_101500.cfg' },
  { id: '7', deviceId: '4', deviceName: 'Access-Switch-02', timestamp: '2023-05-10 09:45', size: '31 KB', version: 'v1.0', status: 'modified', filename: 'Access-Switch-02_20230510_094500.cfg' },
  { id: '8', deviceId: '5', deviceName: 'Router-01', timestamp: '2023-05-15 15:00', size: '28 KB', version: 'v1.0', status: 'active', filename: 'Router-01_20230515_150000.cfg' },
];

const ConfigManagementPage = () => {
  const [devices, setDevices] = useState<Device[]>(mockDevices);
  const [configFiles, setConfigFiles] = useState<ConfigFile[]>(mockConfigFiles);
  const [selectedDevice, setSelectedDevice] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [selectedConfig, setSelectedConfig] = useState<ConfigFile | null>(null);
  const [configContent, setConfigContent] = useState<string>('');

  // 模态框状态
  const { isOpen: isEditConfigOpen, onOpen: onEditConfigOpen, onClose: onEditConfigClose } = useDisclosure();
  const { isOpen: isViewConfigOpen, onOpen: onViewConfigOpen, onClose: onViewConfigClose } = useDisclosure();

  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 处理搜索
  const handleSearch = () => {
    if (!searchQuery) {
      setDevices(mockDevices);
      return;
    }

    const filtered = mockDevices.filter(device =>
      device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      device.ip.includes(searchQuery) ||
      device.model.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setDevices(filtered);
  };

  // 获取设备的配置文件
  const getDeviceConfigs = (deviceId: string) => {
    return configFiles.filter(config => config.deviceId === deviceId);
  };

  // 获取设备的活动配置
  const getActiveConfig = (deviceId: string) => {
    return configFiles.find(config => config.deviceId === deviceId && config.status === 'active');
  };

  // 处理查看配置
  const handleViewConfig = (config: ConfigFile) => {
    setSelectedConfig(config);

    // 模拟获取配置内容
    const sampleConfig = `#
 version 7.0
!
 sysname ${config.deviceName}
!
 clock timezone CST add 08:00:00
!
radius-server template default
!
domain default
!
role name level-0
!
role name level-1
!
role name level-2
!
role name level-3
!
role name level-4
!
role name level-5
!
role name level-6
!
role name level-7
!
role name level-8
!
role name level-9
!
role name level-10
!
role name level-11
!
role name level-12
!
role name level-13
!
role name level-14
!
role name level-15
!
vlan 1
!
vlan 10
 description MANAGEMENT
!
vlan 20
 description SERVERS
!
vlan 30
 description USERS
!
interface Vlanif1
 ip address 192.168.1.${config.deviceId} *************
!
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan 2 to 4094
!
interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan 2 to 4094
!
interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 30
!
interface GigabitEthernet0/0/4
 port link-type access
 port default vlan 30
!
interface NULL0
!
ip route-static 0.0.0.0 0.0.0.0 *************
!
user-interface con 0
 authentication-mode password
 idle-timeout 0 0
!
user-interface vty 0 4
 authentication-mode aaa
 user privilege level 15
 idle-timeout 0 0
!
ntp-service unicast-server ************0
!
return`;

    setConfigContent(sampleConfig);
    onViewConfigOpen();
  };

  // 处理编辑配置
  const handleEditConfig = (config: ConfigFile) => {
    setSelectedConfig(config);

    // 模拟获取配置内容
    const sampleConfig = `#
 version 7.0
!
 sysname ${config.deviceName}
!
 clock timezone CST add 08:00:00
!
radius-server template default
!
domain default
!
role name level-0
!
role name level-1
!
role name level-2
!
role name level-3
!
role name level-4
!
role name level-5
!
role name level-6
!
role name level-7
!
role name level-8
!
role name level-9
!
role name level-10
!
role name level-11
!
role name level-12
!
role name level-13
!
role name level-14
!
role name level-15
!
vlan 1
!
vlan 10
 description MANAGEMENT
!
vlan 20
 description SERVERS
!
vlan 30
 description USERS
!
interface Vlanif1
 ip address 192.168.1.${config.deviceId} *************
!
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan 2 to 4094
!
interface GigabitEthernet0/0/2
 port link-type trunk
 port trunk allow-pass vlan 2 to 4094
!
interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 30
!
interface GigabitEthernet0/0/4
 port link-type access
 port default vlan 30
!
interface NULL0
!
ip route-static 0.0.0.0 0.0.0.0 *************
!
user-interface con 0
 authentication-mode password
 idle-timeout 0 0
!
user-interface vty 0 4
 authentication-mode aaa
 user privilege level 15
 idle-timeout 0 0
!
ntp-service unicast-server ************0
!
return`;

    setConfigContent(sampleConfig);
    onEditConfigOpen();
  };

  // 保存编辑后的配置
  const handleSaveConfig = () => {
    if (!selectedConfig) return;

    // 在实际应用中，这里应该发送请求到后端保存配置
    toast({
      title: "配置已保存",
      description: `${selectedConfig.deviceName} 的配置已成功保存为新版本`,
      status: "success",
      duration: 3000,
      isClosable: true,
    });

    onEditConfigClose();
  };

  // 下载配置文件
  const handleDownloadConfig = (config: ConfigFile) => {
    // 实际应用中，这里应该是从服务器获取配置文件内容
    const element = document.createElement('a');
    const file = new Blob([configContent], {type: 'text/plain'});
    element.href = URL.createObjectURL(file);
    element.download = config.filename;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  return (
    <Container maxW="container.xl" py={8}>
      {/* 页面标题区域 */}
      <Box
        mb={8}
        p={6}
        borderRadius="xl"
        bg={bgColor}
        boxShadow="sm"
        borderWidth="1px"
        borderColor={borderColor}
        position="relative"
        overflow="hidden"
      >
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          height="6px"
          bgGradient="linear(to-r, blue.400, teal.500)"
        />

        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>网络设备配置管理</Heading>
            <Text color="gray.500" fontSize="md">管理和比较华为网络设备的配置文件</Text>
          </Box>
          <HStack spacing={4} mt={{ base: 4, md: 0 }}>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="teal"
              size="md"
              boxShadow="sm"
            >
              比较配置
            </Button>
            <Button
              leftIcon={<FilePlus weight="bold" />}
              variant="outline"
              colorScheme="blue"
              size="md"
            >
              创建配置模板
            </Button>
          </HStack>
        </Flex>
      </Box>

      {/* 主要内容区域 */}
      <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={6}>
        {/* 设备列表卡片 */}
        <Box
          borderRadius="xl"
          bg={bgColor}
          boxShadow="md"
          overflow="hidden"
          borderWidth="1px"
          borderColor={borderColor}
          height="fit-content"
        >
          <Box p={4} borderBottomWidth="1px" borderColor={borderColor} bg={useColorModeValue('gray.50', 'gray.700')}>
            <Flex justify="space-between" align="center">
              <Heading size="md" color={useColorModeValue('gray.700', 'white')}>设备列表</Heading>
              <Tooltip label="刷新设备列表">
                <IconButton
                  aria-label="刷新"
                  icon={<ArrowClockwise weight="bold" />}
                  size="sm"
                  variant="ghost"
                  colorScheme="blue"
                />
              </Tooltip>
            </Flex>
            <Flex mt={4} position="relative">
              <Input
                placeholder="搜索设备..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                pr="40px"
              />
              <IconButton
                aria-label="搜索"
                icon={<MagnifyingGlass weight="bold" />}
                position="absolute"
                right={0}
                top={0}
                borderLeftRadius={0}
                colorScheme="teal"
                onClick={handleSearch}
              />
            </Flex>
          </Box>

          <Box maxH="600px" overflowY="auto">
            {devices.map(device => (
              <Box
                key={device.id}
                p={4}
                borderBottomWidth="1px"
                borderColor={borderColor}
                bg={selectedDevice === device.id ? useColorModeValue('teal.50', 'rgba(49, 151, 149, 0.1)') : 'transparent'}
                _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                cursor="pointer"
                onClick={() => setSelectedDevice(device.id)}
                transition="background 0.2s"
              >
                <Flex justify="space-between" align="center">
                  <Box>
                    <Flex align="center">
                      <Badge
                        colorScheme={
                          device.status === 'online' ? 'green' :
                          device.status === 'warning' ? 'yellow' : 'red'
                        }
                        mr={2}
                        borderRadius="full"
                        px={2}
                        py={0.5}
                      >
                        {device.status === 'online' ? '在线' :
                         device.status === 'warning' ? '警告' : '离线'}
                      </Badge>
                      <Text fontWeight="bold">{device.name}</Text>
                    </Flex>
                    <Text fontSize="sm" color="gray.500" mt={1}>{device.ip} | {device.model}</Text>
                  </Box>
                  <Box>
                    {device.lastConfig ? (
                      <Text fontSize="xs" color="gray.500">
                        最近配置: {device.lastConfig}
                      </Text>
                    ) : (
                      <Badge colorScheme="red" variant="outline" fontSize="xs">
                        无配置
                      </Badge>
                    )}
                  </Box>
                </Flex>
              </Box>
            ))}
          </Box>
        </Box>

        {/* 配置管理区域 */}
        <Box
          gridColumn={{ base: "1", lg: "2 / span 2" }}
          borderRadius="xl"
          bg={bgColor}
          boxShadow="md"
          overflow="hidden"
          borderWidth="1px"
          borderColor={borderColor}
        >
          {selectedDevice ? (
            <>
              <Box p={4} borderBottomWidth="1px" borderColor={borderColor} bg={useColorModeValue('gray.50', 'gray.700')}>
                <Flex justify="space-between" align="center">
                  <Box>
                    <Heading size="md" color={useColorModeValue('gray.700', 'white')}>
                      {devices.find(d => d.id === selectedDevice)?.name} 配置管理
                    </Heading>
                    <Text fontSize="sm" color="gray.500" mt={1}>
                      管理设备的配置文件版本和历史记录
                    </Text>
                  </Box>
                  <HStack>
                    <Button
                      leftIcon={<CloudArrowUp weight="bold" />}
                      colorScheme="teal"
                      size="sm"
                      isDisabled={devices.find(d => d.id === selectedDevice)?.status === 'offline'}
                    >
                      获取最新配置
                    </Button>
                    <Button
                      leftIcon={<PencilSimple weight="bold" />}
                      colorScheme="blue"
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        const activeConfig = getActiveConfig(selectedDevice);
                        if (activeConfig) {
                          handleEditConfig(activeConfig);
                        } else {
                          toast({
                            title: "无法编辑配置",
                            description: "未找到当前活动配置",
                            status: "error",
                            duration: 3000,
                            isClosable: true,
                          });
                        }
                      }}
                    >
                      编辑配置
                    </Button>
                  </HStack>
                </Flex>
              </Box>

              <Box p={4}>
                <Tabs variant="soft-rounded" colorScheme="teal" size="md">
                  <TabList>
                    <Tab>配置文件</Tab>
                    <Tab>配置比较</Tab>
                    <Tab>部署历史</Tab>
                  </TabList>

                  <TabPanels>
                    <TabPanel px={0}>
                      <Table variant="simple" size="md">
                        <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                          <Tr>
                            <Th>文件名</Th>
                            <Th>版本</Th>
                            <Th>时间</Th>
                            <Th>大小</Th>
                            <Th>状态</Th>
                            <Th width="100px" textAlign="center">操作</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {getDeviceConfigs(selectedDevice).map(config => (
                            <Tr key={config.id}>
                              <Td fontWeight="medium">{config.filename}</Td>
                              <Td>{config.version}</Td>
                              <Td>{config.timestamp}</Td>
                              <Td>{config.size}</Td>
                              <Td>
                                <Badge
                                  colorScheme={
                                    config.status === 'active' ? 'green' :
                                    config.status === 'modified' ? 'orange' : 'gray'
                                  }
                                >
                                  {config.status === 'active' ? '当前活动' :
                                   config.status === 'modified' ? '已修改' : '历史版本'}
                                </Badge>
                              </Td>
                              <Td>
                                <HStack spacing={1} justify="center">
                                  <Tooltip label="查看配置">
                                    <IconButton
                                      icon={<MagnifyingGlass weight="bold" />}
                                      variant="ghost"
                                      size="sm"
                                      colorScheme="blue"
                                      aria-label="查看配置"
                                      onClick={() => handleViewConfig(config)}
                                    />
                                  </Tooltip>
                                  <Tooltip label="下载配置">
                                    <IconButton
                                      icon={<Download weight="bold" />}
                                      variant="ghost"
                                      size="sm"
                                      colorScheme="teal"
                                      aria-label="下载配置"
                                      onClick={() => handleDownloadConfig(config)}
                                    />
                                  </Tooltip>
                                  <Tooltip label="编辑配置">
                                    <IconButton
                                      icon={<PencilSimple weight="bold" />}
                                      variant="ghost"
                                      size="sm"
                                      colorScheme="orange"
                                      aria-label="编辑配置"
                                      onClick={() => handleEditConfig(config)}
                                    />
                                  </Tooltip>
                                  {config.status !== 'active' && (
                                    <Tooltip label="部署配置">
                                      <IconButton
                                        icon={<CloudArrowUp weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="green"
                                        aria-label="部署配置"
                                      />
                                    </Tooltip>
                                  )}
                                </HStack>
                              </Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </TabPanel>

                    <TabPanel px={0}>
                      <Box borderWidth="1px" borderColor={borderColor} borderRadius="md" overflow="hidden">
                        <Flex p={4} bg={useColorModeValue('gray.50', 'gray.700')} borderBottomWidth="1px" borderColor={borderColor}>
                          <Box flex="1">
                            <Text fontWeight="medium" mb={2}>选择基准配置</Text>
                            <Select
                              placeholder="选择基准配置文件"
                              size="md"
                              bg={useColorModeValue('white', 'gray.600')}
                            >
                              {getDeviceConfigs(selectedDevice).map(config => (
                                <option key={`base-${config.id}`} value={config.id}>
                                  {config.version} ({config.timestamp}) {config.status === 'active' ? '- 当前活动' : ''}
                                </option>
                              ))}
                            </Select>
                          </Box>
                          <Center px={4}>
                            <Icon as={ArrowsClockwise} boxSize={6} color="gray.500" />
                          </Center>
                          <Box flex="1">
                            <Text fontWeight="medium" mb={2}>选择比较配置</Text>
                            <Select
                              placeholder="选择比较配置文件"
                              size="md"
                              bg={useColorModeValue('white', 'gray.600')}
                            >
                              {getDeviceConfigs(selectedDevice).map(config => (
                                <option key={`compare-${config.id}`} value={config.id}>
                                  {config.version} ({config.timestamp}) {config.status === 'active' ? '- 当前活动' : ''}
                                </option>
                              ))}
                              <option value="other-device">其他设备的配置...</option>
                            </Select>
                          </Box>
                        </Flex>

                        <Box p={4}>
                          <Button
                            colorScheme="teal"
                            leftIcon={<ArrowsClockwise weight="bold" />}
                            size="md"
                            width="full"
                          >
                            比较配置
                          </Button>

                          <Box mt={6} p={4} borderWidth="1px" borderColor={borderColor} borderRadius="md" bg={useColorModeValue('gray.50', 'gray.700')}>
                            <Flex justify="space-between" align="center" mb={4}>
                              <Heading size="sm">配置差异</Heading>
                              <HStack>
                                <Button size="xs" leftIcon={<ArrowsIn weight="bold" />} variant="ghost">
                                  折叠相同项
                                </Button>
                                <Button size="xs" leftIcon={<Download weight="bold" />} variant="ghost">
                                  导出差异
                                </Button>
                              </HStack>
                            </Flex>

                            <Box
                              p={4}
                              borderRadius="md"
                              bg={useColorModeValue('white', 'gray.800')}
                              height="300px"
                              overflowY="auto"
                              fontFamily="mono"
                              fontSize="sm"
                              position="relative"
                            >
                              <Flex
                                position="absolute"
                                top={0}
                                left={0}
                                right={0}
                                p={2}
                                bg={useColorModeValue('gray.100', 'gray.700')}
                                borderBottomWidth="1px"
                                borderColor={borderColor}
                                justify="space-between"
                              >
                                <Text fontWeight="bold">基准配置: v1.0 (2023-05-15)</Text>
                                <Text fontWeight="bold">比较配置: v0.9 (2023-05-14)</Text>
                              </Flex>

                              <Box pt={10}>
                                <Text color="gray.500" mb={4}>选择两个配置文件进行比较...</Text>

                                {/* 这里将显示配置差异，示例： */}
                                <Box mb={2}>
                                  <Text color="green.500">+ interface GigabitEthernet0/0/5</Text>
                                  <Text color="green.500">+  port link-type access</Text>
                                  <Text color="green.500">+  port default vlan 30</Text>
                                </Box>

                                <Box mb={2}>
                                  <Text color="red.500">- interface GigabitEthernet0/0/6</Text>
                                  <Text color="red.500">-  port link-type trunk</Text>
                                  <Text color="red.500">-  port trunk allow-pass vlan 2 to 4094</Text>
                                </Box>

                                <Box mb={2}>
                                  <Text color="gray.500"> interface Vlanif1</Text>
                                  <Text color="red.500">-  ip address *********** *************</Text>
                                  <Text color="green.500">+  ip address ************ *************</Text>
                                </Box>
                              </Box>
                            </Box>

                            <SimpleGrid columns={2} spacing={4} mt={4}>
                              <Box p={3} borderRadius="md" bg={useColorModeValue('green.50', 'green.900')} borderWidth="1px" borderColor={useColorModeValue('green.100', 'green.700')}>
                                <Flex align="center" mb={1}>
                                  <Box w="10px" h="10px" borderRadius="full" bg="green.500" mr={2} />
                                  <Text fontWeight="medium" color={useColorModeValue('green.700', 'green.300')}>新增配置</Text>
                                </Flex>
                                <Text fontSize="sm" color={useColorModeValue('green.600', 'green.400')}>3 项</Text>
                              </Box>

                              <Box p={3} borderRadius="md" bg={useColorModeValue('red.50', 'red.900')} borderWidth="1px" borderColor={useColorModeValue('red.100', 'red.700')}>
                                <Flex align="center" mb={1}>
                                  <Box w="10px" h="10px" borderRadius="full" bg="red.500" mr={2} />
                                  <Text fontWeight="medium" color={useColorModeValue('red.700', 'red.300')}>删除配置</Text>
                                </Flex>
                                <Text fontSize="sm" color={useColorModeValue('red.600', 'red.400')}>4 项</Text>
                              </Box>
                            </SimpleGrid>
                          </Box>
                        </Box>
                      </Box>
                    </TabPanel>

                    <TabPanel px={0}>
                      <Box borderWidth="1px" borderColor={borderColor} borderRadius="md" overflow="hidden">
                        <Flex p={4} bg={useColorModeValue('gray.50', 'gray.700')} borderBottomWidth="1px" borderColor={borderColor} justify="space-between" align="center">
                          <Heading size="sm">配置部署历史</Heading>
                          <HStack>
                            <Select
                              placeholder="全部状态"
                              size="sm"
                              width="150px"
                              bg={useColorModeValue('white', 'gray.600')}
                            >
                              <option value="success">成功</option>
                              <option value="failed">失败</option>
                              <option value="pending">进行中</option>
                            </Select>
                            <IconButton
                              aria-label="刷新"
                              icon={<ArrowClockwise weight="bold" />}
                              size="sm"
                              variant="ghost"
                              colorScheme="blue"
                            />
                          </HStack>
                        </Flex>

                        <Box>
                          <Table variant="simple" size="md">
                            <Thead bg={useColorModeValue('gray.50', 'gray.700')}>
                              <Tr>
                                <Th>部署时间</Th>
                                <Th>配置版本</Th>
                                <Th>操作人员</Th>
                                <Th>状态</Th>
                                <Th>备注</Th>
                                <Th width="100px" textAlign="center">操作</Th>
                              </Tr>
                            </Thead>
                            <Tbody>
                              <Tr>
                                <Td>2023-05-15 14:30</Td>
                                <Td>v1.0</Td>
                                <Td>admin</Td>
                                <Td>
                                  <Badge colorScheme="green">成功</Badge>
                                </Td>
                                <Td>初始配置部署</Td>
                                <Td>
                                  <HStack spacing={1} justify="center">
                                    <Tooltip label="查看详情">
                                      <IconButton
                                        icon={<MagnifyingGlass weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="blue"
                                        aria-label="查看详情"
                                      />
                                    </Tooltip>
                                    <Tooltip label="回滚到此版本">
                                      <IconButton
                                        icon={<ArrowClockwise weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="orange"
                                        aria-label="回滚到此版本"
                                      />
                                    </Tooltip>
                                  </HStack>
                                </Td>
                              </Tr>
                              <Tr>
                                <Td>2023-05-14 11:20</Td>
                                <Td>v0.9</Td>
                                <Td>admin</Td>
                                <Td>
                                  <Badge colorScheme="green">成功</Badge>
                                </Td>
                                <Td>更新VLAN配置</Td>
                                <Td>
                                  <HStack spacing={1} justify="center">
                                    <Tooltip label="查看详情">
                                      <IconButton
                                        icon={<MagnifyingGlass weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="blue"
                                        aria-label="查看详情"
                                      />
                                    </Tooltip>
                                    <Tooltip label="回滚到此版本">
                                      <IconButton
                                        icon={<ArrowClockwise weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="orange"
                                        aria-label="回滚到此版本"
                                      />
                                    </Tooltip>
                                  </HStack>
                                </Td>
                              </Tr>
                              <Tr>
                                <Td>2023-05-13 09:15</Td>
                                <Td>v0.8</Td>
                                <Td>operator</Td>
                                <Td>
                                  <Badge colorScheme="red">失败</Badge>
                                </Td>
                                <Td>尝试更新路由配置</Td>
                                <Td>
                                  <HStack spacing={1} justify="center">
                                    <Tooltip label="查看详情">
                                      <IconButton
                                        icon={<MagnifyingGlass weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="blue"
                                        aria-label="查看详情"
                                      />
                                    </Tooltip>
                                    <Tooltip label="重试部署">
                                      <IconButton
                                        icon={<ArrowClockwise weight="bold" />}
                                        variant="ghost"
                                        size="sm"
                                        colorScheme="teal"
                                        aria-label="重试部署"
                                      />
                                    </Tooltip>
                                  </HStack>
                                </Td>
                              </Tr>
                            </Tbody>
                          </Table>
                        </Box>

                        <Box p={4} borderTopWidth="1px" borderColor={borderColor} bg={useColorModeValue('gray.50', 'gray.700')}>
                          <Flex justify="space-between" align="center">
                            <Text fontSize="sm" color="gray.500">
                              显示 1-3 条，共 3 条记录
                            </Text>
                            <HStack>
                              <Button size="sm" leftIcon={<CaretLeft />} variant="ghost" isDisabled>
                                上一页
                              </Button>
                              <Button size="sm" variant="solid" colorScheme="teal">
                                1
                              </Button>
                              <Button size="sm" rightIcon={<CaretRight />} variant="ghost" isDisabled>
                                下一页
                              </Button>
                            </HStack>
                          </Flex>
                        </Box>
                      </Box>

                      <Box mt={6} p={4} borderWidth="1px" borderColor={borderColor} borderRadius="md" bg={useColorModeValue('gray.50', 'gray.700')}>
                        <Heading size="sm" mb={4}>部署新配置</Heading>

                        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                          <Box>
                            <Text fontWeight="medium" mb={2}>选择配置版本</Text>
                            <Select
                              placeholder="选择要部署的配置版本"
                              bg={useColorModeValue('white', 'gray.600')}
                            >
                              {getDeviceConfigs(selectedDevice)
                                .filter(config => config.status !== 'active')
                                .map(config => (
                                <option key={`deploy-${config.id}`} value={config.id}>
                                  {config.version} ({config.timestamp})
                                </option>
                              ))}
                            </Select>
                          </Box>

                          <Box>
                            <Text fontWeight="medium" mb={2}>部署备注</Text>
                            <Input
                              placeholder="输入部署说明..."
                              bg={useColorModeValue('white', 'gray.600')}
                            />
                          </Box>
                        </SimpleGrid>

                        <Box mt={4}>
                          <Checkbox colorScheme="teal" defaultChecked mb={4}>
                            部署前备份当前配置
                          </Checkbox>

                          <Button
                            colorScheme="teal"
                            leftIcon={<CloudArrowUp weight="bold" />}
                            isDisabled={devices.find(d => d.id === selectedDevice)?.status === 'offline'}
                            width="full"
                          >
                            部署配置
                          </Button>
                        </Box>
                      </Box>
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </Box>
            </>
          ) : (
            <Flex
              direction="column"
              align="center"
              justify="center"
              p={10}
              height="100%"
              minH="400px"
              color="gray.500"
            >
              <Icon as={FileText} boxSize={16} mb={4} />
              <Heading size="md" mb={2}>选择设备查看配置</Heading>
              <Text textAlign="center">
                从左侧列表中选择一个设备来管理其配置文件
              </Text>
            </Flex>
          )}
        </Box>
      </SimpleGrid>

      {/* 查看配置模态框 */}
      <Modal
        isOpen={isViewConfigOpen}
        onClose={onViewConfigClose}
        size="xl"
        motionPreset="slideInBottom"
        scrollBehavior="inside"
      >
        <ModalOverlay bg="blackAlpha.300" backdropFilter="blur(5px)" />
        <ModalContent
          borderRadius="xl"
          boxShadow="xl"
          bg={useColorModeValue('white', 'gray.800')}
          maxW={{ base: "95%", md: "800px" }}
        >
          <ModalHeader
            borderBottomWidth="1px"
            borderColor={borderColor}
            py={4}
            bg={useColorModeValue('gray.50', 'gray.700')}
            borderTopRadius="xl"
          >
            <Flex justify="space-between" align="center">
              <Box>
                <Heading size="md" color={useColorModeValue('gray.700', 'white')}>
                  {selectedConfig?.deviceName} 配置文件
                </Heading>
                <HStack spacing={2} mt={1}>
                  <Icon as={Clock} color="gray.500" boxSize={4} />
                  <Text fontSize="sm" color="gray.500" fontWeight="normal">
                    {selectedConfig?.timestamp}
                  </Text>
                  <Text fontSize="sm" color="gray.500">•</Text>
                  <Text fontSize="sm" color="gray.500" fontWeight="normal">
                    {selectedConfig?.size}
                  </Text>
                </HStack>
              </Box>
              <Badge
                colorScheme={
                  selectedConfig?.status === 'active' ? 'green' :
                  selectedConfig?.status === 'modified' ? 'orange' : 'gray'
                }
                px={2}
                py={1}
                borderRadius="full"
                fontSize="sm"
              >
                {selectedConfig?.status === 'active' ? '当前活动' :
                 selectedConfig?.status === 'modified' ? '已修改' : '历史版本'}
              </Badge>
            </Flex>
          </ModalHeader>
          <ModalCloseButton size="lg" top={3} right={3} />

          <ModalBody p={0}>
            <Flex
              bg={useColorModeValue('gray.50', 'gray.900')}
              p={2}
              borderBottomWidth="1px"
              borderColor={borderColor}
              justify="space-between"
              align="center"
            >
              <Text fontSize="sm" fontWeight="medium" color={useColorModeValue('gray.600', 'gray.400')}>
                文件名: <Text as="span" fontFamily="mono" fontWeight="normal">{selectedConfig?.filename}</Text>
              </Text>
              <HStack>
                <Tooltip label="复制全部">
                  <IconButton
                    aria-label="复制全部"
                    icon={<Copy weight="bold" />}
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      navigator.clipboard.writeText(configContent);
                      toast({
                        title: "已复制到剪贴板",
                        status: "success",
                        duration: 2000,
                        isClosable: true,
                      });
                    }}
                  />
                </Tooltip>
                <Select
                  size="sm"
                  width="120px"
                  variant="filled"
                  defaultValue="dark"
                  borderRadius="md"
                  bg={useColorModeValue('white', 'gray.700')}
                >
                  <option value="dark">深色主题</option>
                  <option value="light">浅色主题</option>
                </Select>
              </HStack>
            </Flex>

            <Box
              bg="gray.900"
              color="green.300"
              p={4}
              fontFamily="mono"
              fontSize="sm"
              overflowX="auto"
              whiteSpace="pre"
              minHeight="400px"
              maxHeight="60vh"
              overflowY="auto"
              css={{
                '&::-webkit-scrollbar': {
                  width: '8px',
                  height: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'rgba(0,0,0,0.2)',
                }
              }}
            >
              {configContent}
            </Box>
          </ModalBody>

          <ModalFooter
            borderTopWidth="1px"
            borderColor={borderColor}
            py={3}
            bg={useColorModeValue('gray.50', 'gray.700')}
          >
            <HStack spacing={3}>
              <Button
                colorScheme="blue"
                leftIcon={<Download weight="bold" />}
                onClick={() => selectedConfig && handleDownloadConfig(selectedConfig)}
                size="md"
                boxShadow="sm"
              >
                下载配置
              </Button>
              <Button
                colorScheme="orange"
                leftIcon={<PencilSimple weight="bold" />}
                onClick={() => {
                  onViewConfigClose();
                  selectedConfig && handleEditConfig(selectedConfig);
                }}
                size="md"
                boxShadow="sm"
              >
                编辑配置
              </Button>
              <Button
                variant="ghost"
                onClick={onViewConfigClose}
                size="md"
              >
                关闭
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 编辑配置模态框 */}
      <Modal
        isOpen={isEditConfigOpen}
        onClose={onEditConfigClose}
        size="xl"
        motionPreset="slideInBottom"
        scrollBehavior="inside"
      >
        <ModalOverlay bg="blackAlpha.300" backdropFilter="blur(5px)" />
        <ModalContent
          borderRadius="xl"
          boxShadow="xl"
          bg={useColorModeValue('white', 'gray.800')}
          maxW={{ base: "95%", md: "800px" }}
        >
          <ModalHeader
            borderBottomWidth="1px"
            borderColor={borderColor}
            py={4}
            bg={useColorModeValue('gray.50', 'gray.700')}
            borderTopRadius="xl"
          >
            <Flex justify="space-between" align="center">
              <Box>
                <Heading size="md" color={useColorModeValue('gray.700', 'white')}>
                  编辑 {selectedConfig?.deviceName} 配置
                </Heading>
                <Text fontSize="sm" color="gray.500" mt={1}>
                  修改配置后将创建新的配置版本
                </Text>
              </Box>
              <Badge
                colorScheme="orange"
                px={2}
                py={1}
                borderRadius="full"
                fontSize="sm"
              >
                编辑中
              </Badge>
            </Flex>
          </ModalHeader>
          <ModalCloseButton size="lg" top={3} right={3} />

          <ModalBody p={0}>
            <Flex
              bg={useColorModeValue('gray.50', 'gray.900')}
              p={2}
              borderBottomWidth="1px"
              borderColor={borderColor}
              justify="space-between"
              align="center"
            >
              <Text fontSize="sm" fontWeight="medium" color={useColorModeValue('gray.600', 'gray.400')}>
                基于版本: <Text as="span" fontWeight="normal">{selectedConfig?.version}</Text>
              </Text>
              <HStack>
                <Tooltip label="撤销更改">
                  <IconButton
                    aria-label="撤销更改"
                    icon={<ArrowClockwise weight="bold" />}
                    size="sm"
                    variant="ghost"
                  />
                </Tooltip>
                <Select
                  size="sm"
                  width="120px"
                  variant="filled"
                  defaultValue="dark"
                  borderRadius="md"
                  bg={useColorModeValue('white', 'gray.700')}
                >
                  <option value="dark">深色主题</option>
                  <option value="light">浅色主题</option>
                </Select>
              </HStack>
            </Flex>

            <Textarea
              value={configContent}
              onChange={(e) => setConfigContent(e.target.value)}
              fontFamily="mono"
              fontSize="sm"
              bg="gray.900"
              color="green.300"
              border="none"
              p={4}
              minHeight="500px"
              resize="vertical"
              spellCheck="false"
              _focus={{
                boxShadow: 'none',
                border: 'none',
              }}
              css={{
                '&::-webkit-scrollbar': {
                  width: '8px',
                  height: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'rgba(0,0,0,0.2)',
                }
              }}
            />
          </ModalBody>

          <ModalFooter
            borderTopWidth="1px"
            borderColor={borderColor}
            py={3}
            bg={useColorModeValue('gray.50', 'gray.700')}
          >
            <HStack spacing={3}>
              <Button
                colorScheme="teal"
                leftIcon={<FloppyDisk weight="bold" />}
                onClick={handleSaveConfig}
                size="md"
                boxShadow="sm"
              >
                保存配置
              </Button>
              <Button
                colorScheme="blue"
                leftIcon={<CloudArrowUp weight="bold" />}
                size="md"
                boxShadow="sm"
              >
                保存并部署
              </Button>
              <Button
                variant="ghost"
                onClick={onEditConfigClose}
                size="md"
              >
                取消
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  );
};

export default ConfigManagementPage;
