'use client'

import React, { useState, useEffect } from 'react'
import {
  <PERSON>ifiHigh,
  ArrowsClockwise,
  Users,
  ChartLine,
  Gauge,
  Warning,
  MagnifyingGlass,
  Desktop
} from '@phosphor-icons/react'
import dynamic from 'next/dynamic'
import { PageContainer, PageHeader, RefreshButton } from '@/components/ui/page-container'
import { StatCard } from '@/components/ui/stat-card'
import { DataTable } from '@/components/ui/data-table'
import { DashboardCard, InfoCard } from '@/components/ui/dashboard-card'
import { MetricProgress } from '@/components/ui/metric-progress'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

// 动态导入Chart.js相关组件，避免SSR问题
const Line = dynamic(
  () => import('react-chartjs-2').then((mod) => mod.Line),
  { ssr: false }
)

// 在客户端组件中初始化Chart.js
const initChartJS = () => {
  if (typeof window !== 'undefined') {
    import('chart.js').then(({ Chart, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend }) => {
      Chart.register(
        CategoryScale,
        LinearScale,
        PointElement,
        LineElement,
        Title,
        Tooltip,
        Legend
      )
    })
  }
}

// 无线设备类型定义
interface WirelessDevice {
  id: string
  device_id: string
  name: string
  model: string
  manufacturer: string
  location: string
  floor: string
  ip_address: string
  mac_address: string
  status: 'online' | 'offline' | 'warning'
  firmware_version: string
  uptime: number
  last_seen: string
  position_x: number
  position_y: number
}

// 无线设备数据类型定义
interface WirelessDeviceData {
  id: number
  device_id: string
  timestamp: string
  status: string
  cpu_usage: number
  memory_usage: number
  temperature: number
  clients_count: number
  clients_2g: number
  clients_5g: number
  channel_2g: number
  channel_5g: number
  signal_strength: number
  noise_level: number
  tx_power: number
  rx_bytes: number
  tx_bytes: number
}

// 无线客户端类型定义
interface WirelessClient {
  id: number
  device_id: string
  mac_address: string
  ip_address: string
  hostname: string
  connected_at: string
  last_seen: string
  signal_strength: number
  frequency_band: string
  tx_rate: number
  rx_rate: number
  tx_bytes: number
  rx_bytes: number
}

// 模拟数据 - 无线设备
const mockDevices: WirelessDevice[] = [
  {
    id: '1',
    device_id: 'ap-01',
    name: 'AP-1F-01',
    model: 'Cisco AIR-AP3802I-H-K9',
    manufacturer: 'Cisco',
    location: '1楼大厅',
    floor: '1F',
    ip_address: '*************',
    mac_address: '00:11:22:33:44:55',
    status: 'online',
    firmware_version: '**********',
    uptime: 1209600, // 14天
    last_seen: '2023-06-15T14:30:00Z',
    position_x: 150,
    position_y: 100,
  },
  {
    id: '2',
    device_id: 'ap-02',
    name: 'AP-1F-02',
    model: 'Cisco AIR-AP3802I-H-K9',
    manufacturer: 'Cisco',
    location: '1楼会议室',
    floor: '1F',
    ip_address: '*************',
    mac_address: '00:11:22:33:44:56',
    status: 'online',
    firmware_version: '**********',
    uptime: 864000, // 10天
    last_seen: '2023-06-15T14:30:00Z',
    position_x: 250,
    position_y: 150,
  },
  {
    id: '3',
    device_id: 'ap-03',
    name: 'AP-2F-01',
    model: 'Cisco AIR-AP3802I-H-K9',
    manufacturer: 'Cisco',
    location: '2楼办公区',
    floor: '2F',
    ip_address: '*************',
    mac_address: '00:11:22:33:44:57',
    status: 'warning',
    firmware_version: '**********',
    uptime: 432000, // 5天
    last_seen: '2023-06-15T14:30:00Z',
    position_x: 150,
    position_y: 100,
  },
  {
    id: '4',
    device_id: 'ap-04',
    name: 'AP-2F-02',
    model: 'Cisco AIR-AP3802I-H-K9',
    manufacturer: 'Cisco',
    location: '2楼会议室',
    floor: '2F',
    ip_address: '*************',
    mac_address: '00:11:22:33:44:58',
    status: 'offline',
    firmware_version: '**********',
    uptime: 0,
    last_seen: '2023-06-10T14:30:00Z',
    position_x: 250,
    position_y: 150,
  },
];

// 模拟数据 - 无线设备数据
const mockDeviceData: WirelessDeviceData[] = [
  {
    id: 1,
    device_id: 'ap-01',
    timestamp: '2023-06-15T14:30:00Z',
    status: 'online',
    cpu_usage: 25,
    memory_usage: 45,
    temperature: 42,
    clients_count: 12,
    clients_2g: 5,
    clients_5g: 7,
    channel_2g: 6,
    channel_5g: 36,
    signal_strength: 85,
    noise_level: 10,
    tx_power: 20,
    rx_bytes: 1024000,
    tx_bytes: 512000,
  },
  {
    id: 2,
    device_id: 'ap-02',
    timestamp: '2023-06-15T14:30:00Z',
    status: 'online',
    cpu_usage: 18,
    memory_usage: 38,
    temperature: 39,
    clients_count: 8,
    clients_2g: 3,
    clients_5g: 5,
    channel_2g: 11,
    channel_5g: 40,
    signal_strength: 90,
    noise_level: 8,
    tx_power: 20,
    rx_bytes: 768000,
    tx_bytes: 384000,
  },
  {
    id: 3,
    device_id: 'ap-03',
    timestamp: '2023-06-15T14:30:00Z',
    status: 'warning',
    cpu_usage: 72,
    memory_usage: 81,
    temperature: 58,
    clients_count: 15,
    clients_2g: 8,
    clients_5g: 7,
    channel_2g: 1,
    channel_5g: 44,
    signal_strength: 65,
    noise_level: 15,
    tx_power: 20,
    rx_bytes: 1536000,
    tx_bytes: 768000,
  },
];

// 模拟数据 - 无线客户端
const mockClients: WirelessClient[] = [
  {
    id: 1,
    device_id: 'ap-01',
    mac_address: 'AA:BB:CC:DD:EE:FF',
    ip_address: '*************',
    hostname: 'user-laptop-1',
    connected_at: '2023-06-15T10:30:00Z',
    last_seen: '2023-06-15T14:30:00Z',
    signal_strength: 75,
    frequency_band: '5GHz',
    tx_rate: 433,
    rx_rate: 433,
    tx_bytes: 102400,
    rx_bytes: 204800,
  },
  {
    id: 2,
    device_id: 'ap-01',
    mac_address: 'AA:BB:CC:DD:EE:FE',
    ip_address: '*************',
    hostname: 'user-phone-1',
    connected_at: '2023-06-15T11:30:00Z',
    last_seen: '2023-06-15T14:30:00Z',
    signal_strength: 65,
    frequency_band: '2.4GHz',
    tx_rate: 144,
    rx_rate: 144,
    tx_bytes: 51200,
    rx_bytes: 102400,
  },
];

export default function WirelessMonitoringPageNew() {
  // 初始化Chart.js
  useEffect(() => {
    initChartJS()
  }, [])

  const [devices, setDevices] = useState<WirelessDevice[]>(mockDevices);
  const [deviceData, setDeviceData] = useState<WirelessDeviceData[]>(mockDeviceData);
  const [clients, setClients] = useState<WirelessClient[]>(mockClients);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFloor, setSelectedFloor] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedDevice, setSelectedDevice] = useState<WirelessDevice | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('24h');

  // 过滤设备
  const filteredDevices = devices.filter(device => {
    // 按楼层过滤
    if (selectedFloor !== 'all' && device.floor !== selectedFloor) {
      return false;
    }

    // 按状态过滤
    if (selectedStatus !== 'all' && device.status !== selectedStatus) {
      return false;
    }

    // 按搜索词过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        device.name.toLowerCase().includes(query) ||
        device.ip_address.includes(query) ||
        device.location.toLowerCase().includes(query) ||
        device.mac_address.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // 获取设备数据
  const getDeviceData = (deviceId: string) => {
    return deviceData.find(data => data.device_id === deviceId);
  };

  // 获取设备客户端
  const getDeviceClients = (deviceId: string) => {
    return clients.filter(client => client.device_id === deviceId);
  };

  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green';
      case 'offline':
        return 'gray';
      case 'warning':
        return 'amber';
      default:
        return 'gray';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'warning':
        return '警告';
      default:
        return status;
    }
  };

  // 格式化时间
  const formatUptime = (seconds: number) => {
    if (seconds === 0) return '离线';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天 ${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  // 刷新数据
  const refreshData = () => {
    setIsLoading(true);

    // 模拟API调用
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // 生成图表数据
  const generateChartData = (deviceId: string, dataType: string) => {
    // 这里应该从API获取历史数据，现在使用模拟数据
    const labels = Array.from({ length: 24 }, (_, i) => `${i}:00`);

    let data: number[] = [];
    let label = '';
    let borderColor = '';

    switch (dataType) {
      case 'clients':
        data = Array.from({ length: 24 }, () => Math.floor(Math.random() * 20) + 5);
        label = '客户端数量';
        borderColor = 'rgb(75, 192, 192)';
        break;
      case 'cpu':
        data = Array.from({ length: 24 }, () => Math.floor(Math.random() * 50) + 10);
        label = 'CPU使用率 (%)';
        borderColor = 'rgb(255, 99, 132)';
        break;
      case 'memory':
        data = Array.from({ length: 24 }, () => Math.floor(Math.random() * 40) + 30);
        label = '内存使用率 (%)';
        borderColor = 'rgb(54, 162, 235)';
        break;
      case 'traffic':
        data = Array.from({ length: 24 }, () => Math.floor(Math.random() * 100) + 50);
        label = '流量 (Mbps)';
        borderColor = 'rgb(153, 102, 255)';
        break;
    }

    return {
      labels,
      datasets: [
        {
          label,
          data,
          borderColor,
          backgroundColor: borderColor.replace('rgb', 'rgba').replace(')', ', 0.2)'),
          tension: 0.3,
        },
      ],
    };
  };

  return (
    <PageContainer>
      <PageHeader
        title="无线网络监控"
        description="监控和管理无线接入点和客户端"
      >
        <RefreshButton
          onClick={refreshData}
          isLoading={isLoading}
          text="刷新数据"
          loadingText="刷新中..."
        />
      </PageHeader>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="总接入点"
          value={devices.length}
          icon={<WifiHigh size={20} weight="fill" />}
          helpText={`${devices.filter(d => d.status === 'online').length} 在线`}
          colorScheme="teal"
        />

        <StatCard
          title="总客户端"
          value={clients.length}
          icon={<Users size={20} weight="fill" />}
          helpText={`${clients.filter(c => c.frequency_band === '5GHz').length} 5GHz`}
          colorScheme="blue"
        />

        <StatCard
          title="平均信号强度"
          value={`${Math.round(deviceData.reduce((sum, data) => sum + data.signal_strength, 0) / deviceData.length)}%`}
          icon={<Gauge size={20} weight="fill" />}
          colorScheme="purple"
        />

        <StatCard
          title="警告设备"
          value={devices.filter(d => d.status === 'warning').length}
          icon={<Warning size={20} weight="fill" />}
          helpText={`${devices.filter(d => d.status === 'offline').length} 离线设备`}
          colorScheme="amber"
        />
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative w-full sm:w-64">
          <MagnifyingGlass className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" size={16} />
          <Input
            placeholder="搜索设备..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select value={selectedFloor} onValueChange={setSelectedFloor}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="选择楼层" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有楼层</SelectItem>
            <SelectItem value="1F">1楼</SelectItem>
            <SelectItem value="2F">2楼</SelectItem>
            <SelectItem value="3F">3楼</SelectItem>
          </SelectContent>
        </Select>
        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有状态</SelectItem>
            <SelectItem value="online">在线</SelectItem>
            <SelectItem value="offline">离线</SelectItem>
            <SelectItem value="warning">警告</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {selectedDevice ? (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">设备详情: {selectedDevice.name}</h2>
            <Button
              variant="outline"
              onClick={() => setSelectedDevice(null)}
            >
              返回列表
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DashboardCard
              title="基本信息"
              icon={<WifiHigh size={20} weight="fill" />}
            >
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">设备ID</p>
                  <p>{selectedDevice.device_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">状态</p>
                  <Badge className={`bg-${getStatusColor(selectedDevice.status)}-100 text-${getStatusColor(selectedDevice.status)}-700`}>
                    {getStatusText(selectedDevice.status)}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">型号</p>
                  <p>{selectedDevice.model}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">制造商</p>
                  <p>{selectedDevice.manufacturer}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">位置</p>
                  <p>{selectedDevice.location}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">楼层</p>
                  <p>{selectedDevice.floor}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">IP地址</p>
                  <p>{selectedDevice.ip_address}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">MAC地址</p>
                  <p>{selectedDevice.mac_address}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">固件版本</p>
                  <p>{selectedDevice.firmware_version}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">运行时间</p>
                  <p>{formatUptime(selectedDevice.uptime)}</p>
                </div>
              </div>
            </DashboardCard>

            <DashboardCard
              title="性能指标"
              icon={<Gauge size={20} weight="fill" />}
            >
              {getDeviceData(selectedDevice.device_id) ? (
                <div className="space-y-4">
                  <MetricProgress
                    label="CPU使用率"
                    value={getDeviceData(selectedDevice.device_id)?.cpu_usage || 0}
                    colorScheme={getDeviceData(selectedDevice.device_id)?.cpu_usage || 0 > 80 ? "red" : "blue"}
                  />
                  <MetricProgress
                    label="内存使用率"
                    value={getDeviceData(selectedDevice.device_id)?.memory_usage || 0}
                    colorScheme={getDeviceData(selectedDevice.device_id)?.memory_usage || 0 > 80 ? "red" : "green"}
                  />
                  <MetricProgress
                    label="温度"
                    value={getDeviceData(selectedDevice.device_id)?.temperature || 0}
                    max={80}
                    colorScheme={getDeviceData(selectedDevice.device_id)?.temperature || 0 > 60 ? "red" : "green"}
                  />
                  <div className="border-t pt-4 mt-4"></div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">客户端数量</p>
                      <p>{getDeviceData(selectedDevice.device_id)?.clients_count}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">信号强度</p>
                      <p>{getDeviceData(selectedDevice.device_id)?.signal_strength}%</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">2.4GHz客户端</p>
                      <p>{getDeviceData(selectedDevice.device_id)?.clients_2g}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">5GHz客户端</p>
                      <p>{getDeviceData(selectedDevice.device_id)?.clients_5g}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">2.4GHz信道</p>
                      <p>{getDeviceData(selectedDevice.device_id)?.channel_2g}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">5GHz信道</p>
                      <p>{getDeviceData(selectedDevice.device_id)?.channel_5g}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <p>无性能数据</p>
              )}
            </DashboardCard>
          </div>

          <Tabs defaultValue="clients">
            <TabsList>
              <TabsTrigger value="clients">客户端</TabsTrigger>
              <TabsTrigger value="trends">历史趋势</TabsTrigger>
            </TabsList>
            <TabsContent value="clients" className="pt-4">
              <DataTable
                data={getDeviceClients(selectedDevice.device_id)}
                columns={[
                  {
                    key: 'hostname',
                    header: '主机名',
                    cell: (client) => client.hostname || '未知'
                  },
                  {
                    key: 'ip_address',
                    header: 'IP地址',
                    cell: (client) => client.ip_address
                  },
                  {
                    key: 'mac_address',
                    header: 'MAC地址',
                    cell: (client) => client.mac_address
                  },
                  {
                    key: 'signal_strength',
                    header: '信号强度',
                    cell: (client) => `${client.signal_strength}%`
                  },
                  {
                    key: 'frequency_band',
                    header: '频段',
                    cell: (client) => client.frequency_band
                  },
                  {
                    key: 'connected_at',
                    header: '连接时间',
                    cell: (client) => new Date(client.connected_at).toLocaleString()
                  }
                ]}
                emptyMessage="无客户端连接"
              />
            </TabsContent>
            <TabsContent value="trends" className="pt-4">
              <div className="flex items-center gap-4 mb-4">
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="选择时间范围" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="24h">最近24小时</SelectItem>
                    <SelectItem value="7d">最近7天</SelectItem>
                    <SelectItem value="30d">最近30天</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <DashboardCard title="客户端数量趋势">
                  <div className="h-[300px]">
                    <Line
                      data={generateChartData(selectedDevice.device_id, 'clients')}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true
                          }
                        }
                      }}
                    />
                  </div>
                </DashboardCard>
                <DashboardCard title="CPU使用率趋势">
                  <div className="h-[300px]">
                    <Line
                      data={generateChartData(selectedDevice.device_id, 'cpu')}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true,
                            max: 100
                          }
                        }
                      }}
                    />
                  </div>
                </DashboardCard>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <DataTable
          data={filteredDevices}
          columns={[
            {
              key: 'name',
              header: '名称',
              cell: (device: WirelessDevice | null) => (
                <div className="flex items-center">
                  <div className="p-1.5 rounded-md bg-teal-50 dark:bg-teal-900 mr-3">
                    <WifiHigh size={18} className="text-teal-500 dark:text-teal-300" />
                  </div>
                  <span className="font-medium">{device?.name || '-'}</span>
                </div>
              )
            },
            {
              key: 'status',
              header: '状态',
              cell: (device: WirelessDevice | null) => (
                <Badge className={`bg-${getStatusColor(device?.status || 'offline')}-100 text-${getStatusColor(device?.status || 'offline')}-700 dark:bg-${getStatusColor(device?.status || 'offline')}-900/30 dark:text-${getStatusColor(device?.status || 'offline')}-400`}>
                  {getStatusText(device?.status || 'offline')}
                </Badge>
              )
            },
            {
              key: 'location',
              header: '位置',
              cell: (device: WirelessDevice | null) => device?.location || '-'
            },
            {
              key: 'ip_address',
              header: 'IP地址',
              cell: (device: WirelessDevice | null) => <span className="font-mono">{device?.ip_address || '-'}</span>
            },
            {
              key: 'clients',
              header: '客户端',
              cell: (device: WirelessDevice | null) => (
                device?.status !== 'offline' ? (
                  <div className="flex items-center">
                    <Users size={16} className="mr-1" />
                    <span>{device?.device_id ? getDeviceData(device.device_id)?.clients_count || 0 : 0}</span>
                  </div>
                ) : (
                  <span className="text-muted-foreground">-</span>
                )
              )
            },
            {
              key: 'signal',
              header: '信号',
              cell: (device: WirelessDevice | null) => (
                device?.status !== 'offline' ? (
                  <span>{device?.device_id ? getDeviceData(device.device_id)?.signal_strength || 0 : 0}%</span>
                ) : (
                  <span className="text-muted-foreground">-</span>
                )
              )
            }
          ]}
          onRowClick={setSelectedDevice}
          emptyMessage="没有找到匹配的设备"
        />
      )}
    </PageContainer>
  )
}
