'use client'

import { useState, useEffect } from 'react'
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  HStack,
  VStack,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Tooltip,
  Divider,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Progress,
  Tag,
  TagLabel,
  TagLeftIcon,
  Switch,
  Select,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
} from '@chakra-ui/react'
import {
  MagnifyingGlass,
  Plus,
  Gear,
  ArrowsClockwise,
  Warning,
  CheckCircle,
  XCircle,
  Buildings,
  MapPin,
  Pencil,
  ClockCounterClockwise,
  Article,
  Wrench,
  ChartLine,
  Pulse,
  WifiHigh,
  Globe,
  Gauge,
  CaretDown,
  DotsThreeVertical,
  Trash,
  Desktop,
  Broadcast,
  ArrowsDownUp,
  ArrowUp,
  ArrowDown,
  Clock,
  Calendar,
  Funnel,
} from '@phosphor-icons/react'
import axios from 'axios'

// 网络设备类型定义
interface NetworkDevice {
  id: number
  name: string
  model?: string
  serial_number?: string
  manufacturer?: string
  location?: string
  ip_address: string
  mac_address?: string
  device_type: string
  status: 'online' | 'offline' | 'warning' | 'error'
  snmp_enabled: boolean
  firmware_version?: string
  uptime?: number
  cpu_usage?: number
  memory_usage?: number
  temperature?: number
  last_seen?: string
}

// 网络接口类型定义
interface NetworkInterface {
  id: number
  device_id: number
  name: string
  description?: string
  interface_type?: string
  mac_address?: string
  ip_address?: string
  subnet_mask?: string
  speed?: number
  duplex?: string
  admin_status: boolean
  operational_status: boolean
  mtu?: number
  vlan?: number
  in_octets?: number
  out_octets?: number
  in_packets?: number
  out_packets?: number
  in_errors?: number
  out_errors?: number
  in_discards?: number
  out_discards?: number
  last_updated?: string
}

// 带宽记录类型定义
interface BandwidthRecord {
  id: number
  device_id: number
  interface_id: number
  timestamp: string
  in_octets: number
  out_octets: number
  in_packets: number
  out_packets: number
  in_errors: number
  out_errors: number
  in_discards: number
  out_discards: number
  in_bandwidth: number
  out_bandwidth: number
  in_utilization: number
  out_utilization: number
}

// 流量阈值类型定义
interface TrafficThreshold {
  id: number
  name: string
  description?: string
  device_id?: number
  interface_id?: number
  in_bandwidth_warning?: number
  in_bandwidth_critical?: number
  out_bandwidth_warning?: number
  out_bandwidth_critical?: number
  in_utilization_warning?: number
  in_utilization_critical?: number
  out_utilization_warning?: number
  out_utilization_critical?: number
  notify_email: boolean
  notify_sms: boolean
  notify_webhook: boolean
  notification_cooldown: number
  created_at: string
  updated_at: string
}

// 模拟数据 - 网络设备
const mockDevices: NetworkDevice[] = [
  {
    id: 1,
    name: 'Core Switch',
    model: 'Cisco Catalyst 9300',
    serial_number: 'FDO2303X0NE',
    manufacturer: 'Cisco',
    location: '数据中心',
    ip_address: '***********',
    mac_address: '00:1A:2B:3C:4D:5E',
    device_type: 'switch',
    status: 'online',
    snmp_enabled: true,
    firmware_version: '17.3.4',
    uptime: 2592000, // 30天（秒）
    cpu_usage: 15,
    memory_usage: 45,
    temperature: 38,
    last_seen: '2023-05-15T10:30:00Z'
  },
  {
    id: 2,
    name: 'Edge Router',
    model: 'Cisco ISR 4451',
    serial_number: 'FTX2145A0BC',
    manufacturer: 'Cisco',
    location: '网络机房',
    ip_address: '***********',
    mac_address: 'AA:BB:CC:DD:EE:FF',
    device_type: 'router',
    status: 'warning',
    snmp_enabled: true,
    firmware_version: '16.9.5',
    uptime: 1296000, // 15天（秒）
    cpu_usage: 65,
    memory_usage: 72,
    temperature: 42,
    last_seen: '2023-05-15T11:15:00Z'
  },
  {
    id: 3,
    name: 'Access Switch Floor 3',
    model: 'Cisco Catalyst 2960',
    serial_number: 'FOC2046V0AB',
    manufacturer: 'Cisco',
    location: '3楼网络间',
    ip_address: '***********',
    mac_address: '11:22:33:44:55:66',
    device_type: 'switch',
    status: 'offline',
    snmp_enabled: true,
    firmware_version: '15.2(4)E7',
    last_seen: '2023-05-14T16:45:00Z'
  }
];

// 模拟数据 - 网络接口
const mockInterfaces: NetworkInterface[] = [
  {
    id: 1,
    device_id: 1,
    name: 'GigabitEthernet1/0/1',
    description: 'Uplink to ISP',
    interface_type: 'gigabit_ethernet',
    mac_address: '00:1A:2B:3C:4D:5F',
    ip_address: '***********',
    subnet_mask: '*************',
    speed: 1000000000, // 1Gbps
    duplex: 'full',
    admin_status: true,
    operational_status: true,
    mtu: 1500,
    vlan: 1,
    in_octets: 1500000000,
    out_octets: 500000000,
    in_packets: 1000000,
    out_packets: 500000,
    in_errors: 0,
    out_errors: 0,
    in_discards: 0,
    out_discards: 0,
    last_updated: '2023-05-15T10:30:00Z'
  },
  {
    id: 2,
    device_id: 1,
    name: 'GigabitEthernet1/0/2',
    description: 'Link to Edge Router',
    interface_type: 'gigabit_ethernet',
    mac_address: '00:1A:2B:3C:4D:60',
    speed: 1000000000, // 1Gbps
    duplex: 'full',
    admin_status: true,
    operational_status: true,
    mtu: 1500,
    vlan: 1,
    in_octets: 800000000,
    out_octets: 1200000000,
    in_packets: 600000,
    out_packets: 900000,
    in_errors: 0,
    out_errors: 0,
    in_discards: 0,
    out_discards: 0,
    last_updated: '2023-05-15T10:30:00Z'
  },
  {
    id: 3,
    device_id: 2,
    name: 'GigabitEthernet0/0/0',
    description: 'WAN Interface',
    interface_type: 'gigabit_ethernet',
    mac_address: 'AA:BB:CC:DD:EE:F0',
    ip_address: '***********',
    subnet_mask: '***************',
    speed: 1000000000, // 1Gbps
    duplex: 'full',
    admin_status: true,
    operational_status: true,
    mtu: 1500,
    in_octets: 2500000000,
    out_octets: 1800000000,
    in_packets: 1500000,
    out_packets: 1200000,
    in_errors: 120,
    out_errors: 0,
    in_discards: 50,
    out_discards: 0,
    last_updated: '2023-05-15T11:15:00Z'
  }
];

// 模拟带宽数据
const generateMockBandwidthData = (interfaceId: number, hours: number = 24): BandwidthRecord[] => {
  const records: BandwidthRecord[] = [];
  const now = new Date();
  const baseInBandwidth = Math.random() * 500000000; // 基础入方向带宽 (500Mbps)
  const baseOutBandwidth = Math.random() * 300000000; // 基础出方向带宽 (300Mbps)

  // 查找接口
  const networkInterface = mockInterfaces.find(i => i.id === interfaceId);
  if (!networkInterface) return records;

  // 查找设备
  const device = mockDevices.find(d => d.id === networkInterface.device_id);
  if (!device) return records;

  // 生成过去n小时的数据，每5分钟一个点
  for (let i = 0; i < hours * 12; i++) {
    const timestamp = new Date(now.getTime() - (i * 5 * 60 * 1000));

    // 生成带有波动的带宽数据
    const timeOfDay = timestamp.getHours();
    let multiplier = 1;

    // 模拟工作时间带宽高峰
    if (timeOfDay >= 9 && timeOfDay <= 18) {
      multiplier = 1.5;
    }
    // 模拟午休时间带宽下降
    if (timeOfDay >= 12 && timeOfDay <= 13) {
      multiplier = 0.8;
    }
    // 模拟夜间带宽低谷
    if (timeOfDay >= 0 && timeOfDay <= 6) {
      multiplier = 0.3;
    }

    // 添加随机波动
    const randomFactor = 0.7 + Math.random() * 0.6;

    const inBandwidth = baseInBandwidth * multiplier * randomFactor;
    const outBandwidth = baseOutBandwidth * multiplier * randomFactor;

    // 计算利用率 (假设接口速度为1Gbps)
    const speed = networkInterface.speed || 1000000000;
    const inUtilization = (inBandwidth / speed) * 100;
    const outUtilization = (outBandwidth / speed) * 100;

    records.push({
      id: i + 1,
      device_id: device.id,
      interface_id: interfaceId,
      timestamp: timestamp.toISOString(),
      in_octets: Math.round(inBandwidth / 8 * 300), // 5分钟内的字节数
      out_octets: Math.round(outBandwidth / 8 * 300),
      in_packets: Math.round(inBandwidth / 8 / 1000), // 假设平均包大小为1000字节
      out_packets: Math.round(outBandwidth / 8 / 1000),
      in_errors: Math.round(Math.random() * 5), // 随机错误数
      out_errors: Math.round(Math.random() * 2),
      in_discards: Math.round(Math.random() * 10), // 随机丢弃数
      out_discards: Math.round(Math.random() * 5),
      in_bandwidth: inBandwidth,
      out_bandwidth: outBandwidth,
      in_utilization: inUtilization,
      out_utilization: outUtilization
    });
  }

  // 按时间排序
  return records.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
};

export default function BandwidthMonitoringPage() {
  const [devices, setDevices] = useState<NetworkDevice[]>(mockDevices);
  const [interfaces, setInterfaces] = useState<NetworkInterface[]>(mockInterfaces);
  const [selectedDevice, setSelectedDevice] = useState<NetworkDevice | null>(null);
  const [selectedInterface, setSelectedInterface] = useState<NetworkInterface | null>(null);
  const [bandwidthData, setBandwidthData] = useState<BandwidthRecord[]>([]);
  const [timeRange, setTimeRange] = useState<string>('24h');
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredDevices, setFilteredDevices] = useState<NetworkDevice[]>(mockDevices);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  const { isOpen: isAddDeviceOpen, onOpen: onAddDeviceOpen, onClose: onAddDeviceClose } = useDisclosure();
  const { isOpen: isAddInterfaceOpen, onOpen: onAddInterfaceOpen, onClose: onAddInterfaceClose } = useDisclosure();
  const { isOpen: isAddThresholdOpen, onOpen: onAddThresholdOpen, onClose: onAddThresholdClose } = useDisclosure();

  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const statBg = useColorModeValue('gray.50', 'gray.700');

  // 过滤设备
  useEffect(() => {
    let filtered = devices;

    // 按状态过滤
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(device => device.status === selectedStatus);
    }

    // 按类型过滤
    if (selectedType !== 'all') {
      filtered = filtered.filter(device => device.device_type === selectedType);
    }

    // 按搜索词过滤
    if (searchQuery) {
      filtered = filtered.filter(device =>
        device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.model?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.ip_address.includes(searchQuery)
      );
    }

    setFilteredDevices(filtered);
  }, [searchQuery, selectedStatus, selectedType, devices]);

  // 加载带宽数据
  useEffect(() => {
    if (selectedInterface) {
      setIsLoading(true);
      // 模拟API调用延迟
      setTimeout(() => {
        const hours = timeRange === '24h' ? 24 : timeRange === '7d' ? 168 : timeRange === '30d' ? 720 : 24;
        const data = generateMockBandwidthData(selectedInterface.id, hours);
        setBandwidthData(data);
        setIsLoading(false);
      }, 1000);
    }
  }, [selectedInterface, timeRange]);

  // 获取设备的接口
  const getDeviceInterfaces = (deviceId: number) => {
    return interfaces.filter(i => i.device_id === deviceId);
  };

  // 获取状态徽章颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green';
      case 'offline':
        return 'gray';
      case 'warning':
        return 'yellow';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'warning':
        return '警告';
      case 'error':
        return '错误';
      default:
        return status;
    }
  };

  // 获取设备类型显示文本
  const getDeviceTypeText = (type: string) => {
    switch (type) {
      case 'router':
        return '路由器';
      case 'switch':
        return '交换机';
      case 'firewall':
        return '防火墙';
      case 'load_balancer':
        return '负载均衡器';
      case 'wireless_ap':
        return '无线接入点';
      case 'gateway':
        return '网关';
      case 'other':
        return '其他';
      default:
        return type;
    }
  };

  // 获取设备类型图标
  const getDeviceTypeIcon = (type: string) => {
    switch (type) {
      case 'router':
        return <Globe weight="fill" />;
      case 'switch':
        return <Broadcast weight="fill" />;
      case 'firewall':
        return <Desktop weight="fill" />;
      case 'load_balancer':
        return <ArrowsDownUp weight="fill" />;
      case 'wireless_ap':
        return <WifiHigh weight="fill" />;
      case 'gateway':
        return <Broadcast weight="fill" />;
      default:
        return <Gear weight="fill" />;
    }
  };

  // 格式化带宽显示
  const formatBandwidth = (bps: number) => {
    if (bps >= 1000000000) {
      return `${(bps / 1000000000).toFixed(2)} Gbps`;
    } else if (bps >= 1000000) {
      return `${(bps / 1000000).toFixed(2)} Mbps`;
    } else if (bps >= 1000) {
      return `${(bps / 1000).toFixed(2)} Kbps`;
    } else {
      return `${bps.toFixed(2)} bps`;
    }
  };

  // 处理设备操作
  const handleDeviceAction = (action: string, device: NetworkDevice) => {
    let message = '';

    switch (action) {
      case 'refresh':
        message = `刷新设备 ${device.name} 状态`;
        // 这里应该调用API获取最新状态
        toast({
          title: "操作成功",
          description: message,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        break;
      case 'delete':
        message = `删除设备 ${device.name}`;
        // 从列表中移除
        setDevices(devices.filter(d => d.id !== device.id));
        toast({
          title: "操作成功",
          description: message,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        break;
    }
  };

  // 刷新所有设备状态
  const refreshAllDevices = () => {
    setIsLoading(true);
    // 这里应该调用API获取所有设备的最新状态
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "刷新成功",
        description: "所有设备状态已更新",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    }, 1500);
  };

  // 选择设备
  const handleSelectDevice = (device: NetworkDevice) => {
    setSelectedDevice(device);
    setSelectedInterface(null);
    setBandwidthData([]);
  };

  // 选择接口
  const handleSelectInterface = (networkInterface: NetworkInterface) => {
    setSelectedInterface(networkInterface);
  };

  return (
    <Box p={6}>
      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} justify="space-between" align={{ base: 'flex-start', md: 'center' }}>
          <Box>
            <Heading size="lg" mb={2} color={useColorModeValue('gray.700', 'white')}>网络带宽监控</Heading>
            <Text color="gray.500" fontSize="md">监控网络设备接口带宽使用情况和流量趋势</Text>
          </Box>
          <HStack spacing={4} mt={{ base: 4, md: 0 }}>
            <Button
              leftIcon={<ArrowsClockwise weight="bold" />}
              colorScheme="blue"
              variant="outline"
              onClick={refreshAllDevices}
              isLoading={isLoading}
            >
              刷新状态
            </Button>
            <Button
              leftIcon={<Plus weight="bold" />}
              colorScheme="teal"
              onClick={onAddDeviceOpen}
            >
              添加设备
            </Button>
          </HStack>
        </Flex>
      </Box>

      <Box mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <InputGroup maxW={{ base: '100%', md: '300px' }}>
            <InputLeftElement pointerEvents="none">
              <Box color="gray.500">
                <MagnifyingGlass size={20} />
              </Box>
            </InputLeftElement>
            <Input
              placeholder="搜索设备..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              pl="40px"
            />
          </InputGroup>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">所有状态</option>
            <option value="online">在线</option>
            <option value="offline">离线</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
          </Select>
          <Select
            maxW={{ base: '100%', md: '200px' }}
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
          >
            <option value="all">所有类型</option>
            <option value="router">路由器</option>
            <option value="switch">交换机</option>
            <option value="firewall">防火墙</option>
            <option value="load_balancer">负载均衡器</option>
            <option value="wireless_ap">无线接入点</option>
            <option value="gateway">网关</option>
            <option value="other">其他</option>
          </Select>
        </Flex>
      </Box>

      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* 设备列表 */}
        <Box>
          <Heading size="md" mb={3}>网络设备</Heading>
          <Box
            borderWidth="1px"
            borderRadius="xl"
            overflow="hidden"
            bg={bgColor}
            borderColor={borderColor}
            boxShadow="md"
            height="600px"
            overflowY="auto"
          >
            <Table variant="simple">
              <Thead bg={useColorModeValue('gray.50', 'gray.700')} position="sticky" top={0} zIndex={1}>
                <Tr>
                  <Th py={4}>设备名称</Th>
                  <Th py={4}>状态</Th>
                  <Th py={4}>类型</Th>
                  <Th py={4}>IP地址</Th>
                  <Th py={4}>操作</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredDevices.length > 0 ? (
                  filteredDevices.map(device => (
                    <Tr
                      key={device.id}
                      _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                      cursor="pointer"
                      onClick={() => handleSelectDevice(device)}
                      bg={selectedDevice?.id === device.id ? useColorModeValue('blue.50', 'blue.900') : undefined}
                    >
                      <Td>
                        <Flex align="center">
                          <Box
                            bg={useColorModeValue(
                              device.status === 'online' ? 'green.50' : 'gray.50',
                              device.status === 'online' ? 'green.900' : 'gray.700'
                            )}
                            p={1.5}
                            borderRadius="md"
                            mr={3}
                          >
                            {getDeviceTypeIcon(device.device_type)}
                          </Box>
                          <Box>
                            <Text fontWeight="medium">{device.name}</Text>
                            <Text fontSize="xs" color="gray.500">{device.model}</Text>
                          </Box>
                        </Flex>
                      </Td>
                      <Td>
                        <Badge
                          colorScheme={getStatusColor(device.status)}
                          px={2}
                          py={1}
                          borderRadius="full"
                          variant="subtle"
                        >
                          {getStatusText(device.status)}
                        </Badge>
                      </Td>
                      <Td>{getDeviceTypeText(device.device_type)}</Td>
                      <Td>
                        <Text fontFamily="mono">{device.ip_address}</Text>
                      </Td>
                      <Td onClick={(e) => e.stopPropagation()}>
                        <HStack spacing={1}>
                          <Tooltip label="刷新状态">
                            <IconButton
                              icon={<ArrowsClockwise weight="bold" />}
                              aria-label="刷新状态"
                              size="sm"
                              variant="ghost"
                              colorScheme="blue"
                              onClick={() => handleDeviceAction('refresh', device)}
                            />
                          </Tooltip>
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              icon={<DotsThreeVertical weight="bold" />}
                              variant="ghost"
                              size="sm"
                              aria-label="更多操作"
                            />
                            <MenuList>
                              <MenuItem icon={<Pencil size={16} />}>编辑设备</MenuItem>
                              <MenuItem icon={<Plus size={16} />} onClick={onAddInterfaceOpen}>添加接口</MenuItem>
                              <MenuItem icon={<ChartLine size={16} />}>查看详情</MenuItem>
                              <MenuDivider />
                              <MenuItem
                                icon={<Trash size={16} />}
                                color="red.500"
                                onClick={() => handleDeviceAction('delete', device)}
                              >
                                删除设备
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </HStack>
                      </Td>
                    </Tr>
                  ))
                ) : (
                  <Tr>
                    <Td colSpan={5} textAlign="center" py={8}>
                      <VStack spacing={3}>
                        <Box
                          p={3}
                          borderRadius="full"
                          bg={useColorModeValue('gray.100', 'gray.700')}
                        >
                          <MagnifyingGlass size={24} weight="duotone" />
                        </Box>
                        <Text>没有找到匹配的设备</Text>
                        <Text fontSize="sm" color="gray.500">
                          尝试使用不同的搜索条件或清除筛选器
                        </Text>
                      </VStack>
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </Box>
        </Box>

        {/* 接口列表和带宽图表 */}
        <Box>
          {selectedDevice ? (
            <Box>
              <Flex justify="space-between" align="center" mb={3}>
                <Heading size="md">接口列表</Heading>
                <HStack>
                  <Button
                    leftIcon={<Plus weight="bold" />}
                    colorScheme="teal"
                    size="sm"
                    onClick={onAddInterfaceOpen}
                  >
                    添加接口
                  </Button>
                  <Button
                    leftIcon={<Warning weight="bold" />}
                    colorScheme="orange"
                    size="sm"
                    onClick={onAddThresholdOpen}
                  >
                    设置阈值
                  </Button>
                </HStack>
              </Flex>
              <Box
                borderWidth="1px"
                borderRadius="xl"
                overflow="hidden"
                bg={bgColor}
                borderColor={borderColor}
                boxShadow="md"
                height="280px"
                overflowY="auto"
                mb={4}
              >
                <Table variant="simple">
                  <Thead bg={useColorModeValue('gray.50', 'gray.700')} position="sticky" top={0} zIndex={1}>
                    <Tr>
                      <Th py={3}>接口名称</Th>
                      <Th py={3}>状态</Th>
                      <Th py={3}>速率</Th>
                      <Th py={3}>入带宽</Th>
                      <Th py={3}>出带宽</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {getDeviceInterfaces(selectedDevice.id).length > 0 ? (
                      getDeviceInterfaces(selectedDevice.id).map(networkInterface => (
                        <Tr
                          key={networkInterface.id}
                          _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }}
                          cursor="pointer"
                          onClick={() => handleSelectInterface(networkInterface)}
                          bg={selectedInterface?.id === networkInterface.id ? useColorModeValue('blue.50', 'blue.900') : undefined}
                        >
                          <Td>
                            <Flex align="center">
                              <Box
                                bg={useColorModeValue(
                                  networkInterface.operational_status ? 'green.50' : 'gray.50',
                                  networkInterface.operational_status ? 'green.900' : 'gray.700'
                                )}
                                p={1.5}
                                borderRadius="md"
                                mr={3}
                              >
                                <ChartLine size={16} weight="fill" />
                              </Box>
                              <Box>
                                <Text fontWeight="medium">{networkInterface.name}</Text>
                                <Text fontSize="xs" color="gray.500">{networkInterface.description}</Text>
                              </Box>
                            </Flex>
                          </Td>
                          <Td>
                            <Badge
                              colorScheme={networkInterface.operational_status ? 'green' : 'gray'}
                              px={2}
                              py={1}
                              borderRadius="full"
                              variant="subtle"
                            >
                              {networkInterface.operational_status ? '活动' : '非活动'}
                            </Badge>
                          </Td>
                          <Td>
                            {networkInterface.speed ? (
                              <Text>{formatBandwidth(networkInterface.speed)}</Text>
                            ) : '-'}
                          </Td>
                          <Td>
                            <HStack>
                              <ArrowDown size={16} color="green" />
                              <Text>{networkInterface.in_octets ? formatBandwidth((networkInterface.in_octets * 8) / 300) : '-'}</Text>
                            </HStack>
                          </Td>
                          <Td>
                            <HStack>
                              <ArrowUp size={16} color="blue" />
                              <Text>{networkInterface.out_octets ? formatBandwidth((networkInterface.out_octets * 8) / 300) : '-'}</Text>
                            </HStack>
                          </Td>
                        </Tr>
                      ))
                    ) : (
                      <Tr>
                        <Td colSpan={5} textAlign="center" py={4}>
                          <Text>没有接口记录</Text>
                        </Td>
                      </Tr>
                    )}
                  </Tbody>
                </Table>
              </Box>

              {selectedInterface && (
                <Box>
                  <Flex justify="space-between" align="center" mb={3}>
                    <Heading size="md">带宽使用趋势</Heading>
                    <HStack>
                      <Select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        size="sm"
                        width="120px"
                      >
                        <option value="24h">24小时</option>
                        <option value="7d">7天</option>
                        <option value="30d">30天</option>
                      </Select>
                    </HStack>
                  </Flex>
                  <Box
                    borderWidth="1px"
                    borderRadius="xl"
                    overflow="hidden"
                    bg={bgColor}
                    borderColor={borderColor}
                    boxShadow="md"
                    height="280px"
                    p={4}
                  >
                    {isLoading ? (
                      <Center height="100%">
                        <Spinner size="xl" color="teal.500" />
                      </Center>
                    ) : bandwidthData.length > 0 ? (
                      <Text>这里将显示带宽图表</Text>
                    ) : (
                      <Center height="100%">
                        <VStack spacing={3}>
                          <Box
                            p={3}
                            borderRadius="full"
                            bg={useColorModeValue('gray.100', 'gray.700')}
                          >
                            <ChartLine size={24} weight="duotone" />
                          </Box>
                          <Text>没有带宽数据</Text>
                          <Text fontSize="sm" color="gray.500">
                            选择一个接口查看带宽使用趋势
                          </Text>
                        </VStack>
                      </Center>
                    )}
                  </Box>
                </Box>
              )}
            </Box>
          ) : (
            <Box
              borderWidth="1px"
              borderRadius="xl"
              overflow="hidden"
              bg={bgColor}
              borderColor={borderColor}
              boxShadow="md"
              height="600px"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <VStack spacing={4}>
                <Box
                  p={4}
                  borderRadius="full"
                  bg={useColorModeValue('gray.100', 'gray.700')}
                >
                  <Broadcast size={40} weight="duotone" />
                </Box>
                <Text fontSize="lg" fontWeight="medium">选择一个设备查看接口和带宽信息</Text>
                <Text color="gray.500" textAlign="center" maxW="400px">
                  从左侧列表中选择一个网络设备，查看其接口列表和带宽使用情况
                </Text>
              </VStack>
            </Box>
          )}
        </Box>
      </SimpleGrid>
    </Box>
  );
}
