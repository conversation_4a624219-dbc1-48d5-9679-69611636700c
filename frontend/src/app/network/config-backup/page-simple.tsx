'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

export default function ConfigBackupPageSimple() {
  // 状态管理
  const [devices, setDevices] = useState<Array<{
    id: number;
    name: string;
    ip: string;
    type: string;
    vendor: string;
    lastBackup: string;
    status: string;
  }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDevices, setSelectedDevices] = useState<number[]>([]);
  const [backupSuccess, setBackupSuccess] = useState<string | null>(null);
  const [backupError, setBackupError] = useState<string | null>(null);

  // 模拟设备数据
  const mockDevices = [
    {
      id: 1,
      name: 'Core-Router-01',
      ip: '***********',
      type: 'router',
      vendor: 'cisco',
      lastBackup: '2023-03-15T08:30:00',
      status: 'success'
    },
    {
      id: 2,
      name: 'Distribution-Switch-01',
      ip: '***********',
      type: 'switch',
      vendor: 'cisco',
      lastBackup: '2023-03-15T08:35:00',
      status: 'success'
    },
    {
      id: 3,
      name: 'Access-Switch-01',
      ip: '***********',
      type: 'switch',
      vendor: 'huawei',
      lastBackup: '2023-03-15T08:40:00',
      status: 'success'
    },
    {
      id: 4,
      name: 'Firewall-01',
      ip: '***********',
      type: 'firewall',
      vendor: 'fortinet',
      lastBackup: '2023-03-14T10:30:00',
      status: 'warning'
    },
    {
      id: 5,
      name: 'Core-Router-02',
      ip: '***********',
      type: 'router',
      vendor: 'cisco',
      lastBackup: '2023-03-13T09:30:00',
      status: 'error'
    }
  ];

  // 加载设备数据
  useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      setDevices(mockDevices);
      setIsLoading(false);
    }, 1000);
  }, []);

  // 处理搜索
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // 过滤设备
  const filteredDevices = devices.filter(device => 
    device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    device.ip.includes(searchQuery) ||
    device.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
    device.vendor.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 处理设备选择
  const handleDeviceSelect = (deviceId: number) => {
    if (selectedDevices.includes(deviceId)) {
      setSelectedDevices(selectedDevices.filter(id => id !== deviceId));
    } else {
      setSelectedDevices([...selectedDevices, deviceId]);
    }
  };

  // 处理全选
  const handleSelectAll = () => {
    if (selectedDevices.length === filteredDevices.length) {
      setSelectedDevices([]);
    } else {
      setSelectedDevices(filteredDevices.map(device => device.id));
    }
  };

  // 处理备份
  const handleBackup = () => {
    if (selectedDevices.length === 0) {
      setBackupError('请选择至少一个设备进行备份');
      return;
    }

    setBackupError(null);
    setBackupSuccess(null);

    // 模拟备份过程
    setTimeout(() => {
      setBackupSuccess(`成功备份 ${selectedDevices.length} 个设备的配置`);
      
      // 更新设备的最后备份时间
      const now = new Date().toISOString();
      setDevices(devices.map(device => {
        if (selectedDevices.includes(device.id)) {
          return { ...device, lastBackup: now, status: 'success' };
        }
        return device;
      }));
    }, 1000);
  };

  // 获取状态样式
  const getStatusStyle = (status) => {
    switch (status) {
      case 'success':
        return { color: '#38A169', backgroundColor: '#F0FFF4' };
      case 'warning':
        return { color: '#D69E2E', backgroundColor: '#FFFFF0' };
      case 'error':
        return { color: '#E53E3E', backgroundColor: '#FFF5F5' };
      case 'pending':
        return { color: '#718096', backgroundColor: '#F7FAFC' };
      default:
        return { color: '#718096', backgroundColor: '#F7FAFC' };
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (dateString === 'Never') return '从未备份';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>网络设备配置备份</h1>
        <p style={{ color: '#718096' }}>管理和备份网络设备的配置文件，确保网络配置安全</p>
      </div>

      {/* 导航链接 */}
      <div style={{ marginBottom: '1.5rem' }}>
        <Link href="/" style={{ marginRight: '1rem', color: '#3182CE', textDecoration: 'none' }}>首页</Link>
        <Link href="/assets" style={{ marginRight: '1rem', color: '#3182CE', textDecoration: 'none' }}>资产管理</Link>
        <span style={{ color: '#4A5568' }}>配置备份</span>
      </div>

      {/* 操作区域 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '1rem',
        padding: '1rem',
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <input 
            type="text" 
            placeholder="搜索设备..." 
            value={searchQuery}
            onChange={handleSearch}
            style={{
              padding: '0.5rem',
              border: '1px solid #E2E8F0',
              borderRadius: '0.25rem',
              marginRight: '0.5rem'
            }}
          />
        </div>
        <div>
          <button 
            onClick={handleBackup}
            style={{
              backgroundColor: '#3182CE',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '0.25rem',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            开始备份
          </button>
        </div>
      </div>

      {/* 消息提示 */}
      {backupSuccess && (
        <div style={{ 
          padding: '0.75rem 1rem', 
          backgroundColor: '#F0FFF4', 
          color: '#38A169', 
          borderRadius: '0.25rem',
          marginBottom: '1rem'
        }}>
          {backupSuccess}
        </div>
      )}

      {backupError && (
        <div style={{ 
          padding: '0.75rem 1rem', 
          backgroundColor: '#FFF5F5', 
          color: '#E53E3E', 
          borderRadius: '0.25rem',
          marginBottom: '1rem'
        }}>
          {backupError}
        </div>
      )}

      {/* 设备列表 */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '0.5rem', 
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        overflow: 'hidden'
      }}>
        {isLoading ? (
          <div style={{ padding: '2rem', textAlign: 'center' }}>
            <p>加载中...</p>
          </div>
        ) : (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#F7FAFC' }}>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>
                  <input 
                    type="checkbox" 
                    checked={selectedDevices.length === filteredDevices.length && filteredDevices.length > 0}
                    onChange={handleSelectAll}
                  />
                </th>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>设备名称</th>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>IP地址</th>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>类型</th>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>厂商</th>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>最后备份时间</th>
                <th style={{ padding: '0.75rem', textAlign: 'left' }}>状态</th>
              </tr>
            </thead>
            <tbody>
              {filteredDevices.map(device => (
                <tr key={device.id} style={{ borderTop: '1px solid #E2E8F0' }}>
                  <td style={{ padding: '0.75rem' }}>
                    <input 
                      type="checkbox" 
                      checked={selectedDevices.includes(device.id)}
                      onChange={() => handleDeviceSelect(device.id)}
                    />
                  </td>
                  <td style={{ padding: '0.75rem' }}>{device.name}</td>
                  <td style={{ padding: '0.75rem' }}>{device.ip}</td>
                  <td style={{ padding: '0.75rem' }}>{device.type}</td>
                  <td style={{ padding: '0.75rem' }}>{device.vendor}</td>
                  <td style={{ padding: '0.75rem' }}>{formatDate(device.lastBackup)}</td>
                  <td style={{ padding: '0.75rem' }}>
                    <span style={{ 
                      display: 'inline-block',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '0.25rem',
                      fontSize: '0.75rem',
                      ...getStatusStyle(device.status)
                    }}>
                      {device.status === 'success' ? '成功' : 
                       device.status === 'warning' ? '警告' : 
                       device.status === 'error' ? '错误' : '待处理'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
