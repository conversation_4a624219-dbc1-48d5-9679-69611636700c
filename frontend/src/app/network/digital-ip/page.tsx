'use client'

import React, { useState, useEffect, useRef } from 'react'
import api from '../../../api'
import {
  useToast, Box, Flex, Text, Input, Select, Button, Modal, ModalOverlay,
  ModalContent, ModalHeader, ModalFooter, ModalBody, ModalCloseButton,
  Badge, Progress, Table, Thead, Tbody, Tr, Th, Td, TableContainer,
  VStack, HStack, Divider, Icon, FormControl, FormLabel, FormErrorMessage,
  Tooltip, Grid, GridItem,
  Stat, StatLabel, StatNumber, StatHelpText, StatArrow, StatGroup,
  Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon,
  Drawer, DrawerBody, DrawerFooter, DrawerHeader, DrawerOverlay, DrawerContent, DrawerCloseButton,
  useDisclosure, InputGroup, InputRightAddon, Tag, TagLabel, TagCloseButton, Spinner,
  AlertDialog, AlertDialogBody, AlertDialogFooter, AlertDialogHeader, AlertDialogContent,
  AlertDialogOverlay, useColorModeValue, IconButton, Menu, MenuButton, MenuList, MenuItem,
  Switch
} from '@chakra-ui/react'
import {
  ChevronDownIcon, ChevronUpIcon, ChevronLeftIcon, ChevronRightIcon,
  SearchIcon, AddIcon, WarningIcon, CheckIcon, CloseIcon, InfoIcon,
  RepeatIcon, ExternalLinkIcon, SettingsIcon, DeleteIcon, EditIcon,
  ArrowBackIcon, ArrowForwardIcon, CopyIcon, DownloadIcon, LinkIcon
} from '@chakra-ui/icons'

// 导入图表组件 - 使用模拟图表数据
// 注意：在实际项目中，您需要安装 chart.js 和 react-chartjs-2 包
// 这里我们使用模拟数据来展示图表的样子

// 模拟图表组件
const Line = ({ data, options }: any) => (
  <Box h="100%" position="relative">
    <Box
      position="absolute"
      top="0"
      left="0"
      right="0"
      bottom="0"
      display="flex"
      alignItems="center"
      justifyContent="center"
      flexDirection="column"
    >
      <Box
        width="80%"
        height="60%"
        bg="gray.50"
        borderRadius="md"
        position="relative"
        overflow="hidden"
      >
        {/* 模拟线图 */}
        <Box
          position="absolute"
          bottom="20%"
          left="0"
          right="0"
          height="2px"
          bg="gray.200"
        />
        <Box
          position="absolute"
          bottom="40%"
          left="0"
          right="0"
          height="2px"
          bg="gray.200"
        />
        <Box
          position="absolute"
          bottom="60%"
          left="0"
          right="0"
          height="2px"
          bg="gray.200"
        />
        <Box
          position="absolute"
          bottom="80%"
          left="0"
          right="0"
          height="2px"
          bg="gray.200"
        />

        {/* 模拟数据线 */}
        <Box
          position="absolute"
          bottom="30%"
          left="10%"
          width="80%"
          height="40%"
          borderRadius="md"
          bg="blue.100"
          opacity="0.3"
        />
        <Box
          position="absolute"
          bottom="10%"
          left="10%"
          width="80%"
          height="2px"
          bg="blue.500"
          borderRadius="full"
        >
          <Box
            position="absolute"
            bottom="-3px"
            left="0%"
            width="6px"
            height="6px"
            bg="blue.500"
            borderRadius="full"
          />
          <Box
            position="absolute"
            bottom="-3px"
            left="20%"
            width="6px"
            height="6px"
            bg="blue.500"
            borderRadius="full"
          />
          <Box
            position="absolute"
            bottom="10px"
            left="40%"
            width="6px"
            height="6px"
            bg="blue.500"
            borderRadius="full"
          />
          <Box
            position="absolute"
            bottom="30px"
            left="60%"
            width="6px"
            height="6px"
            bg="blue.500"
            borderRadius="full"
          />
          <Box
            position="absolute"
            bottom="15px"
            left="80%"
            width="6px"
            height="6px"
            bg="blue.500"
            borderRadius="full"
          />
          <Box
            position="absolute"
            bottom="20px"
            left="100%"
            width="6px"
            height="6px"
            bg="blue.500"
            borderRadius="full"
          />
        </Box>
      </Box>
      <Text fontSize="sm" color="gray.400" mt={4}>IP使用趋势图</Text>
    </Box>
  </Box>
)

const Bar = ({ data, options }: any) => (
  <Box h="100%" position="relative">
    <Box
      position="absolute"
      top="0"
      left="0"
      right="0"
      bottom="0"
      display="flex"
      alignItems="center"
      justifyContent="center"
      flexDirection="column"
    >
      <Box
        width="80%"
        height="60%"
        bg="gray.50"
        borderRadius="md"
        position="relative"
        p={4}
        display="flex"
        alignItems="flex-end"
        justifyContent="space-around"
      >
        {/* 模拟柱状图 */}
        <Box width="15%" height="60%" bg="blue.500" borderRadius="md" />
        <Box width="15%" height="80%" bg="blue.500" borderRadius="md" />
        <Box width="15%" height="40%" bg="blue.500" borderRadius="md" />
        <Box width="15%" height="70%" bg="blue.500" borderRadius="md" />
        <Box width="15%" height="50%" bg="blue.500" borderRadius="md" />
      </Box>
      <Text fontSize="sm" color="gray.400" mt={4}>VLAN使用率对比图</Text>
    </Box>
  </Box>
)

const Pie = ({ data, options }: any) => (
  <Box h="100%" position="relative">
    <Box
      position="absolute"
      top="0"
      left="0"
      right="0"
      bottom="0"
      display="flex"
      alignItems="center"
      justifyContent="center"
      flexDirection="column"
    >
      <Box
        width="60%"
        height="60%"
        borderRadius="full"
        position="relative"
        overflow="hidden"
      >
        {/* 模拟饼图 */}
        <Box
          position="absolute"
          top="0"
          left="0"
          width="50%"
          height="50%"
          bg="green.500"
        />
        <Box
          position="absolute"
          top="0"
          right="0"
          width="50%"
          height="50%"
          bg="blue.500"
        />
        <Box
          position="absolute"
          bottom="0"
          left="0"
          width="50%"
          height="50%"
          bg="purple.500"
        />
        <Box
          position="absolute"
          bottom="0"
          right="0"
          width="50%"
          height="50%"
          bg="yellow.500"
        />
      </Box>
      <Text fontSize="sm" color="gray.400" mt={4}>IP状态分布图</Text>
    </Box>
  </Box>
)

const Doughnut = ({ data, options }: any) => (
  <Box h="100%" position="relative">
    <Box
      position="absolute"
      top="0"
      left="0"
      right="0"
      bottom="0"
      display="flex"
      alignItems="center"
      justifyContent="center"
      flexDirection="column"
    >
      <Box
        width="60%"
        height="60%"
        borderRadius="full"
        position="relative"
        overflow="hidden"
        bg="white"
      >
        {/* 模拟环形图 */}
        <Box
          position="absolute"
          top="0"
          left="0"
          width="50%"
          height="50%"
          bg="green.500"
        />
        <Box
          position="absolute"
          top="0"
          right="0"
          width="50%"
          height="50%"
          bg="blue.500"
        />
        <Box
          position="absolute"
          bottom="0"
          left="0"
          width="50%"
          height="50%"
          bg="purple.500"
        />
        <Box
          position="absolute"
          bottom="0"
          right="0"
          width="50%"
          height="50%"
          bg="yellow.500"
        />
        <Box
          position="absolute"
          top="25%"
          left="25%"
          width="50%"
          height="50%"
          bg="white"
          borderRadius="full"
        />
      </Box>
      <Text fontSize="sm" color="gray.400" mt={4}>IP类型分布图</Text>
    </Box>
  </Box>
)

interface IPAddress {
  id: string
  ip: string
  type: 'ipv4' | 'ipv6'
  status: 'allocated' | 'available' | 'reserved' | 'blocked'
  description: string
  vlan?: string
  assignedTo?: string
  oaNumber?: string
  location?: string
  isActive: boolean
  conflict?: boolean
  lastUsed?: string | null
  state?: 'online' | 'offline' | 'blocked' | 'warning'
  macAddress?: string
  assetInfo?: {
    id: string
    name: string
    type: string
    department: string
    owner: string
  }
  usageHistory?: {
    date: string
    status: string
    user?: string
    oaNumber?: string
  }[]
  violationInfo?: {
    isViolation: boolean
    type?: 'unauthorized' | 'conflict' | 'misuse'
    description?: string
    detectedAt?: string
  }
}

interface IPPool {
  id: string
  network: string
  description: string
  usageRate: number
  lastOptimized: string
  vlanId: string
  organization?: string
  totalIps?: number
  allocatedIps?: number
  availableIps?: number
  reservedIps?: number
  conflictIps?: number
  idleIps?: number
  ipRange?: string
  gateway?: string
  subnet?: string
  dhcpEnabled?: boolean
  dnsServers?: string[]
}

interface VLAN {
  id: string
  name: string
  color?: string
  description?: string
  organization?: string
  ipPools?: IPPool[]
  usageRate?: number
}

interface Stats {
  totalIps: number
  allocated: number
  available: number
  reserved: number
  conflict: number
  idle: number
  usageRate: number
  blocked: number
  unauthorized: number
  warning: number
  online: number
  offline: number
  lastUpdated?: string
}

interface AutoAssignSettings {
  type: 'ipv4' | 'ipv6'
  subnet: string
  vlan: string
  count?: number
  startIp?: string
  endIp?: string
  gateway?: string
  dnsServers?: string[]
  leaseTime?: number
  assignToAsset?: boolean
  department?: string
}

interface IPCalculatorResult {
  networkAddress: string
  broadcastAddress: string
  subnetMask: string
  cidrNotation: string
  totalHosts: number
  usableHosts: number
  ipRange: string
  possibleSubnets?: {
    mask: string
    count: number
    hostsPerSubnet: number
  }[]
}

interface AssetInfo {
  id: string
  name: string
  type: string
  serialNumber?: string
  macAddress?: string
  department?: string
  owner?: string
  location?: string
  purchaseDate?: string
  warrantyExpire?: string
  status?: string
}

export default function DigitalIPManagement() {
  const toast = useToast()
  const cancelRef = useRef<HTMLButtonElement>(null)

  // Drawer和对话框控制
  const {
    isOpen: isIpDetailOpen,
    onOpen: onIpDetailOpen,
    onClose: onIpDetailClose
  } = useDisclosure()

  const {
    isOpen: isCalculatorOpen,
    onOpen: onCalculatorOpen,
    onClose: onCalculatorClose
  } = useDisclosure()

  const {
    isOpen: isConfirmDialogOpen,
    onOpen: onConfirmDialogOpen,
    onClose: onConfirmDialogClose
  } = useDisclosure()

  // 状态定义
  const [selectedVlan, setSelectedVlan] = useState<string>('all')
  const [vlans, setVlans] = useState<VLAN[]>([])
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isAddIpModalOpen, setIsAddIpModalOpen] = useState(false)
  const [isAutoAssignModalOpen, setIsAutoAssignModalOpen] = useState(false)

  const [newIp, setNewIp] = useState<Omit<IPAddress, 'id'>>({
    ip: '',
    type: 'ipv4',
    status: 'available',
    description: '',
    vlan: '',
    assignedTo: '',
    oaNumber: '',
    location: '',
    isActive: true
  })

  const [subnets, setSubnets] = useState<IPPool[]>([])
  const [ipList, setIpList] = useState<IPAddress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<Stats>({
    totalIps: 0,
    allocated: 0,
    available: 0,
    reserved: 0,
    conflict: 0,
    idle: 0,
    usageRate: 0,
    blocked: 0,
    unauthorized: 0,
    warning: 0,
    online: 0,
    offline: 0,
    lastUpdated: new Date().toISOString()
  })

  const [currentIp, setCurrentIp] = useState<IPAddress | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const [autoAssignSettings, setAutoAssignSettings] = useState<AutoAssignSettings>({
    type: 'ipv4',
    subnet: '',
    vlan: '',
    count: 1,
    assignToAsset: false
  })

  const [oaNumber, setOaNumber] = useState('')
  const [newSegment, setNewSegment] = useState({
    vlan: '',
    cidr: '',
    description: '',
    organization: '',
    gateway: '',
    dhcpEnabled: true,
    dnsServers: ['*******', '***************']
  })

  const [cidrError, setCidrError] = useState('')
  const [confirmAction, setConfirmAction] = useState<() => Promise<void>>(() => Promise.resolve())
  const [confirmMessage, setConfirmMessage] = useState('')
  const [confirmTitle, setConfirmTitle] = useState('')

  // IP计算器状态
  const [calculatorInput, setCalculatorInput] = useState('')
  const [calculatorResult, setCalculatorResult] = useState<IPCalculatorResult | null>(null)

  // 图表数据
  const [ipUsageChartData, setIpUsageChartData] = useState<any>(null)
  const [ipTrendChartData, setIpTrendChartData] = useState<any>(null)
  const [vlanComparisonData, setVlanComparisonData] = useState<any>(null)

  // 从API获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        const [vlansRes, subnetsRes, ipsRes] = await Promise.all([
          api.get('/network-config/ip-pools/dhcp-pools'),
          api.get('/network-config/ip-pools/'),
          api.get('/network-config/ip-addresses/dhcp-leases')
        ])
        setVlans(vlansRes.data)
        setSubnets(subnetsRes.data)
        setIpList(ipsRes.data)

        // 模拟一些额外数据
        const mockIps = generateMockIpData(vlansRes.data, 200);
        setIpList(prev => [...prev, ...mockIps]);

      } catch (err: any) {
        toast({
          title: "获取数据失败",
          description: err.message,
          status: "error",
          duration: 5000,
          isClosable: true,
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // 生成模拟IP数据用于展示
  const generateMockIpData = (vlans: VLAN[], count: number): IPAddress[] => {
    const statuses: ('allocated' | 'available' | 'reserved' | 'blocked')[] = ['allocated', 'available', 'reserved', 'blocked'];
    const states: ('online' | 'offline' | 'blocked' | 'warning')[] = ['online', 'offline', 'blocked', 'warning'];
    const mockIps: IPAddress[] = [];

    for (let i = 0; i < count; i++) {
      const vlan = vlans[Math.floor(Math.random() * vlans.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const state = states[Math.floor(Math.random() * states.length)];
      const isConflict = Math.random() < 0.05; // 5%的冲突率
      const hasViolation = Math.random() < 0.1; // 10%的违规率

      const lastUsed = status === 'available' ?
        (Math.random() < 0.7 ? null : new Date(Date.now() - Math.random() * 100 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]) :
        new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const macAddress = status === 'allocated' ?
        `${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}` :
        undefined;

      const hasAssetInfo = status === 'allocated' && Math.random() < 0.8;

      mockIps.push({
        id: `mock-${i}`,
        ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        type: Math.random() > 0.9 ? 'ipv6' : 'ipv4',
        status,
        description: `测试IP-${i}`,
        vlan: vlan?.id,
        assignedTo: status === 'allocated' ? `用户${Math.floor(Math.random() * 100)}` : undefined,
        oaNumber: status === 'allocated' ? `OA-${Math.floor(Math.random() * 10000)}` : undefined,
        location: `位置-${Math.floor(Math.random() * 10)}`,
        isActive: true,
        conflict: isConflict,
        lastUsed,
        state,
        macAddress,
        assetInfo: hasAssetInfo ? {
          id: `asset-${Math.floor(Math.random() * 1000)}`,
          name: `设备-${Math.floor(Math.random() * 1000)}`,
          type: Math.random() > 0.5 ? '服务器' : '工作站',
          department: `部门-${Math.floor(Math.random() * 10)}`,
          owner: `负责人-${Math.floor(Math.random() * 20)}`
        } : undefined,
        usageHistory: status === 'allocated' ? [
          {
            date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: 'allocated',
            user: `用户${Math.floor(Math.random() * 100)}`,
            oaNumber: `OA-${Math.floor(Math.random() * 10000)}`
          },
          {
            date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: 'available'
          }
        ] : undefined,
        violationInfo: hasViolation ? {
          isViolation: true,
          type: ['unauthorized', 'conflict', 'misuse'][Math.floor(Math.random() * 3)] as 'unauthorized' | 'conflict' | 'misuse',
          description: `违规原因-${Math.floor(Math.random() * 5)}`,
          detectedAt: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toISOString()
        } : undefined
      });
    }

    return mockIps;
  };

  // 计算统计信息
  useEffect(() => {
    if (ipList.length > 0) {
      const allocated = ipList.filter(ip => ip.status === 'allocated').length
      const available = ipList.filter(ip => ip.status === 'available').length
      const reserved = ipList.filter(ip => ip.status === 'reserved').length
      const blocked = ipList.filter(ip => ip.status === 'blocked').length
      const conflict = ipList.filter(ip => ip.conflict).length
      const idle = ipList.filter(ip => ip.lastUsed === null && ip.status === 'available').length
      const unauthorized = ipList.filter(ip => ip.violationInfo?.type === 'unauthorized').length
      const warning = ipList.filter(ip => ip.state === 'warning').length
      const online = ipList.filter(ip => ip.state === 'online').length
      const offline = ipList.filter(ip => ip.state === 'offline').length

      const totalIps = ipList.length
      const usageRate = totalIps > 0 ? Math.round((allocated / totalIps) * 100) : 0

      setStats({
        totalIps,
        allocated,
        available,
        reserved,
        conflict,
        idle,
        usageRate,
        blocked,
        unauthorized,
        warning,
        online,
        offline,
        lastUpdated: new Date().toISOString()
      })

      // 更新图表数据
      updateChartData()
    }
  }, [ipList])

  // 更新图表数据
  const updateChartData = () => {
    // IP使用状态饼图
    setIpUsageChartData({
      labels: ['已分配', '可用', '预留', '阻断', '冲突'],
      datasets: [
        {
          data: [stats.allocated, stats.available, stats.reserved, stats.blocked, stats.conflict],
          backgroundColor: ['#48BB78', '#4299E1', '#9F7AEA', '#F56565', '#ECC94B'],
          borderWidth: 1
        }
      ]
    })

    // IP使用趋势图 (模拟数据)
    const labels = Array.from({length: 7}, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (6 - i))
      return date.toLocaleDateString('zh-CN', {month: 'short', day: 'numeric'})
    })

    setIpTrendChartData({
      labels,
      datasets: [
        {
          label: '已分配',
          data: Array.from({length: 7}, () => Math.floor(Math.random() * 50) + stats.allocated - 25),
          borderColor: '#48BB78',
          backgroundColor: 'rgba(72, 187, 120, 0.2)',
          fill: true,
          tension: 0.4
        },
        {
          label: '可用',
          data: Array.from({length: 7}, () => Math.floor(Math.random() * 50) + stats.available - 25),
          borderColor: '#4299E1',
          backgroundColor: 'rgba(66, 153, 225, 0.2)',
          fill: true,
          tension: 0.4
        }
      ]
    })

    // VLAN使用率对比图
    if (vlans.length > 0) {
      setVlanComparisonData({
        labels: vlans.slice(0, 5).map(vlan => vlan.name),
        datasets: [
          {
            label: 'VLAN使用率',
            data: vlans.slice(0, 5).map(() => Math.floor(Math.random() * 100)),
            backgroundColor: '#4299E1',
            borderRadius: 6
          }
        ]
      })
    }
  }

  // 模拟实时监控
  useEffect(() => {
    const interval = setInterval(() => {
      setIpList(currentList => {
        return currentList.map(ip => {
          if (Math.random() < 0.05) {
            return {
              ...ip,
              state: ip.state === 'online' ? 'offline' : 'online',
              lastUsed: ip.state === 'online' ? ip.lastUsed : new Date().toISOString().split('T')[0]
            }
          }
          return ip
        })
      })
    }, 30000)

    return () => clearInterval(interval)
  }, [])



  // 计算闲置和占用检测
  const calculateUsageStatus = (ip: IPAddress) => {
    if (ip.status === 'reserved') return { status: 'reserved', text: '预留IP', color: 'purple' }
    if (ip.status === 'blocked') return { status: 'blocked', text: '已阻断', color: 'red' }

    if (ip.conflict) return { status: 'conflict', text: 'IP冲突', color: 'orange' }

    if (ip.violationInfo?.isViolation) {
      const violationType = ip.violationInfo.type || 'unauthorized';
      const violationTexts = {
        'unauthorized': '未授权使用',
        'conflict': 'MAC冲突',
        'misuse': '违规使用'
      };
      return {
        status: 'violation',
        text: violationTexts[violationType],
        color: 'red',
        detail: ip.violationInfo.description
      }
    }

    if (!ip.lastUsed) return { status: 'available', text: '未使用', color: 'blue' }

    const daysSinceLastUse = Math.floor((new Date().getTime() - new Date(ip.lastUsed).getTime()) / (1000 * 60 * 60 * 24))

    if (daysSinceLastUse > 90) {
      return { status: 'idle', text: `闲置 (${daysSinceLastUse}天)`, color: 'yellow' }
    } else if (daysSinceLastUse <= 1) {
      return { status: 'recently_used', text: `近期使用 (${daysSinceLastUse}天前)`, color: 'green' }
    } else {
      return { status: 'used', text: `${daysSinceLastUse}天前使用`, color: 'green' }
    }
  }

  // 获取IP状态标签颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'allocated': 'green',
      'available': 'blue',
      'reserved': 'purple',
      'blocked': 'red',
      'online': 'green',
      'offline': 'gray',
      'warning': 'orange',
      'conflict': 'red',
      'idle': 'yellow',
      'violation': 'red'
    }
    return colorMap[status] || 'gray'
  }

  // 过滤IP列表
  const getFilteredIps = () => {
    // 基于VLAN筛选
    let filtered = selectedVlan === 'all'
      ? ipList
      : ipList.filter(ip => ip.vlan === selectedVlan)

    // 基于搜索词筛选
    if (searchTerm) {
      filtered = filtered.filter(ip =>
        ip.ip.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ip.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (ip.assignedTo && ip.assignedTo.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (ip.macAddress && ip.macAddress.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (ip.oaNumber && ip.oaNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (ip.assetInfo?.name && ip.assetInfo.name.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }



    return filtered
  }

  const filteredIps = getFilteredIps()

  // 统计卡片颜色和数据
  const statColors = [
    'blue.500', 'green.500', 'blue.400', 'purple.500',
    'red.500', 'yellow.500', 'red.400', 'orange.500',
    'green.400', 'gray.500'
  ]

  const statLabels = [
    '总数', '已分配', '可用', '预留',
    '冲突', '闲置', '阻断', '告警',
    '在线', '离线'
  ]

  const statValues = [
    stats.totalIps, stats.allocated, stats.available, stats.reserved,
    stats.conflict, stats.idle, stats.blocked, stats.warning,
    stats.online, stats.offline
  ]

  // 获取状态标签的文本表示
  const getStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      'allocated': '已分配',
      'available': '可用',
      'reserved': '预留',
      'blocked': '阻断',
      'online': '在线',
      'offline': '离线',
      'warning': '告警',
      'conflict': '冲突',
      'idle': '闲置',
      'violation': '违规'
    }
    return statusMap[status] || status
  }

  // IP计算器函数
  const calculateIpRange = (cidr: string) => {
    try {
      // 简单的CIDR解析，实际项目中可能需要更复杂的实现
      const [ipPart, prefixPart] = cidr.split('/')
      const prefix = parseInt(prefixPart)

      if (prefix < 0 || prefix > 32 || isNaN(prefix)) {
        return null
      }

      const ipParts = ipPart.split('.').map(p => parseInt(p))
      if (ipParts.length !== 4 || ipParts.some(p => p < 0 || p > 255 || isNaN(p))) {
        return null
      }

      // 计算网络地址和广播地址
      const ipNum = ipParts.reduce((acc, octet, i) => acc + (octet << (24 - 8 * i)), 0)
      const mask = ~((1 << (32 - prefix)) - 1)
      const networkNum = ipNum & mask
      const broadcastNum = networkNum | ~mask

      // 转换回IP地址格式
      const networkIp = [
        (networkNum >> 24) & 255,
        (networkNum >> 16) & 255,
        (networkNum >> 8) & 255,
        networkNum & 255
      ].join('.')

      const broadcastIp = [
        (broadcastNum >> 24) & 255,
        (broadcastNum >> 16) & 255,
        (broadcastNum >> 8) & 255,
        broadcastNum & 255
      ].join('.')

      // 计算子网掩码
      const subnetMask = [
        (mask >> 24) & 255,
        (mask >> 16) & 255,
        (mask >> 8) & 255,
        mask & 255
      ].join('.')

      // 计算可用主机数
      const totalHosts = Math.pow(2, 32 - prefix)
      const usableHosts = totalHosts > 2 ? totalHosts - 2 : 0

      // 计算IP范围
      const firstUsable = networkNum === broadcastNum ? networkIp :
        [
          ((networkNum + 1) >> 24) & 255,
          ((networkNum + 1) >> 16) & 255,
          ((networkNum + 1) >> 8) & 255,
          (networkNum + 1) & 255
        ].join('.')

      const lastUsable = networkNum === broadcastNum ? broadcastIp :
        [
          ((broadcastNum - 1) >> 24) & 255,
          ((broadcastNum - 1) >> 16) & 255,
          ((broadcastNum - 1) >> 8) & 255,
          (broadcastNum - 1) & 255
        ].join('.')

      const ipRange = `${firstUsable} - ${lastUsable}`

      // 计算可能的子网划分
      const possibleSubnets: Array<{
        mask: string;
        count: number;
        hostsPerSubnet: number;
      }> = []
      for (let i = 1; i <= 8; i++) {
        const newPrefix = prefix + i
        if (newPrefix <= 30) {
          possibleSubnets.push({
            mask: `/${newPrefix}`,
            count: Math.pow(2, i),
            hostsPerSubnet: Math.pow(2, 32 - newPrefix) - 2
          })
        }
      }

      return {
        networkAddress: networkIp,
        broadcastAddress: broadcastIp,
        subnetMask,
        cidrNotation: cidr,
        totalHosts,
        usableHosts,
        ipRange,
        possibleSubnets
      }
    } catch (error) {
      console.error('CIDR计算错误:', error)
      return null
    }
  }

  // 处理IP计算
  const handleCalculateIp = () => {
    if (!calculatorInput) {
      toast({
        title: "输入错误",
        description: "请输入有效的CIDR格式",
        status: "error",
        duration: 3000,
        isClosable: true,
      })
      return
    }

    const result = calculateIpRange(calculatorInput)
    if (result) {
      setCalculatorResult(result)
    } else {
      toast({
        title: "计算错误",
        description: "无法计算给定的CIDR，请检查格式",
        status: "error",
        duration: 3000,
        isClosable: true,
      })
    }
  }

  // 自动分配IP函数
  const autoAssignIp = async () => {
    if (!oaNumber) {
      toast({
        title: "OA单号缺失",
        description: "请先输入关联的OA单号",
        status: "error",
        duration: 5000,
        isClosable: true,
      })
      return
    }

    try {
      // 模拟API调用
      // const res = await api.post('/network-config/ip-addresses/auto-assign', {
      //   oaNumber,
      //   ...autoAssignSettings
      // })

      // 模拟响应数据
      const mockResponse: IPAddress = {
        id: `auto-${Date.now()}`,
        ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        type: autoAssignSettings.type,
        status: 'allocated',
        description: `自动分配给OA单号: ${oaNumber}`,
        vlan: autoAssignSettings.vlan,
        assignedTo: '系统自动分配',
        oaNumber,
        location: '',
        isActive: true,
        lastUsed: new Date().toISOString().split('T')[0],
        state: 'online',
        macAddress: `${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}:${Math.floor(Math.random()*256).toString(16).padStart(2,'0')}`
      }

      setIpList([...ipList, mockResponse])
      toast({
        title: "IP分配成功",
        description: `IP地址 ${mockResponse.ip} 已分配给OA单号 ${oaNumber}`,
        status: "success",
        duration: 5000,
        isClosable: true,
      })
      setOaNumber('')
      setIsAutoAssignModalOpen(false)
    } catch (err: any) {
      toast({
        title: "IP分配失败",
        description: err.response?.data?.message || err.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      })
    }
  }

  // 添加IP函数
  const handleAddIp = async () => {
    if (!newIp.ip || !newIp.description) {
      toast({
        title: "验证失败",
        description: "IP地址和描述是必填项",
        status: "error",
        duration: 5000,
        isClosable: true,
      })
      return
    }

    try {
      // 模拟API调用
      // const res = await api.post('/network-config/ip-addresses/', newIp)

      // 模拟响应数据
      const mockResponse = {
        ...newIp,
        id: `manual-${Date.now()}`
      }

      setIpList([...ipList, mockResponse])
      toast({
        title: "IP添加成功",
        description: `IP地址 ${mockResponse.ip} 已添加到列表中`,
        status: "success",
        duration: 5000,
        isClosable: true,
      })
      setIsAddIpModalOpen(false)
      setNewIp({
        ip: '',
        type: 'ipv4',
        status: 'available',
        description: '',
        vlan: '',
        assignedTo: '',
        oaNumber: '',
        location: '',
        isActive: true
      })
    } catch (err: any) {
      toast({
        title: "IP添加失败",
        description: err.response?.data?.message || err.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      })
    }
  }

  // 编辑IP相关函数
  const handleEditIp = (ip: IPAddress) => {
    setCurrentIp(ip)
    setIsEditModalOpen(true)
  }

  const handleUpdateIp = () => {
    if (!currentIp) return

    setIpList(ipList.map(ip =>
      ip.id === currentIp.id ? { ...currentIp } : ip
    ))

    toast({
      title: "IP更新成功",
      description: `IP地址 ${currentIp.ip} 已更新`,
      status: "success",
      duration: 5000,
      isClosable: true,
    })

    setIsEditModalOpen(false)
  }

  // 删除IP相关函数
  const handleDeleteIp = (ip: IPAddress) => {
    setCurrentIp(ip)
    setConfirmTitle('确认删除IP')
    setConfirmMessage(`您确定要删除IP地址 ${ip.ip} 吗？此操作不可撤销。`)
    setConfirmAction(() => async () => {
      try {
        // 模拟API调用
        // await api.delete(`/network-config/ip-addresses/${ip.id}`)

        setIpList(ipList.filter(item => item.id !== ip.id))
        toast({
          title: "IP删除成功",
          description: `IP地址 ${ip.ip} 已删除`,
          status: "success",
          duration: 5000,
          isClosable: true,
        })
      } catch (err: any) {
        toast({
          title: "IP删除失败",
          description: err.response?.data?.message || err.message,
          status: "error",
          duration: 5000,
          isClosable: true,
        })
      }
    })
    onConfirmDialogOpen()
  }

  // 查看IP详情
  const handleViewIpDetail = (ip: IPAddress) => {
    setCurrentIp(ip)
    onIpDetailOpen()
  }

  // 添加网段方法
  const handleAddSegment = async () => {
    if (!newSegment.vlan || !newSegment.cidr) {
      toast({
        title: "验证失败",
        description: "VLAN和CIDR是必填项",
        status: "error",
        duration: 5000,
        isClosable: true,
      })
      return
    }

    if (!validateCidr(newSegment.cidr)) {
      toast({
        title: "CIDR格式错误",
        description: "请输入有效的CIDR格式，如***********/24",
        status: "error",
        duration: 5000,
        isClosable: true,
      })
      return
    }

    try {
      // 模拟API调用
      // const res = await api.post('/network-config/ip-pools/', newSegment)

      // 计算IP范围
      const ipCalcResult = calculateIpRange(newSegment.cidr)
      if (!ipCalcResult) {
        throw new Error('无法计算IP范围')
      }

      // 模拟响应数据
      const mockResponse: IPPool = {
        id: `pool-${Date.now()}`,
        network: newSegment.cidr,
        description: newSegment.description,
        usageRate: 0,
        lastOptimized: new Date().toISOString(),
        vlanId: newSegment.vlan,
        organization: newSegment.organization,
        totalIps: ipCalcResult.usableHosts,
        allocatedIps: 0,
        availableIps: ipCalcResult.usableHosts,
        reservedIps: 0,
        conflictIps: 0,
        idleIps: 0,
        ipRange: ipCalcResult.ipRange,
        gateway: newSegment.gateway || ipCalcResult.networkAddress.replace(/\.\d+$/, '.1'),
        subnet: ipCalcResult.subnetMask,
        dhcpEnabled: newSegment.dhcpEnabled,
        dnsServers: newSegment.dnsServers
      }

      setSubnets([...subnets, mockResponse])

      // 生成该网段的IP地址
      const newIps: IPAddress[] = []
      const ipParts = ipCalcResult.networkAddress.split('.')
      const baseIp = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.`

      // 只生成前20个IP用于演示
      const startIp = parseInt(ipParts[3]) + 1
      const endIp = Math.min(startIp + 20, 254)

      for (let i = startIp; i <= endIp; i++) {
        newIps.push({
          id: `auto-gen-${Date.now()}-${i}`,
          ip: `${baseIp}${i}`,
          type: 'ipv4',
          status: 'available',
          description: `${newSegment.description || 'VLAN'} IP`,
          vlan: newSegment.vlan,
          isActive: true,
          state: 'offline'
        })
      }

      setIpList([...ipList, ...newIps])

      toast({
        title: "地址段添加成功",
        description: `已添加CIDR ${newSegment.cidr} 到VLAN ${newSegment.vlan}`,
        status: "success",
        duration: 5000,
        isClosable: true,
      })

      setIsAddModalOpen(false)
      setNewSegment({
        vlan: '',
        cidr: '',
        description: '',
        organization: '',
        gateway: '',
        dhcpEnabled: true,
        dnsServers: ['*******', '***************']
      })
    } catch (err: any) {
      toast({
        title: "地址段添加失败",
        description: err.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      })
    }
  }

  // CIDR格式验证
  function validateCidr(value: string) {
    try {
      // 简单校验CIDR格式
      return /^([0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$/.test(value)
    } catch {
      return false
    }
  }

  return (
    <Box px={{ base: 2, md: 8 }} py={6} maxW="1400px" mx="auto">
      {/* 页面标题 */}
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Text fontSize="2xl" fontWeight="bold" mb={1}>数字IP管理系统</Text>
          <Text color="gray.500">全生命周期IP地址管理与监控平台</Text>
        </Box>
        <HStack spacing={4}>
          <Button
            leftIcon={<AddIcon />}
            colorScheme="blue"
            onClick={() => setIsAddIpModalOpen(true)}
            size="sm"
          >
            添加IP
          </Button>
          <Button
            leftIcon={<AddIcon />}
            colorScheme="teal"
            onClick={() => setIsAddModalOpen(true)}
            size="sm"
          >
            添加VLAN地址段
          </Button>
          <Button
            leftIcon={<RepeatIcon />}
            colorScheme="purple"
            onClick={() => setIsAutoAssignModalOpen(true)}
            size="sm"
          >
            自动分配IP
          </Button>
          <IconButton
            aria-label="IP计算器"
            icon={<SettingsIcon />}
            colorScheme="gray"
            variant="outline"
            onClick={onCalculatorOpen}
            size="sm"
          />
        </HStack>
      </Flex>

      {/* 顶部统计卡片区 */}
      <Grid
        templateColumns={{ base: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)', lg: 'repeat(5, 1fr)' }}
        gap={4}
        mb={8}
      >
        {statLabels.map((label, idx) => (
          <GridItem key={label}>
            <Box bg="white" borderRadius="lg" boxShadow="sm" p={4} h="100%">
              <Stat>
                <StatLabel color="gray.500">{label}</StatLabel>
                <StatNumber fontSize="2xl" fontWeight="bold" color={statColors[idx]}>
                  {statValues[idx]}
                </StatNumber>
                {idx === 1 && (
                  <StatHelpText>
                    <StatArrow type="increase" />
                    {Math.floor(Math.random() * 10) + 1}% 较上周
                  </StatHelpText>
                )}
                {idx === 2 && (
                  <StatHelpText>
                    <StatArrow type="decrease" />
                    {Math.floor(Math.random() * 10) + 1}% 较上周
                  </StatHelpText>
                )}
              </Stat>
            </Box>
          </GridItem>
        ))}
        {/* 使用率卡片 */}
        <GridItem colSpan={{ base: 2, md: 3, lg: 5 }}>
          <Box bg="white" borderRadius="lg" boxShadow="sm" p={4}>
            <Flex justify="space-between" align="center" mb={2}>
              <Text fontSize="sm" fontWeight="medium">IP地址使用率</Text>
              <Text fontSize="sm" color="gray.500">
                最后更新: {new Date(stats.lastUpdated || Date.now()).toLocaleString()}
              </Text>
            </Flex>
            <Flex align="center" gap={4}>
              <Text fontSize="2xl" fontWeight="bold" color="blue.600" minW="80px">{stats.usageRate}%</Text>
              <Progress
                flex="1"
                value={stats.usageRate}
                colorScheme={stats.usageRate > 90 ? 'red' : stats.usageRate > 70 ? 'orange' : 'blue'}
                borderRadius="md"
                size="md"
                hasStripe={stats.usageRate > 90}
                isAnimated={stats.usageRate > 90}
              />
            </Flex>
          </Box>
        </GridItem>
      </Grid>

      {/* 图表和数据可视化区域 */}
      <Box bg="white" borderRadius="lg" boxShadow="sm" p={6} mb={6}>
        <Flex justify="space-between" align="center" mb={6}>
          <Box>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">数据分析与可视化</Text>
            <Text fontSize="sm" color="gray.500" mt={1}>
              IP地址使用情况实时监控与分析
            </Text>
          </Box>
          <HStack>
            <Menu>
              <MenuButton
                as={Button}
                rightIcon={<ChevronDownIcon />}
                size="sm"
                variant="outline"
                colorScheme="blue"
              >
                时间范围: 近7天
              </MenuButton>
              <MenuList>
                <MenuItem>今日</MenuItem>
                <MenuItem>近7天</MenuItem>
                <MenuItem>近30天</MenuItem>
                <MenuItem>近90天</MenuItem>
                <MenuItem>自定义...</MenuItem>
              </MenuList>
            </Menu>
            <IconButton
              aria-label="刷新数据"
              icon={<RepeatIcon />}
              size="sm"
              colorScheme="blue"
              variant="ghost"
            />
          </HStack>
        </Flex>

        <Grid templateColumns={{ base: '1fr', lg: '1fr 1fr' }} gap={8} mb={8}>
          {/* IP地址状态分布 - 改进版 */}
          <GridItem>
            <Box
              borderWidth="1px"
              borderColor="gray.100"
              borderRadius="lg"
              overflow="hidden"
            >
              <Box
                bg="blue.50"
                px={4}
                py={3}
                borderBottomWidth="1px"
                borderColor="gray.100"
              >
                <Flex justify="space-between" align="center">
                  <Text fontSize="md" fontWeight="medium" color="blue.700">IP地址状态分布</Text>
                  <HStack spacing={1}>
                    <IconButton
                      aria-label="查看详情"
                      icon={<InfoIcon />}
                      size="xs"
                      variant="ghost"
                      colorScheme="blue"
                    />
                    <Menu>
                      <MenuButton
                        as={IconButton}
                        aria-label="更多选项"
                        icon={<ChevronDownIcon />}
                        size="xs"
                        variant="ghost"
                        colorScheme="blue"
                      />
                      <MenuList>
                        <MenuItem>查看详细报告</MenuItem>
                        <MenuItem>导出数据</MenuItem>
                        <MenuItem>设置告警阈值</MenuItem>
                      </MenuList>
                    </Menu>
                  </HStack>
                </Flex>
              </Box>

              <Flex direction={{ base: 'column', md: 'row' }} p={4}>
                <Box flex="1" h="220px" position="relative">
                  <Doughnut data={ipUsageChartData} options={{}} />
                </Box>

                <VStack
                  flex="1"
                  align="stretch"
                  spacing={3}
                  p={4}
                  bg="gray.50"
                  borderRadius="md"
                  justify="center"
                >
                  <HStack justify="space-between">
                    <HStack>
                      <Box w="3" h="3" borderRadius="full" bg="green.500" />
                      <Text fontSize="sm">已分配</Text>
                    </HStack>
                    <Text fontSize="sm" fontWeight="bold">{stats.allocated} ({Math.round(stats.allocated/stats.totalIps*100)}%)</Text>
                  </HStack>

                  <HStack justify="space-between">
                    <HStack>
                      <Box w="3" h="3" borderRadius="full" bg="blue.500" />
                      <Text fontSize="sm">可用</Text>
                    </HStack>
                    <Text fontSize="sm" fontWeight="bold">{stats.available} ({Math.round(stats.available/stats.totalIps*100)}%)</Text>
                  </HStack>

                  <HStack justify="space-between">
                    <HStack>
                      <Box w="3" h="3" borderRadius="full" bg="purple.500" />
                      <Text fontSize="sm">预留</Text>
                    </HStack>
                    <Text fontSize="sm" fontWeight="bold">{stats.reserved} ({Math.round(stats.reserved/stats.totalIps*100)}%)</Text>
                  </HStack>

                  <HStack justify="space-between">
                    <HStack>
                      <Box w="3" h="3" borderRadius="full" bg="red.500" />
                      <Text fontSize="sm">冲突/阻断</Text>
                    </HStack>
                    <Text fontSize="sm" fontWeight="bold">{stats.conflict + stats.blocked} ({Math.round((stats.conflict + stats.blocked)/stats.totalIps*100)}%)</Text>
                  </HStack>

                  <HStack justify="space-between">
                    <HStack>
                      <Box w="3" h="3" borderRadius="full" bg="yellow.500" />
                      <Text fontSize="sm">其他</Text>
                    </HStack>
                    <Text fontSize="sm" fontWeight="bold">{stats.idle + stats.warning} ({Math.round((stats.idle + stats.warning)/stats.totalIps*100)}%)</Text>
                  </HStack>
                </VStack>
              </Flex>

              <Box p={4} borderTopWidth="1px" borderColor="gray.100" bg="gray.50">
                <Text fontSize="xs" color="gray.500">
                  上次更新: {new Date().toLocaleString()}
                </Text>
              </Box>
            </Box>
          </GridItem>

          {/* IP使用趋势图 - 改进版 */}
          <GridItem>
            <Box
              borderWidth="1px"
              borderColor="gray.100"
              borderRadius="lg"
              overflow="hidden"
            >
              <Box
                bg="blue.50"
                px={4}
                py={3}
                borderBottomWidth="1px"
                borderColor="gray.100"
              >
                <Flex justify="space-between" align="center">
                  <Text fontSize="md" fontWeight="medium" color="blue.700">IP地址使用趋势</Text>
                  <HStack spacing={1}>
                    <IconButton
                      aria-label="查看详情"
                      icon={<InfoIcon />}
                      size="xs"
                      variant="ghost"
                      colorScheme="blue"
                    />
                    <Menu>
                      <MenuButton
                        as={IconButton}
                        aria-label="更多选项"
                        icon={<ChevronDownIcon />}
                        size="xs"
                        variant="ghost"
                        colorScheme="blue"
                      />
                      <MenuList>
                        <MenuItem>查看详细报告</MenuItem>
                        <MenuItem>导出数据</MenuItem>
                        <MenuItem>设置告警阈值</MenuItem>
                      </MenuList>
                    </Menu>
                  </HStack>
                </Flex>
              </Box>

              <Box p={4} h="280px">
                <Line data={ipTrendChartData} options={{}} />
              </Box>

              <Box p={4} borderTopWidth="1px" borderColor="gray.100" bg="gray.50">
                <Flex justify="space-between" align="center">
                  <Text fontSize="xs" color="gray.500">
                    上次更新: {new Date().toLocaleString()}
                  </Text>
                  <HStack spacing={4}>
                    <HStack>
                      <Box w="2" h="2" borderRadius="full" bg="blue.500" />
                      <Text fontSize="xs" color="gray.600">已分配</Text>
                    </HStack>
                    <HStack>
                      <Box w="2" h="2" borderRadius="full" bg="green.500" />
                      <Text fontSize="xs" color="gray.600">使用率</Text>
                    </HStack>
                  </HStack>
                </Flex>
              </Box>
            </Box>
          </GridItem>
        </Grid>

        {/* VLAN使用率对比图 - 改进版 */}
        <Box
          borderWidth="1px"
          borderColor="gray.100"
          borderRadius="lg"
          overflow="hidden"
        >
          <Box
            bg="blue.50"
            px={4}
            py={3}
            borderBottomWidth="1px"
            borderColor="gray.100"
          >
            <Flex justify="space-between" align="center">
              <Text fontSize="md" fontWeight="medium" color="blue.700">VLAN使用率对比</Text>
              <HStack spacing={1}>
                <IconButton
                  aria-label="查看详情"
                  icon={<InfoIcon />}
                  size="xs"
                  variant="ghost"
                  colorScheme="blue"
                />
                <Menu>
                  <MenuButton
                    as={IconButton}
                    aria-label="更多选项"
                    icon={<ChevronDownIcon />}
                    size="xs"
                    variant="ghost"
                    colorScheme="blue"
                  />
                  <MenuList>
                    <MenuItem>查看详细报告</MenuItem>
                    <MenuItem>导出数据</MenuItem>
                    <MenuItem>设置告警阈值</MenuItem>
                  </MenuList>
                </Menu>
              </HStack>
            </Flex>
          </Box>

          <Grid templateColumns={{ base: '1fr', md: '3fr 2fr' }}>
            <GridItem p={4}>
              <Box h="200px">
                <Bar data={vlanComparisonData} options={{}} />
              </Box>
            </GridItem>

            <GridItem p={4} bg="gray.50" borderLeftWidth={{ base: 0, md: "1px" }} borderTopWidth={{ base: "1px", md: 0 }} borderColor="gray.100">
              <Text fontSize="sm" fontWeight="medium" mb={3}>使用率最高的VLAN</Text>
              <VStack align="stretch" spacing={3}>
                {vlans.slice(0, 4).map((vlan, index) => (
                  <Box key={vlan.id}>
                    <Flex justify="space-between" mb={1}>
                      <Text fontSize="sm">{vlan.name}</Text>
                      <Text fontSize="sm" fontWeight="bold" color={
                        index === 0 ? "red.500" :
                        index === 1 ? "orange.500" :
                        index === 2 ? "yellow.500" :
                        "green.500"
                      }>
                        {85 - index * 10}%
                      </Text>
                    </Flex>
                    <Progress
                      value={85 - index * 10}
                      size="sm"
                      colorScheme={
                        index === 0 ? "red" :
                        index === 1 ? "orange" :
                        index === 2 ? "yellow" :
                        "green"
                      }
                      borderRadius="full"
                    />
                  </Box>
                ))}
              </VStack>

              <Button
                variant="link"
                size="sm"
                colorScheme="blue"
                mt={4}
                rightIcon={<ChevronRightIcon />}
              >
                查看所有VLAN
              </Button>
            </GridItem>
          </Grid>

          <Box p={4} borderTopWidth="1px" borderColor="gray.100" bg="gray.50">
            <Flex justify="space-between" align="center">
              <Text fontSize="xs" color="gray.500">
                上次更新: {new Date().toLocaleString()}
              </Text>
              <Button size="xs" leftIcon={<RepeatIcon />} variant="ghost">
                刷新数据
              </Button>
            </Flex>
          </Box>
        </Box>
      </Box>

      {/* 搜索区域 */}
      <Box
        bg="white"
        p={6}
        borderRadius="lg"
        boxShadow="sm"
        mb={6}
      >
        <Flex
          gap={8}
          align="flex-end"
          wrap={{ base: "wrap", md: "nowrap" }}
          justify="space-between"
        >
          <Box flex="1">
            <Text fontWeight="medium" mb={2} color="gray.700">搜索</Text>
            <InputGroup size="md">
              <Input
                placeholder="搜索IP/MAC/描述/负责人/OA单号/VLAN"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                bg="white"
                borderRadius="md"
                borderColor="gray.300"
                _hover={{ borderColor: "gray.400" }}
                pr="4.5rem"
              />
              <InputRightAddon
                children={<SearchIcon />}
                bg="blue.500"
                color="white"
                cursor="pointer"
                transition="all 0.2s"
                _hover={{ bg: "blue.600" }}
              />
            </InputGroup>
          </Box>

          <HStack spacing={3}>
            <Button
              variant="ghost"
              colorScheme="blue"
              leftIcon={<RepeatIcon />}
              onClick={() => {
                setSearchTerm('');
                setSelectedVlan('all');
              }}
              size="md"
              borderRadius="md"
            >
              重置
            </Button>
            <Button
              colorScheme="blue"
              leftIcon={<SearchIcon />}
              size="md"
              borderRadius="md"
            >
              搜索
            </Button>
          </HStack>
        </Flex>
      </Box>



      {/* VLAN地址段列表 */}
      <Box bg="white" p={6} borderRadius="lg" boxShadow="sm" mb={6}>
        <Flex justify="space-between" align="center" mb={6}>
          <Box>
            <Text fontSize="xl" fontWeight="bold" color="gray.800">VLAN地址段列表</Text>
            <Text fontSize="sm" color="gray.500" mt={1}>
              管理所有VLAN网段及IP资源
            </Text>
          </Box>
          <HStack spacing={4}>
            <Box
              bg="blue.50"
              px={4}
              py={2}
              borderRadius="md"
              display="flex"
              alignItems="center"
            >
              <Text fontSize="sm" fontWeight="medium" color="blue.700">
                共 {subnets.length} 个VLAN段
              </Text>
            </Box>
            <Button
              leftIcon={<AddIcon />}
              colorScheme="teal"
              onClick={() => setIsAddModalOpen(true)}
              size="sm"
              borderRadius="md"
            >
              添加VLAN段
            </Button>
          </HStack>
        </Flex>

        {/* VLAN地址段表格 */}
        <Box overflowX="auto">
          <Table variant="simple" size="md" width="100%">
            <Thead bg="gray.50">
              <Tr>
                <Th width="10%">VLAN号</Th>
                <Th width="15%">IP段</Th>
                <Th width="15%">网关</Th>
                <Th width="20%">用途</Th>
                <Th width="10%">类型</Th>
                <Th width="10%">已使用/总数</Th>
                <Th width="12%">使用率</Th>
                <Th width="8%">操作</Th>
              </Tr>
            </Thead>
            <Tbody>
              {/* 模拟数据 */}
              {[
                {
                  id: "vlan-1",
                  vlanId: "10",
                  network: "************/24",
                  gateway: "************",
                  description: "办公网络",
                  dhcpEnabled: true,
                  usedIps: 156,
                  totalIps: 254
                },
                {
                  id: "vlan-2",
                  vlanId: "20",
                  network: "************/24",
                  gateway: "************",
                  description: "开发测试网络",
                  dhcpEnabled: false,
                  usedIps: 78,
                  totalIps: 254
                },
                {
                  id: "vlan-3",
                  vlanId: "30",
                  network: "************/24",
                  gateway: "************",
                  description: "服务器网络",
                  dhcpEnabled: false,
                  usedIps: 230,
                  totalIps: 254
                },
                {
                  id: "vlan-4",
                  vlanId: "40",
                  network: "************/24",
                  gateway: "************",
                  description: "访客网络",
                  dhcpEnabled: true,
                  usedIps: 45,
                  totalIps: 254
                },
                {
                  id: "vlan-5",
                  vlanId: "50",
                  network: "************/24",
                  gateway: "************",
                  description: "管理网络",
                  dhcpEnabled: false,
                  usedIps: 12,
                  totalIps: 254
                },
                {
                  id: "vlan-6",
                  vlanId: "60",
                  network: "**********/24",
                  gateway: "**********",
                  description: "物联网设备",
                  dhcpEnabled: true,
                  usedIps: 180,
                  totalIps: 254
                }
              ].map((subnet) => {
                // 计算使用率
                const usageRate = subnet.totalIps > 0 ? Math.round((subnet.usedIps / subnet.totalIps) * 100) : 0;

                return (
                  <Tr
                    key={subnet.id}
                    _hover={{ bg: "gray.50", cursor: "pointer" }}
                    onClick={() => setSelectedVlan(subnet.vlanId)}
                  >
                    <Td fontWeight="medium" color="blue.600">{subnet.vlanId}</Td>
                    <Td fontFamily="mono">{subnet.network}</Td>
                    <Td fontFamily="mono">{subnet.gateway || '-'}</Td>
                    <Td>{subnet.description}</Td>
                    <Td>
                      <Badge colorScheme={subnet.dhcpEnabled ? "green" : "gray"}>
                        {subnet.dhcpEnabled ? 'DHCP' : '静态'}
                      </Badge>
                    </Td>
                    <Td isNumeric>{subnet.usedIps}/{subnet.totalIps}</Td>
                    <Td>
                      <Flex align="center">
                        <Text mr={2} fontWeight={usageRate > 80 ? "bold" : "normal"} color={
                          usageRate > 90 ? "red.500" :
                          usageRate > 80 ? "orange.500" :
                          usageRate > 60 ? "yellow.500" :
                          "green.500"
                        }>
                          {usageRate}%
                        </Text>
                        <Progress
                          value={usageRate}
                          size="xs"
                          width="80px"
                          colorScheme={
                            usageRate > 90 ? "red" :
                            usageRate > 80 ? "orange" :
                            usageRate > 60 ? "yellow" :
                            "green"
                          }
                          borderRadius="full"
                        />
                      </Flex>
                    </Td>
                    <Td>
                      <HStack spacing={1} justify="center">
                        <IconButton
                          aria-label="查看详情"
                          icon={<InfoIcon />}
                          size="sm"
                          variant="ghost"
                          colorScheme="blue"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedVlan(subnet.vlanId);
                          }}
                        />
                        <IconButton
                          aria-label="编辑VLAN段"
                          icon={<EditIcon />}
                          size="sm"
                          variant="ghost"
                          colorScheme="teal"
                          onClick={(e) => {
                            e.stopPropagation();
                            // 编辑VLAN段的逻辑
                          }}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </Box>
      </Box>

      {/* IP地址表格区 */}
      <Box bg="white" p={6} borderRadius="lg" boxShadow="sm">
        <Flex justify="space-between" align="center" mb={6}>
          <Box>
            <Text fontSize="xl" fontWeight="bold" color="gray.800">IP地址列表</Text>
            <Text fontSize="sm" color="gray.500" mt={1}>
              {selectedVlan !== 'all'
                ? `VLAN ${selectedVlan} 的IP地址`
                : '管理和监控所有IP地址资源'}
            </Text>
          </Box>
          <HStack spacing={4}>
            <Box
              bg="blue.50"
              px={4}
              py={2}
              borderRadius="md"
              display="flex"
              alignItems="center"
            >
              <Text fontSize="sm" fontWeight="medium" color="blue.700">
                共 {filteredIps.length} 条记录
              </Text>
            </Box>
            <Menu>
              <MenuButton
                as={Button}
                rightIcon={<ChevronDownIcon />}
                size="md"
                variant="outline"
                colorScheme="blue"
                borderRadius="md"
              >
                导出
              </MenuButton>
              <MenuList>
                <MenuItem icon={<DownloadIcon />}>导出为Excel</MenuItem>
                <MenuItem icon={<DownloadIcon />}>导出为CSV</MenuItem>
                <MenuItem icon={<RepeatIcon />}>打印列表</MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </Flex>

        {isLoading ? (
          <Flex justify="center" align="center" py={10}>
            <Spinner size="xl" color="blue.500" thickness="4px" />
            <Text ml={4} fontSize="lg" color="gray.500">加载IP地址数据...</Text>
          </Flex>
        ) : filteredIps.length === 0 ? (
          <Box
            py={12}
            px={6}
            borderRadius="lg"
            bg="white"
            borderWidth="1px"
            borderColor="gray.100"
            boxShadow="sm"
          >
            <Flex
              direction="column"
              justify="center"
              align="center"
              maxW="500px"
              mx="auto"
              textAlign="center"
            >
              <Box
                bg="blue.50"
                p={4}
                borderRadius="full"
                mb={6}
              >
                <InfoIcon boxSize={8} color="blue.500" />
              </Box>

              <Text fontSize="xl" fontWeight="bold" color="gray.700" mb={3}>
                没有找到符合条件的IP地址
              </Text>

              <Text fontSize="md" color="gray.500" mb={6} lineHeight="tall">
                当前筛选条件下未找到符合条件的IP地址。您可以尝试调整筛选条件或清除搜索关键词。
              </Text>

              <Flex gap={4} wrap="wrap" justify="center">
                <Button
                  leftIcon={<AddIcon />}
                  colorScheme="blue"
                  onClick={() => setIsAddIpModalOpen(true)}
                  size="md"
                  borderRadius="md"
                >
                  添加IP地址
                </Button>
                <Button
                  leftIcon={<RepeatIcon />}
                  variant="outline"
                  colorScheme="blue"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedVlan('all');
                  }}
                  size="md"
                  borderRadius="md"
                >
                  清除筛选条件
                </Button>
              </Flex>
            </Flex>
          </Box>
        ) : selectedVlan !== 'all' ? (
          // 表格式IP地址列表 - 当选择了特定VLAN时显示
          <>
            {/* 获取当前VLAN的信息 */}
            {(() => {
              const currentVlan = subnets.find(s => s.vlanId === selectedVlan);
              if (!currentVlan) return null;

              // 计算IP地址范围
              const ipParts = currentVlan.network.split('/')[0].split('.');
              const baseIp = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.`;
              const ipRange = Array.from({ length: 256 }, (_, i) => i);

              // 获取该VLAN下的所有IP
              const vlanIps = filteredIps.filter(ip => ip.vlan === selectedVlan);

              return (
                <Box>
                  {/* VLAN信息卡片 */}
                  <Box
                    p={4}
                    bg="blue.50"
                    borderRadius="md"
                    mb={4}
                    borderWidth="1px"
                    borderColor="blue.100"
                  >
                    <Flex justify="space-between" align="center">
                      <Box>
                        <Flex align="center" mb={1}>
                          <Text fontSize="lg" fontWeight="bold" color="blue.700" mr={2}>
                            VLAN {currentVlan.vlanId}
                          </Text>
                          <Badge colorScheme="blue">{currentVlan.network}</Badge>
                        </Flex>
                        <Text fontSize="sm" color="blue.600">{currentVlan.description}</Text>
                      </Box>
                      <HStack>
                        <Button
                          size="sm"
                          leftIcon={<RepeatIcon />}
                          colorScheme="blue"
                          variant="outline"
                          onClick={() => setSelectedVlan('all')}
                        >
                          返回VLAN列表
                        </Button>
                        <Button
                          size="sm"
                          leftIcon={<AddIcon />}
                          colorScheme="blue"
                          onClick={() => setIsAddIpModalOpen(true)}
                        >
                          添加IP
                        </Button>
                      </HStack>
                    </Flex>
                    <Grid templateColumns="repeat(4, 1fr)" gap={4} mt={4}>
                      <Box>
                        <Text fontSize="xs" color="gray.500">网关</Text>
                        <Text fontWeight="medium">{currentVlan.gateway || '未设置'}</Text>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">类型</Text>
                        <Badge colorScheme={currentVlan.dhcpEnabled ? "green" : "gray"}>
                          {currentVlan.dhcpEnabled ? 'DHCP' : '静态'}
                        </Badge>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">IP使用率</Text>
                        <Flex align="center">
                          <Text fontWeight="medium" mr={2}>
                            {Math.round((vlanIps.filter(ip => ip.status === 'allocated').length / vlanIps.length) * 100)}%
                          </Text>
                          <Progress
                            value={Math.round((vlanIps.filter(ip => ip.status === 'allocated').length / vlanIps.length) * 100)}
                            size="xs"
                            width="80px"
                            colorScheme="blue"
                          />
                        </Flex>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">DNS服务器</Text>
                        <Text fontWeight="medium">{currentVlan.dnsServers?.join(', ') || '未设置'}</Text>
                      </Box>
                    </Grid>
                  </Box>

                  {/* IP地址表格 */}
                  <Box
                    borderWidth="1px"
                    borderColor="gray.200"
                    borderRadius="md"
                    overflow="hidden"
                  >
                    <Grid
                      templateColumns="repeat(16, 1fr)"
                      gap={0}
                      bg="gray.50"
                      p={2}
                      borderBottomWidth="1px"
                      borderColor="gray.200"
                    >
                      {Array.from({ length: 16 }, (_, i) => (
                        <Text
                          key={i}
                          fontSize="xs"
                          fontWeight="bold"
                          textAlign="center"
                          color="gray.500"
                        >
                          {i.toString(16).toUpperCase()}
                        </Text>
                      ))}
                    </Grid>

                    <Box maxH="500px" overflowY="auto" p={2}>
                      {/* 每行16个IP地址 */}
                      {Array.from({ length: 16 }, (_, row) => (
                        <Grid
                          key={row}
                          templateColumns="repeat(16, 1fr)"
                          gap={1}
                          mb={1}
                        >
                          {Array.from({ length: 16 }, (_, col) => {
                            const ipLastOctet = row * 16 + col;
                            const fullIp = `${baseIp}${ipLastOctet}`;
                            const ipObj = vlanIps.find(ip => ip.ip === fullIp);

                            // 确定单元格的颜色
                            let bgColor = "gray.100"; // 默认未分配
                            let textColor = "gray.400";

                            if (ipObj) {
                              if (ipObj.status === 'allocated') {
                                bgColor = "green.100";
                                textColor = "green.800";
                              } else if (ipObj.status === 'reserved') {
                                bgColor = "purple.100";
                                textColor = "purple.800";
                              } else if (ipObj.status === 'blocked') {
                                bgColor = "red.100";
                                textColor = "red.800";
                              } else if (ipObj.status === 'available') {
                                bgColor = "blue.100";
                                textColor = "blue.800";
                              }

                              if (ipObj.conflict) {
                                bgColor = "orange.100";
                                textColor = "orange.800";
                              }

                              if (ipObj.violationInfo?.isViolation) {
                                bgColor = "red.100";
                                textColor = "red.800";
                              }
                            }

                            return (
                              <Box
                                key={col}
                                bg={bgColor}
                                color={textColor}
                                borderRadius="sm"
                                p={1}
                                textAlign="center"
                                fontSize="xs"
                                fontWeight={ipObj ? "bold" : "normal"}
                                cursor={ipObj ? "pointer" : "default"}
                                _hover={ipObj ? {
                                  bg: bgColor.replace('100', '200'),
                                  transform: "scale(1.05)",
                                  zIndex: 1,
                                  boxShadow: "sm"
                                } : {}}
                                transition="all 0.2s"
                                onClick={() => {
                                  if (ipObj) {
                                    handleViewIpDetail(ipObj);
                                  }
                                }}
                                position="relative"
                              >
                                {ipLastOctet}

                                {/* 悬停提示 */}
                                {ipObj && (
                                  <Tooltip
                                    label={
                                      <VStack align="start" spacing={1} p={1}>
                                        <Text fontWeight="bold">{ipObj.ip}</Text>
                                        <Flex>
                                          <Text width="80px" color="gray.300">状态:</Text>
                                          <Badge colorScheme={getStatusColor(ipObj.status)}>
                                            {getStatusText(ipObj.status)}
                                          </Badge>
                                        </Flex>
                                        {ipObj.macAddress && (
                                          <Flex>
                                            <Text width="80px" color="gray.300">MAC:</Text>
                                            <Text>{ipObj.macAddress}</Text>
                                          </Flex>
                                        )}
                                        {ipObj.assignedTo && (
                                          <Flex>
                                            <Text width="80px" color="gray.300">负责人:</Text>
                                            <Text>{ipObj.assignedTo}</Text>
                                          </Flex>
                                        )}
                                        {ipObj.assetInfo && (
                                          <Flex>
                                            <Text width="80px" color="gray.300">设备:</Text>
                                            <Text>{ipObj.assetInfo.name}</Text>
                                          </Flex>
                                        )}
                                        {ipObj.description && (
                                          <Flex>
                                            <Text width="80px" color="gray.300">描述:</Text>
                                            <Text>{ipObj.description}</Text>
                                          </Flex>
                                        )}
                                      </VStack>
                                    }
                                    hasArrow
                                    placement="top"
                                    bg="gray.800"
                                    color="white"
                                    borderRadius="md"
                                    px={3}
                                    py={2}
                                  >
                                    <Box
                                      position="absolute"
                                      top={0}
                                      left={0}
                                      right={0}
                                      bottom={0}
                                    />
                                  </Tooltip>
                                )}
                              </Box>
                            );
                          })}
                        </Grid>
                      ))}
                    </Box>
                  </Box>

                  {/* 图例 */}
                  <Flex mt={4} wrap="wrap" gap={4}>
                    <HStack>
                      <Box w="16px" h="16px" bg="green.100" borderRadius="sm" />
                      <Text fontSize="sm">已分配</Text>
                    </HStack>
                    <HStack>
                      <Box w="16px" h="16px" bg="blue.100" borderRadius="sm" />
                      <Text fontSize="sm">可用</Text>
                    </HStack>
                    <HStack>
                      <Box w="16px" h="16px" bg="purple.100" borderRadius="sm" />
                      <Text fontSize="sm">预留</Text>
                    </HStack>
                    <HStack>
                      <Box w="16px" h="16px" bg="red.100" borderRadius="sm" />
                      <Text fontSize="sm">阻断/违规</Text>
                    </HStack>
                    <HStack>
                      <Box w="16px" h="16px" bg="orange.100" borderRadius="sm" />
                      <Text fontSize="sm">冲突</Text>
                    </HStack>
                    <HStack>
                      <Box w="16px" h="16px" bg="gray.100" borderRadius="sm" />
                      <Text fontSize="sm">未分配</Text>
                    </HStack>
                  </Flex>
                </Box>
              );
            })()}
          </>
        ) : (
          // 卡片式IP地址列表 - 当显示所有IP时
          <>
            {/* 卡片式表头 */}
            <Flex px={4} py={3} bg="gray.50" borderRadius="md" fontWeight="bold" mb={2}>
              <Box flex="1.2">IP地址</Box>
              <Box flex="0.6">类型/状态</Box>
              <Box flex="1.5">描述</Box>
              <Box flex="0.8">MAC地址</Box>
              <Box flex="0.8">负责人</Box>
              <Box flex="0.8">最后使用</Box>
              <Box flex="1">操作</Box>
            </Flex>

            {/* 卡片式数据行 */}
            {filteredIps.map(ip => {
              const usageStatus = calculateUsageStatus(ip);
              return (
                <Flex
                  key={ip.id}
                  px={4}
                  py={3}
                  bg={ip.violationInfo?.isViolation ? 'red.50' : ip.conflict ? 'orange.50' : 'white'}
                  borderRadius="lg"
                  boxShadow="sm"
                  align="center"
                  mb={3}
                  _hover={{ boxShadow: "md", bg: ip.violationInfo?.isViolation ? 'red.100' : ip.conflict ? 'orange.100' : "gray.50" }}
                  borderLeft={ip.violationInfo?.isViolation ? '4px solid' : ip.conflict ? '4px solid' : 'none'}
                  borderLeftColor={ip.violationInfo?.isViolation ? 'red.500' : ip.conflict ? 'orange.500' : 'transparent'}
                  cursor="pointer"
                  onClick={() => handleViewIpDetail(ip)}
                >
                  <Box flex="1.2">
                    <HStack>
                      <Text fontWeight="medium">{ip.ip}</Text>
                      {ip.state === 'online' && (
                        <Badge colorScheme="green" variant="solid" fontSize="2xs" borderRadius="full">在线</Badge>
                      )}
                      {ip.state === 'warning' && (
                        <Tooltip label="IP地址存在异常">
                          <WarningIcon color="orange.500" />
                        </Tooltip>
                      )}
                      {ip.violationInfo?.isViolation && (
                        <Tooltip label={ip.violationInfo.description || '违规使用'}>
                          <WarningIcon color="red.500" />
                        </Tooltip>
                      )}
                    </HStack>
                    {ip.vlan && (
                      <Text fontSize="xs" color="gray.500">
                        VLAN: {vlans.find(v => v.id === ip.vlan)?.name || ip.vlan}
                      </Text>
                    )}
                  </Box>

                  <Box flex="0.6">
                    <Badge
                      colorScheme={getStatusColor(ip.status)}
                      mb={1}
                    >
                      {getStatusText(ip.status)}
                    </Badge>
                    <Text fontSize="xs" color="gray.500">{ip.type.toUpperCase()}</Text>
                  </Box>

                  <Box flex="1.5">
                    <Text noOfLines={1}>{ip.description}</Text>
                    {ip.oaNumber && (
                      <Text fontSize="xs" color="blue.500">
                        OA单号: {ip.oaNumber}
                      </Text>
                    )}
                  </Box>

                  <Box flex="0.8">
                    <Text fontSize="sm" fontFamily="mono" noOfLines={1}>
                      {ip.macAddress || '-'}
                    </Text>
                    {ip.assetInfo && (
                      <Text fontSize="xs" color="teal.500" noOfLines={1}>
                        {ip.assetInfo.name}
                      </Text>
                    )}
                  </Box>

                  <Box flex="0.8">
                    <Text>{ip.assignedTo || '-'}</Text>
                    {ip.assetInfo?.department && (
                      <Text fontSize="xs" color="gray.500">
                        {ip.assetInfo.department}
                      </Text>
                    )}
                  </Box>

                  <Box flex="0.8">
                    <Text>{ip.lastUsed || '-'}</Text>
                    <Text fontSize="xs" color={usageStatus.color + '.500'}>
                      {usageStatus.text}
                    </Text>
                  </Box>

                  <Box flex="1">
                    <Flex gap={2}>
                      <IconButton
                        aria-label="查看详情"
                        icon={<InfoIcon />}
                        size="sm"
                        colorScheme="blue"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewIpDetail(ip);
                        }}
                      />
                      <IconButton
                        aria-label="编辑IP"
                        icon={<EditIcon />}
                        size="sm"
                        colorScheme="teal"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditIp(ip);
                        }}
                      />
                      <IconButton
                        aria-label="删除IP"
                        icon={<DeleteIcon />}
                        size="sm"
                        colorScheme="red"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteIp(ip);
                        }}
                      />
                    </Flex>
                  </Box>
                </Flex>
              );
            })}

            {/* 分页控件 */}
            <Flex justify="space-between" align="center" mt={6}>
              <Text fontSize="sm" color="gray.500">
                显示 {filteredIps.length} 条，共 {ipList.length} 条
              </Text>
              <HStack>
                <Button size="sm" leftIcon={<ChevronLeftIcon />} disabled>上一页</Button>
                <Button size="sm" variant="solid" colorScheme="blue">1</Button>
                <Button size="sm" variant="ghost">2</Button>
                <Button size="sm" variant="ghost">3</Button>
                <Button size="sm" rightIcon={<ChevronRightIcon />}>下一页</Button>
              </HStack>
            </Flex>
          </>
        )}
      </Box>

      {/* IP地址详情抽屉 */}
      <Drawer
        isOpen={isIpDetailOpen}
        placement="right"
        onClose={onIpDetailClose}
        size="md"
      >
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader borderBottomWidth="1px">
            IP地址详情
          </DrawerHeader>

          <DrawerBody>
            {currentIp && (
              <VStack spacing={6} align="stretch" py={4}>
                {/* 基本信息卡片 */}
                <Box bg="gray.50" p={4} borderRadius="md">
                  <Flex justify="space-between" align="center" mb={4}>
                    <HStack>
                      <Text fontSize="xl" fontWeight="bold">{currentIp.ip}</Text>
                      <Badge
                        colorScheme={getStatusColor(currentIp.status)}
                        fontSize="sm"
                      >
                        {getStatusText(currentIp.status)}
                      </Badge>
                      {currentIp.state === 'online' && (
                        <Badge colorScheme="green" variant="solid">在线</Badge>
                      )}
                      {currentIp.state === 'offline' && (
                        <Badge colorScheme="gray">离线</Badge>
                      )}
                    </HStack>
                    <HStack>
                      <IconButton
                        aria-label="复制IP"
                        icon={<CopyIcon />}
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          navigator.clipboard.writeText(currentIp.ip);
                          toast({
                            title: "已复制",
                            description: `IP地址 ${currentIp.ip} 已复制到剪贴板`,
                            status: "success",
                            duration: 2000,
                            isClosable: true,
                          });
                        }}
                      />
                      <IconButton
                        aria-label="编辑IP"
                        icon={<EditIcon />}
                        size="sm"
                        colorScheme="blue"
                        variant="ghost"
                        onClick={() => {
                          onIpDetailClose();
                          handleEditIp(currentIp);
                        }}
                      />
                    </HStack>
                  </Flex>

                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <GridItem>
                      <Text color="gray.500" fontSize="sm">类型</Text>
                      <Text>{currentIp.type.toUpperCase()}</Text>
                    </GridItem>
                    <GridItem>
                      <Text color="gray.500" fontSize="sm">VLAN</Text>
                      <Text>{vlans.find(v => v.id === currentIp.vlan)?.name || currentIp.vlan || '-'}</Text>
                    </GridItem>
                    <GridItem>
                      <Text color="gray.500" fontSize="sm">MAC地址</Text>
                      <Text fontFamily="mono">{currentIp.macAddress || '-'}</Text>
                    </GridItem>
                    <GridItem>
                      <Text color="gray.500" fontSize="sm">最后使用</Text>
                      <Text>{currentIp.lastUsed || '-'}</Text>
                    </GridItem>
                    <GridItem colSpan={2}>
                      <Text color="gray.500" fontSize="sm">描述</Text>
                      <Text>{currentIp.description}</Text>
                    </GridItem>
                  </Grid>
                </Box>

                {/* 分配信息 */}
                {currentIp.status === 'allocated' && (
                  <Box p={4} borderRadius="md" borderWidth="1px">
                    <Text fontSize="md" fontWeight="bold" mb={3}>分配信息</Text>
                    <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">负责人</Text>
                        <Text>{currentIp.assignedTo || '-'}</Text>
                      </GridItem>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">OA单号</Text>
                        <Text color="blue.500">{currentIp.oaNumber || '-'}</Text>
                      </GridItem>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">位置</Text>
                        <Text>{currentIp.location || '-'}</Text>
                      </GridItem>
                    </Grid>
                  </Box>
                )}

                {/* 关联资产信息 */}
                {currentIp.assetInfo && (
                  <Box p={4} borderRadius="md" borderWidth="1px">
                    <Flex justify="space-between" align="center" mb={3}>
                      <Text fontSize="md" fontWeight="bold">关联资产</Text>
                      <Button size="xs" rightIcon={<ExternalLinkIcon />} variant="ghost">
                        查看资产详情
                      </Button>
                    </Flex>
                    <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">资产名称</Text>
                        <Text>{currentIp.assetInfo.name}</Text>
                      </GridItem>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">资产类型</Text>
                        <Text>{currentIp.assetInfo.type}</Text>
                      </GridItem>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">部门</Text>
                        <Text>{currentIp.assetInfo.department || '-'}</Text>
                      </GridItem>
                      <GridItem>
                        <Text color="gray.500" fontSize="sm">负责人</Text>
                        <Text>{currentIp.assetInfo.owner || '-'}</Text>
                      </GridItem>
                    </Grid>
                  </Box>
                )}

                {/* 违规信息 */}
                {currentIp.violationInfo?.isViolation && (
                  <Box p={4} borderRadius="md" borderWidth="1px" borderColor="red.300" bg="red.50">
                    <Flex align="center" mb={3}>
                      <WarningIcon color="red.500" mr={2} />
                      <Text fontSize="md" fontWeight="bold" color="red.500">违规信息</Text>
                    </Flex>
                    <Text mb={2}>
                      违规类型: {
                        currentIp.violationInfo.type === 'unauthorized' ? '未授权使用' :
                        currentIp.violationInfo.type === 'conflict' ? 'MAC冲突' : '违规使用'
                      }
                    </Text>
                    <Text mb={2}>违规描述: {currentIp.violationInfo.description || '-'}</Text>
                    <Text>
                      检测时间: {
                        currentIp.violationInfo.detectedAt ?
                        new Date(currentIp.violationInfo.detectedAt).toLocaleString() :
                        '-'
                      }
                    </Text>
                  </Box>
                )}

                {/* 使用历史 */}
                {currentIp.usageHistory && currentIp.usageHistory.length > 0 && (
                  <Box p={4} borderRadius="md" borderWidth="1px">
                    <Text fontSize="md" fontWeight="bold" mb={3}>使用历史</Text>
                    <VStack spacing={3} align="stretch">
                      {currentIp.usageHistory.map((history, idx) => (
                        <Box
                          key={idx}
                          p={3}
                          borderRadius="md"
                          bg="gray.50"
                          borderLeft="4px solid"
                          borderLeftColor={
                            history.status === 'allocated' ? 'green.400' : 'blue.400'
                          }
                        >
                          <Flex justify="space-between" align="center">
                            <Text fontWeight="medium">
                              {getStatusText(history.status)}
                            </Text>
                            <Text fontSize="sm" color="gray.500">{history.date}</Text>
                          </Flex>
                          {history.user && (
                            <Text fontSize="sm">负责人: {history.user}</Text>
                          )}
                          {history.oaNumber && (
                            <Text fontSize="sm" color="blue.500">OA单号: {history.oaNumber}</Text>
                          )}
                        </Box>
                      ))}
                    </VStack>
                  </Box>
                )}
              </VStack>
            )}
          </DrawerBody>

          <DrawerFooter borderTopWidth="1px">
            <Button variant="outline" mr={3} onClick={onIpDetailClose}>
              关闭
            </Button>
            <Button
              colorScheme="blue"
              onClick={() => {
                onIpDetailClose();
                if (currentIp) handleEditIp(currentIp);
              }}
            >
              编辑
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>

      {/* IP计算器模态框 */}
      <Modal isOpen={isCalculatorOpen} onClose={onCalculatorClose} size="lg" isCentered>
        <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
        <ModalContent borderRadius="lg" boxShadow="xl" maxW="700px">
          <ModalHeader bg="blue.50" color="blue.700" borderTopRadius="lg" py={4} px={6}>IP地址计算器</ModalHeader>
          <ModalCloseButton top={4} right={4} />
          <ModalBody pb={6} pt={6} px={6}>
            <FormControl>
              <FormLabel>输入CIDR格式的网段</FormLabel>
              <InputGroup>
                <Input
                  placeholder="例如: ***********/24"
                  value={calculatorInput}
                  onChange={(e) => setCalculatorInput(e.target.value)}
                />
                <InputRightAddon
                  children="计算"
                  bg="blue.500"
                  color="white"
                  cursor="pointer"
                  onClick={handleCalculateIp}
                />
              </InputGroup>
            </FormControl>

            {calculatorResult && (
              <Box mt={6} p={4} borderWidth="1px" borderRadius="md">
                <Text fontSize="lg" fontWeight="bold" mb={4}>计算结果</Text>

                <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                  <GridItem>
                    <Text fontWeight="medium">网络地址</Text>
                    <Text fontFamily="mono">{calculatorResult.networkAddress}</Text>
                  </GridItem>
                  <GridItem>
                    <Text fontWeight="medium">广播地址</Text>
                    <Text fontFamily="mono">{calculatorResult.broadcastAddress}</Text>
                  </GridItem>
                  <GridItem>
                    <Text fontWeight="medium">子网掩码</Text>
                    <Text fontFamily="mono">{calculatorResult.subnetMask}</Text>
                  </GridItem>
                  <GridItem>
                    <Text fontWeight="medium">CIDR表示法</Text>
                    <Text fontFamily="mono">{calculatorResult.cidrNotation}</Text>
                  </GridItem>
                  <GridItem>
                    <Text fontWeight="medium">总主机数</Text>
                    <Text>{calculatorResult.totalHosts}</Text>
                  </GridItem>
                  <GridItem>
                    <Text fontWeight="medium">可用主机数</Text>
                    <Text>{calculatorResult.usableHosts}</Text>
                  </GridItem>
                  <GridItem colSpan={2}>
                    <Text fontWeight="medium">IP范围</Text>
                    <Text fontFamily="mono">{calculatorResult.ipRange}</Text>
                  </GridItem>
                </Grid>

                {calculatorResult.possibleSubnets && calculatorResult.possibleSubnets.length > 0 && (
                  <Box mt={4}>
                    <Text fontWeight="bold" mb={2}>可能的子网划分</Text>
                    <TableContainer>
                      <Table size="sm" variant="simple">
                        <Thead>
                          <Tr>
                            <Th>子网掩码</Th>
                            <Th>子网数量</Th>
                            <Th>每个子网可用主机数</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {calculatorResult.possibleSubnets.map((subnet, idx) => (
                            <Tr key={idx}>
                              <Td>{subnet.mask}</Td>
                              <Td>{subnet.count}</Td>
                              <Td>{subnet.hostsPerSubnet}</Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
              </Box>
            )}
          </ModalBody>

          <ModalFooter>
            <Button onClick={onCalculatorClose}>关闭</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 确认对话框 */}
      <AlertDialog
        isOpen={isConfirmDialogOpen}
        leastDestructiveRef={cancelRef}
        onClose={onConfirmDialogClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {confirmTitle}
            </AlertDialogHeader>

            <AlertDialogBody>
              {confirmMessage}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onConfirmDialogClose}>
                取消
              </Button>
              <Button
                colorScheme="red"
                onClick={async () => {
                  await confirmAction();
                  onConfirmDialogClose();
                }}
                ml={3}
              >
                确认
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      {/* 添加VLAN地址段模态框 */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        size="md"
        isCentered
        motionPreset="slideInBottom"
      >
        <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
        <ModalContent
          borderRadius="lg"
          boxShadow="xl"
          maxW="550px"
          p={0}
          bg="white"
          mx="auto"
        >
          <ModalHeader
            bg="blue.50"
            color="blue.700"
            borderTopRadius="lg"
            borderBottom="1px solid"
            borderColor="gray.100"
            py={4}
            px={6}
            fontSize="lg"
            fontWeight="bold"
          >
            添加VLAN地址段
          </ModalHeader>
          <ModalCloseButton top={4} right={4} />

          <ModalBody py={6} px={6}>
            <VStack spacing={6} align="stretch">
              {/* 基本信息分组 */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={4}>基本信息</Text>

                {/* VLAN手动输入 */}
                <FormControl isRequired mb={4}>
                  <FormLabel fontWeight="medium">VLAN</FormLabel>
                  <Input
                    placeholder="请输入VLAN编号或名称"
                    value={newSegment.vlan}
                    onChange={e => setNewSegment({ ...newSegment, vlan: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>

                {/* IP网段 */}
                <FormControl isRequired mb={4} isInvalid={!!cidrError}>
                  <FormLabel fontWeight="medium">IP网段（CIDR）</FormLabel>
                  <Input
                    placeholder="如 ***********/24"
                    value={newSegment.cidr}
                    onChange={e => {
                      setNewSegment({ ...newSegment, cidr: e.target.value });
                      setCidrError(validateCidr(e.target.value) ? '' : '请输入合法的CIDR格式');
                    }}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                  {cidrError && <FormErrorMessage>{cidrError}</FormErrorMessage>}
                </FormControl>

                {/* 描述 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">描述</FormLabel>
                  <Input
                    placeholder="可选，备注本地址段用途"
                    value={newSegment.description}
                    onChange={e => setNewSegment({ ...newSegment, description: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>

                {/* 组织 */}
                <FormControl>
                  <FormLabel fontWeight="medium">所属组织</FormLabel>
                  <Input
                    placeholder="可选，所属组织或部门"
                    value={newSegment.organization}
                    onChange={e => setNewSegment({ ...newSegment, organization: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>
              </Box>

              {/* 高级选项 */}
              <Accordion allowToggle>
                <AccordionItem border="none" borderTop="1px solid" borderColor="gray.100">
                  <AccordionButton px={0} py={3}>
                    <Box flex="1" textAlign="left" fontWeight="medium" color="gray.600">
                      高级选项
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel pb={4} pt={2}>
                    <VStack spacing={4} align="stretch">
                      <FormControl>
                        <FormLabel fontWeight="medium">网关</FormLabel>
                        <Input
                          placeholder="例如: 192.168.1.1"
                          value={newSegment.gateway}
                          onChange={e => setNewSegment({ ...newSegment, gateway: e.target.value })}
                          size="md"
                          borderRadius="md"
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel fontWeight="medium">DHCP</FormLabel>
                        <Select
                          value={newSegment.dhcpEnabled ? "true" : "false"}
                          onChange={e => setNewSegment({ ...newSegment, dhcpEnabled: e.target.value === "true" })}
                          borderRadius="md"
                        >
                          <option value="true">启用</option>
                          <option value="false">禁用</option>
                        </Select>
                      </FormControl>

                      <FormControl>
                        <FormLabel fontWeight="medium">DNS服务器</FormLabel>
                        <Input
                          placeholder="以逗号分隔，例如: *******,***************"
                          value={newSegment.dnsServers.join(',')}
                          onChange={e => setNewSegment({
                            ...newSegment,
                            dnsServers: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                          })}
                          size="md"
                          borderRadius="md"
                        />
                      </FormControl>
                    </VStack>
                  </AccordionPanel>
                </AccordionItem>
              </Accordion>
            </VStack>
          </ModalBody>

          <ModalFooter
            borderTop="1px solid"
            borderColor="gray.100"
            py={4}
            px={6}
            justifyContent="flex-end"
          >
            <Button
              variant="outline"
              mr={3}
              onClick={() => setIsAddModalOpen(false)}
              borderRadius="md"
            >
              取消
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleAddSegment}
              isDisabled={!newSegment.vlan || !newSegment.cidr || !!cidrError}
              borderRadius="md"
            >
              添加地址段
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 添加单个IP模态框 */}
      <Modal
        isOpen={isAddIpModalOpen}
        onClose={() => setIsAddIpModalOpen(false)}
        size="md"
        isCentered
        motionPreset="slideInBottom"
      >
        <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
        <ModalContent
          borderRadius="lg"
          boxShadow="xl"
          maxW="550px"
          p={0}
          bg="white"
          mx="auto"
        >
          <ModalHeader
            bg="blue.50"
            color="blue.700"
            borderTopRadius="lg"
            borderBottom="1px solid"
            borderColor="gray.100"
            py={4}
            px={6}
            fontSize="lg"
            fontWeight="bold"
          >
            添加IP地址
          </ModalHeader>
          <ModalCloseButton top={4} right={4} />

          <ModalBody py={6} px={6}>
            <VStack spacing={6} align="stretch">
              {/* 基本信息分组 */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={4}>基本信息</Text>

                {/* IP地址 */}
                <FormControl isRequired mb={4}>
                  <FormLabel fontWeight="medium">IP地址</FormLabel>
                  <Input
                    placeholder="请输入IP地址"
                    value={newIp.ip}
                    onChange={e => setNewIp({ ...newIp, ip: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>

                {/* IP类型 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">IP类型</FormLabel>
                  <Select
                    value={newIp.type}
                    onChange={e => setNewIp({ ...newIp, type: e.target.value as 'ipv4' | 'ipv6' })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  >
                    <option value="ipv4">IPv4</option>
                    <option value="ipv6">IPv6</option>
                  </Select>
                </FormControl>

                {/* 状态 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">状态</FormLabel>
                  <Select
                    value={newIp.status}
                    onChange={e => setNewIp({ ...newIp, status: e.target.value as 'allocated' | 'available' | 'reserved' | 'blocked' })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  >
                    <option value="available">可用</option>
                    <option value="allocated">已分配</option>
                    <option value="reserved">预留</option>
                    <option value="blocked">阻断</option>
                  </Select>
                </FormControl>

                {/* VLAN */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">VLAN</FormLabel>
                  <Select
                    value={newIp.vlan || ''}
                    onChange={e => setNewIp({ ...newIp, vlan: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  >
                    <option value="">请选择VLAN</option>
                    {vlans.map(vlan => (
                      <option key={vlan.id} value={vlan.id}>{vlan.name}</option>
                    ))}
                  </Select>
                </FormControl>

                {/* 描述 */}
                <FormControl isRequired>
                  <FormLabel fontWeight="medium">描述</FormLabel>
                  <Input
                    placeholder="请输入IP地址用途描述"
                    value={newIp.description}
                    onChange={e => setNewIp({ ...newIp, description: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>
              </Box>

              {/* 如果状态是已分配，显示额外字段 */}
              {newIp.status === 'allocated' && (
                <Box>
                  <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={4}>分配信息</Text>

                  <FormControl mb={4}>
                    <FormLabel fontWeight="medium">负责人</FormLabel>
                    <Input
                      placeholder="请输入负责人"
                      value={newIp.assignedTo || ''}
                      onChange={e => setNewIp({ ...newIp, assignedTo: e.target.value })}
                      focusBorderColor="blue.500"
                      size="md"
                      bg="white"
                      borderRadius="md"
                    />
                  </FormControl>

                  <FormControl mb={4}>
                    <FormLabel fontWeight="medium">OA单号</FormLabel>
                    <Input
                      placeholder="请输入关联的OA单号"
                      value={newIp.oaNumber || ''}
                      onChange={e => setNewIp({ ...newIp, oaNumber: e.target.value })}
                      focusBorderColor="blue.500"
                      size="md"
                      bg="white"
                      borderRadius="md"
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel fontWeight="medium">MAC地址</FormLabel>
                    <Input
                      placeholder="请输入MAC地址"
                      value={newIp.macAddress || ''}
                      onChange={e => setNewIp({ ...newIp, macAddress: e.target.value })}
                      focusBorderColor="blue.500"
                      size="md"
                      bg="white"
                      borderRadius="md"
                    />
                  </FormControl>
                </Box>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter
            borderTop="1px solid"
            borderColor="gray.100"
            py={4}
            px={6}
            justifyContent="flex-end"
          >
            <Button
              variant="outline"
              mr={3}
              onClick={() => setIsAddIpModalOpen(false)}
              borderRadius="md"
            >
              取消
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleAddIp}
              isDisabled={!newIp.ip || !newIp.description}
              borderRadius="md"
            >
              添加IP
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 自动分配IP模态框 */}
      <Modal
        isOpen={isAutoAssignModalOpen}
        onClose={() => setIsAutoAssignModalOpen(false)}
        size="md"
        isCentered
        motionPreset="slideInBottom"
      >
        <ModalOverlay bg="rgba(0, 0, 0, 0.4)" />
        <ModalContent
          borderRadius="lg"
          boxShadow="xl"
          maxW="550px"
          p={0}
          bg="white"
          mx="auto"
        >
          <ModalHeader
            bg="blue.50"
            color="blue.700"
            borderTopRadius="lg"
            borderBottom="1px solid"
            borderColor="gray.100"
            py={4}
            px={6}
            fontSize="lg"
            fontWeight="bold"
          >
            自动分配IP地址
          </ModalHeader>
          <ModalCloseButton top={4} right={4} />

          <ModalBody py={6} px={6}>
            <VStack spacing={6} align="stretch">
              {/* 基本信息分组 */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={4}>工单信息</Text>

                {/* OA单号 */}
                <FormControl isRequired mb={4}>
                  <FormLabel fontWeight="medium">OA单号</FormLabel>
                  <Input
                    placeholder="请输入OA工单号"
                    value={oaNumber}
                    onChange={e => setOaNumber(e.target.value)}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>

                {/* 部门 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">部门</FormLabel>
                  <Input
                    placeholder="请输入申请部门"
                    value={autoAssignSettings.department || ''}
                    onChange={e => setAutoAssignSettings({ ...autoAssignSettings, department: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>
              </Box>

              {/* IP配置分组 */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color="gray.600" mb={4}>IP配置</Text>

                {/* IP类型 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">IP类型</FormLabel>
                  <Select
                    value={autoAssignSettings.type}
                    onChange={e => setAutoAssignSettings({ ...autoAssignSettings, type: e.target.value as 'ipv4' | 'ipv6' })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  >
                    <option value="ipv4">IPv4</option>
                    <option value="ipv6">IPv6</option>
                  </Select>
                </FormControl>

                {/* VLAN */}
                <FormControl isRequired mb={4}>
                  <FormLabel fontWeight="medium">VLAN</FormLabel>
                  <Select
                    value={autoAssignSettings.vlan}
                    onChange={e => setAutoAssignSettings({ ...autoAssignSettings, vlan: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  >
                    <option value="">请选择VLAN</option>
                    {vlans.map(vlan => (
                      <option key={vlan.id} value={vlan.id}>{vlan.name}</option>
                    ))}
                  </Select>
                </FormControl>

                {/* 子网 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">子网</FormLabel>
                  <Select
                    value={autoAssignSettings.subnet}
                    onChange={e => setAutoAssignSettings({ ...autoAssignSettings, subnet: e.target.value })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  >
                    <option value="">自动选择</option>
                    {subnets
                      .filter(subnet => !autoAssignSettings.vlan || subnet.vlanId === autoAssignSettings.vlan)
                      .map(subnet => (
                        <option key={subnet.id} value={subnet.network}>{subnet.network} - {subnet.description}</option>
                      ))
                    }
                  </Select>
                </FormControl>

                {/* 分配数量 */}
                <FormControl mb={4}>
                  <FormLabel fontWeight="medium">分配数量</FormLabel>
                  <Input
                    type="number"
                    min={1}
                    max={10}
                    value={autoAssignSettings.count}
                    onChange={e => setAutoAssignSettings({
                      ...autoAssignSettings,
                      count: parseInt(e.target.value) || 1
                    })}
                    focusBorderColor="blue.500"
                    size="md"
                    bg="white"
                    borderRadius="md"
                  />
                </FormControl>
              </Box>

              {/* 高级选项 */}
              <Box borderTop="1px solid" borderColor="gray.100" pt={4}>
                {/* 关联资产 */}
                <FormControl mb={4}>
                  <Flex align="center" justify="space-between">
                    <FormLabel fontWeight="medium" mb={0}>关联资产</FormLabel>
                    <Switch
                      colorScheme="blue"
                      isChecked={autoAssignSettings.assignToAsset}
                      onChange={e => setAutoAssignSettings({
                        ...autoAssignSettings,
                        assignToAsset: e.target.checked
                      })}
                      size="md"
                    />
                  </Flex>
                  <Text fontSize="sm" color="gray.500" mt={1}>
                    启用后将自动关联到资产系统中的设备
                  </Text>
                </FormControl>
              </Box>
            </VStack>
          </ModalBody>

          <ModalFooter
            borderTop="1px solid"
            borderColor="gray.100"
            py={4}
            px={6}
            justifyContent="flex-end"
          >
            <Button
              variant="outline"
              mr={3}
              onClick={() => setIsAutoAssignModalOpen(false)}
              borderRadius="md"
            >
              取消
            </Button>
            <Button
              colorScheme="blue"
              onClick={autoAssignIp}
              isDisabled={!oaNumber || !autoAssignSettings.vlan}
              borderRadius="md"
            >
              自动分配
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}
