'use client'

import React from 'react';
import { Box, Heading, Button, useToast, Flex } from '@chakra-ui/react';

const NetworkTopologyPage = () => {
  const toast = useToast();

  const handleCreateTopology = () => {
    toast({
      title: '新建网络拓扑',
      description: '这里将弹出新建网络拓扑的对话框（待实现）',
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  return (
    <Box p={8}>
      <Flex align="center" justify="space-between" mb={6}>
        <Heading size="lg">网络拓扑</Heading>
        <Button colorScheme="teal" onClick={handleCreateTopology}>
          新建网络拓扑
        </Button>
      </Flex>
      {/* 这里可以放置网络拓扑的展示内容 */}
      <Box mt={8} color="gray.500">
        暂无网络拓扑数据。
      </Box>
    </Box>
  );
};

export default NetworkTopologyPage; 