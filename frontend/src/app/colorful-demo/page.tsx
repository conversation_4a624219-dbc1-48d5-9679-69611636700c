'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { 
  ArrowR<PERSON>, 
  Sparkles, 
  Palette, 
  Lightbulb, 
  Zap, 
  Star, 
  Heart, 
  Rocket, 
  Flame 
} from 'lucide-react'

export default function ColorfulDemo() {
  const [darkMode, setDarkMode] = useState(false)
  
  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
    document.documentElement.classList.toggle('dark')
  }
  
  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <div className="container mx-auto py-10 space-y-8">
        {/* 顶部导航 */}
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold gradient-text">丰富多彩的设计风格</h1>
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch id="dark-mode" checked={darkMode} onCheckedChange={toggleDarkMode} />
              <Label htmlFor="dark-mode">暗色模式</Label>
            </div>
            <Button asChild>
              <Link href="/">
                返回首页
              </Link>
            </Button>
          </div>
        </div>
        
        {/* 主要内容 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 左侧卡片 */}
          <Card className="col-span-1 border-colorful-blue/50 shadow-lg shadow-colorful-blue/20 animate-float">
            <CardHeader className="bg-gradient-to-r from-colorful-blue to-colorful-indigo text-white rounded-t-lg">
              <CardTitle>丰富多彩的卡片</CardTitle>
              <CardDescription className="text-white/80">使用渐变和阴影效果</CardDescription>
            </CardHeader>
            <CardContent className="pt-6 space-y-4">
              <div className="flex items-center gap-2">
                <Badge className="bg-colorful-blue text-white">新功能</Badge>
                <Badge className="bg-colorful-green text-white">推荐</Badge>
                <Badge className="bg-colorful-purple text-white">热门</Badge>
              </div>
              <p>这个卡片展示了丰富多彩的设计风格，包括渐变背景、阴影效果和动画。</p>
            </CardContent>
            <CardFooter>
              <Button className="w-full bg-gradient-to-r from-colorful-blue to-colorful-indigo hover:opacity-90">
                了解更多 <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
          
          {/* 中间卡片 */}
          <Card className="col-span-1 md:col-span-2 border-colorful-purple/50 shadow-lg shadow-colorful-purple/20">
            <CardHeader>
              <CardTitle>设计元素展示</CardTitle>
              <CardDescription>探索丰富多彩的设计元素</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="colors" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="colors" className="data-[state=active]:bg-colorful-pink data-[state=active]:text-white">
                    <Palette className="mr-2 h-4 w-4" />
                    颜色
                  </TabsTrigger>
                  <TabsTrigger value="components" className="data-[state=active]:bg-colorful-green data-[state=active]:text-white">
                    <Zap className="mr-2 h-4 w-4" />
                    组件
                  </TabsTrigger>
                  <TabsTrigger value="effects" className="data-[state=active]:bg-colorful-orange data-[state=active]:text-white">
                    <Sparkles className="mr-2 h-4 w-4" />
                    效果
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="colors" className="p-4 space-y-4">
                  <h3 className="text-lg font-medium">丰富多彩的颜色系统</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {[
                      { name: '红色', color: 'bg-colorful-red' },
                      { name: '橙色', color: 'bg-colorful-orange' },
                      { name: '黄色', color: 'bg-colorful-yellow' },
                      { name: '绿色', color: 'bg-colorful-green' },
                      { name: '青色', color: 'bg-colorful-teal' },
                      { name: '蓝色', color: 'bg-colorful-blue' },
                      { name: '靛蓝', color: 'bg-colorful-indigo' },
                      { name: '紫色', color: 'bg-colorful-purple' },
                      { name: '粉色', color: 'bg-colorful-pink' },
                    ].map((item) => (
                      <div key={item.name} className="flex flex-col items-center">
                        <div className={`${item.color} w-12 h-12 rounded-full mb-2`}></div>
                        <span className="text-sm">{item.name}</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="components" className="p-4 space-y-4">
                  <h3 className="text-lg font-medium">组件展示</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">姓名</Label>
                      <Input id="name" placeholder="请输入姓名" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">邮箱</Label>
                      <Input id="email" placeholder="请输入邮箱" />
                    </div>
                    <Button className="bg-colorful-green text-white hover:bg-colorful-green/90">
                      <Lightbulb className="mr-2 h-4 w-4" />
                      提交
                    </Button>
                    <Button variant="outline" className="border-colorful-pink text-colorful-pink hover:bg-colorful-pink/10">
                      <Heart className="mr-2 h-4 w-4" />
                      收藏
                    </Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="effects" className="p-4 space-y-4">
                  <h3 className="text-lg font-medium">特效展示</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="glass p-4 rounded-lg">
                      <h4 className="font-medium flex items-center">
                        <Star className="mr-2 h-4 w-4 text-colorful-yellow" />
                        玻璃态效果
                      </h4>
                      <p className="text-sm mt-2">模糊背景的玻璃态效果，增加界面层次感</p>
                    </div>
                    <div className="gradient-bg p-4 rounded-lg text-white">
                      <h4 className="font-medium flex items-center">
                        <Flame className="mr-2 h-4 w-4" />
                        渐变背景
                      </h4>
                      <p className="text-sm mt-2">丰富多彩的渐变背景，增加视觉吸引力</p>
                    </div>
                    <div className="animate-float p-4 rounded-lg border border-colorful-indigo/50">
                      <h4 className="font-medium flex items-center">
                        <Rocket className="mr-2 h-4 w-4 text-colorful-indigo" />
                        浮动动画
                      </h4>
                      <p className="text-sm mt-2">轻微的上下浮动动画，增加界面活力</p>
                    </div>
                    <div className="p-4 rounded-lg border border-colorful-teal/50 shadow-lg shadow-colorful-teal/20">
                      <h4 className="font-medium flex items-center">
                        <Zap className="mr-2 h-4 w-4 text-colorful-teal" />
                        彩色阴影
                      </h4>
                      <p className="text-sm mt-2">带有颜色的阴影效果，增强立体感</p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
        
        {/* 底部卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[
            { title: '简约现代', icon: <Star className="h-5 w-5" />, color: 'bg-colorful-blue' },
            { title: '丰富多彩', icon: <Palette className="h-5 w-5" />, color: 'bg-colorful-purple' },
            { title: '创新科技', icon: <Zap className="h-5 w-5" />, color: 'bg-colorful-green' },
            { title: '温暖自然', icon: <Heart className="h-5 w-5" />, color: 'bg-colorful-orange' },
          ].map((item, index) => (
            <Card key={index} className="overflow-hidden">
              <div className={`${item.color} p-4 flex justify-center text-white`}>
                {item.icon}
              </div>
              <CardContent className="p-4 text-center">
                <h3 className="font-medium">{item.title}</h3>
                <p className="text-sm text-muted-foreground mt-2">设计风格展示</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
