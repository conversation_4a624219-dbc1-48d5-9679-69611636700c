'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { useTheme } from 'next-themes'
import { Sun, Moon } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function TestPage() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="container mx-auto py-8 space-y-8">
      <h1 className="text-3xl font-bold">组件测试页面</h1>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">主题切换</h2>
          <Button 
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            className="gap-2"
          >
            {theme === 'dark' ? (
              <>
                <Sun className="h-4 w-4" />
                切换到浅色模式
              </>
            ) : (
              <>
                <Moon className="h-4 w-4" />
                切换到深色模式
              </>
            )}
          </Button>
          <p className="mt-2 text-sm text-muted-foreground">
            当前主题: {theme}
          </p>
        </Card>

        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">基础组件</h2>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Button variant="default">主要按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="destructive">危险按钮</Button>
            </div>
            <div className="flex gap-2">
              <Badge variant="default">默认</Badge>
              <Badge variant="secondary">次要</Badge>
              <Badge variant="destructive">危险</Badge>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h2 className="text-xl font-semibold mb-4">表单组件</h2>
          <div className="space-y-4">
            <Input placeholder="输入框" />
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择项" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="option1">选项1</SelectItem>
                <SelectItem value="option2">选项2</SelectItem>
                <SelectItem value="option3">选项3</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center space-x-2">
              <Checkbox id="terms" />
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                同意条款
              </label>
            </div>
          </div>
        </Card>

        <Card className="p-4 col-span-full">
          <h2 className="text-xl font-semibold mb-4">标签页</h2>
          <Tabs defaultValue="account" className="w-[400px]">
            <TabsList>
              <TabsTrigger value="account">账户</TabsTrigger>
              <TabsTrigger value="password">密码</TabsTrigger>
            </TabsList>
            <TabsContent value="account">
              这里是账户内容
            </TabsContent>
            <TabsContent value="password">
              这里是密码内容
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </div>
  )
}