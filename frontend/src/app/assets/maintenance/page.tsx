'use client'

import React, { useState } from 'react'
import { Box, Heading, Flex, useColorMode, Text, HStack } from '@chakra-ui/react'
import { Card, Breadcrumb, Table, Tag, Input, Select, Space, Form, Modal, DatePicker, Tabs, Button } from 'antd'
import { useRouter } from 'next/navigation'
import {
  SearchOutlined,
  PlusOutlined,
  FilterOutlined,
  ExportOutlined,
  ImportOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ToolOutlined,
  HistoryOutlined,
  ScheduleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons'

const { Option } = Select;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

// 示例维护记录数据
const maintenanceRecords = [
  { key: '1', id: 'M-001', device: 'Web 服务器 Dell R740', deviceId: 'DEV-002', type: '定期维护', date: '2023-05-15', status: 'completed', operator: '张工', description: '系统更新和安全补丁安装' },
  { key: '2', id: 'M-002', device: '备份服务器 HP DL380', deviceId: 'DEV-005', type: '故障修复', date: '2023-06-02', status: 'completed', operator: '李工', description: '更换故障硬盘' },
  { key: '3', id: 'M-003', device: '交换机 Router-01', deviceId: 'DEV-001', type: '固件更新', date: '2023-06-10', status: 'pending', operator: '王工', description: '更新交换机固件到最新版本' },
  { key: '4', id: 'M-004', device: '存储设备 NetApp FAS', deviceId: 'DEV-004', type: '硬盘更换', date: '2023-06-18', status: 'in-progress', operator: '刘工', description: '更换老化硬盘' },
  { key: '5', id: 'M-005', device: '安全网关 Palo Alto', deviceId: 'DEV-008', type: '故障修复', date: '2023-06-05', status: 'completed', operator: '张工', description: '解决网络连接问题' },
  { key: '6', id: 'M-006', device: 'GPU 计算节点 NVIDIA', deviceId: 'DEV-006', type: '定期维护', date: '2023-06-20', status: 'pending', operator: '李工', description: '清洁散热系统和更新驱动' },
  { key: '7', id: 'M-007', device: 'UPS 电源', deviceId: 'DEV-010', type: '电池更换', date: '2023-07-01', status: 'scheduled', operator: '王工', description: '更换老化电池组' },
  { key: '8', id: 'M-008', device: '负载均衡器 F5', deviceId: 'DEV-007', type: '配置优化', date: '2023-06-25', status: 'scheduled', operator: '刘工', description: '优化负载均衡策略' },
  { key: '9', id: 'M-009', device: 'Web 服务器 Dell R740', deviceId: 'DEV-002', type: '内存升级', date: '2023-07-05', status: 'scheduled', operator: '张工', description: '增加服务器内存容量' },
  { key: '10', id: 'M-010', device: '数据库服务器 IBM X3650', deviceId: 'DEV-003', type: '定期维护', date: '2023-06-15', status: 'completed', operator: '李工', description: '数据库优化和系统更新' },
];

// 示例设备列表数据
const deviceListData = [
  { key: '1', id: 'DEV-001', name: '交换机 Router-01', type: '网络设备' },
  { key: '2', id: 'DEV-002', name: 'Web 服务器 Dell R740', type: '服务器' },
  { key: '3', id: 'DEV-003', name: '数据库服务器 IBM X3650', type: '服务器' },
  { key: '4', id: 'DEV-004', name: '存储设备 NetApp FAS', type: '存储' },
  { key: '5', id: 'DEV-005', name: '备份服务器 HP DL380', type: '服务器' },
  { key: '6', id: 'DEV-006', name: 'GPU 计算节点 NVIDIA', type: '服务器' },
  { key: '7', id: 'DEV-007', name: '负载均衡器 F5', type: '网络设备' },
  { key: '8', id: 'DEV-008', name: '安全网关 Palo Alto', type: '网络设备' },
  { key: '9', id: 'DEV-009', name: 'KVM 控制台', type: '周边设备' },
  { key: '10', id: 'DEV-010', name: 'UPS 电源', type: '电源设备' },
];

// 示例维护人员数据
const operators = ['张工', '李工', '王工', '刘工', '赵工'];

// 示例维护类型数据
const maintenanceTypes = ['定期维护', '故障修复', '固件更新', '硬盘更换', '电池更换', '配置优化', '内存升级'];

const MaintenancePage = () => {
  const { colorMode } = useColorMode()
  const router = useRouter()
  const [searchText, setSearchText] = useState('')
  const [selectedType, setSelectedType] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null)
  const [selectedOperator, setSelectedOperator] = useState<string | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState('all')
  const [form] = Form.useForm()

  // 过滤维护记录
  const filteredRecords = maintenanceRecords.filter(record => {
    const matchesSearch = searchText ? 
      record.device.toLowerCase().includes(searchText.toLowerCase()) || 
      record.id.toLowerCase().includes(searchText.toLowerCase()) :
      true
    
    const matchesType = selectedType ? record.type === selectedType : true
    const matchesStatus = selectedStatus ? record.status === selectedStatus : true
    const matchesOperator = selectedOperator ? record.operator === selectedOperator : true
    
    // 根据标签页过滤
    const matchesTab = activeTab === 'all' ? true :
                      activeTab === 'completed' ? record.status === 'completed' :
                      activeTab === 'in-progress' ? record.status === 'in-progress' :
                      activeTab === 'pending' ? record.status === 'pending' :
                      activeTab === 'scheduled' ? record.status === 'scheduled' : true
    
    return matchesSearch && matchesType && matchesStatus && matchesOperator && matchesTab
  })

  // 维护记录列定义
  const maintenanceColumns = [
    {
      title: '记录ID',
      dataIndex: 'id',
      key: 'id',
      sorter: (a: any, b: any) => a.id.localeCompare(b.id),
    },
    {
      title: '设备',
      dataIndex: 'device',
      key: 'device',
      sorter: (a: any, b: any) => a.device.localeCompare(b.device),
    },
    {
      title: '设备ID',
      dataIndex: 'deviceId',
      key: 'deviceId',
    },
    {
      title: '维护类型',
      dataIndex: 'type',
      key: 'type',
      filters: maintenanceTypes.map(type => ({ text: type, value: type })),
      onFilter: (value: any, record: any) => record.type === value,
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      sorter: (a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={
          status === 'completed' ? 'green' : 
          status === 'in-progress' ? 'blue' : 
          status === 'pending' ? 'orange' :
          status === 'scheduled' ? 'purple' : 'red'
        }>
          {
            status === 'completed' ? '已完成' : 
            status === 'in-progress' ? '进行中' : 
            status === 'pending' ? '待处理' :
            status === 'scheduled' ? '已计划' : '取消'
          }
        </Tag>
      ),
      filters: [
        { text: '已完成', value: 'completed' },
        { text: '进行中', value: 'in-progress' },
        { text: '待处理', value: 'pending' },
        { text: '已计划', value: 'scheduled' },
      ],
      onFilter: (value: any, record: any) => record.status === value,
    },
    {
      title: '操作人员',
      dataIndex: 'operator',
      key: 'operator',
      filters: operators.map(operator => ({ text: operator, value: operator })),
      onFilter: (value: any, record: any) => record.operator === value,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="small">
          <Button size="small" icon={<EyeOutlined />} type="text" onClick={() => router.push(`/assets/maintenance/${record.id}`)}>
            查看
          </Button>
          <Button size="small" icon={<EditOutlined />} type="text">
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  // 处理添加维护记录
  const handleAddMaintenance = () => {
    setIsModalVisible(true)
  }

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      console.log('添加维护记录:', values)
      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key)
  }

  return (
    <Box maxW="1400px" mx="auto" px={6} py={8}>
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>
          <a onClick={() => router.push('/assets')}>资产管理系统</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>维护记录</Breadcrumb.Item>
      </Breadcrumb>

      <Card
        style={{ margin: 24 }}
        bodyStyle={{ padding: 24 }}
        title={<span><Button icon={<ArrowLeftOutlined />} type="link" onClick={() => router.push('/assets')} style={{ marginRight: 8, padding: 0 }}>返回</Button>维护管理</span>}
      >
        <Tabs activeKey={activeTab} onChange={handleTabChange} style={{ marginBottom: '16px' }}>
          <TabPane 
            tab={<span><HistoryOutlined /> 全部记录</span>} 
            key="all" 
          />
          <TabPane 
            tab={<span><CheckCircleOutlined style={{ color: 'green' }} /> 已完成</span>} 
            key="completed" 
          />
          <TabPane 
            tab={<span><ToolOutlined style={{ color: 'blue' }} /> 进行中</span>} 
            key="in-progress" 
          />
          <TabPane 
            tab={<span><WarningOutlined style={{ color: 'orange' }} /> 待处理</span>} 
            key="pending" 
          />
          <TabPane 
            tab={<span><ScheduleOutlined style={{ color: 'purple' }} /> 已计划</span>} 
            key="scheduled" 
          />
        </Tabs>

        <Flex justify="space-between" align="center" mb={4}>
          <HStack spacing={4}>
            <Input 
              placeholder="搜索设备名称或记录ID" 
              prefix={<SearchOutlined />} 
              style={{ width: 250 }}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              allowClear
            />
            <Select
              placeholder="维护类型"
              style={{ width: 150 }}
              allowClear
              onChange={(value) => setSelectedType(value)}
            >
              {maintenanceTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
            <Select
              placeholder="维护人员"
              style={{ width: 150 }}
              allowClear
              onChange={(value) => setSelectedOperator(value)}
            >
              {operators.map(operator => (
                <Option key={operator} value={operator}>{operator}</Option>
              ))}
            </Select>
          </HStack>
          <HStack spacing={2}>
            <Button icon={<PlusOutlined />} type="primary" onClick={handleAddMaintenance}>
              添加记录
            </Button>
            <Button icon={<ExportOutlined />} type="default">
              导出
            </Button>
          </HStack>
        </Flex>

        <Table 
          columns={maintenanceColumns} 
          dataSource={filteredRecords} 
          pagination={{ pageSize: 10 }}
          rowKey="key"
        />
      </Card>

      {/* 添加维护记录模态框 */}
      <Modal
        title="添加维护记录"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="deviceId"
            label="设备"
            rules={[{ required: true, message: '请选择设备' }]}
          >
            <Select placeholder="请选择设备">
              {deviceListData.map(device => (
                <Option key={device.id} value={device.id}>{device.name} ({device.id})</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="type"
            label="维护类型"
            rules={[{ required: true, message: '请选择维护类型' }]}
          >
            <Select placeholder="请选择维护类型">
              {maintenanceTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="date"
            label="维护日期"
            rules={[{ required: true, message: '请选择维护日期' }]}
          >
            <Input type="date" />
          </Form.Item>
          <Form.Item
            name="operator"
            label="操作人员"
            rules={[{ required: true, message: '请选择操作人员' }]}
          >
            <Select placeholder="请选择操作人员">
              {operators.map(operator => (
                <Option key={operator} value={operator}>{operator}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="completed">已完成</Option>
              <Option value="in-progress">进行中</Option>
              <Option value="pending">待处理</Option>
              <Option value="scheduled">已计划</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="description"
            label="维护描述"
          >
            <Input.TextArea rows={4} placeholder="请输入维护描述" />
          </Form.Item>
        </Form>
      </Modal>
    </Box>
  )
}

export default MaintenancePage