'use client'
import React, { useRef, useState, useEffect } from 'react'
import { Card, Table, Button, Tag, Row, Col, Select, DatePicker, Input, Modal, message, Descriptions, Tabs, Statistic, Alert } from 'antd'
import { useRouter } from 'next/navigation'
import { ArrowLeftOutlined, QrcodeOutlined } from '@ant-design/icons'
import { Html5Qrcode } from 'html5-qrcode'
import { useQuery } from '@tanstack/react-query'
import * as XLSX from 'xlsx'

// 获取当前用户信息
async function fetchCurrentUser() {
  try {
    const res = await fetch('/api/user/me')
    if (res.ok) return await res.json()
  } catch {}
  // fallback: localStorage
  if (typeof window !== 'undefined') {
    const local = localStorage.getItem('user')
    if (local) return JSON.parse(local)
  }
  return null
}

// 获取盘点任务
async function fetchInventoryTasks() {
  const res = await fetch('/api/inventory/tasks')
  if (!res.ok) throw new Error('获取盘点任务失败')
  return await res.json()
}

// 获取资产
async function fetchAssets() {
  const res = await fetch('/api/assets')
  if (!res.ok) throw new Error('获取资产失败')
  return await res.json()
}

// 获取盘点历史
async function fetchInventoryRecords(taskId: string) {
  if (!taskId) return []
  const res = await fetch(`/api/inventory/records?taskId=${taskId}`)
  if (!res.ok) throw new Error('获取盘点历史失败')
  return await res.json()
}

// 盘点登记
async function registerInventoryCheck(data: { assetCode: string, user: string, time: string, taskId: string }): Promise<{ success: boolean, message: string }> {
  const res = await fetch('/api/inventory/check', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
  return await res.json()
}

const columns = [
  { title: '任务编号', dataIndex: 'taskNo', key: 'taskNo' },
  { title: '任务名称', dataIndex: 'name', key: 'name' },
  { title: '状态', dataIndex: 'status', key: 'status', render: (status: string) => <Tag color={status === 'pending' ? 'orange' : status === 'in-progress' ? 'blue' : 'green'}>{status === 'pending' ? '待开始' : status === 'in-progress' ? '进行中' : '已完成'}</Tag> },
  { title: '负责人', dataIndex: 'manager', key: 'manager' },
  { title: '开始日期', dataIndex: 'startDate', key: 'startDate' },
  { title: '结束日期', dataIndex: 'endDate', key: 'endDate' },
]

const InventoryPage = () => {
  const router = useRouter()
  const [scanVisible, setScanVisible] = useState(false)
  const [scanResult, setScanResult] = useState('')
  const [foundAsset, setFoundAsset] = useState<any>(null)
  const [isRegistering, setIsRegistering] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [currentTask, setCurrentTask] = useState<any>(null)
  const qrRef = useRef<any>(null)
  const html5QrCodeRef = useRef<any>(null)
  const [activeTab, setActiveTab] = useState('tasks')
  const [exporting, setExporting] = useState(false)
  const [abnormalModal, setAbnormalModal] = useState<{ visible: boolean, record: any }>({ visible: false, record: null })
  const [abnormalRemark, setAbnormalRemark] = useState('')
  const [createTaskModal, setCreateTaskModal] = useState(false)
  const [newTask, setNewTask] = useState<{ name: string; assetCodes: string[] }>({ name: '', assetCodes: [] })
  const [editTaskModal, setEditTaskModal] = useState<{ visible: boolean, task: any }>({ visible: false, task: null })
  const [selectedAssetCodes, setSelectedAssetCodes] = useState<string[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])

  // 获取用户
  useEffect(() => {
    fetchCurrentUser().then(setUser)
  }, [])

  // 获取盘点任务
  const { data: tasks = [], isLoading: tasksLoading } = useQuery({
    queryKey: ['inventoryTasks'],
    queryFn: fetchInventoryTasks
  })

  // 获取资产
  const { data: assets = [], isLoading: assetsLoading } = useQuery({
    queryKey: ['assets'],
    queryFn: fetchAssets
  })

  // 自动创建一个模拟盘点任务（仅前端mock，便于测试）
  useEffect(() => {
    if (tasks.length === 0 && assets.length > 0) {
      const mockTask = {
        id: 'mock-task-1',
        taskNo: 'T-001',
        name: '模拟盘点任务',
        status: 'in-progress',
        manager: '测试管理员',
        startDate: '2024-06-01',
        endDate: '2024-06-30',
        assetCodes: assets.slice(0, 5).map((a: any) => getCategory(a) ? a.code : null).filter(Boolean)
      }
      // 这里直接将mock任务插入tasks（仅前端，不影响后端）
      tasks.push(mockTask)
    }
  }, [tasks, assets])

  // 资产分类选项自动适配字段
  const getCategory = (a: any) => a.categoryName || a.category || a.categoryLabel || ''
  const categoryOptions = Array.from(new Set(assets.map(getCategory).filter(Boolean))).map(c => ({ label: c, value: c }))
  // 资产组选项自动适配字段
  const getGroups = (a: any) => a.groups || a.group || a.assetGroups || []
  const groupSet = new Set<string>()
  assets.forEach((a: any) => (getGroups(a) || []).forEach((g: string) => groupSet.add(g)))
  const groupOptions = Array.from(groupSet).map(g => ({ label: g, value: g }))

  // 当前任务下资产
  const taskAssets = currentTask ? assets.filter((a: any) => (currentTask.assetCodes || []).includes(a.code)) : []

  // 获取盘点历史
  const { data: records = [], refetch: refetchRecords, isLoading: recordsLoading } = useQuery({
    queryKey: ['inventoryRecords', currentTask?.id],
    queryFn: () => fetchInventoryRecords(currentTask?.id),
    enabled: !!currentTask?.id
  })

  // 统计
  const checkedCodes = new Set(records.map((r: any) => r.assetCode))
  const total = taskAssets.length
  const checked = checkedCodes.size
  const unchecked = total - checked
  const abnormal = records.filter((r: any) => r.status === 'abnormal').length

  // 盘点人权限控制（mock：仅盘点员可盘点）
  const isChecker = user && (user.role === '盘点员' || user.role === 'admin')

  // 打开扫码弹窗时初始化扫码
  const handleOpenScan = () => {
    setScanVisible(true)
    setScanResult('')
    setFoundAsset(null)
    setTimeout(() => {
      if (!qrRef.current) return
      if (!html5QrCodeRef.current) {
        html5QrCodeRef.current = new Html5Qrcode(qrRef.current.id)
      }
      html5QrCodeRef.current.start(
        { facingMode: 'environment' },
        { fps: 10, qrbox: 250 },
        (decodedText: string) => {
          setScanResult(decodedText)
          // 查找资产（仅限本任务）
          const asset = taskAssets.find((a: any) => a.code === decodedText)
          setFoundAsset(asset || null)
          if (!asset) {
            message.error('未找到对应资产，或该资产不属于当前盘点任务')
          }
          html5QrCodeRef.current.stop().then(() => {
            html5QrCodeRef.current = null
          })
        },
        (error: any) => {}
      ).catch((err: any) => {
        message.error('摄像头启动失败')
      })
    }, 300)
  }

  // 关闭弹窗时停止扫码
  const handleCloseScan = () => {
    setScanVisible(false)
    setScanResult('')
    setFoundAsset(null)
    if (html5QrCodeRef.current) {
      html5QrCodeRef.current.stop().catch(() => {})
      html5QrCodeRef.current = null
    }
  }

  // 盘点登记
  const handleRegisterInventory = async () => {
    if (!user || !currentTask) {
      message.error('请先登录并选择盘点任务')
      return
    }
    setIsRegistering(true)
    try {
      const res = await registerInventoryCheck({
        assetCode: foundAsset.code,
        user: user.username || user.name,
        time: new Date().toISOString(),
        taskId: currentTask.id
      })
      setIsRegistering(false)
      if (res.success) {
        message.success('盘点登记成功！')
        setScanResult('')
        setFoundAsset(null)
        // 重新启动扫码，支持连续盘点
        if (qrRef.current) {
          html5QrCodeRef.current = new Html5Qrcode(qrRef.current.id)
          html5QrCodeRef.current.start(
            { facingMode: 'environment' },
            { fps: 10, qrbox: 250 },
            (decodedText: string) => {
              setScanResult(decodedText)
              const asset = taskAssets.find((a: any) => a.code === decodedText)
              setFoundAsset(asset || null)
              if (!asset) {
                message.error('未找到对应资产，或该资产不属于当前盘点任务')
              }
              html5QrCodeRef.current.stop().then(() => {
                html5QrCodeRef.current = null
              })
            },
            (error: any) => {}
          ).catch(() => {})
        }
      } else {
        message.error(res.message || '盘点登记失败')
      }
    } catch (e) {
      setIsRegistering(false)
      message.error('盘点登记失败')
    }
  }

  // 导出Excel
  const handleExport = () => {
    setExporting(true)
    const ws = XLSX.utils.json_to_sheet(records)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '盘点记录')
    XLSX.writeFile(wb, `inventory_records_${currentTask?.name || ''}.xlsx`)
    setExporting(false)
  }

  // 处理异常资产
  const handleAbnormal = () => {
    // mock: 标记为已处理，实际应调用后端接口
    message.success('异常资产已处理')
    setAbnormalModal({ visible: false, record: null })
    setAbnormalRemark('')
    refetchRecords()
  }

  // 新建盘点任务
  const handleCreateTask = () => {
    // mock: 实际应调用后端接口
    message.success('新建盘点任务成功')
    setCreateTaskModal(false)
    setNewTask({ name: '', assetCodes: [] })
    setSelectedAssetCodes([])
    setSelectedCategories([])
    setSelectedGroups([])
    // 刷新任务列表
    setTimeout(() => window.location.reload(), 800)
  }

  // 编辑盘点任务
  const handleEditTask = (task: any) => {
    setEditTaskModal({ visible: true, task })
    setSelectedAssetCodes(task.assetCodes || [])
    setSelectedCategories(Array.from(new Set(task.assetCodes.map((a: any) => a.categoryName).filter(Boolean))))
  }
  const handleSaveEditTask = () => {
    // mock: 实际应调用后端接口
    message.success('任务已更新')
    setEditTaskModal({ visible: false, task: null })
    setSelectedAssetCodes([])
    setSelectedCategories([])
    setTimeout(() => window.location.reload(), 800)
  }

  // 资产表格列
  const assetTableColumns = [
    { title: '资产编号', dataIndex: 'code', key: 'code' },
    { title: '资产名称', dataIndex: 'name', key: 'name' },
    { title: '分类', dataIndex: 'categoryName', key: 'categoryName' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    { title: '位置', dataIndex: 'location', key: 'location' },
  ]

  // 分类+资产组筛选后的资产
  const filteredAssets = assets.filter((a: any) => {
    const cat = getCategory(a)
    const groups = getGroups(a)
    const matchCategory = selectedCategories.length === 0 || selectedCategories.includes(cat)
    const matchGroup = selectedGroups.length === 0 || groups.some((g: string) => selectedGroups.includes(g))
    return matchCategory && matchGroup
  })

  // 全选当前筛选资产
  const handleSelectAllFilteredAssets = () => {
    setSelectedAssetCodes(filteredAssets.map((a: any) => a.code))
  }

  return (
    <Card 
      title={
        <span>
          <Button
            icon={<ArrowLeftOutlined />}
            type="link"
            onClick={() => router.push('/assets')}
            style={{ marginRight: 8, padding: 0 }}
          >
            返回
          </Button>
          资产盘点管理
        </span>
      }
      extra={
        <>
          <Button type="primary" style={{ marginRight: 8 }} onClick={() => setCreateTaskModal(true)}>
            新建盘点任务
          </Button>
          <Select
            placeholder="请选择盘点任务"
            style={{ width: 200, marginRight: 8 }}
            loading={tasksLoading}
            value={currentTask?.id}
            onChange={id => setCurrentTask(tasks.find((t: any) => t.id === id))}
            allowClear
          >
            {tasks.map((task: any) => (
              <Select.Option key={task.id} value={task.id}>{task.name}</Select.Option>
            ))}
          </Select>
          <Button icon={<QrcodeOutlined />} style={{ marginRight: 8 }} onClick={handleOpenScan} disabled={!currentTask || !isChecker}>
            扫码盘点
          </Button>
        </>
      }
    >
      {currentTask && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}><Statistic title="任务资产总数" value={total} /></Col>
          <Col span={6}><Statistic title="已盘点" value={checked} valueStyle={{ color: '#52c41a' }} /></Col>
          <Col span={6}><Statistic title="未盘点" value={unchecked} valueStyle={{ color: '#faad14' }} /></Col>
          <Col span={6}><Statistic title="异常资产" value={abnormal} valueStyle={{ color: abnormal > 0 ? '#f5222d' : '#52c41a' }} /></Col>
        </Row>
      )}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <Tabs.TabPane tab="盘点任务" key="tasks">
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={6}><Input placeholder="任务编号/名称" /></Col>
            <Col span={6}><Select placeholder="状态" style={{ width: '100%' }} allowClear><Select.Option value="pending">待开始</Select.Option><Select.Option value="in-progress">进行中</Select.Option><Select.Option value="completed">已完成</Select.Option></Select></Col>
            <Col span={6}><Input placeholder="负责人" /></Col>
            <Col span={6}><DatePicker.RangePicker style={{ width: '100%' }} /></Col>
          </Row>
          <Table columns={[
            ...columns,
            {
              title: '操作',
              key: 'action',
              render: (_: any, record: any) => <Button size="small" onClick={() => handleEditTask(record)}>编辑</Button>
            }
          ]} dataSource={tasks} pagination={{ pageSize: 10 }} rowKey="id" />
        </Tabs.TabPane>
        <Tabs.TabPane tab="盘点历史" key="history">
          <Button onClick={handleExport} loading={exporting} style={{ marginBottom: 12 }}>导出Excel</Button>
          {recordsLoading ? <Alert type="info" message="加载中..." /> : (
            <Table
              columns={[
                { title: '资产编号', dataIndex: 'assetCode', key: 'assetCode' },
                { title: '资产名称', dataIndex: 'assetName', key: 'assetName' },
                { title: '盘点人', dataIndex: 'user', key: 'user' },
                { title: '盘点时间', dataIndex: 'time', key: 'time' },
                { title: '状态', dataIndex: 'status', key: 'status', render: (status: string) => status === 'abnormal' ? <Tag color="red">异常</Tag> : <Tag color="green">正常</Tag> },
                { title: '备注', dataIndex: 'remark', key: 'remark' },
                { title: '操作', key: 'action', render: (_: any, record: any) => record.status === 'abnormal' ? <Button size="small" onClick={() => setAbnormalModal({ visible: true, record })}>处理</Button> : null }
              ]}
              dataSource={records}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          )}
        </Tabs.TabPane>
      </Tabs>
      <Modal
        open={scanVisible}
        onCancel={handleCloseScan}
        footer={null}
        title="扫码盘点"
        destroyOnClose
        width={350}
      >
        <div ref={qrRef} id="qr-reader" style={{ width: 300, margin: '0 auto' }} />
        {scanResult && (
          <div style={{ marginTop: 16 }}>
            <div>扫码结果：<b>{scanResult}</b></div>
            {foundAsset ? (
              <>
                <Descriptions bordered size="small" column={1} style={{ marginTop: 8 }}>
                  <Descriptions.Item label="资产名称">{foundAsset.name}</Descriptions.Item>
                  <Descriptions.Item label="资产编号">{foundAsset.code}</Descriptions.Item>
                  <Descriptions.Item label="分类">{foundAsset.categoryName || foundAsset.type || '-'}</Descriptions.Item>
                  <Descriptions.Item label="状态">{foundAsset.status || foundAsset.lifecycleStatus || '-'}</Descriptions.Item>
                  <Descriptions.Item label="位置">{foundAsset.location || '-'}</Descriptions.Item>
                </Descriptions>
                <Button type="primary" loading={isRegistering} block style={{ marginTop: 12 }} onClick={handleRegisterInventory}>
                  盘点登记
                </Button>
              </>
            ) : (
              <div style={{ color: 'red', marginTop: 8 }}>未找到对应资产，或该资产不属于当前盘点任务</div>
            )}
          </div>
        )}
      </Modal>
      <Modal
        open={abnormalModal.visible}
        onCancel={() => setAbnormalModal({ visible: false, record: null })}
        onOk={handleAbnormal}
        title="异常资产处理"
        okText="标记为已处理"
      >
        <p>资产编号：{abnormalModal.record?.assetCode}</p>
        <Input.TextArea rows={3} value={abnormalRemark} onChange={e => setAbnormalRemark(e.target.value)} placeholder="处理备注" />
      </Modal>
      <Modal
        open={createTaskModal}
        onCancel={() => setCreateTaskModal(false)}
        onOk={() => {
          setNewTask(t => {
            const updated = { ...t, assetCodes: selectedAssetCodes }
            handleCreateTask()
            return updated
          })
        }}
        title="新建盘点任务"
        okText="创建"
      >
        <div style={{ marginBottom: 12, display: 'flex', gap: 8 }}>
          <Select
            mode="multiple"
            allowClear
            style={{ minWidth: 180 }}
            placeholder="选择资产分类"
            options={categoryOptions}
            value={selectedCategories}
            onChange={setSelectedCategories}
          />
          <Button onClick={handleSelectAllFilteredAssets}>全选当前筛选资产</Button>
        </div>
        <Input
          style={{ marginBottom: 12 }}
          value={newTask.name}
          onChange={e => setNewTask(t => ({ ...t, name: e.target.value }))}
          placeholder="任务名称"
        />
        <Table
          rowSelection={{
            selectedRowKeys: selectedAssetCodes,
            onChange: (keys) => setSelectedAssetCodes(keys as string[])
          }}
          columns={assetTableColumns}
          dataSource={filteredAssets}
          rowKey="code"
          size="small"
          pagination={{ pageSize: 5 }}
        />
      </Modal>
      <Modal
        open={editTaskModal.visible}
        onCancel={() => setEditTaskModal({ visible: false, task: null })}
        onOk={handleSaveEditTask}
        title="编辑盘点任务"
        okText="保存"
      >
        <div style={{ marginBottom: 12, display: 'flex', gap: 8 }}>
          <Select
            mode="multiple"
            allowClear
            style={{ minWidth: 180 }}
            placeholder="选择资产分类"
            options={categoryOptions}
            value={selectedCategories}
            onChange={setSelectedCategories}
          />
          <Button onClick={handleSelectAllFilteredAssets}>全选当前筛选资产</Button>
        </div>
        <Input
          style={{ marginBottom: 12 }}
          value={editTaskModal.task?.name || ''}
          onChange={e => setEditTaskModal(m => ({ ...m, task: { ...m.task, name: e.target.value } }))}
          placeholder="任务名称"
        />
        <Table
          rowSelection={{
            selectedRowKeys: selectedAssetCodes,
            onChange: (keys) => setSelectedAssetCodes(keys as string[])
          }}
          columns={assetTableColumns}
          dataSource={filteredAssets}
          rowKey="code"
          size="small"
          pagination={{ pageSize: 5 }}
        />
      </Modal>
    </Card>
  )
}

export default InventoryPage 