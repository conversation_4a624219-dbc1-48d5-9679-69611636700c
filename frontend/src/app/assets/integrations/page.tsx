'use client'
import React, { useState } from 'react'
import { Card, Tabs, Form, Input, Button, Switch, Select, Table, Tag, Space, message, Tooltip, Divider, Alert } from 'antd'
import { useRouter } from 'next/navigation'
import { 
  ApiOutlined, 
  <PERSON>Outlined, 
  <PERSON><PERSON><PERSON>cleOutlined, 
  CloseCircleOutlined, 
  SyncOutlined,
  SettingOutlined,
  Question<PERSON>ircleOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons'
import type { TableColumnsType } from 'antd'

const { TabPane } = Tabs
const { Option } = Select

// 集成系统类型
interface IntegrationSystem {
  id: string
  name: string
  type: 'erp' | 'mes' | 'iot' | 'communication'
  status: 'active' | 'inactive' | 'error'
  lastSync: string
  endpoint: string
  description: string
  config: Record<string, any>
}

// 系统集成日志
interface IntegrationLog {
  id: string
  systemId: string
  timestamp: string
  action: 'sync' | 'config' | 'test' | 'error'
  status: 'success' | 'failure'
  message: string
}

const AssetIntegrationsPage = () => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('systems')
  const [editingSystem, setEditingSystem] = useState<IntegrationSystem | null>(null)
  const [isFormVisible, setIsFormVisible] = useState(false)
  const [form] = Form.useForm()

  // 模拟集成系统数据
  const [systems, setSystems] = useState<IntegrationSystem[]>([
    {
      id: '1',
      name: 'SAP ERP',
      type: 'erp',
      status: 'active',
      lastSync: '2023-05-10 15:30:00',
      endpoint: 'https://erp-api.example.com/v1',
      description: '与SAP ERP系统集成，同步资产财务信息',
      config: {
        apiKey: 'sap-api-key-123',
        syncInterval: 'daily',
        syncAssets: true,
        syncFinancials: true
      }
    },
    {
      id: '2',
      name: '生产MES系统',
      type: 'mes',
      status: 'inactive',
      lastSync: '2023-05-05 09:15:00',
      endpoint: 'https://mes.internal.example.com/api',
      description: '与生产MES系统集成，获取设备运行数据',
      config: {
        username: 'mes-user',
        password: '********',
        syncInterval: 'hourly',
        syncOperationData: true
      }
    },
    {
      id: '3',
      name: 'IoT平台',
      type: 'iot',
      status: 'error',
      lastSync: '2023-05-09 18:45:00',
      endpoint: 'wss://iot-platform.example.com/devices',
      description: '与物联网平台集成，实时监控设备状态',
      config: {
        apiKey: 'iot-api-key-456',
        syncInterval: 'realtime',
        deviceTypes: ['sensor', 'controller', 'gateway']
      }
    },
    {
      id: '4',
      name: '企业微信',
      type: 'communication',
      status: 'active',
      lastSync: '2023-05-10 10:00:00',
      endpoint: 'https://qyapi.weixin.qq.com/cgi-bin/',
      description: '与企业微信集成，发送资产报警和通知',
      config: {
        corpId: 'wx123456',
        corpSecret: '********',
        agentId: '1000001',
        notifyEvents: ['alarm', 'maintenance', 'expiration']
      }
    }
  ])

  // 模拟集成日志数据
  const [logs, setLogs] = useState<IntegrationLog[]>([
    {
      id: '101',
      systemId: '1',
      timestamp: '2023-05-10 15:30:00',
      action: 'sync',
      status: 'success',
      message: '成功同步123条资产记录'
    },
    {
      id: '102',
      systemId: '3',
      timestamp: '2023-05-09 18:45:00',
      action: 'error',
      status: 'failure',
      message: '连接IoT平台失败: 认证错误'
    },
    {
      id: '103',
      systemId: '2',
      timestamp: '2023-05-05 09:15:00',
      action: 'sync',
      status: 'success',
      message: '成功同步设备运行数据'
    },
    {
      id: '104',
      systemId: '4',
      timestamp: '2023-05-10 10:00:00',
      action: 'test',
      status: 'success',
      message: '企业微信连接测试成功'
    }
  ])

  // 系统集成表格列
  const systemColumns: TableColumnsType<IntegrationSystem> = [
    {
      title: '系统名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {text}
          <Tag color={
            record.type === 'erp' ? 'blue' : 
            record.type === 'mes' ? 'green' : 
            record.type === 'iot' ? 'purple' : 
            'orange'
          }>
            {
              record.type === 'erp' ? 'ERP' : 
              record.type === 'mes' ? 'MES' : 
              record.type === 'iot' ? 'IoT' : 
              '通讯'
            }
          </Tag>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'active' ? 'success' : 
          status === 'inactive' ? 'default' : 
          'error'
        }>
          {
            status === 'active' ? <><CheckCircleOutlined /> 活跃</> : 
            status === 'inactive' ? <><CloseCircleOutlined /> 未启用</> : 
            <><CloseCircleOutlined /> 错误</>
          }
        </Tag>
      )
    },
    {
      title: '最后同步',
      dataIndex: 'lastSync',
      key: 'lastSync'
    },
    {
      title: '接口地址',
      dataIndex: 'endpoint',
      key: 'endpoint',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="primary" 
            size="small" 
            icon={<SettingOutlined />}
            onClick={() => handleEditSystem(record)}
          >
            配置
          </Button>
          <Button 
            size="small" 
            icon={<SyncOutlined />}
            onClick={() => handleSyncSystem(record)}
          >
            同步
          </Button>
          <Button 
            type={record.status === 'active' ? 'default' : 'primary'} 
            size="small"
            danger={record.status === 'active'}
            onClick={() => handleToggleStatus(record)}
          >
            {record.status === 'active' ? '停用' : '启用'}
          </Button>
        </Space>
      )
    }
  ]

  // 日志表格列
  const logColumns: TableColumnsType<IntegrationLog> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      sorter: (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    },
    {
      title: '系统',
      dataIndex: 'systemId',
      key: 'systemId',
      render: (systemId) => {
        const system = systems.find(s => s.id === systemId)
        return system ? system.name : systemId
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action) => (
        <Tag color={
          action === 'sync' ? 'blue' : 
          action === 'config' ? 'green' : 
          action === 'test' ? 'purple' : 
          'red'
        }>
          {
            action === 'sync' ? '同步' : 
            action === 'config' ? '配置' : 
            action === 'test' ? '测试' : 
            '错误'
          }
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'success' ? 'success' : 'error'}>
          {status === 'success' ? '成功' : '失败'}
        </Tag>
      )
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    }
  ]

  // 编辑系统
  const handleEditSystem = (system: IntegrationSystem) => {
    setEditingSystem(system)
    setIsFormVisible(true)
    form.setFieldsValue({
      name: system.name,
      type: system.type,
      endpoint: system.endpoint,
      description: system.description,
      ...system.config
    })
  }

  // 添加系统
  const handleAddSystem = () => {
    setEditingSystem(null)
    setIsFormVisible(true)
    form.resetFields()
  }

  // 提交表单
  const handleFormSubmit = () => {
    form.validateFields().then(values => {
      const { name, type, endpoint, description, ...configValues } = values
      
      if (editingSystem) {
        // 更新系统
        const updatedSystems = systems.map(system => {
          if (system.id === editingSystem.id) {
            return {
              ...system,
              name,
              type,
              endpoint,
              description,
              config: configValues
            }
          }
          return system
        })
        setSystems(updatedSystems)
        message.success('系统配置已更新')
        
        // 添加日志
        const newLog: IntegrationLog = {
          id: Date.now().toString(),
          systemId: editingSystem.id,
          timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
          action: 'config',
          status: 'success',
          message: '系统配置已更新'
        }
        setLogs([newLog, ...logs])
      } else {
        // 添加系统
        const newSystem: IntegrationSystem = {
          id: Date.now().toString(),
          name,
          type,
          status: 'inactive',
          lastSync: '-',
          endpoint,
          description,
          config: configValues
        }
        setSystems([...systems, newSystem])
        message.success('系统已添加')
      }
      
      setIsFormVisible(false)
    })
  }

  // 同步系统
  const handleSyncSystem = (system: IntegrationSystem) => {
    message.loading(`正在与${system.name}同步数据...`, 2)
      .then(() => {
        // 更新系统同步时间
        const updatedSystems = systems.map(s => {
          if (s.id === system.id) {
            return {
              ...s,
              lastSync: new Date().toISOString().replace('T', ' ').substring(0, 19)
            }
          }
          return s
        })
        setSystems(updatedSystems)
        
        // 添加日志
        const newLog: IntegrationLog = {
          id: Date.now().toString(),
          systemId: system.id,
          timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
          action: 'sync',
          status: 'success',
          message: `成功与${system.name}同步数据`
        }
        setLogs([newLog, ...logs])
        
        message.success(`成功与${system.name}同步数据`)
      })
  }

  // 切换系统状态
  const handleToggleStatus = (system: IntegrationSystem) => {
    const newStatus: 'active' | 'inactive' | 'error' = system.status === 'active' ? 'inactive' : 'active'
    const updatedSystems = systems.map(s => {
      if (s.id === system.id) {
        return {
          ...s,
          status: newStatus
        }
      }
      return s
    })
    setSystems(updatedSystems)
    
    // 添加日志
    const newLog: IntegrationLog = {
      id: Date.now().toString(),
      systemId: system.id,
      timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
      action: 'config',
      status: 'success',
      message: `系统状态已更改为${newStatus === 'active' ? '活跃' : '未启用'}`
    }
    setLogs([newLog, ...logs])
    
    message.success(`${system.name}已${newStatus === 'active' ? '启用' : '停用'}`)
  }

  // 渲染系统配置表单
  const renderSystemForm = () => {
    const systemType = Form.useWatch('type', form)
    
    return (
      <Card
        title={editingSystem ? `编辑系统: ${editingSystem.name}` : '添加新系统'}
        extra={
          <Button onClick={() => setIsFormVisible(false)}>取消</Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          initialValues={{ type: 'erp' }}
        >
          <Form.Item
            name="name"
            label="系统名称"
            rules={[{ required: true, message: '请输入系统名称' }]}
          >
            <Input placeholder="请输入系统名称" />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="系统类型"
            rules={[{ required: true, message: '请选择系统类型' }]}
          >
            <Select>
              <Option value="erp">ERP系统</Option>
              <Option value="mes">MES系统</Option>
              <Option value="iot">IoT平台</Option>
              <Option value="communication">通讯平台</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="endpoint"
            label="接口地址"
            rules={[{ required: true, message: '请输入接口地址' }]}
          >
            <Input placeholder="请输入接口地址" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入系统描述" />
          </Form.Item>
          
          <Divider>系统特定配置</Divider>
          
          {systemType === 'erp' && (
            <>
              <Form.Item
                name="apiKey"
                label="API密钥"
                rules={[{ required: true, message: '请输入API密钥' }]}
              >
                <Input.Password placeholder="请输入API密钥" />
              </Form.Item>
              
              <Form.Item
                name="syncInterval"
                label="同步频率"
                rules={[{ required: true, message: '请选择同步频率' }]}
              >
                <Select>
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每天</Option>
                  <Option value="weekly">每周</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="syncAssets"
                label="同步资产数据"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="syncFinancials"
                label="同步财务数据"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </>
          )}
          
          {systemType === 'mes' && (
            <>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
              
              <Form.Item
                name="password"
                label="密码"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
              
              <Form.Item
                name="syncInterval"
                label="同步频率"
                rules={[{ required: true, message: '请选择同步频率' }]}
              >
                <Select>
                  <Option value="realtime">实时</Option>
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每天</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="syncOperationData"
                label="同步运行数据"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </>
          )}
          
          {systemType === 'iot' && (
            <>
              <Form.Item
                name="apiKey"
                label="API密钥"
                rules={[{ required: true, message: '请输入API密钥' }]}
              >
                <Input.Password placeholder="请输入API密钥" />
              </Form.Item>
              
              <Form.Item
                name="syncInterval"
                label="同步频率"
                rules={[{ required: true, message: '请选择同步频率' }]}
              >
                <Select>
                  <Option value="realtime">实时</Option>
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每天</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="deviceTypes"
                label="设备类型"
                rules={[{ required: true, message: '请选择设备类型' }]}
              >
                <Select mode="multiple">
                  <Option value="sensor">传感器</Option>
                  <Option value="controller">控制器</Option>
                  <Option value="gateway">网关</Option>
                  <Option value="terminal">终端</Option>
                </Select>
              </Form.Item>
            </>
          )}
          
          {systemType === 'communication' && (
            <>
              <Form.Item
                name="corpId"
                label="企业ID"
                rules={[{ required: true, message: '请输入企业ID' }]}
              >
                <Input placeholder="请输入企业ID" />
              </Form.Item>
              
              <Form.Item
                name="corpSecret"
                label="企业密钥"
                rules={[{ required: true, message: '请输入企业密钥' }]}
              >
                <Input.Password placeholder="请输入企业密钥" />
              </Form.Item>
              
              <Form.Item
                name="agentId"
                label="应用ID"
                rules={[{ required: true, message: '请输入应用ID' }]}
              >
                <Input placeholder="请输入应用ID" />
              </Form.Item>
              
              <Form.Item
                name="notifyEvents"
                label="通知事件"
                rules={[{ required: true, message: '请选择通知事件' }]}
              >
                <Select mode="multiple">
                  <Option value="alarm">告警</Option>
                  <Option value="maintenance">维护</Option>
                  <Option value="expiration">到期</Option>
                  <Option value="approval">审批</Option>
                </Select>
              </Form.Item>
            </>
          )}
          
          <Form.Item>
            <Button type="primary" htmlType="submit">
              {editingSystem ? '更新' : '添加'}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    )
  }

  return (
    <div>
      <Card 
        title={
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => router.push('/assets')} />
            系统集成
          </Space>
        }
        extra={
          <Tooltip title="系统集成允许资产管理系统与其他企业系统进行数据交换">
            <QuestionCircleOutlined />
          </Tooltip>
        }
      >
        {isFormVisible ? (
          renderSystemForm()
        ) : (
          <>
            <Alert
              message="系统集成"
              description="通过系统集成，资产管理系统可以与企业ERP、MES、IoT平台和通讯工具进行数据交换，实现资产全生命周期的自动化管理。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane 
                tab={<span><ApiOutlined />集成系统</span>} 
                key="systems"
              >
                <div style={{ marginBottom: 16 }}>
                  <Button 
                    type="primary" 
                    icon={<LinkOutlined />}
                    onClick={handleAddSystem}
                  >
                    添加系统
                  </Button>
                </div>
                
                <Table 
                  columns={systemColumns} 
                  dataSource={systems}
                  rowKey="id"
                  pagination={false}
                />
              </TabPane>
              
              <TabPane 
                tab={<span><SyncOutlined />集成日志</span>} 
                key="logs"
              >
                <Table 
                  columns={logColumns} 
                  dataSource={logs}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                />
              </TabPane>
            </Tabs>
          </>
        )}
      </Card>
    </div>
  )
}

export default AssetIntegrationsPage 