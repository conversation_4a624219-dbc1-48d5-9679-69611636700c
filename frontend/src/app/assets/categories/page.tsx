'use client'
import React, { useState } from 'react'
import { Card, Tree, Button, Space, message, Dropdown, Popconfirm, Input, Row, Col } from 'antd'
import { PlusOutlined, DeleteOutlined, EditOutlined, SearchOutlined, FilterOutlined, ArrowUpOutlined, ArrowDownOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { 
  getAssetCategories,
  type AssetCategory as BaseAssetCategory,
  updateAssetCategory,
  createAssetCategory,
  deleteAssetCategory
} from '@/api/device'
import { DataNode } from 'antd/es/tree';
import { Form, Select, Modal, Tag } from 'antd';

// 扩展AssetCategory类型，添加sort_order字段
interface AssetCategory extends BaseAssetCategory {
  sort_order?: number;
}

const AssetCategoriesPage = () => {
  const router = useRouter()
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [editingCategory, setEditingCategory] = useState<AssetCategory | null>(null)
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [autoExpandParent, setAutoExpandParent] = useState(true)
  const [selectedParentCategory, setSelectedParentCategory] = useState<number | null>(null)
  const [selectedSubCategory, setSelectedSubCategory] = useState<number | null>(null)

  const { data: categories, isLoading, refetch } = useQuery<AssetCategory[]>({
    queryKey: ['assetCategories'],
    queryFn: getAssetCategories
  })

  // 获取所有标签
  const allTags = React.useMemo(() => {
    if (!categories) return [];
    const tags = new Set<string>();
    // 注释掉tags相关代码，因为AssetCategory的attributes中没有tags字段
    // categories.forEach(cat => {
    //   if (cat.attributes?.tags?.length) {
    //     cat.attributes.tags.forEach((tag: string) => tags.add(tag));
    //   }
    // });
    return Array.from(tags);
  }, [categories]);

  // 获取一级分类（父分类）
  const parentCategories = React.useMemo(() => {
    if (!categories) return [];
    return categories.filter(cat => !cat.parent_id || cat.parent_id === null);
  }, [categories]);

  // 获取二级分类（子分类）
  const subCategories = React.useMemo(() => {
    if (!categories || !selectedParentCategory) return [];
    return categories.filter(cat => cat.parent_id === selectedParentCategory);
  }, [categories, selectedParentCategory]);

  // 删除分类
  const handleDelete = async (id: number) => {
    try {
      await deleteAssetCategory(id);
      message.success('分类删除成功');
      refetch();
    } catch (error) {
      message.error('删除失败，请检查是否有子分类或关联资产');
    }
  };

  // 上移/下移分类（同级内排序）
  const handleMove = async (category: AssetCategory, direction: 'up' | 'down') => {
    const sameLevelCategories = categories?.filter(c => c.parent_id === category.parent_id) || [];
    const currentIndex = sameLevelCategories.findIndex(c => c.id === category.id);
    
    if (direction === 'up' && currentIndex > 0) {
      const targetCategory = sameLevelCategories[currentIndex - 1];
      // 使用sort_order或默认值
      const currentSortOrder = category.sort_order || category.id;
      const targetSortOrder = targetCategory.sort_order || targetCategory.id;
      
      await updateAssetCategory(category.id, { sort_order: targetSortOrder } as Partial<BaseAssetCategory>);
      await updateAssetCategory(targetCategory.id, { sort_order: currentSortOrder } as Partial<BaseAssetCategory>);
      message.success('移动成功');
      refetch();
    } else if (direction === 'down' && currentIndex < sameLevelCategories.length - 1) {
      const targetCategory = sameLevelCategories[currentIndex + 1];
      // 使用sort_order或默认值
      const currentSortOrder = category.sort_order || category.id;
      const targetSortOrder = targetCategory.sort_order || targetCategory.id;
      
      await updateAssetCategory(category.id, { sort_order: targetSortOrder } as Partial<BaseAssetCategory>);
      await updateAssetCategory(targetCategory.id, { sort_order: currentSortOrder } as Partial<BaseAssetCategory>);
      message.success('移动成功');
      refetch();
    }
  };

  // 新增/编辑弹窗提交
  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      const payload = {
        ...values,
        attributes: { tags: values.tags || [] },
        parent_id: values.parent_id || null,
        level: values.parent_id ? (categories?.find(c => c.id === values.parent_id)?.level || 1) + 1 : 1
      }
      if (editingCategory) {
        // 编辑
        await updateAssetCategory(editingCategory.id, payload)
        message.success('分类更新成功')
      } else {
        // 新增
        await createAssetCategory(payload)
        message.success('分类创建成功')
      }
      setModalVisible(false)
      setEditingCategory(null)
      form.resetFields()
      // 刷新
      router.refresh?.() // nextjs13+支持
    } catch (e) {}
  }

  // 打开弹窗
  const openModal = (category?: AssetCategory) => {
    setEditingCategory(category || null)
    setModalVisible(true)
    if (category) {
      form.setFieldsValue({
        name: category.name,
        code: category.code,
        parent_id: category.parent_id,
        // tags: category.attributes?.tags || [] // 注释掉，因为attributes中没有tags字段
      })
    } else {
      form.resetFields()
    }
  }

  // 搜索过滤
  const getFilteredCategories = () => {
    if (!categories) return [];
    
    // 标签过滤 - 注释掉，因为attributes中没有tags字段
    let filtered = categories;
    // if (selectedTags.length > 0) {
    //   filtered = filtered.filter(cat => {
    //     const catTags = cat.attributes?.tags || [];
    //     return selectedTags.some(tag => catTags.includes(tag));
    //   });
    // }
    
    // 按一级分类筛选
    if (selectedParentCategory) {
      filtered = filtered.filter(cat => {
        // 显示选中的一级分类及其所有子分类
        return cat.id === selectedParentCategory || cat.parent_id === selectedParentCategory;
      });
    }
    
    // 按二级分类筛选
    if (selectedSubCategory) {
      filtered = filtered.filter(cat => {
        // 显示选中的二级分类及其父分类
        return cat.id === selectedSubCategory || 
               (cat.id === selectedParentCategory && selectedParentCategory);
      });
    }
    
    // 搜索过滤
    if (searchValue) {
      filtered = filtered.filter(cat => 
        cat.name.toLowerCase().includes(searchValue.toLowerCase()) || 
        (cat.code || '').toLowerCase().includes(searchValue.toLowerCase())
      );
      
      // 获取所有匹配项的父节点ID，用于展开
      const matchedIds = filtered.map(cat => cat.id);
      const parentIds: number[] = [];
      
      const getParentIds = (id: number) => {
        const cat = categories.find(c => c.id === id);
        if (cat && cat.parent_id) {
          parentIds.push(cat.parent_id);
          getParentIds(cat.parent_id);
        }
      };
      
      matchedIds.forEach(id => getParentIds(id));
      setExpandedKeys([...matchedIds, ...parentIds]);
      setAutoExpandParent(true);
    }
    
    return filtered;
  };

  // 构建树结构，节点title显示标签
  const buildCategoryTree = (categories: AssetCategory[]): DataNode[] => {
    if (!categories) return [];
    const idToNode: Record<number, DataNode & { children?: DataNode[] }> = {};
    categories.forEach(cat => {
      idToNode[cat.id] = {
        key: cat.id,
        title: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <span>
              {cat.name}
              {/* 注释掉tags显示，因为AssetCategory的attributes中没有tags字段 */}
              {/* cat.attributes?.tags?.length ? cat.attributes.tags.map((tag: string) => (
                <Tag 
                  color="blue" 
                  key={tag} 
                  style={{ marginLeft: 4 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTags(prev => 
                      prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]
                    );
                  }}
                >{tag}</Tag>
              )) : null */}
            </span>
            <Space onClick={e => e.stopPropagation()}>
              <Button 
                type="text" 
                icon={<EditOutlined />} 
                size="small"
                onClick={() => openModal(cat)}
              />
              <Popconfirm
                title="确定要删除此分类吗？"
                description="删除后无法恢复，且会影响关联的资产"
                onConfirm={() => handleDelete(cat.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button 
                  type="text" 
                  icon={<DeleteOutlined />} 
                  danger 
                  size="small"
                />
              </Popconfirm>
              <Button 
                type="text" 
                icon={<ArrowUpOutlined />} 
                size="small"
                onClick={() => handleMove(cat, 'up')}
              />
              <Button 
                type="text" 
                icon={<ArrowDownOutlined />} 
                size="small"
                onClick={() => handleMove(cat, 'down')}
              />
            </Space>
          </div>
        ),
        children: [],
        ...cat
      };
    });
    
    // 构建树
    const tree: DataNode[] = [];
    categories.forEach(cat => {
      if (cat.parent_id && idToNode[cat.parent_id]) {
        idToNode[cat.parent_id].children = idToNode[cat.parent_id].children || [];
        idToNode[cat.parent_id].children!.push(idToNode[cat.id]);
      } else {
        tree.push(idToNode[cat.id]);
      }
    });
    
    return tree;
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const filteredCategories = getFilteredCategories();

  return (
    <Card 
      title={<span><Button icon={<ArrowLeftOutlined />} type="link" onClick={() => router.push('/assets')} style={{ marginRight: 8, padding: 0 }}>返回</Button>资产分类管理</span>}
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          新增分类
        </Button>
      }
    >
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Input
            placeholder="搜索分类名称或编码"
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            allowClear
          />
        </Col>
        <Col span={6}>
          <Select
            placeholder="选择一级分类"
            style={{ width: '100%' }}
            allowClear
            value={selectedParentCategory}
            onChange={(value) => {
              setSelectedParentCategory(value);
              setSelectedSubCategory(null); // 清空二级分类选择
            }}
          >
            {parentCategories.map(category => (
              <Select.Option key={category.id} value={category.id}>
                {category.name} ({category.code})
              </Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={6}>
          <Select
            placeholder="选择二级分类"
            style={{ width: '100%' }}
            allowClear
            value={selectedSubCategory}
            disabled={!selectedParentCategory} // 如果没有选择一级分类，则禁用
            onChange={(value) => setSelectedSubCategory(value)}
          >
            {subCategories.map(category => (
              <Select.Option key={category.id} value={category.id}>
                {category.name} ({category.code})
              </Select.Option>
            ))}
          </Select>
        </Col>
        <Col span={6}>
          <Space>
            {(selectedParentCategory || selectedSubCategory || searchValue) && (
              <Button 
                type="primary" 
                onClick={() => {
                  setSelectedParentCategory(null);
                  setSelectedSubCategory(null);
                  setSearchValue('');
                }}
              >
                清除筛选
              </Button>
            )}
          </Space>
        </Col>
      </Row>

      <Tree
        treeData={buildCategoryTree(filteredCategories)}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        onExpand={onExpand}
        fieldNames={{ title: 'title', key: 'key', children: 'children' }}
        onSelect={([selectedId], { node }) => {
          if (selectedId && categories) {
            const cat = categories.find(c => c.id === selectedId)
            if (cat) openModal(cat)
          }
        }}
      />
      
      <Modal
        title={editingCategory ? '编辑分类' : '新增分类'}
        open={modalVisible}
        onOk={handleOk}
        onCancel={() => { setModalVisible(false); setEditingCategory(null); form.resetFields() }}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item name="name" label="分类名称" rules={[{ required: true, message: '请输入分类名称' }]}> <Input /> </Form.Item>
          <Form.Item name="code" label="分类编码" rules={[{ required: true, message: '请输入分类编码' }]}> <Input /> </Form.Item>
          <Form.Item name="parent_id" label="父级分类">
            <Select allowClear placeholder="请选择父级分类">
              {categories?.filter(c => !editingCategory || c.id !== editingCategory.id).map(c => (
                <Select.Option key={c.id} value={c.id}>{c.name}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Select mode="tags" placeholder="请输入或选择标签" allowClear>
              {allTags.map(tag => (
                <Select.Option key={tag} value={tag}>{tag}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default AssetCategoriesPage
