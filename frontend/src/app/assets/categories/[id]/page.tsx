'use client'
import React, { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Card, Form, Input, Select, Button, Space, message, Divider, Switch, InputNumber, Row, Col } from 'antd'
import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons'
import { useQuery, useMutation } from '@tanstack/react-query'
import { getAssetCategory, updateAssetCategory, getAssetCategories } from '@/api/device'
import type { AssetCategory } from '@/api/device'

const { Option } = Select
const { TextArea } = Input

const AssetCategoryEditPage = () => {
  const params = useParams()
  const router = useRouter()
  const [form] = Form.useForm()
  const categoryId = params?.id ? Number(params.id) : 0
  const isNew = params?.id === 'new'
  
  // 获取所有分类（用于选择父级分类）
  const { data: categories } = useQuery<AssetCategory[]>({
    queryKey: ['assetCategories'],
    queryFn: getAssetCategories,
    enabled: true
  })
  
  // 获取当前编辑的分类
  const { data: category, isLoading } = useQuery<AssetCategory>({
    queryKey: ['assetCategory', categoryId],
    queryFn: () => getAssetCategory(categoryId),
    enabled: !isNew && !!categoryId
  })
  
  // 获取所有标签
  const allTags = React.useMemo(() => {
    if (!categories) return [];
    const tags = new Set<string>();
    // 注释掉tags相关代码，因为AssetCategory的attributes中没有tags字段
    // categories.forEach(cat => {
    //   if (cat.attributes?.tags?.length) {
    //     cat.attributes.tags.forEach((tag: string) => tags.add(tag));
    //   }
    // });
    return Array.from(tags);
  }, [categories]);
  
  // 更新表单数据
  useEffect(() => {
    if (category && !isNew) {
      form.setFieldsValue({
        name: category.name,
        code: category.code,
        parent_id: category.parent_id,
        // tags: category.attributes?.tags || [], // 注释掉，因为attributes中没有tags字段
        // 编码规则
        codeRule: category.attributes?.codeRule || {
          enabled: true,
          prefix: category.code || '',
          digitLength: 6,
          includeDate: false,
          dateFormat: 'YYMMDD'
        }
      })
    }
  }, [category, form, isNew])
  
  // 提交表单
  const { mutate: submitForm, isPending } = useMutation({
    mutationFn: async (values: any) => {
      const payload = {
        ...values,
        attributes: {
          tags: values.tags || [],
          codeRule: values.codeRule
        }
      }
      
      if (isNew) {
        // 新增分类
        return await updateAssetCategory(0, payload)
      } else {
        // 更新分类
        return await updateAssetCategory(categoryId, payload)
      }
    },
    onSuccess: () => {
      message.success(`${isNew ? '创建' : '更新'}分类成功`)
      router.push('/assets/categories')
    },
    onError: () => {
      message.error(`${isNew ? '创建' : '更新'}分类失败`)
    }
  })
  
  return (
    <Card
      title={
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={() => router.push('/assets/categories')} />
          {isNew ? '新增分类' : '编辑分类'}
        </Space>
      }
      loading={isLoading && !isNew}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={submitForm}
        initialValues={{
          codeRule: {
            enabled: true,
            prefix: '',
            digitLength: 6,
            includeDate: false,
            dateFormat: 'YYMMDD'
          }
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="分类名称"
              name="name"
              rules={[{ required: true, message: '请输入分类名称' }]}
            >
              <Input placeholder="请输入分类名称" />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              label="分类编码"
              name="code"
              rules={[{ required: true, message: '请输入分类编码' }]}
            >
              <Input placeholder="请输入分类编码，如NET、SW等" />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="父级分类"
              name="parent_id"
            >
              <Select 
                placeholder="请选择父级分类" 
                allowClear
                showSearch
                filterOption={(input, option) => 
                  (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                }
              >
                {categories?.filter(c => c.id !== categoryId).map(c => (
                  <Option key={c.id} value={c.id}>{c.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              label="标签"
              name="tags"
            >
              <Select 
                mode="tags" 
                placeholder="请输入或选择标签" 
                allowClear
              >
                {allTags.map(tag => (
                  <Option key={tag} value={tag}>{tag}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Divider orientation="left">资产编码规则</Divider>
        
        <Form.Item
          name={['codeRule', 'enabled']}
          valuePropName="checked"
        >
          <Switch checkedChildren="启用自动编码" unCheckedChildren="禁用自动编码" />
        </Form.Item>
        
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => 
            prevValues.codeRule?.enabled !== currentValues.codeRule?.enabled
          }
        >
          {({ getFieldValue }) => {
            const enabled = getFieldValue(['codeRule', 'enabled']);
            
            return enabled ? (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="编码前缀"
                      name={['codeRule', 'prefix']}
                      tooltip="资产编码的前缀，默认使用分类编码"
                    >
                      <Input placeholder="默认使用分类编码" />
                    </Form.Item>
                  </Col>
                  
                  <Col span={12}>
                    <Form.Item
                      label="序列号位数"
                      name={['codeRule', 'digitLength']}
                      tooltip="自动生成的序列号位数"
                    >
                      <InputNumber min={4} max={10} />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name={['codeRule', 'includeDate']}
                      valuePropName="checked"
                      label="包含日期"
                    >
                      <Switch checkedChildren="包含" unCheckedChildren="不包含" />
                    </Form.Item>
                  </Col>
                  
                  <Col span={12}>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) => 
                        prevValues.codeRule?.includeDate !== currentValues.codeRule?.includeDate
                      }
                    >
                      {({ getFieldValue }) => {
                        const includeDate = getFieldValue(['codeRule', 'includeDate']);
                        
                        return includeDate ? (
                          <Form.Item
                            label="日期格式"
                            name={['codeRule', 'dateFormat']}
                          >
                            <Select>
                              <Option value="YYMMDD">年月日 (YYMMDD)</Option>
                              <Option value="YYYYMMDD">完整年月日 (YYYYMMDD)</Option>
                              <Option value="YYMM">年月 (YYMM)</Option>
                            </Select>
                          </Form.Item>
                        ) : null;
                      }}
                    </Form.Item>
                  </Col>
                </Row>
                
                <Form.Item label="编码预览">
                  <Input 
                    value={`${form.getFieldValue(['codeRule', 'prefix']) || form.getFieldValue('code') || 'PREFIX'}-${form.getFieldValue(['codeRule', 'includeDate']) ? (new Date().toISOString().slice(2, 10).replace(/-/g, '').slice(0, form.getFieldValue(['codeRule', 'dateFormat']) === 'YYYYMMDD' ? 8 : 6)) : ''}${Array(form.getFieldValue(['codeRule', 'digitLength']) || 6).fill('0').join('').slice(0, 3)}...`}
                    disabled
                  />
                </Form.Item>
              </>
            ) : null;
          }}
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={isPending}
            icon={<SaveOutlined />}
          >
            保存
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )
}

export default AssetCategoryEditPage