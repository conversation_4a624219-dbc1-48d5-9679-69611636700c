'use client'

import React, { useState } from 'react'
import { Box, Heading, Flex, Button, Grid, GridItem, useColorMode, Text } from '@chakra-ui/react'
import DataCenterLayout from '@/components/RackLayout/DataCenterLayout'
import RackLayout from '@/components/RackLayout/RackLayout'
import { ArrowLeft } from '@phosphor-icons/react'
import { Card, Breadcrumb } from 'antd'
import { useRouter } from 'next/navigation'

// 示例数据
const mockDevices = [
  { id: 1, name: '交换机 Router-01', position_start: 40, position_end: 42 },
  { id: 2, name: 'Web 服务器 Dell R740', position_start: 36, position_end: 37 },
  { id: 3, name: '数据库服务器 IBM X3650', position_start: 32, position_end: 34 },
  { id: 4, name: '存储设备 NetApp FAS', position_start: 26, position_end: 30 },
  { id: 5, name: '备份服务器 HP DL380', position_start: 22, position_end: 24 },
  { id: 6, name: 'GPU 计算节点 NVIDIA', position_start: 18, position_end: 20 },
  { id: 7, name: '负载均衡器 F5', position_start: 14, position_end: 15 },
  { id: 8, name: '安全网关 Palo Alto', position_start: 10, position_end: 11 },
  { id: 9, name: 'KVM 控制台', position_start: 8, position_end: 8 },
  { id: 10, name: 'UPS 电源', position_start: 1, position_end: 4 },
]

// 示例机柜数据
const mockRacks = [
  { id: 'A01', name: 'A01', totalU: 42, usedU: 14, location: '右侧' },
  { id: 'A02', name: 'A02', totalU: 42, usedU: 7, location: '右侧' },
  { id: 'A03', name: 'A03', totalU: 42, usedU: 13, location: '右侧' },
  { id: 'A04', name: 'A04', totalU: 42, usedU: 4, location: '右侧' },
  { id: 'A05', name: 'A05', totalU: 42, usedU: 17, location: '右侧' },
  { id: 'A06', name: 'A06', totalU: 42, usedU: 11, location: '右侧' },
  { id: 'A07', name: 'A07', totalU: 42, usedU: 24, location: '右侧' },
  { id: 'A08', name: 'A08', totalU: 42, usedU: 20, location: '左侧' },
  { id: 'A09', name: 'A09', totalU: 42, usedU: 3, location: '左侧' },
  { id: 'A10', name: 'A10', totalU: 42, usedU: 12, location: '左侧' },
  { id: 'A11', name: 'A11', totalU: 42, usedU: 8, location: '左侧' },
  { id: 'A12', name: 'A12', totalU: 42, usedU: 5, location: '左侧' },
  { id: 'A13', name: 'A13', totalU: 42, usedU: 22, location: '左侧' },
  { id: 'A14', name: 'A14', totalU: 42, usedU: 27, location: '左侧' },
  { id: 'A15', name: 'A15', totalU: 42, usedU: 1, location: '左侧' },
]

const RackLayoutPage = () => {
  const { colorMode } = useColorMode()
  const [selectedRack, setSelectedRack] = useState<string | null>(null)
  const router = useRouter()

  const handleRackSelect = (rackId: string) => {
    setSelectedRack(rackId)
  }

  const handleBackToOverview = () => {
    setSelectedRack(null)
  }
  
  // 获取当前选中机柜的设备
  const getDevicesForRack = (rackId: string) => {
    // 这里简单返回全部设备，实际应用中应该根据机柜ID筛选对应设备
    return mockDevices
  }

  // 基于机柜ID计算空间状态
  const getSpaceStatus = (rackId: string) => {
    const rack = mockRacks.find(r => r.id === rackId)
    if (!rack) return 'sufficient'
    
    const utilization = (rack.usedU / rack.totalU) * 100
    if (utilization < 70) {
      return 'sufficient' // 空间充足
    } else if (utilization < 90) {
      return 'limited' // 空间有限
    } else {
      return 'tight' // 空间紧张
    }
  }

  return (
    <Box maxW="1400px" mx="auto" px={6} py={8}>
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>
          <a onClick={() => router.push('/assets')}>资产管理系统</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>机柜布局</Breadcrumb.Item>
      </Breadcrumb>

      <Card title="机柜布局" style={{ marginBottom: '24px' }}>
        {selectedRack ? (
          <Box>
            <Button 
              leftIcon={<ArrowLeft />} 
              onClick={handleBackToOverview}
              mb={4}
              size="sm"
              variant="outline"
            >
              返回机柜布局
            </Button>
            <Flex justify="space-between" align="center" mb={4}>
              <Heading size="md">机柜 {selectedRack} 详情</Heading>
            </Flex>
            <Grid templateColumns="250px 1fr" gap={6}>
              <GridItem>
                <Box 
                  border="1px" 
                  borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
                  borderRadius="md"
                  p={4}
                >
                  <Heading size="sm" mb={4}>机柜信息</Heading>
                  <Grid templateColumns="1fr 1fr" gap={2}>
                    <Box fontWeight="bold" fontSize="sm">机柜名称:</Box>
                    <Box fontSize="sm">{selectedRack}</Box>
                    
                    <Box fontWeight="bold" fontSize="sm">位置:</Box>
                    <Box fontSize="sm">
                      {mockRacks.find(r => r.id === selectedRack)?.location || '未知'}
                    </Box>
                    
                    <Box fontWeight="bold" fontSize="sm">总容量:</Box>
                    <Box fontSize="sm">42U</Box>
                    
                    <Box fontWeight="bold" fontSize="sm">已使用:</Box>
                    <Box fontSize="sm">
                      {mockRacks.find(r => r.id === selectedRack)?.usedU || 0}U
                    </Box>
                    
                    <Box fontWeight="bold" fontSize="sm">剩余空间:</Box>
                    <Box 
                      fontSize="sm"
                      color={
                        getSpaceStatus(selectedRack) === 'sufficient' ? 'green.500' : 
                        getSpaceStatus(selectedRack) === 'limited' ? 'yellow.500' : 'red.500'
                      }
                      fontWeight="medium"
                    >
                      {42 - (mockRacks.find(r => r.id === selectedRack)?.usedU || 0)}U
                    </Box>
                    
                    <Box fontWeight="bold" fontSize="sm">使用率:</Box>
                    <Box fontSize="sm">
                      {((mockRacks.find(r => r.id === selectedRack)?.usedU || 0) / 42 * 100).toFixed(1)}%
                    </Box>
                    
                    <Box fontWeight="bold" fontSize="sm">空间状态:</Box>
                    <Box 
                      fontSize="sm"
                      color={
                        getSpaceStatus(selectedRack) === 'sufficient' ? 'green.500' : 
                        getSpaceStatus(selectedRack) === 'limited' ? 'yellow.500' : 'red.500'
                      }
                      fontWeight="bold"
                    >
                      {getSpaceStatus(selectedRack) === 'sufficient' ? '空间充足' : 
                       getSpaceStatus(selectedRack) === 'limited' ? '空间有限' : '空间紧张'}
                    </Box>
                  </Grid>
                </Box>
              </GridItem>
              <GridItem>
                <RackLayout 
                  totalU={42} 
                  devices={getDevicesForRack(selectedRack)} 
                />
              </GridItem>
            </Grid>
          </Box>
        ) : (
          <Box>
            <Text mb={4}>数据中心机柜布局图，点击机柜查看详情</Text>
            <DataCenterLayout racks={mockRacks} onSelectRack={handleRackSelect} />
          </Box>
        )}
      </Card>
    </Box>
  )
}

export default RackLayoutPage 