'use client'
import React from 'react'
import { Card, Table, Button, Tag, Row, Col, Select, DatePicker, Input, Modal, Form, message } from 'antd'
import { useRouter } from 'next/navigation'
import { ArrowLeftOutlined } from '@ant-design/icons'

const columns = [
  { title: '资产名称', dataIndex: 'asset', key: 'asset' },
  { title: '报废原因', dataIndex: 'reason', key: 'reason' },
  { title: '报废日期', dataIndex: 'date', key: 'date' },
  { title: '关联OA单号', dataIndex: 'oaNo', key: 'oaNo', render: (oaNo: string) => oaNo || '-' },
  { title: '状态', dataIndex: 'status', key: 'status', render: (status: string) => <Tag color={status === 'pending' ? 'orange' : status === 'approved' ? 'blue' : 'green'}>{status === 'pending' ? '待审批' : status === 'approved' ? '已审批' : '已完成'}</Tag> },
  { title: '操作人', dataIndex: 'operator', key: 'operator' },
]

const DisposalPage = () => {
  const router = useRouter()
  const [modalVisible, setModalVisible] = React.useState(false)
  const [form] = Form.useForm()
  const [disposalRecords, setDisposalRecords] = React.useState([
    { key: '1', asset: '服务器', reason: '设备老化', date: '2024-05-01', status: 'pending', operator: '张三', oaNo: '**********' },
    { key: '2', asset: '交换机', reason: '损坏不可修复', date: '2024-05-10', status: 'approved', operator: '李四', oaNo: '**********' },
    { key: '3', asset: '打印机', reason: '淘汰', date: '2024-05-15', status: 'completed', operator: '王五', oaNo: '' },
  ])
  const handleCreate = () => {
    form.validateFields().then(values => {
      setDisposalRecords(records => [
        { ...values, key: String(records.length + 1), status: 'pending', operator: '当前用户', oaNo: values.oaNo || '' },
        ...records
      ])
      message.success('报废申请已提交')
      setModalVisible(false)
      form.resetFields()
    })
  }
  return (
    <Card 
      title={
        <span>
          <Button
            icon={<ArrowLeftOutlined />}
            type="link"
            onClick={() => router.push('/assets')}
            style={{ marginRight: 8, padding: 0 }}
          >
            返回
          </Button>
          资产报废管理
        </span>
      }
      extra={<Button type="primary" onClick={() => setModalVisible(true)}>新建报废申请</Button>}
    >
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}><Input placeholder="资产名称" /></Col>
        <Col span={8}><Select placeholder="状态" style={{ width: '100%' }} allowClear><Select.Option value="pending">待审批</Select.Option><Select.Option value="approved">已审批</Select.Option><Select.Option value="completed">已完成</Select.Option></Select></Col>
        <Col span={8}><DatePicker.RangePicker style={{ width: '100%' }} /></Col>
      </Row>
      <Table columns={columns} dataSource={disposalRecords} pagination={{ pageSize: 10 }} />
      <Modal
        open={modalVisible}
        title="新建报废申请"
        onCancel={() => setModalVisible(false)}
        onOk={handleCreate}
        okText="提交"
      >
        <Form form={form} layout="vertical">
          <Form.Item label="资产名称" name="asset" rules={[{ required: true, message: '请输入资产名称' }]}> <Input /> </Form.Item>
          <Form.Item label="报废原因" name="reason" rules={[{ required: true, message: '请输入报废原因' }]}> <Input /> </Form.Item>
          <Form.Item label="报废日期" name="date" rules={[{ required: true, message: '请选择报废日期' }]}> <DatePicker style={{ width: '100%' }} /> </Form.Item>
          <Form.Item label="关联OA单号" name="oaNo" rules={[{ required: false }]}> <Input placeholder="可自定义输入OA单号" /> </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default DisposalPage 