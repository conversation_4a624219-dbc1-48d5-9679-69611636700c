'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { AssetForm } from '@/components/AssetForm'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { Asset } from '@/types/asset'

export default function AssetEditPage() {
  const params = useParams()
  const id = params?.id as string
  const router = useRouter()
  const [asset, setAsset] = useState<Asset | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    const loadAsset = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/assets/${id}`)
        const data = await response.json()
        setAsset(data)
      } catch (err) {
        console.error('加载资产失败:', err)
      } finally {
        setLoading(false)
      }
    }

    loadAsset()
  }, [id])

  const handleSubmit = async (data: Asset) => {
    try {
      setSubmitting(true)
      const response = await fetch(`/api/assets/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (response.ok) {
        router.push(`/assets/${id}`)
      }
    } catch (err) {
      console.error('更新资产失败:', err)
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!asset) {
    return (
      <div className="container mx-auto py-8">
        <p>找不到该资产</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center gap-4">
        <Button asChild variant="outline" size="icon">
          <Link href={`/assets/${id}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">编辑资产</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>资产信息</CardTitle>
        </CardHeader>
        <CardContent>
          <AssetForm
            initialData={asset}
            onSubmit={handleSubmit}
          />
          <div className="flex justify-end mt-4 gap-2">
            <Button
              variant="outline"
              asChild
            >
              <Link href={`/assets/${id}`}>
                取消
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}