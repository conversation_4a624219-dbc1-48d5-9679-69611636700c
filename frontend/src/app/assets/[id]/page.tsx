'use client'

import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Asset } from '@/types/asset'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Monitor, RefreshCw, Settings } from 'lucide-react'
import { MonitoringDataView } from '@/components/MonitoringDataView'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'
import { AlertRules } from '@/components/AlertRules'
import { AlertLogs } from '@/components/AlertLogs'

export default function AssetDetailPage() {
  const params = useParams()
  const id = params?.id as string
  const [asset, setAsset] = useState<Asset | null>(null)
  const [loading, setLoading] = useState(true)
  const [monitoringDevices, setMonitoringDevices] = useState<any[]>([])
  const [refreshing, setRefreshing] = useState(false)

  const loadData = async () => {
    try {
      setLoading(true)

      // 获取资产信息
      const assetRes = await fetch(`/api/assets/${id}`)
      const assetData = await assetRes.json()
      setAsset(assetData)

      // 获取监控设备
      const monitorRes = await fetch(`/api/monitoring?assetId=${id}`)
      const monitorData = await monitorRes.json()
      setMonitoringDevices(monitorData)
    } catch (err) {
      console.error('加载数据失败:', err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [id])

  const handleRefresh = () => {
    setRefreshing(true)
    loadData()
  }

  if (loading && !asset) {
    return (
      <div className="container mx-auto py-8 space-y-4">
        <Skeleton className="h-8 w-1/3" />
        <div className="grid gap-4 md:grid-cols-2">
          <Skeleton className="h-[300px]" />
          <Skeleton className="h-[300px]" />
        </div>
      </div>
    )
  }

  if (!asset) {
    return (
      <div className="container mx-auto py-8">
        <p>找不到该资产</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">{asset.name}</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button asChild>
            <Link href={`/assets/${id}/edit`}>
              <Settings className="h-4 w-4 mr-2" />
              编辑
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">类型</span>
              <Badge variant="outline">
                {asset.type === 'server' && '服务器'}
                {asset.type === 'network' && '网络设备'}
                {asset.type === 'printer' && '打印机'}
                {asset.type === 'other' && '其他'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">IP地址</span>
              <span>{asset.ipAddress}</span>
            </div>
            {asset.macAddress && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">MAC地址</span>
                <span>{asset.macAddress}</span>
              </div>
            )}
            {asset.location && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">位置</span>
                <span>{asset.location}</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>监控状态</CardTitle>
              {monitoringDevices.length > 0 && (
                <Badge className="gap-1">
                  <Monitor className="h-3 w-3" />
                  已监控
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {monitoringDevices.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-[200px] gap-4">
                <p className="text-muted-foreground">未配置监控</p>
                <Button asChild>
                  <Link href={`/assets/${id}/edit`}>
                    配置监控
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {monitoringDevices.map((device) => (
                  <div key={device.id} className="space-y-2">
                    <h3 className="font-medium">
                      {device.type.toUpperCase()} 监控
                      <span className="text-sm text-muted-foreground ml-2">
                        (每 {device.interval} 秒采集一次)
                      </span>
                    </h3>

                    {device.type === 'snmp' ? (
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {device.oids.map((oid: string) => (
                          <MonitoringDataView
                            key={oid}
                            deviceId={device.id}
                            type="snmp"
                            oidOrCommand={oid}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {device.commands.map((cmd: string) => (
                          <MonitoringDataView
                            key={cmd}
                            deviceId={device.id}
                            type="ssh"
                            oidOrCommand={cmd}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {monitoringDevices.length > 0 && (
        <>
          <Card>
            <CardHeader>
              <CardTitle>报警规则</CardTitle>
              <CardDescription>
                配置设备的监控报警规则
              </CardDescription>
            </CardHeader>
            <CardContent>
              {monitoringDevices.map((device) => (
                <div key={device.id} className="mb-8">
                  <h3 className="text-lg font-medium mb-4">
                    {device.host} - {device.type.toUpperCase()}
                  </h3>
                  <AlertRules
                    deviceId={device.id}
                    deviceType={device.type}
                    metrics={device.type === 'snmp' ? device.oids : device.commands}
                  />
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>报警历史</CardTitle>
              <CardDescription>
                设备的报警历史记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              {monitoringDevices.map((device) => (
                <div key={device.id} className="mb-8">
                  <h3 className="text-lg font-medium mb-4">
                    {device.host} - {device.type.toUpperCase()}
                  </h3>
                  <AlertLogs deviceId={device.id} />
                </div>
              ))}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}