'use client'
import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Statistic, Progress, Divider, Alert, List, Tag, Button, Space, Tabs } from 'antd'
import { useQuery } from '@tanstack/react-query'
import { getAssets, getAssetCategories } from '@/api/device'
import type { Asset, AssetCategory } from '@/api/device'
import { 
  WarningOutlined, 
  CheckCircleOutlined, 
  DollarOutlined, 
  PieChartOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { useRouter } from 'next/navigation'

// 生命周期状态中文名称
const lifecycleStatusNames = {
  planning: '规划中',
  purchased: '已采购',
  deployed: '已部署',
  maintenance: '维护中',
  retired: '已报废'
}

// 生命周期状态颜色
const lifecycleStatusColors = {
  planning: '#1890ff',
  purchased: '#13c2c2',
  deployed: '#52c41a',
  maintenance: '#faad14',
  retired: '#f5222d'
}

const AssetDashboardPage = () => {
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])
  
  // 获取资产数据
  const { data: assets = [], isLoading: assetsLoading } = useQuery<Asset[]>({
    queryKey: ['assets'],
    queryFn: () => getAssets()
  })
  
  // 获取资产分类数据
  const { data: categories = [], isLoading: categoriesLoading } = useQuery<AssetCategory[]>({
    queryKey: ['assetCategories'],
    queryFn: () => getAssetCategories()
  })
  
  // 计算资产总价值
  const totalAssetValue = Array.isArray(assets) ? assets.reduce((sum, asset) => sum + (asset.price || 0), 0) : 0
  
  // 计算当前资产价值（考虑折旧）
  const currentAssetValue = Array.isArray(assets) ? assets.reduce((sum, asset) => {
    if (!asset.price || !asset.purchaseDate || !asset.depreciationMethod || asset.depreciationMethod === 'none') {
      return sum + (asset.price || 0)
    }
    
    const originalPrice = asset.price
    const purchaseDate = new Date(asset.purchaseDate)
    const today = new Date()
    const monthsDiff = (today.getFullYear() - purchaseDate.getFullYear()) * 12 + today.getMonth() - purchaseDate.getMonth()
    
    let currentValue = originalPrice
    
    if (asset.depreciationMethod === 'straight-line' && asset.depreciationPeriod) {
      // 直线折旧法
      const monthlyDepreciation = originalPrice / asset.depreciationPeriod
      const depreciationAmount = Math.min(monthsDiff * monthlyDepreciation, originalPrice)
      currentValue = Math.max(originalPrice - depreciationAmount, 0)
    } else if (asset.depreciationMethod === 'declining-balance' && asset.depreciationRate) {
      // 余额递减法
      const monthlyRate = asset.depreciationRate / 12 / 100
      for (let i = 0; i < monthsDiff; i++) {
        const monthlyDepreciation = currentValue * monthlyRate
        currentValue -= monthlyDepreciation
      }
    }
    
    return sum + currentValue
  }, 0) : 0
  
  // 计算折旧率
  const depreciationRate = totalAssetValue > 0 ? ((totalAssetValue - currentAssetValue) / totalAssetValue) * 100 : 0
  
  // 按生命周期状态统计资产数量
  const assetsByLifecycleStatus = Array.isArray(assets) ? assets.reduce((acc, asset) => {
    const status = asset.lifecycleStatus || 'unknown'
    acc[status] = (acc[status] || 0) + 1
    return acc
  }, {} as Record<string, number>) : {}
  
  // 按分类统计资产数量
  const assetsByCategory = Array.isArray(assets) ? assets.reduce((acc, asset) => {
    acc[asset.categoryId] = (acc[asset.categoryId] || 0) + 1
    return acc
  }, {} as Record<number, number>) : {}
  
  // 查找即将到期的维保资产
  const warningAssets = Array.isArray(assets) ? assets.filter(asset => {
    if (!asset.warrantyExpireDate) return false
    
    const today = new Date()
    const expireDate = new Date(asset.warrantyExpireDate)
    const diffTime = expireDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays >= 0 && diffDays <= 30
  }).sort((a, b) => {
    const dateA = new Date(a.warrantyExpireDate || '').getTime()
    const dateB = new Date(b.warrantyExpireDate || '').getTime()
    return dateA - dateB
  }) : []
  
  // 生成生命周期状态饼图配置
  const getLifecycleChartOption = () => {
    const data = Object.entries(assetsByLifecycleStatus).map(([status, count]) => ({
      name: lifecycleStatusNames[status as keyof typeof lifecycleStatusNames] || '未知',
      value: count,
      itemStyle: {
        color: lifecycleStatusColors[status as keyof typeof lifecycleStatusColors] || '#999'
      }
    }))
    
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: '资产生命周期',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          data: data
        }
      ]
    }
  }
  
  // 生成资产分类柱状图配置
  const getCategoryChartOption = () => {
    const categoryMap = new Map(categories.map(cat => [cat.id, cat]))
    const data = Object.entries(assetsByCategory)
      .map(([categoryId, count]) => {
        const category = categoryMap.get(Number(categoryId))
        return {
          name: category?.name || '未知分类',
          value: count,
          categoryId: Number(categoryId)
        }
      })
      .sort((a, b) => b.value - a.value)
    
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
      },
      yAxis: {
        type: 'category',
        data: data.map(item => item.name)
      },
      series: [
        {
          name: '资产数量',
          type: 'bar',
          data: data.map(item => ({
            value: item.value,
            itemStyle: {
              color: `hsl(${(item.categoryId * 30) % 360}, 70%, 60%)`
            }
          }))
        }
      ]
    }
  }
  
  // 生成折旧趋势图配置
  const getDepreciationChartOption = () => {
    // 按月份分组资产价值和折旧后价值
    const monthlyData: Record<string, { original: number, current: number }> = {}
    
    if (Array.isArray(assets)) {
      assets.forEach(asset => {
      if (!asset.price || !asset.purchaseDate) return
      
      const purchaseDate = new Date(asset.purchaseDate)
      const yearMonth = `${purchaseDate.getFullYear()}-${(purchaseDate.getMonth() + 1).toString().padStart(2, '0')}`
      
      if (!monthlyData[yearMonth]) {
        monthlyData[yearMonth] = { original: 0, current: 0 }
      }
      
      let currentValue = asset.price
      
      if (asset.depreciationMethod && asset.depreciationMethod !== 'none') {
        const today = new Date()
        const monthsDiff = (today.getFullYear() - purchaseDate.getFullYear()) * 12 + today.getMonth() - purchaseDate.getMonth()
        
        if (asset.depreciationMethod === 'straight-line' && asset.depreciationPeriod) {
          const monthlyDepreciation = asset.price / asset.depreciationPeriod
          const depreciationAmount = Math.min(monthsDiff * monthlyDepreciation, asset.price)
          currentValue = Math.max(asset.price - depreciationAmount, 0)
        } else if (asset.depreciationMethod === 'declining-balance' && asset.depreciationRate) {
          const monthlyRate = asset.depreciationRate / 12 / 100
          for (let i = 0; i < monthsDiff; i++) {
            const monthlyDepreciation = currentValue * monthlyRate
            currentValue -= monthlyDepreciation
          }
        }
      }
      
      monthlyData[yearMonth].original += asset.price
      monthlyData[yearMonth].current += currentValue
    })
    }
    
    // 排序月份
    const sortedMonths = Object.keys(monthlyData).sort()
    
    return {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['原始价值', '当前价值']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: sortedMonths
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => `¥${(value / 10000).toFixed(0)}万`
        }
      },
      series: [
        {
          name: '原始价值',
          type: 'line',
          stack: 'Total',
          data: sortedMonths.map(month => monthlyData[month].original)
        },
        {
          name: '当前价值',
          type: 'line',
          stack: 'Total',
          data: sortedMonths.map(month => monthlyData[month].current)
        }
      ]
    }
  }
  
  // 生成维保到期预警图配置
  const getWarrantyChartOption = () => {
    // 按月份统计维保到期资产数量
    const monthlyExpireCount: Record<string, number> = {}
    
    if (Array.isArray(assets)) {
      assets.forEach(asset => {
        if (!asset.warrantyExpireDate) return
        
        const expireDate = new Date(asset.warrantyExpireDate)
        const yearMonth = `${expireDate.getFullYear()}-${(expireDate.getMonth() + 1).toString().padStart(2, '0')}`
        
        monthlyExpireCount[yearMonth] = (monthlyExpireCount[yearMonth] || 0) + 1
      })
    }
    
    // 排序月份
    const sortedMonths = Object.keys(monthlyExpireCount).sort()
    
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: sortedMonths
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '维保到期资产数',
          type: 'bar',
          data: sortedMonths.map(month => ({
            value: monthlyExpireCount[month],
            itemStyle: {
              color: function(params: any) {
                // 根据值设置不同的颜色
                const count = params.value
                if (count > 5) return '#f5222d' // 红色
                if (count > 3) return '#faad14' // 黄色
                return '#52c41a' // 绿色
              }
            }
          }))
        }
      ]
    }
  }
  
  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* 资产概览统计卡片 */}
        <Col span={6}>
          <Card>
            <Statistic 
              title="资产总数" 
              value={assets.length} 
              prefix={<PieChartOutlined />} 
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="资产总价值" 
              value={totalAssetValue} 
              precision={2} 
              prefix={<DollarOutlined />}
              formatter={(value) => `¥${(value as number).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="当前资产价值" 
              value={currentAssetValue} 
              precision={2} 
              prefix={<DollarOutlined />}
              formatter={(value) => `¥${(value as number).toLocaleString()}`}
            />
            <div style={{ marginTop: 10 }}>
              <Progress 
                percent={100 - Math.round(depreciationRate)} 
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="维保预警" 
              value={warningAssets.length} 
              valueStyle={{ color: warningAssets.length > 0 ? '#faad14' : '#3f8600' }}
              prefix={<ClockCircleOutlined />}
              suffix={`/${Array.isArray(assets) ? assets.filter(a => a.warrantyExpireDate).length : 0}`}
            />
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 图表区域 */}
        <Col span={12}>
          <Card title="资产生命周期状态" extra={<BarChartOutlined />} suppressHydrationWarning>
            {isClient && (
              <ReactECharts 
                option={getLifecycleChartOption()} 
                style={{ height: 300 }} 
              />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="资产分类分布" extra={<PieChartOutlined />} suppressHydrationWarning>
            {isClient && (
              <ReactECharts 
                option={getCategoryChartOption()} 
                style={{ height: 300 }} 
              />
            )}
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <Card title="资产折旧趋势" extra={<LineChartOutlined />} suppressHydrationWarning>
            {isClient && (
              <ReactECharts 
                option={getDepreciationChartOption()} 
                style={{ height: 300 }} 
              />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="维保到期预警" extra={<WarningOutlined />}>
            {warningAssets.length > 0 ? (
              <List
                size="small"
                bordered
                dataSource={warningAssets.slice(0, 5)}
                renderItem={(asset) => {
                  const expireDate = new Date(asset.warrantyExpireDate || '')
                  const today = new Date()
                  const diffTime = expireDate.getTime() - today.getTime()
                  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                  
                  return (
                    <List.Item
                      actions={[
                        <Button 
                          key="view" 
                          type="link" 
                          size="small"
                          onClick={() => router.push(`/assets/${asset.id}`)}
                        >
                          查看
                        </Button>
                      ]}
                    >
                      <Space>
                        <span>{asset.name}</span>
                        <Tag color={diffDays < 7 ? 'red' : 'orange'}>
                          {diffDays}天后到期
                        </Tag>
                      </Space>
                    </List.Item>
                  )
                }}
                footer={
                  warningAssets.length > 5 ? (
                    <div style={{ textAlign: 'center' }}>
                      <Button type="link" onClick={() => router.push('/assets/list?filter=warranty')}>
                        查看全部 {warningAssets.length} 个预警
                      </Button>
                    </div>
                  ) : null
                }
              />
            ) : (
              <Alert
                message="暂无维保预警"
                description="当前没有即将到期的维保资产"
                type="success"
                showIcon
              />
            )}
          </Card>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="维保到期趋势" extra={<BarChartOutlined />} suppressHydrationWarning>
            {isClient && (
              <ReactECharts 
                option={getWarrantyChartOption()} 
                style={{ height: 300 }} 
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default AssetDashboardPage