'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Package, AlertCircle, CheckCircle2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from '@/components/ui/use-toast'
import { ConsumableAssetForm } from '@/components/ConsumableAssetForm'

interface ConsumableAssetData {
  name: string
  code: string
  category_id: number
  consumable_type: string
  brand: string
  model?: string
  specification: string
  unit: string
  unit_price: number
  currency: string
  current_stock: number
  min_stock_level: number
  max_stock_level: number
  reorder_point: number
  reorder_quantity: number
  supplier: string
  supplier_contact?: string
  supplier_phone?: string
  lead_time_days: number
  shelf_life_days?: number
  storage_location: string
  storage_conditions?: string
  purchase_date?: Date
  expiration_date?: Date
  batch_number?: string
  quality_standard?: string
  usage_department: string
  responsible_person: string
  auto_reorder: boolean
  track_expiration: boolean
  requires_approval: boolean
  status: string
  description?: string
}

export default function ConsumableAssetRegisterPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (data: ConsumableAssetData) => {
    setIsSubmitting(true)
    try {
      // 添加资产类型和分类类型标记
      const submitData = {
        ...data,
        asset_type: 'consumable',
        category_type: 'consumable'
      }

      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '消耗品资产登记失败')
      }

      const result = await response.json()
      
      toast({
        title: '登记成功',
        description: `消耗品资产 "${data.name}" 已成功登记`,
      })

      // 可以选择跳转到资产列表或清空表单继续登记
      // router.push('/assets')
      
    } catch (error) {
      console.error('消耗品资产登记失败:', error)
      toast({
        title: '登记失败',
        description: error instanceof Error ? error.message : '消耗品资产登记失败，请重试',
        variant: 'destructive'
      })
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => router.push('/assets/register-new')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回资产类型选择
        </Button>
        <div className="flex items-center gap-2">
          <Package className="h-6 w-6 text-orange-600" />
          <h1 className="text-2xl font-bold">消耗品资产登记</h1>
        </div>
      </div>

      {/* 说明信息 */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">消耗品资产登记说明：</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>消耗品类型：</strong>包括打印耗材、网络耗材、办公用品、清洁用品、安全用品、维护用品、电子元件、线缆连接器、存储介质等</li>
              <li><strong>库存管理：</strong>设置合理的最低库存、最高库存和补货点，确保库存充足且不积压</li>
              <li><strong>自动补货：</strong>开启后系统会在库存低于补货点时自动提醒或下单</li>
              <li><strong>过期跟踪：</strong>对有保质期的消耗品进行过期监控和提醒</li>
              <li><strong>审批流程：</strong>可设置消耗品领用是否需要审批</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* 重要提醒 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Alert>
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium text-green-700">登记要点：</p>
              <ul className="text-sm space-y-1">
                <li>• 准确填写消耗品规格和单位</li>
                <li>• 设置合理的库存阈值</li>
                <li>• 记录供应商联系方式</li>
                <li>• 注明存储条件和要求</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
        
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium text-blue-700">注意事项：</p>
              <ul className="text-sm space-y-1">
                <li>• 定期盘点库存数量</li>
                <li>• 关注消耗品过期时间</li>
                <li>• 建立供应商评估机制</li>
                <li>• 优化采购周期和数量</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      </div>

      {/* 登记表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            消耗品资产信息
          </CardTitle>
          <CardDescription>
            请填写完整的消耗品资产信息，包括基本信息、库存管理和供应商信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ConsumableAssetForm 
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
          />
        </CardContent>
      </Card>
    </div>
  )
}