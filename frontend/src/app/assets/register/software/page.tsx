'use client'

import { useState } from 'react'
import { ArrowLeft, Package, Shield, Database, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SoftwareAssetForm } from '@/components/SoftwareAssetForm'
import { toast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'

interface SoftwareAssetData {
  name: string
  code: string
  category_id: number
  software_type: string
  version: string
  vendor: string
  license_type: string
  license_key?: string
  license_count: number
  used_licenses: number
  purchase_date?: Date
  expiration_date?: Date
  support_end_date?: Date
  installation_path?: string
  system_requirements?: string
  description?: string
  cost?: number
  department?: string
  responsible_person?: string
  status: string
  is_critical: boolean
  auto_update_enabled: boolean
}

export default function SoftwareAssetRegisterPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  const handleSubmit = async (data: SoftwareAssetData) => {
    setIsSubmitting(true)
    try {
      // 添加资产类型标识
      const assetData = {
        ...data,
        asset_type: 'software',
        category_type: 'software_asset',
        // 格式化日期
        purchase_date: data.purchase_date?.toISOString(),
        expiration_date: data.expiration_date?.toISOString(),
        support_end_date: data.support_end_date?.toISOString(),
      }

      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assetData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '软件资产登记失败')
      }

      const result = await response.json()
      
      toast({
        title: '登记成功',
        description: `软件资产 "${data.name}" 已成功登记，资产编号：${result.code}`,
      })

      // 可以选择跳转到资产详情页或清空表单
      // router.push(`/assets/${result.id}`)
      
    } catch (error) {
      console.error('软件资产登记失败:', error)
      toast({
        title: '登记失败',
        description: error instanceof Error ? error.message : '软件资产登记失败，请重试',
        variant: 'destructive',
      })
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => router.push('/assets/register-new')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回资产类型选择
        </Button>
        <div className="h-6 w-px bg-border" />
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5 text-blue-600" />
          <h1 className="text-2xl font-bold">软件资产登记</h1>
        </div>
      </div>

      {/* 说明卡片 */}
      <Card className="border-blue-200 bg-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Shield className="h-5 w-5" />
            软件资产登记说明
          </CardTitle>
          <CardDescription className="text-blue-700">
            请准确填写软件资产的详细信息，包括许可证信息和使用情况
          </CardDescription>
        </CardHeader>
        <CardContent className="text-sm text-blue-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Database className="h-4 w-4" />
                软件类型包括：
              </h4>
              <ul className="space-y-1 text-xs ml-6">
                <li>• 操作系统（Windows、Linux、macOS等）</li>
                <li>• 数据库软件（MySQL、Oracle、SQL Server等）</li>
                <li>• 办公软件（Office、WPS、Adobe等）</li>
                <li>• 安全软件（防病毒、防火墙等）</li>
                <li>• 开发工具（IDE、编译器等）</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                重要提醒：
              </h4>
              <ul className="space-y-1 text-xs ml-6">
                <li>• 许可证信息务必准确填写</li>
                <li>• 关注软件到期时间和支持期限</li>
                <li>• 记录实际使用的许可证数量</li>
                <li>• 标记业务关键软件便于管理</li>
                <li>• 定期检查软件合规性</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 软件资产登记表单 */}
      <Card>
        <CardHeader>
          <CardTitle>软件资产信息</CardTitle>
          <CardDescription>
            请填写完整的软件资产信息，带 * 的字段为必填项
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SoftwareAssetForm 
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
          />
        </CardContent>
      </Card>
    </div>
  )
}