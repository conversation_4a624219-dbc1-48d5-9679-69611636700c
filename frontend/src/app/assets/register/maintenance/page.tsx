'use client'

import { useState } from 'react'
import { useR<PERSON><PERSON> } from 'next/navigation'
import { ArrowLef<PERSON>, <PERSON><PERSON>, AlertCircle, CheckCircle2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from '@/components/ui/use-toast'
import { MaintenanceAssetForm } from '@/components/MaintenanceAssetForm'

interface MaintenanceAssetData {
  name: string
  code: string
  category_id: number
  maintenance_type: string
  contract_number: string
  service_provider: string
  provider_contact: string
  provider_phone: string
  provider_email?: string
  service_scope: string
  service_level: string
  response_time: number
  resolution_time: number
  coverage_assets: string
  maintenance_frequency: string
  contract_amount: number
  currency: string
  payment_method: string
  payment_schedule: string
  start_date: Date
  end_date: Date
  renewal_notice_days: number
  auto_renewal: boolean
  escalation_procedure?: string
  penalty_clause?: string
  termination_clause?: string
  performance_metrics?: string
  reporting_requirements?: string
  emergency_contact?: string
  emergency_phone?: string
  department: string
  responsible_person: string
  backup_contact?: string
  approval_required: boolean
  status: string
  description?: string
}

export default function MaintenanceAssetRegisterPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (data: MaintenanceAssetData) => {
    setIsSubmitting(true)
    try {
      // 添加资产类型和分类类型标记
      const submitData = {
        ...data,
        asset_type: 'maintenance',
        category_type: 'maintenance'
      }

      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '维保资产登记失败')
      }

      const result = await response.json()
      
      toast({
        title: '登记成功',
        description: `维保合同 "${data.name}" 已成功登记`,
      })

      // 可以选择跳转到资产列表或清空表单继续登记
      // router.push('/assets')
      
    } catch (error) {
      console.error('维保资产登记失败:', error)
      toast({
        title: '登记失败',
        description: error instanceof Error ? error.message : '维保资产登记失败，请重试',
        variant: 'destructive'
      })
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => router.push('/assets/register-new')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回资产类型选择
        </Button>
        <div className="flex items-center gap-2">
          <Wrench className="h-6 w-6 text-purple-600" />
          <h1 className="text-2xl font-bold">维保资产登记</h1>
        </div>
      </div>

      {/* 说明信息 */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">维保资产登记说明：</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>维保类型：</strong>包括硬件维保、软件维保、网络维保、系统维保、设施维保、安全维保、综合维保、应急维保等</li>
              <li><strong>服务级别：</strong>根据业务重要性选择基础、标准、高级、企业或7x24服务级别</li>
              <li><strong>响应时间：</strong>服务商接到故障报告后的响应时间承诺</li>
              <li><strong>解决时间：</strong>从故障报告到问题解决的最长时间承诺</li>
              <li><strong>合同管理：</strong>跟踪合同期限，设置续约提醒，管理付款计划</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* 重要提醒 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Alert>
          <CheckCircle2 className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium text-green-700">登记要点：</p>
              <ul className="text-sm space-y-1">
                <li>• 明确服务范围和覆盖资产</li>
                <li>• 设定合理的SLA指标</li>
                <li>• 记录完整的联系信息</li>
                <li>• 制定升级处理程序</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
        
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium text-blue-700">注意事项：</p>
              <ul className="text-sm space-y-1">
                <li>• 定期评估服务质量</li>
                <li>• 监控合同执行情况</li>
                <li>• 及时处理续约事宜</li>
                <li>• 建立应急联系机制</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      </div>

      {/* 登记表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            维保合同信息
          </CardTitle>
          <CardDescription>
            请填写完整的维保合同信息，包括基本信息、服务商信息和合同条款
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MaintenanceAssetForm 
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
          />
        </CardContent>
      </Card>
    </div>
  )
}