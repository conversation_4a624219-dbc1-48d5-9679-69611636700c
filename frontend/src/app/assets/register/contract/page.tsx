'use client'

import { useState } from 'react'
import { ArrowLeft, FileText, Users, DollarSign, Calendar, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ContractAssetForm } from '@/components/ContractAssetForm'
import { toast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'

interface ContractAssetData {
  name: string
  code: string
  category_id: number
  contract_type: string
  contract_number: string
  vendor: string
  vendor_contact: string
  vendor_phone?: string
  vendor_email?: string
  service_type: string
  service_description: string
  contract_amount: number
  currency: string
  payment_method: string
  payment_schedule: string
  start_date: Date
  end_date: Date
  renewal_notice_days: number
  auto_renewal: boolean
  renewal_terms?: string
  sla_requirements?: string
  penalty_terms?: string
  termination_terms?: string
  department: string
  responsible_person: string
  backup_contact?: string
  status: string
  is_critical: boolean
  requires_approval: boolean
  approval_workflow?: string
  description?: string
}

export default function ContractAssetRegisterPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  const handleSubmit = async (data: ContractAssetData) => {
    setIsSubmitting(true)
    try {
      // 添加资产类型标识
      const assetData = {
        ...data,
        asset_type: 'contract',
        category_type: 'service_contract',
        // 格式化日期
        start_date: data.start_date?.toISOString(),
        end_date: data.end_date?.toISOString(),
      }

      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assetData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '合同资产登记失败')
      }

      const result = await response.json()
      
      toast({
        title: '登记成功',
        description: `合同资产 "${data.name}" 已成功登记，合同编号：${result.contract_number}`,
      })

      // 可以选择跳转到资产详情页或清空表单
      // router.push(`/assets/${result.id}`)
      
    } catch (error) {
      console.error('合同资产登记失败:', error)
      toast({
        title: '登记失败',
        description: error instanceof Error ? error.message : '合同资产登记失败，请重试',
        variant: 'destructive',
      })
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => router.push('/assets/register-new')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回资产类型选择
        </Button>
        <div className="h-6 w-px bg-border" />
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-green-600" />
          <h1 className="text-2xl font-bold">合同资产登记</h1>
        </div>
      </div>

      {/* 说明卡片 */}
      <Card className="border-green-200 bg-green-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Users className="h-5 w-5" />
            合同资产登记说明
          </CardTitle>
          <CardDescription className="text-green-700">
            请准确填写合同资产的详细信息，包括供应商信息、合同条款和服务内容
          </CardDescription>
        </CardHeader>
        <CardContent className="text-sm text-green-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                合同类型包括：
              </h4>
              <ul className="space-y-1 text-xs ml-6">
                <li>• 服务合同（IT服务、云服务、安全服务等）</li>
                <li>• 维保合同（设备维护、技术支持等）</li>
                <li>• 软件许可（软件授权、订阅服务等）</li>
                <li>• 咨询服务（技术咨询、管理咨询等）</li>
                <li>• 外包服务（运维外包、开发外包等）</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                重要提醒：
              </h4>
              <ul className="space-y-1 text-xs ml-6">
                <li>• 合同编号和供应商信息务必准确</li>
                <li>• 关注合同到期时间，设置续约提醒</li>
                <li>• 详细记录SLA要求和违约条款</li>
                <li>• 标记关键合同便于重点管理</li>
                <li>• 定期检查合同履行情况</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">注意事项：</span>
            </div>
            <p className="text-xs text-amber-700 mt-1">
              系统会自动监控合同到期时间，并在设定的提醒天数前发送通知。请确保联系人信息准确，以便及时处理续约事宜。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 合同资产登记表单 */}
      <Card>
        <CardHeader>
          <CardTitle>合同资产信息</CardTitle>
          <CardDescription>
            请填写完整的合同资产信息，带 * 的字段为必填项
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ContractAssetForm 
            onSubmit={handleSubmit}
            isLoading={isSubmitting}
          />
        </CardContent>
      </Card>
    </div>
  )
}