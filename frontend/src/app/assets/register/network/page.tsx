'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ITNetworkAssetForm } from '@/components/ITNetworkAssetForm'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Network } from 'lucide-react'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function NetworkAssetRegisterPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      // 添加资产类型标记
      const assetData = {
        ...data,
        asset_type: 'network',
        category_type: 'hardware'
      }
      
      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assetData),
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: '成功',
          description: `网络设备 "${result.name}" 已成功登记`,
        })
        router.push('/assets')
      } else {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to create network asset')
      }
    } catch (error) {
      console.error('Error creating network asset:', error)
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '网络设备登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/assets/register-new">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回资产类型选择
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-500 text-white">
              <Network className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">网络设备登记</h1>
              <p className="text-muted-foreground mt-1">
                登记交换机、路由器、防火墙等网络设备资产
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 登记表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            网络设备信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ITNetworkAssetForm 
            onSubmit={handleSubmit} 
            initialData={{}}
          />
        </CardContent>
      </Card>

      {/* 网络设备特殊说明 */}
      <div className="bg-green-50 dark:bg-green-950 p-6 rounded-lg">
        <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
          🌐 网络设备登记说明
        </h3>
        <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
          <li>• <strong>交换机</strong>：记录端口数量、VLAN配置、堆叠信息</li>
          <li>• <strong>路由器</strong>：填写路由协议、WAN接口类型</li>
          <li>• <strong>防火墙</strong>：记录安全策略、吞吐量规格</li>
          <li>• <strong>IP地址</strong>：管理IP地址必填，支持IPv4和IPv6</li>
          <li>• <strong>MAC地址</strong>：建议填写管理接口的MAC地址</li>
          <li>• <strong>固件版本</strong>：记录当前运行的固件版本</li>
          <li>• <strong>SNMP配置</strong>：用于设备监控和管理</li>
        </ul>
      </div>
    </div>
  )
}