'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ITAssetRegistrationForm from '@/components/ITAssetRegistrationForm'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Server } from 'lucide-react'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function ServerAssetRegisterPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      // 添加资产类型标记
      const assetData = {
        ...data,
        asset_type: 'server',
        category_type: 'hardware'
      }
      
      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assetData),
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: '成功',
          description: `服务器资产 "${result.name}" 已成功登记`,
        })
        router.push('/assets')
      } else {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to create server asset')
      }
    } catch (error) {
      console.error('Error creating server asset:', error)
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '服务器资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/assets/register-new">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回资产类型选择
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-500 text-white">
              <Server className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">服务器资产登记</h1>
              <p className="text-muted-foreground mt-1">
                登记物理服务器、虚拟服务器等计算设备资产
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 登记表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            服务器资产信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ITAssetRegistrationForm 
            onSubmit={handleSubmit} 
            isLoading={isLoading}
            assetType="server"
          />
        </CardContent>
      </Card>

      {/* 服务器资产特殊说明 */}
      <div className="bg-blue-50 dark:bg-blue-950 p-6 rounded-lg">
        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          📋 服务器资产登记说明
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• <strong>物理服务器</strong>：需要填写机柜位置、功耗等物理信息</li>
          <li>• <strong>虚拟服务器</strong>：需要指定宿主机和虚拟化平台</li>
          <li>• <strong>CPU信息</strong>：建议填写详细的CPU型号和核心数</li>
          <li>• <strong>内存配置</strong>：请填写实际安装的内存容量</li>
          <li>• <strong>存储信息</strong>：包括本地存储和网络存储</li>
          <li>• <strong>网络配置</strong>：填写管理IP和业务IP地址</li>
          <li>• <strong>操作系统</strong>：记录操作系统版本和补丁级别</li>
        </ul>
      </div>
    </div>
  )
}