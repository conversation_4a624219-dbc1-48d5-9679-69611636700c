'use client'

import React from 'react'
import {
  Box,
  Container,
  Heading,
  Text,
  useColorMode,
  Grid,
  GridItem,
  Flex,
  Icon,
  Badge,
  Tooltip,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
} from '@chakra-ui/react'
import { useTranslation } from '@/contexts/LanguageContext'
import { Database, Desktop, HardDrives, WifiHigh } from '@phosphor-icons/react'

const AssetMapPage = () => {
  const { t } = useTranslation()
  const { colorMode } = useColorMode()

  // 定义资产点位数据
  const locations = [
    {
      id: 'datacenter',
      name: '数据中心',
      position: { row: 2, col: 2 },
      type: 'datacenter',
      deviceCount: 128,
      status: 'normal',
    },
    {
      id: 'floor2-electric',
      name: '2楼弱电间',
      position: { row: 1, col: 1 },
      type: 'electric',
      deviceCount: 12,
      status: 'normal',
    },
    {
      id: 'floor2-gym',
      name: '2楼健身房',
      position: { row: 1, col: 3 },
      type: 'office',
      deviceCount: 5,
      status: 'normal',
    },
    {
      id: 'floor4-electric',
      name: '4楼弱电间',
      position: { row: 2, col: 1 },
      type: 'electric',
      deviceCount: 15,
      status: 'warning',
    },
    {
      id: 'floor7-electric',
      name: '7楼弱电间',
      position: { row: 3, col: 1 },
      type: 'electric',
      deviceCount: 18,
      status: 'normal',
    },
    {
      id: 'shanghai-office',
      name: '上海办公区',
      position: { row: 3, col: 3 },
      type: 'office',
      deviceCount: 45,
      status: 'normal',
    },
  ]

  // 获取点位图标
  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'datacenter':
        return HardDrives
      case 'electric':
        return Database
      case 'office':
        return Desktop
      default:
        return WifiHigh
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'warning':
        return 'yellow'
      case 'error':
        return 'red'
      default:
        return 'green'
    }
  }

  return (
    <Container maxW="container.xl" py={8}>
      <Heading size="lg" mb={6}>资产地图</Heading>

      {/* 资产统计卡片 */}
      <Grid templateColumns={{ base: '1fr', md: 'repeat(4, 1fr)' }} gap={4} mb={8}>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>总设备数</StatLabel>
              <StatNumber>223</StatNumber>
            </Stat>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>在线设备</StatLabel>
              <StatNumber>198</StatNumber>
            </Stat>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>告警设备</StatLabel>
              <StatNumber>15</StatNumber>
            </Stat>
          </CardBody>
        </Card>
        <Card>
          <CardBody>
            <Stat>
              <StatLabel>离线设备</StatLabel>
              <StatNumber>10</StatNumber>
            </Stat>
          </CardBody>
        </Card>
      </Grid>

      {/* 资产地图网格 */}
      <Box
        bg={colorMode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="lg"
        p={6}
        boxShadow="sm"
        border="1px solid"
        borderColor={colorMode === 'dark' ? 'gray.700' : 'gray.200'}
      >
        <Grid
          templateColumns="repeat(3, 1fr)"
          templateRows="repeat(3, 1fr)"
          gap={4}
          minH="600px"
        >
          {Array.from({ length: 9 }).map((_, index) => {
            const row = Math.floor(index / 3) + 1
            const col = (index % 3) + 1
            const location = locations.find(
              loc => loc.position.row === row && loc.position.col === col
            )

            return (
              <GridItem
                key={index}
                bg={colorMode === 'dark' ? 'gray.700' : 'gray.50'}
                borderRadius="md"
                p={4}
                border="1px solid"
                borderColor={colorMode === 'dark' ? 'gray.600' : 'gray.200'}
                transition="all 0.2s"
                _hover={{
                  transform: location ? 'scale(1.02)' : 'none',
                  boxShadow: location ? 'lg' : 'none',
                }}
                cursor={location ? 'pointer' : 'default'}
              >
                {location ? (
                  <Tooltip label={`${location.deviceCount} 台设备`}>
                    <Flex direction="column" align="center" justify="center" h="full">
                      <Icon
                        as={getLocationIcon(location.type)}
                        boxSize={12}
                        mb={4}
                        color={colorMode === 'dark' ? 'blue.200' : 'blue.500'}
                      />
                      <Text
                        fontSize="lg"
                        fontWeight="bold"
                        mb={2}
                        textAlign="center"
                      >
                        {location.name}
                      </Text>
                      <Badge
                        colorScheme={getStatusColor(location.status)}
                        fontSize="sm"
                      >
                        {location.deviceCount} 台设备
                      </Badge>
                    </Flex>
                  </Tooltip>
                ) : (
                  <Flex
                    h="full"
                    align="center"
                    justify="center"
                    color={colorMode === 'dark' ? 'gray.500' : 'gray.400'}
                  >
                    <Text fontSize="sm">空闲区域</Text>
                  </Flex>
                )}
              </GridItem>
            )
          })}
        </Grid>
      </Box>
    </Container>
  )
}

export default AssetMapPage 