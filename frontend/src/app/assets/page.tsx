'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  Desktop,
  ChartBar,
  Tag,
  ClipboardText,
  List,
  Wrench,
  Plugs,
  ShieldCheck,
  ShoppingCart,
  Trash,
  Barcode,
  UserSwitch,
  ArrowLeft,
  ArrowRight
} from '@phosphor-icons/react'
import { PageContainer, PageHeader } from '@/components/ui/page-container'
import { StatCard } from '@/components/ui/stat-card'
import { DataTable } from '@/components/ui/data-table'
import { DashboardCard, InfoCard } from '@/components/ui/dashboard-card'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import DataCenterLayout from '@/components/RackLayout/DataCenterLayout'
import RackLayout from '@/components/RackLayout/RackLayout'

// 示例数据
const mockDevices = [
  { id: 1, name: '交换机 Router-01', position_start: 40, position_end: 42 },
  { id: 2, name: 'Web 服务器 Dell R740', position_start: 36, position_end: 37 },
  { id: 3, name: '数据库服务器 IBM X3650', position_start: 32, position_end: 34 },
  { id: 4, name: '存储设备 NetApp FAS', position_start: 26, position_end: 30 },
  { id: 5, name: '备份服务器 HP DL380', position_start: 22, position_end: 24 },
  { id: 6, name: 'GPU 计算节点 NVIDIA', position_start: 18, position_end: 20 },
  { id: 7, name: '负载均衡器 F5', position_start: 14, position_end: 15 },
  { id: 8, name: '安全网关 Palo Alto', position_start: 10, position_end: 11 },
  { id: 9, name: 'KVM 控制台', position_start: 8, position_end: 8 },
  { id: 10, name: 'UPS 电源', position_start: 1, position_end: 4 },
]

// 示例机柜数据
const mockRacks = [
  { id: 'A01', name: 'A01', totalU: 42, usedU: 14, location: '右侧' },
  { id: 'A02', name: 'A02', totalU: 42, usedU: 7, location: '右侧' },
  { id: 'A03', name: 'A03', totalU: 42, usedU: 13, location: '右侧' },
  { id: 'A04', name: 'A04', totalU: 42, usedU: 4, location: '右侧' },
  { id: 'A05', name: 'A05', totalU: 42, usedU: 17, location: '右侧' },
  { id: 'A06', name: 'A06', totalU: 42, usedU: 11, location: '右侧' },
  { id: 'A07', name: 'A07', totalU: 42, usedU: 24, location: '右侧' },
  { id: 'A08', name: 'A08', totalU: 42, usedU: 20, location: '左侧' },
  { id: 'A09', name: 'A09', totalU: 42, usedU: 3, location: '左侧' },
  { id: 'A10', name: 'A10', totalU: 42, usedU: 12, location: '左侧' },
  { id: 'A11', name: 'A11', totalU: 42, usedU: 8, location: '左侧' },
  { id: 'A12', name: 'A12', totalU: 42, usedU: 5, location: '左侧' },
  { id: 'A13', name: 'A13', totalU: 42, usedU: 22, location: '左侧' },
  { id: 'A14', name: 'A14', totalU: 42, usedU: 27, location: '左侧' },
  { id: 'A15', name: 'A15', totalU: 42, usedU: 1, location: '左侧' },
]

// 示例设备列表数据
const deviceListData = [
  { key: '1', name: '交换机 Router-01', type: '网络设备', location: 'A01', status: 'active' },
  { key: '2', name: 'Web 服务器 Dell R740', type: '服务器', location: 'A01', status: 'active' },
  { key: '3', name: '数据库服务器 IBM X3650', type: '服务器', location: 'A02', status: 'active' },
  { key: '4', name: '存储设备 NetApp FAS', type: '存储', location: 'A03', status: 'active' },
  { key: '5', name: '备份服务器 HP DL380', type: '服务器', location: 'A04', status: 'warning' },
];

// 示例维护记录数据
const maintenanceRecords = [
  { key: '1', device: 'Web 服务器 Dell R740', type: '定期维护', date: '2023-05-15', status: 'completed', operator: '张工' },
  { key: '2', device: '备份服务器 HP DL380', type: '故障修复', date: '2023-06-02', status: 'completed', operator: '李工' },
  { key: '3', device: '交换机 Router-01', type: '固件更新', date: '2023-06-10', status: 'pending', operator: '王工' },
  { key: '4', device: '存储设备 NetApp FAS', type: '硬盘更换', date: '2023-06-18', status: 'in-progress', operator: '刘工' },
];

// 资产管理功能列表
const assetFeatures = [
  {
    title: '资产仪表盘',
    icon: <ChartBar size={24} weight="fill" />,
    description: '查看资产统计和数据可视化',
    path: '/assets/dashboard',
    color: 'from-blue-500 to-blue-700'
  },
  {
    title: '资产分类',
    icon: <Tag size={24} weight="fill" />,
    description: '管理资产分类和标签',
    path: '/assets/categories',
    color: 'from-cyan-500 to-cyan-700'
  },
  {
    title: '资产登记',
    icon: <ClipboardText size={24} weight="fill" />,
    description: '添加新资产到系统',
    path: '/assets/register',
    color: 'from-green-500 to-green-700'
  },
  {
    title: '设备列表',
    icon: <List size={24} weight="fill" />,
    description: '查看和管理所有设备',
    path: '/assets/devices',
    color: 'from-amber-500 to-amber-700'
  },
  {
    title: '维护管理',
    icon: <Wrench size={24} weight="fill" />,
    description: '设备维护与运维记录',
    path: '/assets/maintenance',
    color: 'from-purple-500 to-purple-700'
  },
  {
    title: '系统集成',
    icon: <Plugs size={24} weight="fill" />,
    description: '管理外部系统集成',
    path: '/assets/integrations',
    color: 'from-orange-500 to-orange-700'
  },
  {
    title: '安全审计',
    icon: <ShieldCheck size={24} weight="fill" />,
    description: '查看资产操作日志和审计记录',
    path: '/assets/audit',
    color: 'from-red-500 to-red-700'
  },
  {
    title: '资产采购',
    icon: <ShoppingCart size={24} weight="fill" />,
    description: '管理资产采购流程',
    path: '/assets/purchase',
    color: 'from-sky-500 to-sky-700'
  },
  {
    title: '资产报废',
    icon: <Trash size={24} weight="fill" />,
    description: '管理资产报废流程',
    path: '/assets/disposal',
    color: 'from-rose-500 to-rose-700'
  },
  {
    title: '资产盘点',
    icon: <Barcode size={24} weight="fill" />,
    description: '定期资产盘点与核查',
    path: '/assets/inventory',
    color: 'from-emerald-500 to-emerald-700'
  },
  {
    title: '权限管理',
    icon: <UserSwitch size={24} weight="fill" />,
    description: '用户与角色权限分配',
    path: '/admin/permissions',
    color: 'from-indigo-500 to-indigo-700'
  },
];

export default function AssetsPageNew() {
  const [selectedRack, setSelectedRack] = useState<string | null>(null)
  const router = useRouter()

  const handleRackSelect = (rackId: string) => {
    setSelectedRack(rackId)
  }

  const handleBackToOverview = () => {
    setSelectedRack(null)
  }

  // 获取当前选中机柜的设备
  const getDevicesForRack = (rackId: string) => {
    // 这里简单返回全部设备，实际应用中应该根据机柜ID筛选对应设备
    return mockDevices
  }

  // 基于机柜ID计算空间状态
  const getSpaceStatus = (rackId: string) => {
    const rack = mockRacks.find(r => r.id === rackId)
    if (!rack) return 'sufficient'

    const utilization = (rack.usedU / rack.totalU) * 100
    if (utilization < 70) {
      return 'sufficient' // 空间充足
    } else if (utilization < 90) {
      return 'limited' // 空间有限
    } else {
      return 'tight' // 空间紧张
    }
  }

  return (
    <PageContainer>
      <PageHeader
        title="资产管理"
        description="管理和监控IT资产的完整生命周期"
      />

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="资产总数"
          value={1234}
          icon={<Desktop size={20} weight="fill" />}
          colorScheme="blue"
        />

        <StatCard
          title="在线设备"
          value={1100}
          icon={<Desktop size={20} weight="fill" />}
          helpText="89.1% 在线率"
          colorScheme="green"
        />

        <StatCard
          title="待维护"
          value={12}
          icon={<Wrench size={20} weight="fill" />}
          colorScheme="amber"
        />

        <StatCard
          title="即将报废"
          value={5}
          icon={<Trash size={20} weight="fill" />}
          colorScheme="red"
        />
      </div>

      {/* 资产管理功能卡片 */}
      <DashboardCard
        title="资产管理系统"
        icon={<Desktop size={20} weight="fill" />}
        footer={
          <Button
            variant="outline"
            size="sm"
            className="ml-auto"
            onClick={() => router.push('/assets/rack-layout')}
          >
            查看更多
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        }
        className="mb-8"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {assetFeatures.map((feature, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-lg border bg-background p-4 hover:shadow-md transition-all duration-300 cursor-pointer"
              onClick={() => router.push(feature.path)}
            >
              <div className={`absolute top-0 left-0 h-full w-1 bg-gradient-to-b ${feature.color}`}></div>
              <div className="flex items-start gap-4">
                <div className={`flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-gradient-to-br ${feature.color} text-white shadow-sm`}>
                  {feature.icon}
                </div>
                <div>
                  <h3 className="font-semibold">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </DashboardCard>

      {/* 机柜布局部分 */}
      <DashboardCard
        title="机柜布局"
        icon={<Desktop size={20} weight="fill" />}
        footer={
          <Button
            variant="outline"
            size="sm"
            className="ml-auto"
            onClick={() => router.push('/assets/rack-layout')}
          >
            查看更多
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        }
        className="mb-8"
      >
        {selectedRack ? (
          <div>
            <Button
              variant="outline"
              size="sm"
              className="mb-4"
              onClick={handleBackToOverview}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回机柜布局
            </Button>

            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">机柜 {selectedRack} 详情</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">机柜信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="font-medium">机柜名称:</div>
                      <div>{selectedRack}</div>

                      <div className="font-medium">位置:</div>
                      <div>
                        {mockRacks.find(r => r.id === selectedRack)?.location || '未知'}
                      </div>

                      <div className="font-medium">总容量:</div>
                      <div>42U</div>

                      <div className="font-medium">已使用:</div>
                      <div>
                        {mockRacks.find(r => r.id === selectedRack)?.usedU || 0}U
                      </div>

                      <div className="font-medium">剩余空间:</div>
                      <div className={
                        getSpaceStatus(selectedRack) === 'sufficient' ? 'text-green-600 dark:text-green-400' :
                        getSpaceStatus(selectedRack) === 'limited' ? 'text-amber-600 dark:text-amber-400' : 'text-red-600 dark:text-red-400'
                      }>
                        {42 - (mockRacks.find(r => r.id === selectedRack)?.usedU || 0)}U
                      </div>

                      <div className="font-medium">使用率:</div>
                      <div>
                        {((mockRacks.find(r => r.id === selectedRack)?.usedU || 0) / 42 * 100).toFixed(1)}%
                      </div>

                      <div className="font-medium">空间状态:</div>
                      <div className={
                        getSpaceStatus(selectedRack) === 'sufficient' ? 'text-green-600 dark:text-green-400 font-medium' :
                        getSpaceStatus(selectedRack) === 'limited' ? 'text-amber-600 dark:text-amber-400 font-medium' : 'text-red-600 dark:text-red-400 font-medium'
                      }>
                        {getSpaceStatus(selectedRack) === 'sufficient' ? '空间充足' :
                         getSpaceStatus(selectedRack) === 'limited' ? '空间有限' : '空间紧张'}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="lg:col-span-3">
                <div className="bg-white dark:bg-slate-900 rounded-lg border p-4">
                  <RackLayout
                    totalU={42}
                    devices={getDevicesForRack(selectedRack)}
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white dark:bg-slate-900 rounded-lg border p-4">
            <DataCenterLayout racks={mockRacks} onSelectRack={handleRackSelect} />
          </div>
        )}
      </DashboardCard>

      {/* 设备列表部分 */}
      <DataTable
        title="设备列表"
        description="最近添加的设备"
        data={deviceListData}
        columns={[
          {
            key: 'name',
            header: '设备名称',
            cell: (device) => (
              <div className="flex items-center">
                <div className="p-1.5 rounded-md bg-blue-50 dark:bg-blue-900 mr-3">
                  <Desktop size={16} className="text-blue-500 dark:text-blue-300" />
                </div>
                <span className="font-medium">{device.name}</span>
              </div>
            )
          },
          {
            key: 'type',
            header: '设备类型',
            cell: (device) => device.type
          },
          {
            key: 'location',
            header: '位置',
            cell: (device) => device.location
          },
          {
            key: 'status',
            header: '状态',
            cell: (device) => (
              <Badge className={
                device.status === 'active' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                device.status === 'warning' ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400' :
                'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
              }>
                {device.status === 'active' ? '正常' : device.status === 'warning' ? '警告' : '故障'}
              </Badge>
            )
          }
        ]}
        headerActions={
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/assets/devices')}
          >
            查看更多
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        }
        className="mb-8"
      />

      {/* 维护记录部分 */}
      <DataTable
        title="维护记录"
        description="最近的设备维护记录"
        data={maintenanceRecords}
        columns={[
          {
            key: 'device',
            header: '设备',
            cell: (record) => record.device
          },
          {
            key: 'type',
            header: '维护类型',
            cell: (record) => record.type
          },
          {
            key: 'date',
            header: '日期',
            cell: (record) => record.date
          },
          {
            key: 'status',
            header: '状态',
            cell: (record) => (
              <Badge className={
                record.status === 'completed' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                record.status === 'in-progress' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                record.status === 'pending' ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400' :
                'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
              }>
                {
                  record.status === 'completed' ? '已完成' :
                  record.status === 'in-progress' ? '进行中' :
                  record.status === 'pending' ? '待处理' : '取消'
                }
              </Badge>
            )
          },
          {
            key: 'operator',
            header: '操作人员',
            cell: (record) => record.operator
          }
        ]}
        headerActions={
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/assets/maintenance')}
          >
            查看更多
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        }
      />
    </PageContainer>
  )
}
