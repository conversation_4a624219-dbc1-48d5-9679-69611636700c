'use client'
import React from 'react'
import { Form, Input, InputNumber, Button, Card, DatePicker, message, Select, Row, Col } from 'antd'
import { useRouter } from 'next/navigation'
import { ArrowLeftOutlined } from '@ant-design/icons'

const { Option } = Select

const supplierOptions = ['华为', '华三', '联想']

const PurchaseNewPage = () => {
  const [form] = Form.useForm()
  const router = useRouter()

  const handleFinish = (values: any) => {
    // TODO: 调用后端API保存采购单
    message.success('采购单创建成功！')
    router.push('/assets/purchase')
  }

  return (
    <Card
      title={
        <span>
          <Button
            icon={<ArrowLeftOutlined />}
            type="link"
            onClick={() => router.push('/assets/purchase')}
            style={{ marginRight: 8, padding: 0 }}
          >
            返回
          </Button>
          新建采购单
        </span>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        initialValues={{ quantity: 1, amount: 0 }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="资产名称"
              name="asset"
              rules={[{ required: true, message: '请输入资产名称' }]}
            >
              <Input placeholder="请输入资产名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="数量"
              name="quantity"
              rules={[{ required: true, message: '请输入数量' }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} placeholder="请输入数量" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="金额"
              name="amount"
              rules={[{ required: true, message: '请输入金额' }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入金额" prefix="￥" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="供应商"
              name="supplier"
              rules={[{ required: true, message: '请选择供应商' }]}
            >
              <Select placeholder="请选择供应商" mode="tags">
                {supplierOptions.map(s => (
                  <Option key={s} value={s}>{s}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="申请人"
              name="applicant"
              rules={[{ required: true, message: '请输入申请人' }]}
            >
              <Input placeholder="请输入申请人" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="申请日期"
              name="date"
              rules={[{ required: true, message: '请选择申请日期' }]}
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="关联OA单号"
              name="oaNo"
            >
              <Input placeholder="可自定义输入OA单号" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item>
          <Button type="primary" htmlType="submit">提交</Button>
        </Form.Item>
      </Form>
    </Card>
  )
}

export default PurchaseNewPage 