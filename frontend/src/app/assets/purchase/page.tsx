'use client'
import React, { useState } from 'react'
import { Card, Table, Button, Tag, Row, Col, Select, DatePicker, Input } from 'antd'
import { useRouter } from 'next/navigation'
import { ArrowLeftOutlined } from '@ant-design/icons'
import HasPermission from '@/components/HasPermission'

const mockPurchaseOrders = [
  { key: '1', oaNo: 'OA20240601', asset: '服务器', quantity: 10, amount: 50000, supplier: '华为', status: 'pending', applicant: '张三', date: '2024-06-01' },
  { key: '2', oaNo: 'OA20240602', asset: '交换机', quantity: 5, amount: 12000, supplier: '华三', status: 'approved', applicant: '李四', date: '2024-06-02' },
  { key: '3', oaNo: 'OA20240603', asset: '笔记本电脑', quantity: 20, amount: 80000, supplier: '联想', status: 'completed', applicant: '王五', date: '2024-06-03' },
]

const columns = [
  { title: '关联OA单号', dataIndex: 'oaNo', key: 'oaNo' },
  { title: '资产名称', dataIndex: 'asset', key: 'asset' },
  { title: '数量', dataIndex: 'quantity', key: 'quantity' },
  { title: '金额', dataIndex: 'amount', key: 'amount', render: (v: number) => `￥${v}` },
  { title: '供应商', dataIndex: 'supplier', key: 'supplier' },
  { title: '状态', dataIndex: 'status', key: 'status', render: (status: string) => <Tag color={status === 'pending' ? 'orange' : status === 'approved' ? 'blue' : 'green'}>{status === 'pending' ? '待审批' : status === 'approved' ? '已审批' : '已完成'}</Tag> },
  { title: '申请人', dataIndex: 'applicant', key: 'applicant' },
  { title: '申请日期', dataIndex: 'date', key: 'date' },
]

const PurchasePage = () => {
  const router = useRouter()
  return (
    <Card 
      title={
        <span>
          <Button
            icon={<ArrowLeftOutlined />}
            type="link"
            onClick={() => router.push('/assets')}
            style={{ marginRight: 8, padding: 0 }}
          >
            返回
          </Button>
          资产采购管理
        </span>
      }
      extra={
        <HasPermission permission="purchase.create">
          <Button type="primary" onClick={() => router.push('/assets/purchase/new')}>新建采购单</Button>
        </HasPermission>
      }
    >
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}><Input placeholder="关联OA单号/资产名称" /></Col>
        <Col span={6}><Select placeholder="供应商" style={{ width: '100%' }} allowClear><Select.Option value="华为">华为</Select.Option><Select.Option value="华三">华三</Select.Option><Select.Option value="联想">联想</Select.Option></Select></Col>
        <Col span={6}><Select placeholder="状态" style={{ width: '100%' }} allowClear><Select.Option value="pending">待审批</Select.Option><Select.Option value="approved">已审批</Select.Option><Select.Option value="completed">已完成</Select.Option></Select></Col>
        <Col span={6}><DatePicker.RangePicker style={{ width: '100%' }} /></Col>
      </Row>
      <Table columns={columns} dataSource={mockPurchaseOrders} pagination={{ pageSize: 10 }} />
    </Card>
  )
}

export default PurchasePage 