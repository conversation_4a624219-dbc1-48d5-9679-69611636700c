'use client'
import React, { useState, useEffect } from 'react'
import { Card, Table, Tag, Space, Button, DatePicker, Select, Input, Form, Row, Col, Tabs, Alert, Statistic, Divider, Badge, Tooltip, Typography } from 'antd'
import { useRouter } from 'next/navigation'
import { 
  SafetyOutlined, 
  SearchOutlined, 
  FileExcelOutlined, 
  FilePdfOutlined, 
  ClockCircleOutlined,
  UserOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  ArrowLeftOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileTextOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons'
import type { TableColumnsType } from 'antd'
import ReactECharts from 'echarts-for-react'

const { TabPane } = Tabs
const { Option } = Select
const { RangePicker } = DatePicker
const { Title, Text } = Typography

// 操作日志类型
interface AuditLog {
  id: string
  timestamp: string
  user: string
  action: string
  module: string
  target: string
  targetId: string
  details: string
  ipAddress: string
  status: 'success' | 'failure' | 'warning'
}

// 安全事件类型
interface SecurityEvent {
  id: string
  timestamp: string
  eventType: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  description: string
  user?: string
  ipAddress?: string
  status: 'open' | 'investigating' | 'resolved' | 'false-positive'
  resolutionDetails?: string
}

// 审计报告类型
interface AuditReport {
  id: string
  title: string
  period: string
  generatedAt: string
  generatedBy: string
  status: 'draft' | 'published' | 'archived'
  findings: number
  recommendations: number
  fileUrl: string
}

const AssetAuditPage = () => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('logs')
  const [form] = Form.useForm()
  const [searchForm] = Form.useForm()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // 模拟操作日志数据
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([
    {
      id: '1001',
      timestamp: '2023-05-10 14:30:22',
      user: '张三',
      action: '创建',
      module: '资产',
      target: 'Dell服务器',
      targetId: 'A001',
      details: '创建了新资产: Dell PowerEdge R740',
      ipAddress: '*************',
      status: 'success'
    },
    {
      id: '1002',
      timestamp: '2023-05-10 15:45:10',
      user: '李四',
      action: '修改',
      module: '资产分类',
      target: '服务器分类',
      targetId: 'C002',
      details: '修改了资产分类属性',
      ipAddress: '*************',
      status: 'success'
    },
    {
      id: '1003',
      timestamp: '2023-05-10 16:20:05',
      user: '王五',
      action: '删除',
      module: '资产',
      target: '旧打印机',
      targetId: 'A003',
      details: '删除了资产记录',
      ipAddress: '*************',
      status: 'warning'
    },
    {
      id: '1004',
      timestamp: '2023-05-10 17:10:30',
      user: '赵六',
      action: '登录',
      module: '系统',
      target: '用户账户',
      targetId: 'U004',
      details: '登录失败: 密码错误',
      ipAddress: '*************',
      status: 'failure'
    },
    {
      id: '1005',
      timestamp: '2023-05-11 09:05:15',
      user: '张三',
      action: '导出',
      module: '资产',
      target: '资产列表',
      targetId: '-',
      details: '导出了完整资产列表',
      ipAddress: '*************',
      status: 'success'
    }
  ])

  // 模拟安全事件数据
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([
    {
      id: '2001',
      timestamp: '2023-05-09 23:15:40',
      eventType: '多次登录失败',
      severity: 'high',
      description: '同一IP地址多次登录失败尝试',
      user: '未知',
      ipAddress: '************',
      status: 'resolved',
      resolutionDetails: '确认为合法用户密码输入错误，已联系用户重置密码'
    },
    {
      id: '2002',
      timestamp: '2023-05-10 02:30:10',
      eventType: '异常API访问',
      severity: 'medium',
      description: '检测到API访问频率异常',
      ipAddress: '*************',
      status: 'investigating'
    },
    {
      id: '2003',
      timestamp: '2023-05-10 10:45:22',
      eventType: '权限提升',
      severity: 'critical',
      description: '用户尝试提升系统权限',
      user: '王五',
      ipAddress: '*************',
      status: 'open'
    },
    {
      id: '2004',
      timestamp: '2023-05-11 08:20:15',
      eventType: '敏感数据访问',
      severity: 'low',
      description: '用户访问了敏感资产数据',
      user: '李四',
      ipAddress: '*************',
      status: 'false-positive',
      resolutionDetails: '确认用户有合法访问权限'
    }
  ])

  // 模拟审计报告数据
  const [auditReports, setAuditReports] = useState<AuditReport[]>([
    {
      id: '3001',
      title: '资产管理系统月度安全审计',
      period: '2023-04-01 至 2023-04-30',
      generatedAt: '2023-05-05 10:00:00',
      generatedBy: '系统管理员',
      status: 'published',
      findings: 3,
      recommendations: 2,
      fileUrl: '/reports/audit-2023-04.pdf'
    },
    {
      id: '3002',
      title: '资产管理系统季度合规审计',
      period: '2023-01-01 至 2023-03-31',
      generatedAt: '2023-04-10 14:30:00',
      generatedBy: '合规官',
      status: 'published',
      findings: 5,
      recommendations: 4,
      fileUrl: '/reports/compliance-Q1-2023.pdf'
    },
    {
      id: '3003',
      title: '资产管理系统安全漏洞评估',
      period: '2023-05-01 至 2023-05-15',
      generatedAt: '2023-05-16 09:15:00',
      generatedBy: '安全专家',
      status: 'draft',
      findings: 2,
      recommendations: 2,
      fileUrl: '/reports/security-assessment-2023-05.pdf'
    }
  ])

  // 操作日志表格列
  const logColumns: TableColumnsType<AuditLog> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      sorter: (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user'
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action) => {
        const colorMap: Record<string, string> = {
          '创建': 'green',
          '修改': 'blue',
          '删除': 'red',
          '登录': 'purple',
          '导出': 'orange'
        };
        return <Tag color={colorMap[action] || 'default'}>{action}</Tag>;
      }
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module'
    },
    {
      title: '目标',
      dataIndex: 'target',
      key: 'target'
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      ellipsis: true
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          success: { color: 'success', text: '成功', icon: <CheckCircleOutlined /> },
          failure: { color: 'error', text: '失败', icon: <CloseCircleOutlined /> },
          warning: { color: 'warning', text: '警告', icon: <WarningOutlined /> }
        };
        const { color, text, icon } = statusMap[status as keyof typeof statusMap];
        return <Tag color={color} icon={icon}>{text}</Tag>;
      }
    }
  ]

  // 安全事件表格列
  const eventColumns: TableColumnsType<SecurityEvent> = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      sorter: (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType'
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity) => {
        const severityMap = {
          critical: { color: '#f5222d', text: '严重' },
          high: { color: '#fa8c16', text: '高' },
          medium: { color: '#faad14', text: '中' },
          low: { color: '#52c41a', text: '低' }
        };
        const { color, text } = severityMap[severity as keyof typeof severityMap];
        return (
          <Tag color={color} style={{ fontWeight: severity === 'critical' ? 'bold' : 'normal' }}>
            {text}
          </Tag>
        );
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '用户',
      dataIndex: 'user',
      key: 'user',
      render: (user) => user || '未知'
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'open': { color: 'red', text: '未处理' },
          'investigating': { color: 'blue', text: '调查中' },
          'resolved': { color: 'green', text: '已解决' },
          'false-positive': { color: 'gray', text: '误报' }
        };
        const { color, text } = statusMap[status as keyof typeof statusMap];
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewEvent(record)}>
          查看详情
        </Button>
      )
    }
  ]

  // 审计报告表格列
  const reportColumns: TableColumnsType<AuditReport> = [
    {
      title: '报告标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
          {record.status === 'draft' && <Tag color="orange">草稿</Tag>}
        </Space>
      )
    },
    {
      title: '审计周期',
      dataIndex: 'period',
      key: 'period'
    },
    {
      title: '生成时间',
      dataIndex: 'generatedAt',
      key: 'generatedAt'
    },
    {
      title: '生成者',
      dataIndex: 'generatedBy',
      key: 'generatedBy'
    },
    {
      title: '发现问题',
      dataIndex: 'findings',
      key: 'findings',
      render: (findings) => (
        <Badge count={findings} showZero style={{ backgroundColor: findings > 0 ? '#f5222d' : '#52c41a' }} />
      )
    },
    {
      title: '建议',
      dataIndex: 'recommendations',
      key: 'recommendations',
      render: (recommendations) => (
        <Badge count={recommendations} showZero style={{ backgroundColor: '#1890ff' }} />
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'draft': { color: 'orange', text: '草稿' },
          'published': { color: 'green', text: '已发布' },
          'archived': { color: 'default', text: '已归档' }
        };
        const { color, text } = statusMap[status as keyof typeof statusMap];
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewReport(record)}>
            查看
          </Button>
          <Button type="link" icon={<DownloadOutlined />} onClick={() => handleDownloadReport(record)}>
            下载
          </Button>
        </Space>
      )
    }
  ]

  // 查看安全事件详情
  const handleViewEvent = (event: SecurityEvent) => {
    console.log('查看安全事件:', event);
    // 这里可以实现弹窗显示详情
  }

  // 查看审计报告
  const handleViewReport = (report: AuditReport) => {
    console.log('查看审计报告:', report);
    // 这里可以实现预览报告
  }

  // 下载审计报告
  const handleDownloadReport = (report: AuditReport) => {
    console.log('下载审计报告:', report);
    // 这里可以实现下载功能
  }

  // 导出日志
  const handleExportLogs = () => {
    console.log('导出操作日志');
    // 这里可以实现导出功能
  }

  // 搜索日志
  const handleSearchLogs = (values: any) => {
    console.log('搜索条件:', values);
    // 这里可以实现搜索功能
  }

  // 获取安全事件统计图表配置
  const getSecurityEventChartOption = () => {
    // 按严重程度统计安全事件
    const severityCounts = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    };
    
    securityEvents.forEach(event => {
      severityCounts[event.severity]++;
    });
    
    return {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '安全事件',
          type: 'pie',
          radius: '70%',
          data: [
            { value: severityCounts.critical, name: '严重', itemStyle: { color: '#f5222d' } },
            { value: severityCounts.high, name: '高', itemStyle: { color: '#fa8c16' } },
            { value: severityCounts.medium, name: '中', itemStyle: { color: '#faad14' } },
            { value: severityCounts.low, name: '低', itemStyle: { color: '#52c41a' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  // 获取操作日志统计图表配置
  const getAuditLogChartOption = () => {
    // 按操作类型统计日志
    const actionCounts: Record<string, number> = {};
    
    auditLogs.forEach(log => {
      if (!actionCounts[log.action]) {
        actionCounts[log.action] = 0;
      }
      actionCounts[log.action]++;
    });
    
    const actions = Object.keys(actionCounts);
    const counts = actions.map(action => actionCounts[action]);
    
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: actions
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '操作次数',
          type: 'bar',
          data: counts.map((value, index) => {
            const colors = ['#1890ff', '#52c41a', '#f5222d', '#722ed1', '#fa8c16'];
            return {
              value,
              itemStyle: {
                color: colors[index % colors.length]
              }
            };
          })
        }
      ]
    };
  };

  return (
    <div>
      <Card 
        title={
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => router.push('/assets')} />
            安全审计
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="安全审计功能记录所有系统操作，帮助识别潜在安全问题">
              <InfoCircleOutlined />
            </Tooltip>
            <Button 
              type="primary" 
              icon={<FilePdfOutlined />}
              onClick={() => handleExportLogs()}
            >
              导出审计报告
            </Button>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={<span><ClockCircleOutlined />操作日志</span>} 
            key="logs"
          >
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card bordered={false}>
                  <Form 
                    form={searchForm}
                    layout="inline" 
                    onFinish={handleSearchLogs}
                    style={{ marginBottom: 16 }}
                  >
                    <Form.Item name="dateRange" label="时间范围">
                      <RangePicker showTime />
                    </Form.Item>
                    <Form.Item name="user" label="用户">
                      <Input placeholder="用户名" />
                    </Form.Item>
                    <Form.Item name="action" label="操作">
                      <Select placeholder="选择操作" allowClear style={{ width: 120 }}>
                        <Option value="创建">创建</Option>
                        <Option value="修改">修改</Option>
                        <Option value="删除">删除</Option>
                        <Option value="登录">登录</Option>
                        <Option value="导出">导出</Option>
                      </Select>
                    </Form.Item>
                    <Form.Item name="module" label="模块">
                      <Select placeholder="选择模块" allowClear style={{ width: 120 }}>
                        <Option value="资产">资产</Option>
                        <Option value="资产分类">资产分类</Option>
                        <Option value="系统">系统</Option>
                      </Select>
                    </Form.Item>
                    <Form.Item name="status" label="状态">
                      <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
                        <Option value="success">成功</Option>
                        <Option value="failure">失败</Option>
                        <Option value="warning">警告</Option>
                      </Select>
                    </Form.Item>
                    <Form.Item>
                      <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                        搜索
                      </Button>
                    </Form.Item>
                    <Form.Item>
                      <Button onClick={() => searchForm.resetFields()}>
                        重置
                      </Button>
                    </Form.Item>
                    <Form.Item>
                      <Button icon={<FileExcelOutlined />} onClick={handleExportLogs}>
                        导出
                      </Button>
                    </Form.Item>
                  </Form>
                  
                  <Table 
                    columns={logColumns} 
                    dataSource={auditLogs}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                  />
                </Card>
              </Col>
              
              <Col span={24}>
                <Card title="操作统计" bordered={false} suppressHydrationWarning>
                  {isClient && (
                    <ReactECharts 
                      option={getAuditLogChartOption()} 
                      style={{ height: 300 }} 
                    />
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane 
            tab={<span><WarningOutlined />安全事件</span>} 
            key="events"
          >
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Card bordered={false}>
                  <Statistic 
                    title="未处理事件" 
                    value={securityEvents.filter(e => e.status === 'open').length} 
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card bordered={false}>
                  <Statistic 
                    title="调查中事件" 
                    value={securityEvents.filter(e => e.status === 'investigating').length}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card bordered={false}>
                  <Statistic 
                    title="已解决事件" 
                    value={securityEvents.filter(e => e.status === 'resolved').length}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card bordered={false}>
                  <Statistic 
                    title="误报事件" 
                    value={securityEvents.filter(e => e.status === 'false-positive').length}
                    valueStyle={{ color: '#8c8c8c' }}
                  />
                </Card>
              </Col>
              
              <Col span={12}>
                <Card title="安全事件列表" bordered={false}>
                  <Table 
                    columns={eventColumns} 
                    dataSource={securityEvents}
                    rowKey="id"
                    pagination={false}
                  />
                </Card>
              </Col>
              
              <Col span={12}>
                <Card title="安全事件严重程度分布" bordered={false} suppressHydrationWarning>
                  {isClient && (
                    <ReactECharts 
                      option={getSecurityEventChartOption()} 
                      style={{ height: 300 }} 
                    />
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>
          
          <TabPane 
            tab={<span><FileTextOutlined />审计报告</span>} 
            key="reports"
          >
            <Alert
              message="系统合规审计"
              description="定期审计报告有助于确保系统符合相关法规和内部政策要求，提高系统安全性和可靠性。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Table 
              columns={reportColumns} 
              dataSource={auditReports}
              rowKey="id"
              pagination={false}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  )
}

export default AssetAuditPage