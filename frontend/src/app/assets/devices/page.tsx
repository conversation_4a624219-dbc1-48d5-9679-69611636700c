'use client'

import React, { useState } from 'react'
import { <PERSON>, Heading, Flex, Button, useColorMode, Text, HStack } from '@chakra-ui/react'
import { Card, Breadcrumb, Table, Tag, Input, Select, Space, Form, Modal } from 'antd'
import { useRouter } from 'next/navigation'
import {
  SearchOutlined,
  PlusOutlined,
  FilterOutlined,
  ExportOutlined,
  ImportOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons'

const { Option } = Select;

// 示例设备列表数据
const deviceListData = [
  { key: '1', id: 'DEV-001', name: '交换机 Router-01', type: '网络设备', model: 'Cisco Catalyst 9300', location: 'A01', status: 'active', purchaseDate: '2022-01-15', warranty: '36个月', value: 25000 },
  { key: '2', id: 'DEV-002', name: 'Web 服务器 Dell R740', type: '服务器', model: 'Dell PowerEdge R740', location: 'A01', status: 'active', purchaseDate: '2022-02-20', warranty: '36个月', value: 45000 },
  { key: '3', id: 'DEV-003', name: '数据库服务器 IBM X3650', type: '服务器', model: 'IBM System x3650 M5', location: 'A02', status: 'active', purchaseDate: '2021-11-10', warranty: '36个月', value: 50000 },
  { key: '4', id: 'DEV-004', name: '存储设备 NetApp FAS', type: '存储', model: 'NetApp FAS8300', location: 'A03', status: 'active', purchaseDate: '2022-03-05', warranty: '48个月', value: 120000 },
  { key: '5', id: 'DEV-005', name: '备份服务器 HP DL380', type: '服务器', model: 'HP ProLiant DL380 Gen10', location: 'A04', status: 'warning', purchaseDate: '2021-08-18', warranty: '36个月', value: 38000 },
  { key: '6', id: 'DEV-006', name: 'GPU 计算节点 NVIDIA', type: '服务器', model: 'NVIDIA DGX A100', location: 'A05', status: 'active', purchaseDate: '2022-04-12', warranty: '36个月', value: 180000 },
  { key: '7', id: 'DEV-007', name: '负载均衡器 F5', type: '网络设备', model: 'F5 BIG-IP 2000s', location: 'A06', status: 'active', purchaseDate: '2022-01-30', warranty: '36个月', value: 65000 },
  { key: '8', id: 'DEV-008', name: '安全网关 Palo Alto', type: '网络设备', model: 'Palo Alto PA-3260', location: 'A07', status: 'error', purchaseDate: '2021-10-25', warranty: '36个月', value: 75000 },
  { key: '9', id: 'DEV-009', name: 'KVM 控制台', type: '周边设备', model: 'Raritan Dominion KX III', location: 'A08', status: 'active', purchaseDate: '2022-02-08', warranty: '24个月', value: 12000 },
  { key: '10', id: 'DEV-010', name: 'UPS 电源', type: '电源设备', model: 'APC Smart-UPS SRT 10000VA', location: 'A09', status: 'active', purchaseDate: '2022-03-15', warranty: '24个月', value: 35000 },
];

const DevicesPage = () => {
  const { colorMode } = useColorMode()
  const router = useRouter()
  const [searchText, setSearchText] = useState('')
  const [selectedType, setSelectedType] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 设备类型列表
  const deviceTypes = Array.from(new Set(deviceListData.map(device => device.type)))

  // 过滤设备列表
  const filteredDevices = deviceListData.filter(device => {
    const matchesSearch = searchText ? 
      device.name.toLowerCase().includes(searchText.toLowerCase()) || 
      device.id.toLowerCase().includes(searchText.toLowerCase()) :
      true
    
    const matchesType = selectedType ? device.type === selectedType : true
    const matchesStatus = selectedStatus ? device.status === selectedStatus : true
    
    return matchesSearch && matchesType && matchesStatus
  })

  // 设备列表列定义
  const deviceColumns = [
    {
      title: '设备ID',
      dataIndex: 'id',
      key: 'id',
      sorter: (a: any, b: any) => a.id.localeCompare(b.id),
    },
    {
      title: '设备名称',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: any, b: any) => a.name.localeCompare(b.name),
    },
    {
      title: '设备类型',
      dataIndex: 'type',
      key: 'type',
      filters: deviceTypes.map(type => ({ text: type, value: type })),
      onFilter: (value: any, record: any) => record.type === value,
    },
    {
      title: '型号',
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
      sorter: (a: any, b: any) => a.location.localeCompare(b.location),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : status === 'warning' ? 'orange' : 'red'}>
          {status === 'active' ? '正常' : status === 'warning' ? '警告' : '故障'}
        </Tag>
      ),
      filters: [
        { text: '正常', value: 'active' },
        { text: '警告', value: 'warning' },
        { text: '故障', value: 'error' },
      ],
      onFilter: (value: any, record: any) => record.status === value,
    },
    {
      title: '购买日期',
      dataIndex: 'purchaseDate',
      key: 'purchaseDate',
      sorter: (a: any, b: any) => new Date(a.purchaseDate).getTime() - new Date(b.purchaseDate).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="small">
          <Button size="sm" leftIcon={<EyeOutlined />} variant="ghost" onClick={() => router.push(`/assets/${record.id}`)}>
            查看
          </Button>
          <Button size="sm" leftIcon={<EditOutlined />} variant="ghost">
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  // 处理添加设备
  const handleAddDevice = () => {
    setIsModalVisible(true)
  }

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()
      console.log('添加设备:', values)
      setIsModalVisible(false)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false)
    form.resetFields()
  }

  return (
    <Box maxW="1400px" mx="auto" px={6} py={8}>
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>
          <a onClick={() => router.push('/assets')}>资产管理系统</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>设备列表</Breadcrumb.Item>
      </Breadcrumb>

      <Card title="设备列表" style={{ marginBottom: '24px' }}>
        <Flex justify="space-between" align="center" mb={4}>
          <HStack spacing={4}>
            <Input 
              placeholder="搜索设备名称或ID" 
              prefix={<SearchOutlined />} 
              style={{ width: 250 }}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              allowClear
            />
            <Select
              placeholder="设备类型"
              style={{ width: 150 }}
              allowClear
              onChange={(value) => setSelectedType(value)}
            >
              {deviceTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
            <Select
              placeholder="设备状态"
              style={{ width: 150 }}
              allowClear
              onChange={(value) => setSelectedStatus(value)}
            >
              <Option value="active">正常</Option>
              <Option value="warning">警告</Option>
              <Option value="error">故障</Option>
            </Select>
          </HStack>
          <HStack spacing={2}>
            <Button leftIcon={<PlusOutlined />} colorScheme="blue" onClick={handleAddDevice}>
              添加设备
            </Button>
            <Button leftIcon={<ImportOutlined />} variant="outline">
              导入
            </Button>
            <Button leftIcon={<ExportOutlined />} variant="outline">
              导出
            </Button>
          </HStack>
        </Flex>

        <Table 
          columns={deviceColumns} 
          dataSource={filteredDevices} 
          pagination={{ pageSize: 10 }}
          rowKey="key"
        />
      </Card>

      {/* 添加设备模态框 */}
      <Modal
        title="添加设备"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="id"
            label="设备ID"
            rules={[{ required: true, message: '请输入设备ID' }]}
          >
            <Input placeholder="请输入设备ID" />
          </Form.Item>
          <Form.Item
            name="name"
            label="设备名称"
            rules={[{ required: true, message: '请输入设备名称' }]}
          >
            <Input placeholder="请输入设备名称" />
          </Form.Item>
          <Form.Item
            name="type"
            label="设备类型"
            rules={[{ required: true, message: '请选择设备类型' }]}
          >
            <Select placeholder="请选择设备类型">
              {deviceTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="model"
            label="设备型号"
            rules={[{ required: true, message: '请输入设备型号' }]}
          >
            <Input placeholder="请输入设备型号" />
          </Form.Item>
          <Form.Item
            name="location"
            label="设备位置"
            rules={[{ required: true, message: '请输入设备位置' }]}
          >
            <Input placeholder="请输入设备位置" />
          </Form.Item>
          <Form.Item
            name="purchaseDate"
            label="购买日期"
          >
            <Input type="date" />
          </Form.Item>
          <Form.Item
            name="warranty"
            label="保修期"
          >
            <Input placeholder="请输入保修期" />
          </Form.Item>
          <Form.Item
            name="value"
            label="设备价值"
          >
            <Input type="number" placeholder="请输入设备价值" />
          </Form.Item>
        </Form>
      </Modal>
    </Box>
  )
}

export default DevicesPage 