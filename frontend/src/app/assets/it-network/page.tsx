'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Server, Network, Database, HardDrive } from 'lucide-react'
import Link from 'next/link'
import ITAssetRegistrationForm from '@/components/ITAssetRegistrationForm'
import { toast } from '@/components/ui/use-toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function ITNetworkAssetRegisterPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [assetType, setAssetType] = useState('server')

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      // 添加资产类型标记
      const assetData = {
        ...data,
        asset_type: assetType
      }
      
      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assetData),
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: '成功',
          description: `IT网络资产 "${result.name}" 已成功登记`,
        })
        router.push('/assets')
      } else {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to create asset')
      }
    } catch (error) {
      console.error('Error creating asset:', error)
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : 'IT网络资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Link href="/assets">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回资产列表
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">IT网络资产登记</h1>
          <p className="text-muted-foreground mt-2">
            添加新的IT网络资产到系统中，包括服务器、网络设备、存储设备等
          </p>
        </div>
      </div>

      <Tabs value={assetType} onValueChange={setAssetType} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-2xl">
          <TabsTrigger value="server" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            服务器
          </TabsTrigger>
          <TabsTrigger value="network" className="flex items-center gap-2">
            <Network className="h-4 w-4" />
            网络设备
          </TabsTrigger>
          <TabsTrigger value="storage" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            存储设备
          </TabsTrigger>
          <TabsTrigger value="other" className="flex items-center gap-2">
            <HardDrive className="h-4 w-4" />
            其他设备
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="server" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                服务器资产登记
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ITAssetRegistrationForm
                onSubmit={handleSubmit}
                isLoading={isLoading}
                assetType="server"
                initialData={{
                  category_id: 2 // 服务器分类ID
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="network" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                网络设备登记
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ITAssetRegistrationForm
                onSubmit={handleSubmit}
                isLoading={isLoading}
                assetType="network"
                initialData={{
                  category_id: 3 // 网络设备分类ID
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="storage" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                存储设备登记
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ITAssetRegistrationForm
                onSubmit={handleSubmit}
                isLoading={isLoading}
                assetType="storage"
                initialData={{
                  category_id: 4 // 存储设备分类ID
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="other" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HardDrive className="h-5 w-5" />
                其他IT设备登记
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ITAssetRegistrationForm
                onSubmit={handleSubmit}
                isLoading={isLoading}
                assetType="other"
                initialData={{
                  category_id: 5 // 其他设备分类ID
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}