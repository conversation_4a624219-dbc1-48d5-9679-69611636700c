'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Server, Network, Database, Laptop, Printer, FileText, Shield, Wrench, Package, HardDrive } from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface AssetType {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  color: string
  href: string
  category: string
}

const assetTypes: AssetType[] = [
  // 硬件设备
  {
    id: 'server',
    name: '服务器资产',
    description: '物理服务器、虚拟服务器等计算设备',
    icon: Server,
    color: 'bg-blue-500',
    href: '/assets/register/server',
    category: '硬件设备'
  },
  {
    id: 'network',
    name: '网络设备',
    description: '交换机、路由器、防火墙等网络设备',
    icon: Network,
    color: 'bg-green-500',
    href: '/assets/register/network',
    category: '硬件设备'
  },
  {
    id: 'storage',
    name: '存储设备',
    description: 'SAN、NAS、磁盘阵列等存储设备',
    icon: Database,
    color: 'bg-purple-500',
    href: '/assets/register/storage',
    category: '硬件设备'
  },
  {
    id: 'terminal',
    name: '终端设备',
    description: '台式机、笔记本、平板等终端设备',
    icon: Laptop,
    color: 'bg-orange-500',
    href: '/assets/register/terminal',
    category: '硬件设备'
  },
  {
    id: 'printer',
    name: '打印设备',
    description: '打印机、复印机、扫描仪等办公设备',
    icon: Printer,
    color: 'bg-cyan-500',
    href: '/assets/register/printer',
    category: '硬件设备'
  },
  {
    id: 'infrastructure',
    name: '基础设施',
    description: '机柜、UPS、空调等基础设施设备',
    icon: HardDrive,
    color: 'bg-gray-500',
    href: '/assets/register/infrastructure',
    category: '硬件设备'
  },
  // 软件资产
  {
    id: 'software',
    name: '软件资产',
    description: '操作系统、数据库、办公软件等软件资产',
    icon: FileText,
    color: 'bg-indigo-500',
    href: '/assets/register/software',
    category: '软件资产'
  },
  {
    id: 'license',
    name: '授权许可',
    description: '软件许可证、数字证书等授权资产',
    icon: Shield,
    color: 'bg-yellow-500',
    href: '/assets/register/license',
    category: '软件资产'
  },
  // 服务合同
  {
    id: 'contract',
    name: '合同管理',
    description: '采购合同、服务合同、租赁合同等',
    icon: FileText,
    color: 'bg-red-500',
    href: '/assets/register/contract',
    category: '服务合同'
  },
  {
    id: 'maintenance',
    name: '维保服务',
    description: '设备维保、技术支持、保修服务等',
    icon: Wrench,
    color: 'bg-teal-500',
    href: '/assets/register/maintenance',
    category: '服务合同'
  },
  // 消耗品
  {
    id: 'consumables',
    name: '耗材管理',
    description: '打印耗材、网络线缆、配件等消耗品',
    icon: Package,
    color: 'bg-pink-500',
    href: '/assets/register/consumables',
    category: '消耗品'
  }
]

const categories = ['硬件设备', '软件资产', '服务合同', '消耗品']

export default function AssetRegisterNewPage() {
  const router = useRouter()
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const filteredAssetTypes = selectedCategory 
    ? assetTypes.filter(type => type.category === selectedCategory)
    : assetTypes

  const groupedAssetTypes = categories.reduce((acc, category) => {
    acc[category] = assetTypes.filter(type => type.category === category)
    return acc
  }, {} as Record<string, AssetType[]>)

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/assets">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回资产列表
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">资产登记</h1>
          <p className="text-muted-foreground mt-2">
            选择要登记的资产类型，系统支持多种类型的资产管理
          </p>
        </div>
        
        <div className="flex gap-2">
          <Link href="/assets/categories">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              管理分类
            </Button>
          </Link>
        </div>
      </div>

      {/* 分类筛选 */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedCategory === null ? "default" : "outline"}
          onClick={() => setSelectedCategory(null)}
          size="sm"
        >
          全部
        </Button>
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            onClick={() => setSelectedCategory(category)}
            size="sm"
          >
            {category}
            <Badge variant="secondary" className="ml-2">
              {groupedAssetTypes[category].length}
            </Badge>
          </Button>
        ))}
      </div>

      {/* 资产类型卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredAssetTypes.map((assetType) => {
          const IconComponent = assetType.icon
          return (
            <Card 
              key={assetType.id} 
              className="hover:shadow-lg transition-shadow cursor-pointer group"
              onClick={() => router.push(assetType.href)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${assetType.color} text-white group-hover:scale-110 transition-transform`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{assetType.name}</CardTitle>
                    <Badge variant="outline" className="mt-1">
                      {assetType.category}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  {assetType.description}
                </CardDescription>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* 快速操作提示 */}
      <div className="bg-blue-50 dark:bg-blue-950 p-6 rounded-lg">
        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          💡 快速操作提示
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• 点击对应的资产类型卡片开始登记</li>
          <li>• 每种资产类型都有专门的登记表单和字段</li>
          <li>• 支持批量导入功能，可下载对应的Excel模板</li>
          <li>• 资产编码支持自动生成，也可手动输入</li>
          <li>• 登记完成后可在资产列表中查看和管理</li>
        </ul>
      </div>
    </div>
  )
}