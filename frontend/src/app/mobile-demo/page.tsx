"use client"

import React, { useState } from 'react';
import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Text,
  VStack,
  HStack,
  Select,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  useToast,
  useColorModeValue,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Divider,
  SimpleGrid,
  IconButton
} from '@chakra-ui/react';
import { ArrowBackIcon, BellIcon, InfoIcon } from '@chakra-ui/icons';
import { useRouter } from 'next/navigation';
import { useSwipeable } from 'react-swipeable';

// 导入组件
import SwipeableItem from '../../components/mobile/SwipeableItem';
import NotificationCenter from '../../components/notification/NotificationCenter';

// 演示数据
const demoItems = [
  { id: 1, title: '滑动手势演示项目 1', description: '左右滑动查看操作选项' },
  { id: 2, title: '滑动手势演示项目 2', description: '左右滑动查看操作选项' },
  { id: 3, title: '滑动手势演示项目 3', description: '左右滑动查看操作选项' },
];

const MobileDemoPage = () => {
  const router = useRouter();
  const toast = useToast();
  const [notificationTitle, setNotificationTitle] = useState('测试通知');
  const [notificationMessage, setNotificationMessage] = useState('这是一条测试通知消息');
  const [notificationType, setNotificationType] = useState<'info' | 'success' | 'warning' | 'error'>('info');

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // 发送测试通知
  const handleSendNotification = async () => {
    // const success = await sendMockNotification(
    //   notificationTitle,
    //   notificationMessage,
    //   notificationType
    // );
    const success = true; // 临时模拟成功

    if (success) {
      toast({
        title: '通知已发送',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } else {
      toast({
        title: '通知发送失败',
        description: '请确保已授予通知权限',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // 滑动手势处理
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      toast({
        title: '向左滑动',
        status: 'info',
        duration: 2000,
      });
    },
    onSwipedRight: () => {
      toast({
        title: '向右滑动',
        status: 'info',
        duration: 2000,
      });
    },
    trackMouse: false
  });

  return (
    <Container maxW="container.md" p={4}>
      <Flex justify="space-between" align="center" mb={6}>
        <HStack>
          <IconButton
            aria-label="返回"
            icon={<ArrowBackIcon />}
            onClick={() => router.back()}
            variant="ghost"
          />
          <Heading size="md">移动端功能演示</Heading>
        </HStack>
        <HStack>
          <NotificationCenter />
        </HStack>
      </Flex>

      <VStack spacing={8} align="stretch">
        {/* 滑动手势演示 */}
        <Box>
          <Heading size="md" mb={4}>滑动手势演示</Heading>

          <VStack spacing={4} align="stretch">
            {demoItems.map((item) => (
              <SwipeableItem
                key={item.id}
                leftActions={[
                  {
                    icon: <InfoIcon />,
                    label: '详情',
                    color: 'blue.500',
                    onClick: () => toast({
                      title: `查看 ${item.title} 详情`,
                      status: 'info',
                      duration: 2000,
                    })
                  }
                ]}
                rightActions={[
                  {
                    icon: <BellIcon />,
                    label: '提醒',
                    color: 'green.500',
                    onClick: () => toast({
                      title: `已设置 ${item.title} 提醒`,
                      status: 'success',
                      duration: 2000,
                    })
                  },
                  {
                    icon: <InfoIcon />,
                    label: '删除',
                    color: 'red.500',
                    onClick: () => toast({
                      title: `删除 ${item.title}`,
                      status: 'error',
                      duration: 2000,
                    })
                  }
                ]}
              >
                <Box
                  p={4}
                  bg={bgColor}
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor={borderColor}
                >
                  <Heading size="sm" mb={2}>{item.title}</Heading>
                  <Text fontSize="sm">{item.description}</Text>
                </Box>
              </SwipeableItem>
            ))}
          </VStack>

          <Box
            mt={6}
            p={4}
            bg={bgColor}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
            {...swipeHandlers}
          >
            <Text textAlign="center">在此区域左右滑动测试简单手势</Text>
          </Box>
        </Box>

        <Divider />

        {/* 推送通知演示 */}
        <Box>
          <Heading size="md" mb={4}>推送通知演示</Heading>

          <Card bg={bgColor} borderWidth="1px" borderColor={borderColor}>
            <CardHeader>
              <Heading size="sm">发送测试通知</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <FormControl>
                  <FormLabel>通知标题</FormLabel>
                  <Input
                    value={notificationTitle}
                    onChange={(e) => setNotificationTitle(e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>通知内容</FormLabel>
                  <Textarea
                    value={notificationMessage}
                    onChange={(e) => setNotificationMessage(e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>通知类型</FormLabel>
                  <Select
                    value={notificationType}
                    onChange={(e) => setNotificationType(e.target.value as any)}
                  >
                    <option value="info">信息</option>
                    <option value="success">成功</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                  </Select>
                </FormControl>
              </VStack>
            </CardBody>
            <CardFooter>
              <Button colorScheme="teal" onClick={handleSendNotification}>
                发送通知
              </Button>
            </CardFooter>
          </Card>

          <Text fontSize="sm" mt={4} color="gray.500">
            注意：首次使用时需要授予通知权限。如果未显示权限请求，请检查浏览器设置。
          </Text>
        </Box>
      </VStack>
    </Container>
  );
};

export default MobileDemoPage;
