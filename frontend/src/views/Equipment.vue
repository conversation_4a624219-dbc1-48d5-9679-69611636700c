<template>
  <div class="equipment-container p-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold">设备台账</h2>
      <el-button type="primary" @click="showAddDialog">
        <i class="fas fa-plus mr-1"></i> 新增
      </el-button>
    </div>
    
    <!-- 搜索和筛选区域 -->
    <el-card shadow="hover" class="mb-6">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex-1 min-w-[200px]">
          <div class="relative">
            <el-input 
              placeholder="搜索设备名称、编号..." 
              v-model="searchQuery" 
              clearable 
              class="w-full"
              prefix-icon="fas fa-search"
            />
          </div>
        </div>
        
        <div class="flex flex-wrap items-center gap-3">
          <el-select v-model="filterStatus" placeholder="全部状态" clearable class="w-40">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          
          <el-select v-model="filterDepartment" placeholder="全部部门" clearable class="w-40">
            <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          
          <el-button type="primary" @click="searchEquipment">
            <i class="fas fa-search mr-1"></i> 搜索
          </el-button>
          
          <el-button @click="resetFilter">
            <i class="fas fa-redo mr-1"></i> 重置
          </el-button>
          
          <el-button type="default" class="border border-gray-300">
            <i class="fas fa-upload mr-1"></i> 导入
          </el-button>
          
          <el-button type="default" class="border border-gray-300">
            <i class="fas fa-download mr-1"></i> 导出
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 设备列表 -->
    <el-card shadow="hover" class="mb-6">
      <el-table 
        :data="equipmentList" 
        style="width: 100%" 
        border 
        v-loading="loading"
        stripe
        highlight-current-row
      >
        <el-table-column prop="code" label="设备编号" width="120" />
        <el-table-column prop="name" label="设备名称" width="180" />
        <el-table-column prop="type" label="设备类型" width="120" />
        <el-table-column prop="model" label="规格型号" width="120" />
        <el-table-column prop="department" label="所属部门" width="120" />
        <el-table-column prop="status" label="设备状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="owner" label="负责人" width="100" />
        <el-table-column prop="startDate" label="启用时间" width="120" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewDetail(scope.row)">
              <i class="fas fa-eye mr-1"></i> 查看
            </el-button>
            <el-button size="small" type="primary" @click="editEquipment(scope.row)">
              <i class="fas fa-edit mr-1"></i> 编辑
            </el-button>
            <el-button size="small" type="success" @click="showQRCode(scope.row)">
              <i class="fas fa-qrcode mr-1"></i> 二维码
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-end mt-4">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 设备详情对话框 -->
    <el-dialog 
      title="设备详情" 
      v-model="detailVisible" 
      width="70%"
      destroy-on-close
    >
      <EquipmentDetail :equipment="currentEquipment" />
    </el-dialog>
    
    <!-- 设备表单对话框 -->
    <el-dialog 
      :title="isEdit ? '编辑设备' : '新增设备'" 
      v-model="formVisible" 
      width="70%"
      destroy-on-close
    >
      <EquipmentForm 
        :equipment-data="currentEquipment" 
        :is-edit="isEdit"
        @save="saveEquipment"
      />
    </el-dialog>
    
    <!-- 二维码对话框 -->
    <el-dialog 
      title="设备二维码" 
      v-model="qrcodeVisible" 
      width="400px"
      destroy-on-close
      center
    >
      <EquipmentQRCode :equipment="currentEquipment" :equipment-list="equipmentList" />
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import EquipmentDetail from '@/components/EquipmentDetail.vue'
import EquipmentForm from '@/components/EquipmentForm.vue'
import EquipmentQRCode from '@/components/EquipmentQRCode.vue'

export default {
  name: 'Equipment',
  components: { 
    EquipmentDetail,
    EquipmentForm,
    EquipmentQRCode
  },
  setup() {
    // 状态变量
    const loading = ref(false);
    const searchQuery = ref('');
    const filterType = ref('');
    const filterStatus = ref('');
    const filterDepartment = ref('');
    const equipmentList = ref([]);
    const total = ref(0);
    const pageSize = ref(10);
    const currentPage = ref(1);
    
    // 对话框控制
    const detailVisible = ref(false);
    const formVisible = ref(false);
    const qrcodeVisible = ref(false);
    const isEdit = ref(false);
    const currentEquipment = ref({});
    
    // 选项数据
    const typeOptions = [
      { value: '笔记本电脑', label: '笔记本电脑' },
      { value: '台式电脑', label: '台式电脑' },
      { value: '服务器', label: '服务器' },
      { value: '打印机', label: '打印机' },
      { value: '显示器', label: '显示器' },
      { value: '网络设备', label: '网络设备' },
      { value: '移动设备', label: '移动设备' }
    ];
    
    const departmentOptions = [
      { value: 'IT部', label: 'IT部' },
      { value: '设备部', label: '设备部' },
      { value: '运营部', label: '运营部' },
      { value: '研发部', label: '研发部' }
    ];
    
    const statusOptions = [
      { value: '正常', label: '正常' },
      { value: '故障', label: '故障' },
      { value: '维修中', label: '维修中' },
      { value: '待检', label: '待检' }
    ];
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const map = {
        '正常': 'success',
        '故障': 'danger',
        '待检': 'warning',
        '维修中': 'primary'
      };
      return map[status] || '';
    };
    
    // 查看设备详情
    const viewDetail = (equipment) => {
      currentEquipment.value = equipment;
      detailVisible.value = true;
    };
    
    // 编辑设备
    const editEquipment = (equipment) => {
      currentEquipment.value = { ...equipment };
      isEdit.value = true;
      formVisible.value = true;
    };
    
    // 显示新增设备对话框
    const showAddDialog = () => {
      currentEquipment.value = {};
      isEdit.value = false;
      formVisible.value = true;
    };
    
    // 显示二维码
    const showQRCode = (equipment) => {
      currentEquipment.value = equipment;
      qrcodeVisible.value = true;
    };
    
    // 保存设备信息
    const saveEquipment = (formData) => {
      console.log('保存设备信息:', formData);
      // TODO: 调用API保存设备信息
      
      // 模拟保存成功
      formVisible.value = false;
      fetchEquipmentList();
    };
    
    // 搜索设备
    const searchEquipment = () => {
      currentPage.value = 1;
      fetchEquipmentList();
    };
    
    // 重置筛选条件
    const resetFilter = () => {
      searchQuery.value = '';
      filterType.value = '';
      filterStatus.value = '';
      filterDepartment.value = '';
      currentPage.value = 1;
      fetchEquipmentList();
    };
    
    // 处理页码变化
    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchEquipmentList();
    };
    
    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      fetchEquipmentList();
    };
    
    // 获取设备列表
    const fetchEquipmentList = () => {
      loading.value = true;
      
      // 模拟API调用
      setTimeout(() => {
        // 模拟数据
        const mockData = [
          { id: 1, code: '*********', name: '1号水泵', type: '水泵', model: 'WP-100', department: '生产部', owner: '张三', status: '正常', startDate: '2023-01-15' },
          { id: 2, code: '*********', name: '2号空压机', type: '空压机', model: 'AC-200', department: '设备部', owner: '李四', status: '故障', startDate: '2023-02-20' },
          { id: 3, code: '*********', name: '3号电机', type: '电机', model: 'EM-150', department: '生产部', owner: '王五', status: '维修中', startDate: '2023-03-10' },
          { id: 4, code: '*********', name: '主控制阀', type: '控制阀', model: 'CV-300', department: '设备部', owner: '赵六', status: '正常', startDate: '2023-04-05' },
          { id: 5, code: 'EQ2023005', name: '温度传感器', type: '传感器', model: 'TS-50', department: '质检部', owner: '钱七', status: '待检', startDate: '2023-05-12' },
          { id: 6, code: 'EQ2023006', name: '备用水泵', type: '水泵', model: 'WP-100B', department: '生产部', owner: '张三', status: '正常', startDate: '2023-06-18' },
          { id: 7, code: 'EQ2023007', name: '冷却塔风机', type: '电机', model: 'FM-200', department: '设备部', owner: '李四', status: '正常', startDate: '2023-07-22' },
          { id: 8, code: 'EQ2023008', name: '压力传感器', type: '传感器', model: 'PS-100', department: '质检部', owner: '王五', status: '正常', startDate: '2023-08-30' },
          { id: 9, code: 'EQ2023009', name: '备用空压机', type: '空压机', model: 'AC-200B', department: '设备部', owner: '赵六', status: '待检', startDate: '2023-09-14' },
          { id: 10, code: 'EQ2023010', name: '流量控制阀', type: '控制阀', model: 'FCV-150', department: '生产部', owner: '钱七', status: '正常', startDate: '2023-10-25' }
        ];
        
        // 模拟筛选
        let filteredData = [...mockData];
        
        if (searchQuery.value) {
          const query = searchQuery.value.toLowerCase();
          filteredData = filteredData.filter(item => 
            item.name.toLowerCase().includes(query) || 
            item.code.toLowerCase().includes(query)
          );
        }
        
        if (filterType.value) {
          filteredData = filteredData.filter(item => item.type === filterType.value);
        }
        
        if (filterStatus.value) {
          filteredData = filteredData.filter(item => item.status === filterStatus.value);
        }
        
        if (filterDepartment.value) {
          filteredData = filteredData.filter(item => item.department === filterDepartment.value);
        }
        
        total.value = filteredData.length;
        
        // 模拟分页
        const start = (currentPage.value - 1) * pageSize.value;
        const end = start + pageSize.value;
        equipmentList.value = filteredData.slice(start, end);
        
        loading.value = false;
      }, 500);
    };
    
    onMounted(() => {
      fetchEquipmentList();
    });
    
    return {
      loading,
      searchQuery,
      filterType,
      filterStatus,
      filterDepartment,
      equipmentList,
      total,
      pageSize,
      currentPage,
      detailVisible,
      formVisible,
      qrcodeVisible,
      isEdit,
      currentEquipment,
      typeOptions,
      departmentOptions,
      statusOptions,
      getStatusTagType,
      viewDetail,
      editEquipment,
      showAddDialog,
      showQRCode,
      saveEquipment,
      searchEquipment,
      resetFilter,
      handlePageChange,
      handleSizeChange
    };
  }
};
</script>

<style scoped>
.equipment-container {
  min-height: calc(100vh - 60px);
}
</style>