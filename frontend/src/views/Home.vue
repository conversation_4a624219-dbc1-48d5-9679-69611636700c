<template>
  <div class="home p-6 animate__animated animate__fadeIn">
    <h2 class="text-xl font-semibold mb-6">设备资产概览</h2>
    
    <!-- 数据卡片区域 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <el-card shadow="hover" class="bg-primary-light border-l-4 border-primary animate__animated animate__fadeInUp animate__delay-1s">
        <div class="flex items-center">
          <div class="mr-4 text-primary text-3xl">
            <i class="fas fa-server"></i>
          </div>
          <div>
            <div class="text-gray-500 text-sm">设备总数</div>
            <div class="text-2xl font-bold">{{ statistics.totalEquipment }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" class="bg-success-light border-l-4 border-success animate__animated animate__fadeInUp animate__delay-2s">
        <div class="flex items-center">
          <div class="mr-4 text-success text-3xl">
            <i class="fas fa-check-circle"></i>
          </div>
          <div>
            <div class="text-gray-500 text-sm">正常运行</div>
            <div class="text-2xl font-bold">{{ statistics.normalEquipment }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" class="bg-danger-light border-l-4 border-danger animate__animated animate__fadeInUp animate__delay-3s">
        <div class="flex items-center">
          <div class="mr-4 text-danger text-3xl">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div>
            <div class="text-gray-500 text-sm">故障设备</div>
            <div class="text-2xl font-bold">{{ statistics.faultyEquipment }}</div>
          </div>
        </div>
      </el-card>
      
      <el-card shadow="hover" class="bg-warning-light border-l-4 border-warning animate__animated animate__fadeInUp animate__delay-4s">
        <div class="flex items-center">
          <div class="mr-4 text-warning text-3xl">
            <i class="fas fa-clipboard-list"></i>
          </div>
          <div>
            <div class="text-gray-500 text-sm">待处理工单</div>
            <div class="text-2xl font-bold">{{ statistics.pendingOrders }}</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 图表和列表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 设备状态分布 -->
      <el-card shadow="hover" class="lg:col-span-1 animate__animated animate__fadeInLeft">
        <template #header>
          <div class="flex justify-between items-center">
            <span>设备状态分布</span>
            <el-button type="text">查看详情</el-button>
          </div>
        </template>
        <div ref="statusChartRef" style="height: 300px"></div>
      </el-card>
      
      <!-- 最近工单 -->
      <el-card shadow="hover" class="lg:col-span-2 animate__animated animate__fadeInRight">
        <template #header>
          <div class="flex justify-between items-center">
            <span>最近工单</span>
            <el-button type="text" @click="$router.push('/work-order')">查看全部</el-button>
          </div>
        </template>
        <draggable 
          v-model="recentOrders" 
          item-key="id"
          ghost-class="ghost"
          chosen-class="chosen"
          animation="300"
          handle=".drag-handle"
        >
          <template #item="{element}">
            <div class="border-b border-gray-100 p-3 hover:bg-gray-50 transition-colors duration-200">
              <div class="flex items-center">
                <div class="drag-handle cursor-move mr-2 text-gray-400 hover:text-gray-600">
                  <i class="fas fa-grip-vertical"></i>
                </div>
                <div class="flex-1">
                  <div class="flex justify-between">
                    <span class="font-medium">{{ element.title }}</span>
                    <el-tag :type="getStatusType(element.status)" size="small">
                      {{ element.status }}
                    </el-tag>
                  </div>
                  <div class="text-sm text-gray-500 mt-1 flex justify-between">
                    <span>{{ element.equipment }}</span>
                    <span>{{ element.createTime }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </el-card>
    </div>
    
    <!-- 设备3D模型和二维码区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <!-- 设备3D模型 -->
      <el-card shadow="hover" class="animate__animated animate__fadeInUp">
        <template #header>
          <div class="flex justify-between items-center">
            <span>设备3D模型预览</span>
            <el-button type="text">查看详情</el-button>
          </div>
        </template>
        <div ref="equipmentModelRef" style="height: 300px"></div>
      </el-card>
      
      <!-- 设备扫码 -->
      <el-card shadow="hover" class="animate__animated animate__fadeInUp animate__delay-1s">
        <template #header>
          <div class="flex justify-between items-center">
            <span>设备扫码</span>
            <el-button type="text">下载二维码</el-button>
          </div>
        </template>
        <div class="flex flex-col items-center justify-center p-4" style="height: 300px">
          <div ref="qrcodeRef" class="mb-4"></div>
          <p class="text-gray-500 text-center">扫描二维码快速查看设备详情</p>
          <p class="text-gray-400 text-sm text-center mt-2">支持移动端H5访问</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, reactive, defineComponent } from 'vue'
import * as echarts from 'echarts'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import 'animate.css'
import VueDraggable from 'vuedraggable'
import QRCode from 'qrcode'

export default defineComponent({
  name: 'Home',
  components: {
    draggable: VueDraggable
  },
  setup() {
    const statusChartRef = ref<HTMLElement | null>(null)
    const equipmentModelRef = ref<HTMLElement | null>(null)
    const qrcodeRef = ref<HTMLElement | null>(null)
    let statusChart: echarts.ECharts | null = null
    let threeScene: THREE.Scene | null = null
    let threeRenderer: THREE.WebGLRenderer | null = null
    let threeCamera: THREE.PerspectiveCamera | null = null
    
    // 模拟数据
    const statistics = reactive<{
      totalEquipment: number;
      normalEquipment: number;
      faultyEquipment: number;
      pendingOrders: number;
    }>({
      totalEquipment: 128,
      normalEquipment: 98,
      faultyEquipment: 12,
      pendingOrders: 18
    })
    
    // 工单类型定义
    interface WorkOrder {
      id: string;
      title: string;
      equipment: string;
      status: string;
      createTime: string;
    }
    
    // 可拖拽排序的工单列表
    const recentOrders = reactive<WorkOrder[]>([
      { id: 'WO2023002', title: '空压机异常噪音', equipment: '2号空压机', status: '处理中', createTime: '2023-10-14 14:22:18' }
    ])
    
    // 初始化图表
    const initStatusChart = () => {
      if (statusChartRef.value) {
        statusChart = echarts.init(statusChartRef.value)
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: ['正常', '故障', '维修中', '待检']
          },
          series: [
            {
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                },
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 98, name: '正常', itemStyle: { color: '#41B883' } },
                { value: 12, name: '故障', itemStyle: { color: '#E34D59' } },
                { value: 8, name: '维修中', itemStyle: { color: '#4169E1' } },
                { value: 10, name: '待检', itemStyle: { color: '#F7BA1E' } }
              ],
            }
          ]
        }
        statusChart.setOption(option)
      }
    }
    
    // 初始化3D模型
    const initEquipmentModel = () => {
      if (equipmentModelRef.value) {
        // 创建场景
        threeScene = new THREE.Scene()
        threeScene.background = new THREE.Color(0xf0f0f0)
        
        // 创建相机
        threeCamera = new THREE.PerspectiveCamera(75, equipmentModelRef.value.clientWidth / equipmentModelRef.value.clientHeight, 0.1, 1000)
        threeCamera.position.z = 5
        
        // 创建渲染器
        threeRenderer = new THREE.WebGLRenderer({ antialias: true })
        threeRenderer.setSize(equipmentModelRef.value.clientWidth, equipmentModelRef.value.clientHeight)
        equipmentModelRef.value.appendChild(threeRenderer.domElement)
        
        // 添加控制器
        const controls = new OrbitControls(threeCamera, threeRenderer.domElement)
        controls.enableDamping = true
        
        // 添加灯光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
        threeScene.add(ambientLight)
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
        directionalLight.position.set(1, 1, 1)
        threeScene.add(directionalLight)
        
        // 创建一个简单的设备模型（示例用立方体）
        const geometry = new THREE.BoxGeometry(2, 2, 2)
        const material = new THREE.MeshStandardMaterial({ 
          color: 0x4169E1,
          metalness: 0.7,
          roughness: 0.2,
        })
        const cube = new THREE.Mesh(geometry, material)
        threeScene.add(cube)
        
        // 动画循环
        const animate = () => {
          requestAnimationFrame(animate)
          cube.rotation.x += 0.01
          cube.rotation.y += 0.01
          controls.update()
          threeRenderer?.render(threeScene as THREE.Scene, threeCamera as THREE.PerspectiveCamera)
        }
        
        animate()
        
        // 响应窗口大小变化
        window.addEventListener('resize', () => {
          if (equipmentModelRef.value && threeCamera && threeRenderer) {
            threeCamera.aspect = equipmentModelRef.value.clientWidth / equipmentModelRef.value.clientHeight
            threeCamera.updateProjectionMatrix()
            threeRenderer.setSize(equipmentModelRef.value.clientWidth, equipmentModelRef.value.clientHeight)
          }
        })
      }
    }
    
    // 获取状态标签类型
    const getStatusType = (status: string): string => {
      const statusMap: Record<string, string> = {
        '待处理': 'warning',
        '处理中': 'primary',
        '已完成': 'success'
      }
      return statusMap[status] || 'info'
    }
    
    // 生成设备二维码
    const generateQRCode = async () => {
      if (qrcodeRef.value) {
        try {
          const qrCodeUrl = await QRCode.toDataURL('https://example.com/equipment/scan', {
            width: 200,
            margin: 2,
            color: {
              dark: '#4169E1',
              light: '#ffffff'
            }
          })
          
          const img = document.createElement('img')
          img.src = qrCodeUrl
          img.classList.add('animate__animated', 'animate__fadeIn')
          qrcodeRef.value.innerHTML = ''
          qrcodeRef.value.appendChild(img)
        } catch (err) {
          console.error('QR Code generation error:', err)
        }
      }
    }
    
    onMounted(() => {
      initStatusChart()
      initEquipmentModel()
      generateQRCode()
      
      // 监听窗口大小变化，重绘图表
      window.addEventListener('resize', () => {
        statusChart && statusChart.resize()
      })
    })
    
    return {
      statusChartRef,
      equipmentModelRef,
      qrcodeRef,
      statistics,
      recentOrders,
      getStatusType
    }
  }
}
</script>

<style scoped>
.home {
  min-height: calc(100vh - 60px);
}

.el-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.el-card:hover {
  transform: translateY(-2px);
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.chosen {
  background: #eaf6fe;
}

.el-card__header {
  height: 48px;
  border-bottom: 1px solid #EBEEF5;
  padding: 0 16px;
  display: flex;
  align-items: center;
}

.el-card__body {
  padding: 16px;
}

.el-table th {
  background-color: #F2F6FC;
}

.el-table td, .el-table th.is-leaf {
  border-bottom: 1px solid #EBEEF5;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #F5F7FA;
}
</style>