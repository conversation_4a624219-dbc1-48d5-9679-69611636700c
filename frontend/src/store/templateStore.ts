import { create } from 'zustand';
import axios from 'axios';

export interface InspectionItem {
  item: string;
  check_command: string;
  expected_result: string;
  auto_fix: boolean;
  fix_command?: string;
  remark: string;
}

export interface InspectionTemplate {
  id?: number;
  name: string;
  description?: string;
  device_types: string[];
  content: InspectionItem[];
  created_at?: string;
  updated_at?: string;
}

interface InspectionResult {
  results: Array<{
    item: string;
    check_command: string;
    check_result?: string;
    check_error?: string;
    passed: boolean;
    auto_fix: boolean;
    fix_command?: string;
    fix_result?: string;
    fix_error?: string;
    fixed?: boolean;
    remark?: string;
  }>;
}

interface TemplateState {
  templates: InspectionTemplate[];
  loading: boolean;
  currentTemplate?: InspectionTemplate;
  inspectionResult?: InspectionResult;
  aiAnalysis: string;

  // 状态
  inspectStep: 'idle' | 'inspecting' | 'fixing' | 'analyzing' | 'done' | 'error';
  inspectStepMsg: string;

  // 操作
  fetchTemplates: () => Promise<void>;
  createTemplate: (template: InspectionTemplate) => Promise<boolean>;
  updateTemplate: (template: InspectionTemplate) => Promise<boolean>;
  deleteTemplate: (id: number) => Promise<boolean>;
  executeInspection: (templateId: number, device: { ip: string; username: string; password: string }) => Promise<InspectionResult | null>;
  oneClickInspect: (templateId: number) => Promise<void>;
  setCurrentTemplate: (template?: InspectionTemplate) => void;
}

// 创建一个简单的通知函数，替代toast
const notify = (message: string) => {
  console.log(message);
};

export const useTemplateStore = create<TemplateState>((set, get) => ({
  templates: [],
  loading: false,
  aiAnalysis: '',
  inspectStep: 'idle',
  inspectStepMsg: '',

  fetchTemplates: async () => {
    set({ loading: true });
    try {
      const res = await axios.get("/api/inspection-template");
      set({
        templates: res.data.map((tpl: any) => ({
          ...tpl,
          device_types: Array.isArray(tpl.device_types)
            ? tpl.device_types
            : tpl.device_type
              ? [tpl.device_type]
              : [],
        })),
        loading: false
      });
    } catch (e) {
      console.error("获取模板失败", e);
      set({ loading: false });
    }
  },

  createTemplate: async (template: InspectionTemplate) => {
    try {
      const payload = {
        ...template,
        device_type: template.device_types[0],
        check_items: template.content,
        description: template.description || '',
        schedule: '',
        is_active: true,
      };
      delete (payload as any).device_types;
      delete (payload as any).content;

      await axios.post("/api/inspection-template", payload);
      console.log("创建成功");
      get().fetchTemplates();
      return true;
    } catch (e: any) {
      console.error("创建失败", e?.response?.data?.error || e.message);
      return false;
    }
  },

  updateTemplate: async (template: InspectionTemplate) => {
    try {
      const payload = {
        ...template,
        device_type: template.device_types[0],
        check_items: template.content,
        description: template.description || '',
        schedule: '',
        is_active: true,
      };
      delete (payload as any).device_types;
      delete (payload as any).content;

      await axios.put("/api/inspection-template", payload);
      console.log("修改成功");
      get().fetchTemplates();
      return true;
    } catch (e: any) {
      console.error("修改失败", e?.response?.data?.error || e.message);
      return false;
    }
  },

  deleteTemplate: async (id: number) => {
    try {
      await axios.delete("/api/inspection-template", { data: { id } });
      console.log("删除成功");
      get().fetchTemplates();
      return true;
    } catch (e) {
      console.error("删除失败", e);
      return false;
    }
  },

  executeInspection: async (templateId: number, device: { ip: string; username: string; password: string }) => {
    try {
      const res = await axios.post("/api/inspection-execute", {
        template_id: templateId,
        device,
      });
      return res.data;
    } catch (e: any) {
      console.error("执行失败", e?.response?.data?.error || e.message);
      return null;
    }
  },

  oneClickInspect: async (templateId: number) => {
    set({
      inspectStep: 'inspecting',
      inspectStepMsg: '正在执行巡检...'
    });

    try {
      // 1. 执行巡检
      const res = await axios.post("/api/inspection-execute", { template_id: templateId });
      const result = res.data;

      set({
        inspectStep: 'fixing',
        inspectStepMsg: '正在自动修复异常项...'
      });

      // 2. 自动修复异常项
      const fixRes = await axios.post("/api/inspection-auto-fix", {
        template_id: templateId,
        results: result.results
      });

      set({
        inspectStep: 'analyzing',
        inspectStepMsg: '正在AI智能分析...'
      });

      // 3. AI智能分析
      let aiAnalysis = '';
      try {
        const aiRes = await fetch('/api/ollama-analyze', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ results: fixRes.data.results })
        });
        aiAnalysis = (await aiRes.json()).analysis || '';
      } catch (e) {
        aiAnalysis = 'AI分析失败';
      }

      set({
        inspectStep: 'done',
        inspectStepMsg: '巡检完成，生成报告',
        inspectionResult: fixRes.data,
        aiAnalysis
      });

      setTimeout(() => set({ inspectStep: 'idle' }), 2000);
    } catch (e) {
      set({
        inspectStep: 'error',
        inspectStepMsg: '一键巡检/修复失败'
      });
      console.error("一键巡检/修复失败", e);

      setTimeout(() => set({ inspectStep: 'idle' }), 2000);
    }
  },

  setCurrentTemplate: (template?: InspectionTemplate) => {
    set({ currentTemplate: template });
  },

  setInspectStep: (step: 'idle' | 'inspecting' | 'fixing' | 'analyzing' | 'done' | 'error') => {
    set({ inspectStep: step });
  }
}));
