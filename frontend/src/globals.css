@tailwind base;
@tailwind components;
@tailwind utilities;

/* Your custom styles here */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* 丰富多彩的主色调 - 蓝色 */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;

    /* 丰富多彩的次要色调 - 紫色 */
    --secondary: 270 100% 60%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* 丰富多彩的强调色 - 青色 */
    --accent: 180 100% 45%;
    --accent-foreground: 0 0% 100%;

    /* 丰富多彩的警告色 - 红色 */
    --destructive: 0 100% 60%;
    --destructive-foreground: 0 0% 100%;

    /* 丰富多彩的成功色 */
    --success: 142 70% 45%;
    --success-foreground: 0 0% 100%;

    /* 丰富多彩的警告色 */
    --warning: 38 100% 50%;
    --warning-foreground: 0 0% 100%;

    /* 丰富多彩的信息色 */
    --info: 200 100% 50%;
    --info-foreground: 0 0% 100%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 50%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 47.4% 11.2%;
    --foreground: 210 40% 98%;

    --card: 222.2 47.4% 11.2%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;

    /* 丰富多彩的主色调 - 蓝色 */
    --primary: 210 100% 60%;
    --primary-foreground: 0 0% 100%;

    /* 丰富多彩的次要色调 - 紫色 */
    --secondary: 270 100% 70%;
    --secondary-foreground: 0 0% 100%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    /* 丰富多彩的强调色 - 青色 */
    --accent: 180 100% 50%;
    --accent-foreground: 0 0% 100%;

    /* 丰富多彩的警告色 - 红色 */
    --destructive: 0 100% 65%;
    --destructive-foreground: 0 0% 100%;

    /* 丰富多彩的成功色 */
    --success: 142 70% 50%;
    --success-foreground: 0 0% 100%;

    /* 丰富多彩的警告色 */
    --warning: 38 100% 55%;
    --warning-foreground: 0 0% 100%;

    /* 丰富多彩的信息色 */
    --info: 200 100% 55%;
    --info-foreground: 0 0% 100%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 210 100% 60%;
  }
}

/* 应用基础样式 */
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}