export interface Device {
  id: string;
  name: string;
  ip: string;
  type: string;
  rack_id?: string;
  rack_position?: number;
  status: 'online' | 'offline' | 'warning' | 'critical';
  last_seen: string;
  created_at: string;
  updated_at: string;
}

export interface DeviceMetric {
  id: number;
  device_id: string;
  metric_name: string;
  metric_value: number;
  unit: string;
  timestamp: string;
}

export interface MetricData {
  metric_name: string;
  data_points: Array<{ timestamp: string; value: number }>;
  unit: string;
}

export interface Alert {
  id: number;
  device_id: string;
  device_name: string;
  alert_type: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved';
  created_at: string;
  updated_at: string;
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolved_at?: string;
  metric_name?: string;
  metric_value?: number;
  threshold_value?: number;
}

export interface Threshold {
  id: number;
  metric_name: string;
  device_type?: string;
  device_id?: string;
  warning_threshold?: number;
  critical_threshold?: number;
  comparison: 'gt' | 'lt' | 'gte' | 'lte' | 'eq';
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface MetricThreshold {
  metric_name: string;
  warning: number | null;
  critical: number | null;
  comparison: 'gt' | 'lt' | 'gte' | 'lte' | 'eq';
}

export interface DeviceFilter {
  status?: string[];
  type?: string[];
  rack_id?: string[];
}

export interface AlertFilter {
  severity?: string[];
  status?: string[];
  device_id?: string[];
} 