export interface NotificationChannel {
  id?: number
  name: string
  type: 'email' | 'webhook' | 'slack' | 'sms'
  config: EmailConfig | WebhookConfig | SlackConfig | SMSConfig
  isActive: boolean
  createdAt?: Date
  updatedAt?: Date
}

export interface EmailConfig {
  recipients: string[]
  subjectTemplate?: string
}

export interface WebhookConfig {
  url: string
  headers?: Record<string, string>
}

export interface SlackConfig {
  webhookUrl: string
  channel: string
}

export interface SMSConfig {
  phoneNumbers: string[]
  provider: string
}