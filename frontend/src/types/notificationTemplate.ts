export interface NotificationTemplate {
  id?: number
  name: string
  type: 'email' | 'webhook' | 'slack' | 'sms'
  subject?: string  // 邮件主题/Slack标题等
  content: string   // 支持变量插值的模板内容
  variables: string[] // 可用的变量列表
  isDefault: boolean
  createdAt?: Date
  updatedAt?: Date
}

// 模板变量定义
export const ALERT_TEMPLATE_VARIABLES = {
  'alert.name': '报警规则名称',
  'alert.metric': '监控指标',
  'alert.value': '当前值',
  'alert.threshold': '阈值',
  'alert.severity': '严重程度',
  'alert.time': '触发时间',
  'device.name': '设备名称',
  'device.ip': '设备IP',
  'device.type': '设备类型'
}