export interface AlertRule {
  id?: number
  name: string
  deviceId: number
  deviceType: 'snmp' | 'ssh'
  metric: string  // OID或命令
  condition: '>' | '<' | '=' | '!=' | 'contains' | 'not_contains'
  threshold: string
  severity: 'info' | 'warning' | 'critical'
  isActive: boolean
  createdAt?: Date
  updatedAt?: Date
}

export interface AlertLog {
  id?: number
  ruleId: number
  ruleName?: string
  deviceId: number
  deviceName?: string
  deviceIp?: string
  deviceType?: string
  metric: string
  value: string
  threshold?: string
  severity: 'info' | 'warning' | 'critical'
  triggeredAt: Date
  resolvedAt?: Date
  status: 'open' | 'resolved'
}