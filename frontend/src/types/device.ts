// 端口状态类型
export type PortStatus = 'connected' | 'disconnected' | 'error' | 'disabled' | 'warning' | 'up' | 'down';

// 端口类型
export type PortType = 'ethernet' | 'fiber' | 'console' | 'management' | 'sfp' | 'sfpplus' | 'qsfp';

// 端口接口
export interface Port {
  id: string;
  name: string;
  type: string;
  status: PortStatus;
  speed?: string;
  duplex?: 'full' | 'half';
  connectedTo?: string;
  vlan?: number;
  macAddress?: string;
  ipAddress?: string;
}

// 设备接口
export interface Device {
  id: string;
  name: string;
  model?: string;
  manufacturer?: string;
  serialNumber?: string;
  installDate?: string;
  category?: 'server' | 'network' | 'storage' | 'power' | 'other';
  status: 'normal' | 'warning' | 'error' | 'online' | 'offline' | 'maintenance' | 'active' | 'inactive';
  height?: number;
  position?: number;
  ipAddress?: string;
  managementAddress?: string;
  location?: string;
  lastSeen?: string;
  firmware?: string;
  description?: string;
  assetCode?: string;
  fixedAssetId?: string;
  purchaseDate?: string;
  owner?: string;
  ownerContact?: string;
  businessName?: string;
} 