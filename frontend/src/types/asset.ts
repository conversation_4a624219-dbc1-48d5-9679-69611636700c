export interface Asset {
  id?: number
  name: string
  type: 'server' | 'network' | 'printer' | 'other'
  ipAddress: string
  macAddress?: string
  location?: string
  description?: string
  createdAt?: Date
  updatedAt?: Date
  monitoringConfig?: MonitoringConfig
}

// 扩展的IT资产接口
export interface ITAsset {
  // 基础信息
  id?: number
  name: string
  code: string
  category: string
  manufacturer: string
  model: string
  serialNumber: string
  status: 'active' | 'maintenance' | 'standby' | 'offline' | 'retired'
  location: string
  department: string
  responsiblePerson: string
  description?: string
  
  // 采购信息
  purchaseDate?: Date
  supplier?: string
  purchaseOrderNumber?: string
  price?: number
  warrantyExpireDate?: Date
  
  // 网络配置
  ipAddress?: string
  macAddress?: string
  hostname?: string
  domain?: string
  subnetMask?: string
  gateway?: string
  dnsServers?: string
  vlanId?: number
  
  // 硬件规格
  cpuModel?: string
  cpuCores?: number
  memorySize?: number
  storageSize?: number
  storageType?: 'SSD' | 'HDD' | 'NVMe' | 'Hybrid'
  portCount?: number
  portSpeed?: string
  powerConsumption?: number
  operatingTemperature?: string
  
  // 软件信息
  operatingSystem?: string
  osVersion?: string
  firmwareVersion?: string
  
  // 安全配置
  securityLevel?: 'low' | 'medium' | 'high' | 'critical'
  encryptionEnabled: boolean
  
  // 监控配置
  monitoringEnabled: boolean
  monitoringType?: 'snmp' | 'ssh' | 'ping'
  snmpCommunity?: string
  snmpPort?: number
  sshUsername?: string
  sshPort?: number
  
  // 时间戳
  createdAt?: Date
  updatedAt?: Date
}

export interface MonitoringConfig {
  enabled: boolean
  type: 'snmp' | 'ssh' | 'none'
  config: SNMPConfig | SSHConfig | null
}

export interface SNMPConfig {
  community: string
  oids: string[]
  interval: number
}

export interface SSHConfig {
  username: string
  password: string
  commands: string[]
  interval: number
  port?: number
}

// 资产类别定义
export interface AssetCategory {
  id: string
  name: string
  code: string
  description?: string
  level: number
  parentId?: string
  attributes?: Record<string, any>
}

// 资产状态历史
export interface AssetStatusHistory {
  id: string
  assetId: string
  status: string
  previousStatus: string
  changeDate: Date
  changeReason?: string
  changedBy: string
}

// 资产维护记录
export interface AssetMaintenanceRecord {
  id: string
  assetId: string
  maintenanceType: 'preventive' | 'corrective' | 'emergency'
  description: string
  startDate: Date
  endDate?: Date
  cost?: number
  technician: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
}