declare module 'chart.js' {
  import { ChartComponentLike } from 'chart.js';
  
  export const Chart: any;
  export const CategoryScale: any;
  export const LinearScale: any;
  export const PointElement: any;
  export const LineElement: any;
  export const Title: any;
  export const Tooltip: any;
  export const Legend: any;
  export const Filler: any;
  
  export function register(...components: any[]): void;
}

declare module 'react-chartjs-2' {
  import { FC } from 'react';
  
  export interface LineProps {
    data: any;
    options?: any;
    [key: string]: any;
  }
  
  export const Line: FC<LineProps>;
} 