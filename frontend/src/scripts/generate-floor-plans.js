// This script generates simple floor plan placeholder images
// You would run this with Node.js to generate the images
// Or you can manually create the images using any graphics tool

const fs = require('fs');
const path = require('path');

// Ensure the images directory exists
const imagesDir = path.join(__dirname, '../../public/images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Generate placeholder SVG for each floor
for (let floor = 1; floor <= 8; floor++) {
  const floorName = `${floor}F`;
  const color = getRandomColor();
  
  const svgContent = `<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
    <style>
      .room { fill: #f5f5f5; stroke: #333; stroke-width: 2; }
      .corridor { fill: #e0e0e0; stroke: #333; stroke-width: 2; }
      .stairs { fill: #d0d0d0; stroke: #333; stroke-width: 2; }
      .text { font-family: Arial; font-size: 24px; fill: #333; }
      .floor-label { font-family: Arial; font-size: 48px; font-weight: bold; fill: ${color}; }
    </style>
    
    <!-- Background -->
    <rect width="1000" height="700" fill="#f9f9f9" />
    
    <!-- Floor outline -->
    <rect x="50" y="50" width="900" height="600" class="room" />
    
    <!-- Corridors -->
    <rect x="100" y="250" width="800" height="100" class="corridor" />
    <rect x="450" y="100" width="100" height="500" class="corridor" />
    
    <!-- Rooms -->
    <rect x="100" y="100" width="300" height="120" class="room" />
    <rect x="600" y="100" width="300" height="120" class="room" />
    <rect x="100" y="380" width="300" height="220" class="room" />
    <rect x="600" y="380" width="300" height="220" class="room" />
    
    <!-- Stairs -->
    <rect x="250" y="500" width="70" height="100" class="stairs" />
    <path d="M250,500 L320,500 L320,600 L250,600 Z M250,520 L320,520 M250,540 L320,540 M250,560 L320,560 M250,580 L320,580" stroke="#333" fill="none" />
    
    <!-- Floor Label -->
    <text x="500" y="350" text-anchor="middle" class="floor-label">${floorName} Floor Plan</text>
    
    <!-- Room Labels -->
    <text x="250" y="170" text-anchor="middle" class="text">Conference Room</text>
    <text x="750" y="170" text-anchor="middle" class="text">Office Area</text>
    <text x="250" y="490" text-anchor="middle" class="text">Server Room</text>
    <text x="750" y="490" text-anchor="middle" class="text">Work Area</text>
  </svg>`;
  
  const outputPath = path.join(imagesDir, `floor-${floorName}.svg`);
  fs.writeFileSync(outputPath, svgContent);
  
  console.log(`Generated floor plan for ${floorName}: ${outputPath}`);
}

function getRandomColor() {
  const colors = [
    '#3498db', // Blue
    '#e74c3c', // Red
    '#2ecc71', // Green
    '#f39c12', // Orange
    '#9b59b6', // Purple
    '#1abc9c', // Teal
    '#34495e', // Dark Blue
    '#d35400'  // Dark Orange
  ];
  
  return colors[Math.floor(Math.random() * colors.length)];
}

console.log('All floor plans generated successfully.');

// Note: In a real application, you would use actual floor plan images
// This script is just for demonstration purposes 