import { Port } from '@/types/device';

// 生成自定义端口
const generateCustomPorts = (
  deviceId: string, 
  count: number = 24, 
  prefix: string = 'GE', 
  startIndex: number = 1
): Port[] => {
  const ports: Port[] = [];
  
  // 端口类型选项
  const portTypes = ['ethernet', 'fiber', 'sfp', 'management'];
  // 端口状态选项
  const portStatuses: Array<'connected' | 'disconnected' | 'error' | 'disabled'> = [
    'connected', 'disconnected', 'error', 'disabled'
  ];
  // 端口速度选项
  const portSpeeds = ['1Gbps', '10Gbps', '40Gbps', '100Mbps'];
  
  // 生成指定数量的端口
  for (let i = 0; i < count; i++) {
    const portIndex = startIndex + i;
    // 计算端口名称，如 GE1/0/1, GE1/0/2 等
    const slotNum = Math.floor(portIndex / 24) + 1;
    const portNum = portIndex % 24 === 0 ? 24 : portIndex % 24;
    const portName = `${prefix}${slotNum}/0/${portNum}`;
    
    // 随机选择端口类型和状态
    const type = portTypes[Math.floor(Math.random() * portTypes.length)];
    const status = portStatuses[Math.floor(Math.random() * portStatuses.length)];
    const speed = portSpeeds[Math.floor(Math.random() * portSpeeds.length)];
    
    // 创建端口对象
    const port: Port = {
      id: `${deviceId}-port-${portIndex}`,
      name: portName,
      type,
      status,
      speed
    };

    // 如果状态为已连接，添加连接目标
    if (status === 'connected') {
      const targets = ['Server', 'Switch', 'Router', 'Storage'];
      const target = targets[Math.floor(Math.random() * targets.length)];
      port.connectedTo = `${target}-${Math.floor(Math.random() * 10) + 1}`;
    }
    
    ports.push(port);
  }
  
  return ports;
};

// 模拟获取设备端口数据
export const fetchDevicePorts = async (
  deviceId: string, 
  options?: { 
    count?: number, 
    prefix?: string,
    startIndex?: number 
  }
): Promise<Port[]> => {
  console.log(`Fetching ports for device ${deviceId} with options:`, options);
  
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 根据设备ID返回不同的模拟数据
  const mockPorts: Record<string, Port[]> = {
    'device1': [
      { id: 'port1', name: 'GE1/0/1', type: 'ethernet', status: 'connected', speed: '1Gbps' },
      { id: 'port2', name: 'GE1/0/2', type: 'ethernet', status: 'disconnected', speed: '1Gbps' },
      { id: 'port3', name: 'GE1/0/3', type: 'ethernet', status: 'connected', speed: '1Gbps', connectedTo: 'Server01' },
      { id: 'port4', name: 'GE1/0/4', type: 'ethernet', status: 'error', speed: '1Gbps' },
    ],
    'device2': [
      { id: 'port5', name: 'Te1/1', type: 'sfp', status: 'connected', speed: '10Gbps', connectedTo: 'Core Switch' },
      { id: 'port6', name: 'Te1/2', type: 'sfp', status: 'connected', speed: '10Gbps', connectedTo: 'Storage Array' },
      { id: 'port7', name: 'Mgmt0', type: 'ethernet', status: 'connected', speed: '1Gbps', connectedTo: 'Management Network' },
    ]
  };
  
  // 使用内置数据或生成自定义端口
  if (mockPorts[deviceId] && !options) {
    return mockPorts[deviceId];
  } else {
    // 使用自定义选项或默认值生成端口
    const count = options?.count ?? 24;
    const prefix = options?.prefix ?? 'GE';
    const startIndex = options?.startIndex ?? 1;
    
    return generateCustomPorts(deviceId, count, prefix, startIndex);
  }
};

// 获取设备详情
export const fetchDeviceDetails = async (deviceId: string) => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 返回模拟数据
  return {
    id: deviceId,
    name: `Device ${deviceId}`,
    model: 'Cisco Nexus 9000',
    serialNumber: `SN${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
    status: ['normal', 'warning', 'error'][Math.floor(Math.random() * 3)],
    manufacturer: 'Cisco',
    // 其他设备详情...
  };
};

// 维护记录类型
export interface MaintenanceRecord {
  id: number;
  date: string;
  type: 'routine' | 'repair' | 'upgrade';
  description: string;
  cost?: number;
  performedBy?: string;
  nextScheduledDate?: string;
}

// 资产分类类型定义
// 资产类型定义
export interface Asset {
  id: number;
  name: string;
  code: string;
  model?: string;
  categoryId: number;
  status?: string;
  purchaseDate?: string;
  price?: number;
  location?: string;
  description?: string;
  // 生命周期相关字段
  lifecycleStatus?: 'planning' | 'purchased' | 'deployed' | 'maintenance' | 'retired'; // 生命周期状态
  warrantyExpireDate?: string; // 维保到期日
  manufacturer?: string; // 制造商
  supplier?: string; // 供应商
  purchaseOrderNumber?: string; // 采购订单号
  serialNumber?: string; // 序列号
  assetValue?: number; // 当前资产价值（考虑折旧后）
  depreciationMethod?: 'straight-line' | 'declining-balance' | 'none'; // 折旧方法
  depreciationPeriod?: number; // 折旧年限(月)
  depreciationRate?: number; // 折旧率
  maintenanceRecords?: MaintenanceRecord[]; // 维护记录
  lastCheckDate?: string; // 最后盘点日期
  responsiblePerson?: string; // 责任人
  department?: string; // 所属部门
  tags?: string[]; // 资产标签
}

export interface AssetCategory {
  id: number;
  name: string;
  code?: string;
  description?: string;
  level?: number;
  parent_id?: number;
  attributes?: {
    required?: string[];
    optional?: string[];
    codeRule?: {
      enabled?: boolean;
      prefix?: string;
      digitLength?: number;
      includeDate?: boolean;
      dateFormat?: 'YYMMDD' | 'YYYYMMDD' | 'YYMM';
    };
  };
  is_system?: boolean;
}

// 资产分类API
export const getAssetCategories = async (): Promise<AssetCategory[]> => {
  const response = await fetch('/api/asset-categories');
  if (!response.ok) {
    throw new Error('Failed to fetch asset categories');
  }
  const data = await response.json();
  // 确保返回的是数组
  return Array.isArray(data) ? data : [];
};

export const getAssetCategory = async (id: number): Promise<AssetCategory> => {
  const response = await fetch(`/api/asset-categories/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch asset category');
  }
  return response.json();
};

export const createAssetCategory = async (data: Omit<AssetCategory, 'id'>): Promise<AssetCategory> => {
  const response = await fetch('/api/asset-categories', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  if (!response.ok) {
    throw new Error('Failed to create asset category');
  }
  return response.json();
};

export const updateAssetCategory = async (id: number, data: Partial<AssetCategory>): Promise<AssetCategory> => {
  const response = await fetch(`/api/asset-categories/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  if (!response.ok) {
    throw new Error('Failed to update asset category');
  }
  return response.json();
};

export const deleteAssetCategory = async (id: number): Promise<void> => {
  const response = await fetch(`/api/asset-categories/${id}`, {
    method: 'DELETE'
  });
  if (!response.ok) {
    throw new Error('Failed to delete asset category');
  }
};

// 资产API
export const createAsset = async (data: Omit<Asset, 'id'>): Promise<Asset> => {
  const response = await fetch('/api/assets', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  return response.json();
};

export const getAssets = async (): Promise<Asset[]> => {
  const response = await fetch('/api/assets');
  return response.json();
};

export const getAsset = async (id: number): Promise<Asset> => {
  const response = await fetch(`/api/assets/${id}`);
  return response.json();
};

export const updateAsset = async (id: number, data: Partial<Asset>): Promise<Asset> => {
  const response = await fetch(`/api/assets/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  return response.json();
};

export const deleteAsset = async (id: number): Promise<void> => {
  await fetch(`/api/assets/${id}`, {
    method: 'DELETE'
  });
};

// 资产盘点登记（mock实现）
export async function registerInventoryCheck(data: { assetCode: string, user: string, time: string }): Promise<{ success: boolean, message: string }> {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  // 假设盘点成功
  return { success: true, message: '盘点登记成功' };
}
