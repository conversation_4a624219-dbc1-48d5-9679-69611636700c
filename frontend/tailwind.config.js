const { nextui } = require("@nextui-org/react");

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@nextui-org/theme/dist/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // 丰富多彩风格的调色板
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // 丰富多彩的调色板扩展
        vibrant: {
          pink: '#FF2D55',
          purple: '#9C27B0',
          deepPurple: '#673AB7',
          indigo: '#3F51B5',
          blue: '#2196F3',
          lightBlue: '#03A9F4',
          cyan: '#00BCD4',
          teal: '#009688',
          green: '#4CAF50',
          lightGreen: '#8BC34A',
          lime: '#CDDC39',
          yellow: '#FFEB3B',
          amber: '#FFC107',
          orange: '#FF9800',
          deepOrange: '#FF5722',
          red: '#F44336',
        },
        // 保留原有的颜色
        komodo: {
          green: '#33bb73',
          lightGreen: '#57c78c',
          darkGreen: '#207b4b',
          gray: '#f8f9fa',
          darkGray: '#1A202C',
          lightGray: '#F7FAFC',
          border: '#E2E8F0',
        },
        status: {
          running: '#33bb73',
          exited: '#f03e3e',
          stopped: '#868e96',
          unhealthy: '#ff922b',
          disabled: '#adb5bd',
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    nextui()
  ],
};