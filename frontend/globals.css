@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer components {
  .btn-vibrant {
    @apply px-6 py-3 rounded-lg font-medium text-white transition-all;
    @apply bg-gradient-to-r from-vibrant-blue to-vibrant-purple hover:from-vibrant-purple hover:to-vibrant-blue;
    @apply hover:shadow-lg transform hover:-translate-y-1;
  }
  
  .card-vibrant {
    @apply rounded-xl p-6 shadow-lg transition-all;
    @apply bg-white/10 backdrop-blur-md border border-white/20;
    @apply hover:shadow-xl hover:border-white/40;
  }
}

/* 基础样式 */
@layer base {
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
}