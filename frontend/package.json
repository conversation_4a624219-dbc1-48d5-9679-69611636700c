{"name": "rs-asset", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:ssl": "node server.js", "build": "next build", "start": "next start", "start:https": "NODE_ENV=production node server.js", "lint": "next lint"}, "dependencies": {"@ant-design/charts": "^2.2.1", "@ant-design/icons": "^5.5.1", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@nextui-org/react": "^2.6.11", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.7", "@types/pg": "^8.15.4", "antd": "^5.21.0", "axios": "^1.10.0", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^12.18.1", "html5-qrcode": "^2.3.8", "i18next": "^23.7.16", "lucide-react": "^0.517.0", "next": "^14.2.28", "next-themes": "^0.4.6", "pg": "^8.16.0", "react": "^18.2.0", "react-calendar": "^6.0.0", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-i18next": "^14.0.0", "react-icons": "^5.0.1", "react-swipeable": "^7.0.2", "recharts": "^2.15.3", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.25.67", "zustand": "^5.0.4"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "14.1.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}}