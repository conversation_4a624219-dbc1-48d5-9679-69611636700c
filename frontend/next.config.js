/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  distDir: '.next',
  swcMinify: false, // 禁用SWC压缩
  compiler: {
    styledComponents: true,
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 启用压缩和优化
  compress: true,
  // 启用图像优化
  images: {
    domains: ['10.10.163.3'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
  },
  // 启用静态资源优化
  // 启用增量静态再生成
  experimental: {
    scrollRestoration: true,
  },
  // 配置API代理
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://10.10.163.3:8001/api/:path*'
      }
    ]
  },
  // 配置HTTP缓存头
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=86400',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ]
  }
}

module.exports = nextConfig