"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/color2k";
exports.ids = ["vendor-chunks/color2k"];
exports.modules = {

/***/ "(ssr)/./node_modules/color2k/dist/index.exports.import.es.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/color2k/dist/index.exports.import.es.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorError: () => (/* binding */ ColorError$1),\n/* harmony export */   adjustHue: () => (/* binding */ adjustHue),\n/* harmony export */   darken: () => (/* binding */ darken),\n/* harmony export */   desaturate: () => (/* binding */ desaturate),\n/* harmony export */   getContrast: () => (/* binding */ getContrast),\n/* harmony export */   getLuminance: () => (/* binding */ getLuminance),\n/* harmony export */   getScale: () => (/* binding */ getScale),\n/* harmony export */   guard: () => (/* binding */ guard),\n/* harmony export */   hasBadContrast: () => (/* binding */ hasBadContrast),\n/* harmony export */   hsla: () => (/* binding */ hsla),\n/* harmony export */   lighten: () => (/* binding */ lighten),\n/* harmony export */   mix: () => (/* binding */ mix),\n/* harmony export */   opacify: () => (/* binding */ opacify),\n/* harmony export */   parseToHsla: () => (/* binding */ parseToHsla),\n/* harmony export */   parseToRgba: () => (/* binding */ parseToRgba),\n/* harmony export */   readableColor: () => (/* binding */ readableColor),\n/* harmony export */   readableColorIsBlack: () => (/* binding */ readableColorIsBlack),\n/* harmony export */   rgba: () => (/* binding */ rgba),\n/* harmony export */   saturate: () => (/* binding */ saturate),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toHsla: () => (/* binding */ toHsla),\n/* harmony export */   toRgba: () => (/* binding */ toRgba),\n/* harmony export */   transparentize: () => (/* binding */ transparentize)\n/* harmony export */ });\n/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low, high, value) {\n  return Math.min(Math.max(low, value), high);\n}\n\nclass ColorError extends Error {\n  constructor(color) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\nvar ColorError$1 = ColorError;\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color) {\n  if (typeof color !== 'string') throw new ColorError$1(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(r(x, 2), 16)), parseInt(r(arr[3] || 'f', 2), 16) / 255];\n  }\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 16)), parseInt(arr[3] || 'ff', 16) / 255];\n  }\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 10)), parseFloat(arr[3] || '1')];\n  }\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError$1(color);\n    if (guard(0, 100, l) !== l) throw new ColorError$1(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a];\n  }\n  throw new ColorError$1(color);\n}\nfunction hash(str) {\n  let hash = 5381;\n  let i = str.length;\n  while (i) {\n    hash = hash * 33 ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\nconst colorToInt = x => parseInt(x.replace(/_/g, ''), 36);\nconst compressedColorMap = '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'.split(' ').reduce((acc, next) => {\n  const key = colorToInt(next.substring(0, 3));\n  const hex = colorToInt(next.substring(3)).toString(16);\n\n  // NOTE: padStart could be used here but it breaks Node 6 compat\n  // https://github.com/ricokahler/color2k/issues/351\n  let prefix = '';\n  for (let i = 0; i < 6 - hex.length; i++) {\n    prefix += '0';\n  }\n  acc[key] = `${prefix}${hex}`;\n  return acc;\n}, {});\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color) {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError$1(color);\n  return `#${result}`;\n}\nconst r = (str, amount) => Array.from(Array(amount)).map(() => str).join('');\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(`^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(',\\\\s*(\\\\d+)\\\\s*', 2)}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`, 'i');\nconst hslaRegex = /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\nconst roundColor = color => {\n  return Math.round(color * 255);\n};\nconst hslToRgb = (hue, saturation, lightness) => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor);\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (hue % 360 + 360) % 360 / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n  return [finalRed, finalGreen, finalBlue].map(roundColor);\n};\n\n// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color) {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? value : value / 255);\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n  const delta = max - min;\n  const saturation = lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n  const hue = 60 * (red === max ? (green - blue) / delta + (green < blue ? 6 : 0) : green === max ? (blue - red) / delta + 2 : (red - green) / delta + 4);\n  return [hue, saturation, lightness, alpha];\n}\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(hue, saturation, lightness, alpha) {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(0, 100, saturation * 100).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color, degrees) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color, amount) {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color, amount) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color) {\n  if (color === 'transparent') return 0;\n  function f(x) {\n    const channel = x / 255;\n    return channel <= 0.04045 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4);\n  }\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1, color2) {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n  return luminance1 > luminance2 ? (luminance1 + 0.05) / (luminance2 + 0.05) : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red, green, blue, alpha) {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(0, 255, green).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1, color2, weight) {\n  const normalize = (n, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? n : n / 255;\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight = normalizedWeight * alphaDelta === -1 ? normalizedWeight : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n  return rgba(r, g, b, a);\n}\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors) {\n  return n => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n    return mix(color1, color2, weight);\n  };\n}\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(color, standard = 'aa', background = '#fff') {\n  return getContrast(color, background) < guidelines[standard];\n}\n\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color, amount) {\n  return darken(color, -amount);\n}\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color, amount) {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color, amount) {\n  return transparentize(color, -amount);\n}\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color) {\n  return getLuminance(color) > 0.179;\n}\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color) {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color, amount) {\n  return desaturate(color, -amount);\n}\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color) {\n  const [r, g, b, a] = parseToRgba(color);\n  let hex = x => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color) {\n  return rgba(...parseToRgba(color));\n}\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color) {\n  return hsla(...parseToHsla(color));\n}\n\n\n//# sourceMappingURL=index.exports.import.es.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/color2k/dist/index.exports.import.es.mjs\n");

/***/ })

};
;