"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/components/ui/use-toast.ts":
/*!****************************************!*\
  !*** ./src/components/ui/use-toast.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 5;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_VALUE;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/use-toast.ts\n");

/***/ }),

/***/ "./src/contexts/NotificationContext.tsx":
/*!**********************************************!*\
  !*** ./src/contexts/NotificationContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationProvider: () => (/* binding */ NotificationProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_notificationService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/notificationService */ \"./src/services/notificationService.ts\");\n\n\n\n\n// 创建上下文\nconst NotificationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst NotificationProvider = ({ children })=>{\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 计算未读通知数量\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const count = notifications.filter((n)=>!n.read).length;\n        setUnreadCount(count);\n        // 更新页面标题，显示未读通知数量\n        if (count > 0) {\n            document.title = `(${count}) RS Asset`;\n        } else {\n            document.title = \"RS Asset\";\n        }\n    }, [\n        notifications\n    ]);\n    // 从本地存储加载通知\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const storedNotifications = localStorage.getItem(\"notifications\");\n        if (storedNotifications) {\n            try {\n                setNotifications(JSON.parse(storedNotifications));\n            } catch (error) {\n                console.error(\"加载通知失败:\", error);\n            }\n        }\n    }, []);\n    // 保存通知到本地存储\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (notifications.length > 0) {\n            localStorage.setItem(\"notifications\", JSON.stringify(notifications));\n        }\n    }, [\n        notifications\n    ]);\n    // 添加新通知\n    const addNotification = (title, message, type = \"info\", link, data)=>{\n        const newNotification = {\n            id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            title,\n            message,\n            type,\n            timestamp: Date.now(),\n            read: false,\n            link,\n            data\n        };\n        setNotifications((prev)=>[\n                newNotification,\n                ...prev\n            ]);\n        // 发送浏览器通知\n        (0,_services_notificationService__WEBPACK_IMPORTED_MODULE_3__.sendMockNotification)(title, message, type, {\n            url: link,\n            ...data\n        });\n    };\n    // 标记通知为已读\n    const markAsRead = (id)=>{\n        setNotifications((prev)=>prev.map((n)=>n.id === id ? {\n                    ...n,\n                    read: true\n                } : n));\n    };\n    // 标记所有通知为已读\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((n)=>({\n                    ...n,\n                    read: true\n                })));\n    };\n    // 删除通知\n    const deleteNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    };\n    // 清空所有通知\n    const clearAll = ()=>{\n        setNotifications([]);\n        localStorage.removeItem(\"notifications\");\n    };\n    // 上下文值\n    const contextValue = {\n        notifications,\n        unreadCount,\n        addNotification,\n        markAsRead,\n        markAllAsRead,\n        deleteNotification,\n        clearAll\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/opt/RS_asset/frontend/src/contexts/NotificationContext.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n// 使用通知上下文的钩子\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationContext);\n    if (context === undefined) {\n        throw new Error(\"useNotifications must be used within a NotificationProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/NotificationContext.tsx\n");

/***/ }),

/***/ "./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\n// 创建上下文\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 提供者组件\nconst ThemeProvider = ({ children })=>{\n    const [isDarkMode, setIsDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 初始化时从localStorage获取主题设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 检查localStorage\n        const storedTheme = localStorage.getItem(\"theme\");\n        // 检查系统偏好\n        const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n        // 如果localStorage中有设置，使用它；否则使用系统偏好\n        const initialDarkMode = storedTheme ? storedTheme === \"dark\" : prefersDark;\n        setIsDarkMode(initialDarkMode);\n        // 应用主题到document\n        if (initialDarkMode) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n    }, []);\n    // 切换主题\n    const toggleTheme = ()=>{\n        setIsDarkMode((prev)=>{\n            const newDarkMode = !prev;\n            // 保存到localStorage\n            localStorage.setItem(\"theme\", newDarkMode ? \"dark\" : \"light\");\n            // 应用到document\n            if (newDarkMode) {\n                document.documentElement.classList.add(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n            }\n            return newDarkMode;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            isDarkMode,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/opt/RS_asset/frontend/src/contexts/ThemeContext.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n// 自定义钩子，用于在组件中使用主题上下文\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/NotificationContext */ \"./src/contexts/NotificationContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _services_notificationService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services/notificationService */ \"./src/services/notificationService.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    // 注册Service Worker和请求通知权限\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeApp = async ()=>{\n            // 注册Service Worker\n            await (0,_services_notificationService__WEBPACK_IMPORTED_MODULE_5__.registerServiceWorker)();\n            // 请求通知权限\n            await (0,_services_notificationService__WEBPACK_IMPORTED_MODULE_5__.checkNotificationPermission)();\n        };\n        initializeApp();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ChakraProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NotificationContext__WEBPACK_IMPORTED_MODULE_3__.NotificationProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/opt/RS_asset/frontend/src/pages/_app.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/opt/RS_asset/frontend/src/pages/_app.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/opt/RS_asset/frontend/src/pages/_app.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/opt/RS_asset/frontend/src/pages/_app.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/services/notificationService.ts":
/*!*********************************************!*\
  !*** ./src/services/notificationService.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationService: () => (/* binding */ NotificationService),\n/* harmony export */   checkNotificationPermission: () => (/* binding */ checkNotificationPermission),\n/* harmony export */   registerServiceWorker: () => (/* binding */ registerServiceWorker)\n/* harmony export */ });\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/components/ui/use-toast */ \"./src/components/ui/use-toast.ts\");\n\n// 注册 Service Worker\nconst registerServiceWorker = async ()=>{\n    if (\"serviceWorker\" in navigator) {\n        try {\n            const registration = await navigator.serviceWorker.register(\"/service-worker.js\");\n            console.log(\"Service Worker 注册成功:\", registration);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n                title: \"提示\",\n                description: \"后台通知服务已启动。\"\n            });\n        } catch (error) {\n            console.error(\"Service Worker 注册失败:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n                title: \"错误\",\n                description: \"后台通知服务启动失败。\",\n                variant: \"destructive\"\n            });\n        }\n    }\n};\n// 检查和请求通知权限\nconst checkNotificationPermission = async ()=>{\n    if (\"Notification\" in window) {\n        let permission = Notification.permission;\n        if (permission === \"default\") {\n            permission = await Notification.requestPermission();\n        }\n        if (permission === \"granted\") {\n            console.log(\"通知权限已授予。\");\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n                title: \"提示\",\n                description: \"已获得通知权限，您将收到相关提醒。\"\n            });\n        } else {\n            console.log(\"通知权限被拒绝。\");\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n                title: \"警告\",\n                description: \"通知权限被拒绝，您可能无法及时收到重要提醒。\",\n                variant: \"destructive\"\n            });\n        }\n    }\n};\n// 临时模拟数据库查询函数\nconst query = async (sql, params)=>{\n    console.log(\"模拟数据库查询:\", sql, params);\n    return [];\n};\nclass NotificationService {\n    // 获取通知模板\n    static async getNotificationTemplate(type) {\n        try {\n            const result = await query(`SELECT * FROM notification_templates\n         WHERE type = $1 AND is_default = true\n         LIMIT 1`, [\n                type\n            ]);\n            return result[0];\n        } catch (err) {\n            console.error(\"获取通知模板失败:\", err);\n            return null;\n        }\n    }\n    // 替换模板变量\n    static replaceTemplateVariables(template, alert) {\n        return template.replace(/\\{alert\\.name\\}/g, alert.ruleName || \"未知规则\").replace(/\\{alert\\.metric\\}/g, alert.metric).replace(/\\{alert\\.value\\}/g, alert.value).replace(/\\{alert\\.threshold\\}/g, alert.threshold || \"未知\").replace(/\\{alert\\.severity\\}/g, alert.severity).replace(/\\{alert\\.time\\}/g, new Date(alert.triggeredAt).toLocaleString()).replace(/\\{device\\.name\\}/g, alert.deviceName || \"未知设备\").replace(/\\{device\\.ip\\}/g, alert.deviceIp || \"未知IP\").replace(/\\{device\\.type\\}/g, alert.deviceType || \"未知类型\");\n    }\n    // 发送报警通知\n    static async sendAlertNotification(alertLog) {\n        try {\n            // 获取所有激活的通知渠道\n            const channels = await query(\"SELECT * FROM notification_channels WHERE is_active = true\");\n            // 为每个渠道发送通知\n            for (const channel of channels){\n                switch(channel.type){\n                    case \"email\":\n                        await this.sendEmailNotification(channel, alertLog);\n                        break;\n                    case \"webhook\":\n                        await this.sendWebhookNotification(channel, alertLog);\n                        break;\n                    case \"slack\":\n                        await this.sendSlackNotification(channel, alertLog);\n                        break;\n                    case \"sms\":\n                        await this.sendSMSNotification(channel, alertLog);\n                        break;\n                }\n            }\n            // 记录通知发送状态\n            await query(\"UPDATE alert_logs SET notified = true WHERE id = $1\", [\n                alertLog.id\n            ]);\n        } catch (err) {\n            console.error(\"发送通知失败:\", err);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n                title: \"通知发送失败\",\n                description: err instanceof Error ? err.message : \"无法发送报警通知\",\n                variant: \"destructive\"\n            });\n        }\n    }\n    static async sendEmailNotification(channel, alert) {\n        // 获取默认邮件模板\n        const template = await this.getNotificationTemplate(\"email\");\n        // 替换模板变量\n        const subject = this.replaceTemplateVariables(template?.subject || \"报警通知\", alert);\n        const content = this.replaceTemplateVariables(template?.content || \"\", alert);\n        console.log(\"发送邮件通知:\", {\n            to: channel.config.recipients,\n            subject,\n            content\n        });\n    // 实际项目中这里应该调用邮件服务API\n    }\n    static async sendWebhookNotification(channel, alert) {\n        // 实现Webhook通知逻辑\n        console.log(\"发送Webhook通知:\", channel, alert);\n    // 实际项目中这里应该调用Webhook API\n    }\n    static async sendSlackNotification(channel, alert) {\n        // 实现Slack通知逻辑\n        console.log(\"发送Slack通知:\", channel, alert);\n    // 实际项目中这里应该调用Slack API\n    }\n    static async sendSMSNotification(channel, alert) {\n        // 实现短信通知逻辑\n        console.log(\"发送短信通知:\", channel, alert);\n    // 实际项目中这里应该调用短信服务API\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/services/notificationService.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("./src/pages/_app.tsx")));
module.exports = __webpack_exports__;

})();