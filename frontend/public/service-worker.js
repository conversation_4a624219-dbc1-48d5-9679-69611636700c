// 缓存名称和版本
const CACHE_NAME = 'rs-asset-cache-v1';

// 需要缓存的资源列表
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json',
  '/logo.png',
  '/favicon.ico'
];

// 安装Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('缓存已打开');
        return cache.addAll(urlsToCache);
      })
      .then(() => self.skipWaiting())
  );
});

// 激活Service Worker
self.addEventListener('activate', (event) => {
  const cacheWhitelist = [CACHE_NAME];
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            // 删除旧缓存
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// 处理网络请求
self.addEventListener('fetch', (event) => {
  // 对API请求使用网络优先策略
  if (event.request.url.includes('/api/')) {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return caches.match(event.request);
        })
    );
  } else {
    // 对静态资源使用缓存优先策略
    event.respondWith(
      caches.match(event.request)
        .then((response) => {
          // 如果找到缓存的响应，则返回缓存
          if (response) {
            return response;
          }
          
          // 否则发起网络请求
          return fetch(event.request).then(
            (response) => {
              // 检查是否收到有效响应
              if (!response || response.status !== 200 || response.type !== 'basic') {
                return response;
              }
              
              // 克隆响应，因为响应是流，只能使用一次
              const responseToCache = response.clone();
              
              caches.open(CACHE_NAME)
                .then((cache) => {
                  // 将响应添加到缓存
                  cache.put(event.request, responseToCache);
                });
              
              return response;
            }
          );
        })
    );
  }
});

// 处理推送通知
self.addEventListener('push', (event) => {
  if (!event.data) {
    console.log('收到空推送消息');
    return;
  }
  
  try {
    const data = event.data.json();
    
    const title = data.title || '新通知';
    const options = {
      body: data.message || '',
      icon: data.icon || '/logo.png',
      badge: data.badge || '/badge.png',
      tag: data.tag || 'default',
      data: data.data || {},
      actions: data.actions || [],
      vibrate: data.vibrate || [100, 50, 100],
      timestamp: data.timestamp || Date.now()
    };
    
    event.waitUntil(
      self.registration.showNotification(title, options)
    );
  } catch (error) {
    console.error('处理推送消息失败:', error);
    
    // 尝试作为文本处理
    const message = event.data.text();
    
    event.waitUntil(
      self.registration.showNotification('新通知', {
        body: message,
        icon: '/logo.png',
        badge: '/badge.png'
      })
    );
  }
});

// 处理通知点击
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  // 获取通知数据
  const data = event.notification.data || {};
  const url = data.url || '/';
  
  // 如果点击了特定操作
  if (event.action) {
    // 处理特定操作
    console.log('用户点击了通知操作:', event.action);
    
    // 可以根据不同的操作执行不同的行为
    switch (event.action) {
      case 'view':
        // 打开特定URL
        event.waitUntil(
          clients.openWindow(data.viewUrl || url)
        );
        break;
      case 'dismiss':
        // 仅关闭通知，不执行其他操作
        break;
      default:
        // 默认行为
        event.waitUntil(
          clients.openWindow(url)
        );
    }
  } else {
    // 默认点击行为
    event.waitUntil(
      clients.matchAll({ type: 'window' })
        .then((clientList) => {
          // 检查是否已有打开的窗口
          for (const client of clientList) {
            if (client.url === url && 'focus' in client) {
              return client.focus();
            }
          }
          
          // 如果没有打开的窗口，则打开新窗口
          if (clients.openWindow) {
            return clients.openWindow(url);
          }
        })
    );
  }
});

// 处理通知关闭
self.addEventListener('notificationclose', (event) => {
  console.log('用户关闭了通知:', event.notification.tag);
});
