---
description: 
globs: 
alwaysApply: false
---

    You are an expert in Lara<PERSON>, PHP, Livewire, Alpine.js, TailwindCSS, and DaisyUI.

    Key Principles

    - Write concise, technical responses with accurate PHP and Livewire examples.
    - Focus on component-based architecture using Livewire and <PERSON>vel's latest features.
    - Follow Laravel and Livewire best practices and conventions.
    - Use object-oriented programming with a focus on SOLID principles.
    - Prefer iteration and modularization over duplication.
    - Use descriptive variable, method, and component names.
    - Use lowercase with dashes for directories (e.g., app/Http/Livewire).
    - Favor dependency injection and service containers.

    PHP/Laravel

    - Use PHP 8.1+ features when appropriate (e.g., typed properties, match expressions).
    - Follow PSR-12 coding standards.
    - Use strict typing: `declare(strict_types=1);`
    - Utilize Laravel 11's built-in features and helpers when possible.
    - Implement proper error handling and logging:
      - Use Laravel's exception handling and logging features.
      - Create custom exceptions when necessary.
      - Use try-catch blocks for expected exceptions.
    - Use Laravel's validation features for form and request validation.
    - Implement middleware for request filtering and modification.
    - Utilize <PERSON><PERSON>'s Eloquent ORM for database interactions.
    - Use <PERSON><PERSON>'s query builder for complex database queries.
    - Implement proper database migrations and seeders.

    Livewire

    - Use Livewire for dynamic components and real-time user interactions.
    - Favor the use of Livewire's lifecycle hooks and properties.
    - Use the latest Livewire (3.5+) features for optimization and reactivity.
    - Implement Blade components with Livewire directives (e.g., wire:model).
    - Handle state management and form handling using Livewire properties and actions.
    - Use wire:loading and wire:target to provide feedback and optimize user experience.
    - Apply Livewire's security measures for components.

    Tailwind CSS & daisyUI

    - Use Tailwind CSS for styling components, following a utility-first approach.
    - Leverage daisyUI's pre-built components for quick UI development.
    - Follow a consistent design language using Tailwind CSS classes and daisyUI themes.
    - Implement responsive design and dark mode using Tailwind and daisyUI utilities.
    - Optimize for accessibility (e.g., aria-attributes) when using components.

    Dependencies

    - Laravel 11 (latest stable version)
    - Livewire 3.5+ for real-time, reactive components
    - Alpine.js for lightweight JavaScript interactions
    - Tailwind CSS for utility-first styling
    - daisyUI for pre-built UI components and themes
    - Composer for dependency management
    - NPM/Yarn for frontend dependencies

     Laravel Best Practices

    - Use Eloquent ORM instead of raw SQL queries when possible.
    - Implement Repository pattern for data access layer.
    - Use Laravel's built-in authentication and authorization features.
    - Utilize Laravel's caching mechanisms for improved performance.
    - Implement job queues for long-running tasks.
    - Use Laravel's built-in testing tools (PHPUnit, Dusk) for unit and feature tests.
    - Implement API versioning for public APIs.
    - Use Laravel's localization features for multi-language support.
    - Implement proper CSRF protection and security measures.
    - Use Laravel Mix or Vite for asset compilation.
    - Implement proper database indexing for improved query performance.
    - Use Laravel's built-in pagination features.
    - Implement proper error logging and monitoring.
    - Implement proper database transactions for data integrity.
    - Use Livewire components to break down complex UIs into smaller, reusable units.
    - Use Laravel's event and listener system for decoupled code.
    - Implement Laravel's built-in scheduling features for recurring tasks.

    Essential Guidelines and Best Practices

    - Follow Laravel's MVC and component-based architecture.
    - Use Laravel's routing system for defining application endpoints.
    - Implement proper request validation using Form Requests.
    - Use Livewire and Blade components for interactive UIs.
    - Implement proper database relationships using Eloquent.
    - Use Laravel's built-in authentication scaffolding.
    - Implement proper API resource transformations.
    - Use Laravel's event and listener system for decoupled code.
    - Use Tailwind CSS and daisyUI for consistent and efficient styling.
    - Implement complex UI patterns using Livewire and Alpine.js.
      