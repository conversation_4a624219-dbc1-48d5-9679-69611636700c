# RS Asset Management System

燃石IT资产管理与监控系统 - 一个基于 FastAPI + Next.js 的现代化企业级IT资产管理平台，专注于IT资产全生命周期管理、设备实时监控、网络设备管理、耗材管理以及软件授权到期监控。

## 🎯 项目概述

本系统是专为企业IT部门设计的综合性资产管理解决方案，涵盖了从资产采购、部署、维护到报废的完整生命周期管理。系统不仅提供传统的资产管理功能，还集成了先进的SNMP监控、环境监测、网络带宽分析等智能化运维功能。

### 🌟 核心特性

- **📊 资产全生命周期管理**: 从采购到报废的完整资产管理流程
- **🔍 实时设备监控**: 基于SNMP协议的网络设备、打印机、UPS等设备监控
- **🌡️ 环境监测**: 机房温湿度、烟感、水浸等环境参数实时监控
- **📱 移动端支持**: 响应式设计，支持移动设备访问和操作
- **🔔 智能告警**: 多渠道告警通知，支持邮件、短信、企业微信等
- **📈 数据可视化**: 丰富的图表和仪表板，直观展示资产和监控数据
- **🏢 机房管理**: 3D机柜布局、网络拓扑图、设备位置管理
- **📋 巡检管理**: 智能巡检模板、移动端巡检、AI辅助分析
- **💾 配置备份**: 网络设备配置自动备份和版本管理
- **📞 通信管理**: 电话分机管理、IP电话监控

## 技术栈

### 后端
- **FastAPI**: 现代、快速的 Python Web 框架
- **SQLAlchemy**: Python SQL 工具包和对象关系映射
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **Pydantic**: 数据验证和序列化

### 前端
- **Next.js 14**: React 全栈框架
- **TypeScript**: 类型安全的 JavaScript
- **Ant Design**: 企业级 UI 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Zustand**: 轻量级状态管理
- **ECharts**: 数据可视化图表库

### 部署
- **Docker & Docker Compose**: 容器化部署
- **Nginx**: 反向代理和静态文件服务

## 🏗️ 系统架构

### 功能模块

#### 📦 资产管理模块
- **资产分类管理**: 支持多级分类，灵活的资产属性配置
- **资产登记**: 详细的资产信息录入，支持批量导入
- **资产变更**: 资产状态变更、位置调整、责任人变更
- **资产盘点**: 定期盘点计划、移动端扫码盘点
- **资产报废**: 报废流程管理、资产处置记录
- **维护记录**: 维修保养记录、成本统计分析

#### 🖥️ 设备监控模块
- **网络设备监控**: 路由器、交换机、防火墙等网络设备状态监控
- **打印机监控**: 打印机状态、耗材余量、打印量统计
- **UPS监控**: UPS设备状态、电池电量、负载监控
- **服务器监控**: CPU、内存、磁盘使用率监控
- **环境监控**: 机房温湿度、烟感、水浸传感器监控

#### 🌐 网络管理模块
- **网络拓扑**: 自动发现网络拓扑，可视化网络结构
- **带宽监控**: 实时网络流量监控，带宽使用分析
- **端口管理**: 交换机端口状态监控，端口配置管理
- **无线网络**: WiFi热点监控，无线设备管理
- **IP地址管理**: IP地址分配、使用情况统计

#### 🏢 机房管理模块
- **机柜管理**: 3D机柜视图，设备位置可视化
- **布线管理**: 网络布线图，端口连接关系
- **空间管理**: 机房空间利用率，容量规划
- **环境监控**: 机房环境参数实时监控

#### 📋 运维管理模块
- **巡检管理**: 智能巡检模板，移动端巡检应用
- **故障管理**: 故障报告、处理流程、知识库
- **配置管理**: 设备配置备份、版本控制、变更管理
- **性能分析**: 设备性能趋势分析，容量规划

#### 📊 报表分析模块
- **资产报表**: 资产统计、折旧分析、成本报告
- **监控报表**: 设备运行报告、故障统计分析
- **趋势分析**: 历史数据趋势，预测性分析
- **自定义报表**: 灵活的报表配置，定时生成

## 🏗️ 项目结构

```
RS_asset/
├── backend/                 # FastAPI 后端服务
│   ├── api/                # REST API 路由
│   │   ├── assets.py       # 资产管理API
│   │   ├── monitoring.py   # 监控数据API
│   │   ├── network_bandwidth.py # 网络带宽API
│   │   ├── printer.py      # 打印机管理API
│   │   ├── snmp_*.py      # SNMP相关API
│   │   └── phone_extensions.py # 电话分机API
│   ├── models/             # SQLAlchemy 数据模型
│   │   ├── asset.py        # 资产模型
│   │   ├── monitoring.py   # 监控数据模型
│   │   ├── network_bandwidth.py # 网络设备模型
│   │   ├── printer.py      # 打印机模型
│   │   └── *.py           # 其他业务模型
│   ├── schemas/            # Pydantic 数据验证模式
│   ├── services/           # 业务逻辑服务
│   │   ├── monitoring_service.py # 监控服务
│   │   └── snmp_collection_service.py # SNMP采集服务
│   ├── database.py         # 数据库配置和连接
│   ├── main.py            # FastAPI 应用入口
│   └── requirements.txt    # Python 依赖包
├── frontend/               # Next.js 前端应用
│   ├── src/
│   │   ├── app/           # Next.js 13+ App Router
│   │   │   ├── assets/    # 资产管理页面
│   │   │   ├── monitoring/ # 监控页面
│   │   │   ├── network/   # 网络管理页面
│   │   │   ├── power-monitoring/ # 电力监控页面
│   │   │   └── smart-ops/ # 智能运维页面
│   │   ├── components/    # React 组件库
│   │   │   ├── Dashboard/ # 仪表板组件
│   │   │   ├── RackLayout/ # 机柜布局组件
│   │   │   ├── monitoring/ # 监控组件
│   │   │   └── ui/        # 基础UI组件
│   │   ├── services/      # API 服务层
│   │   ├── types/         # TypeScript 类型定义
│   │   └── utils/         # 工具函数
│   ├── public/            # 静态资源文件
│   ├── package.json       # Node.js 依赖配置
│   └── next.config.js     # Next.js 配置
├── docs/                  # 项目文档
│   ├── 项目分析报告.md    # 项目分析文档
│   └── 优化实施计划.md    # 优化计划文档
├── scripts/               # 部署和工具脚本
│   ├── deploy.sh         # 部署脚本
│   └── dev-setup.sh      # 开发环境设置
├── tests/                 # 测试文件目录
├── docker-compose.yml     # Docker 容器编排
└── README.md             # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- **Python**: 3.8+
- **Node.js**: 18+
- **数据库**: PostgreSQL 12+ 
- **缓存**: Redis 6+
- **容器**: Docker & Docker Compose（推荐）

### 一键部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd RS_asset

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等参数

# 3. 启动所有服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec backend python init_db.py

# 5. 访问系统
# 前端界面: http://localhost:3000
# API文档: http://localhost:8000/docs
```

### 本地开发环境

**后端开发**
```bash
cd backend

# 安装依赖
pip install -r requirements.txt

# 配置环境
cp .env.example .env
# 编辑 .env 配置数据库连接

# 初始化数据库
python init_db.py

# 启动开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

**前端开发**
```bash
cd frontend

# 安装依赖
npm install
# 或使用 yarn
yarn install

# 配置环境
cp .env.local.example .env.local
# 编辑 .env.local 配置API地址

# 启动开发服务器
npm run dev
# 或使用 yarn
yarn dev
```

## ⚙️ 配置说明

### 环境变量配置

**后端配置 (backend/.env)**
```env
# 数据库配置
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/rs_asset

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# SNMP配置
SNMP_COMMUNITY=public
SNMP_VERSION=2
SNMP_TIMEOUT=5
SNMP_RETRIES=3

# 监控配置
MONITORING_INTERVAL=60
DATA_RETENTION_DAYS=90

# 告警配置
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
```

**前端配置 (frontend/.env.local)**
```env
# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# 功能开关
NEXT_PUBLIC_ENABLE_MONITORING=true
NEXT_PUBLIC_ENABLE_MOBILE=true
NEXT_PUBLIC_ENABLE_3D_VIEW=true

# 地图配置（如果使用地图功能）
NEXT_PUBLIC_MAP_API_KEY=your-map-api-key
```

## 📖 使用指南

### 🏠 系统首页
登录后进入系统仪表板，可以查看：
- 资产概览统计
- 设备运行状态
- 最新告警信息
- 系统健康度指标

### 📦 资产管理

**1. 资产分类设置**
- 进入「资产管理」→「资产分类」
- 创建多级分类结构（如：IT设备 → 服务器 → 机架服务器）
- 为每个分类配置自定义属性字段

**2. 资产登记**
- 点击「新增资产」录入资产信息
- 支持批量导入Excel文件
- 可上传资产照片和相关文档
- 设置资产责任人和使用部门

**3. 资产生命周期管理**
- 资产变更：位置调整、责任人变更、状态更新
- 维护记录：记录维修保养历史
- 资产盘点：定期盘点计划和执行
- 资产报废：报废申请和处置流程

### 🖥️ 设备监控

**1. 监控设备配置**
- 进入「监控管理」→「设备配置」
- 添加网络设备、打印机、UPS等
- 配置SNMP参数（IP地址、团体名、版本）
- 设置监控项目和采集频率

**2. 实时监控**
- 查看设备实时状态和性能指标
- 监控CPU、内存、网络流量等关键参数
- 查看历史趋势图表
- 设置告警阈值和通知规则

**3. 环境监控**
- 机房温湿度实时监控
- 烟感、水浸等安全传感器状态
- 环境数据历史趋势分析
- 异常情况自动告警

### 🌐 网络管理

**1. 网络拓扑**
- 自动发现网络设备连接关系
- 可视化网络拓扑图
- 设备状态实时更新
- 支持手动调整拓扑布局

**2. 带宽监控**
- 实时网络流量监控
- 带宽使用率统计
- 流量趋势分析
- 异常流量告警

**3. 端口管理**
- 交换机端口状态监控
- 端口配置管理
- VLAN配置查看
- 端口使用率统计

### 🏢 机房管理

**1. 机柜管理**
- 3D机柜视图展示
- 设备位置可视化
- U位使用情况统计
- 电力和散热计算

**2. 空间规划**
- 机房平面图管理
- 设备位置标注
- 空间利用率分析
- 容量规划建议

### 📱 移动端功能

**1. 移动巡检**
- 扫码识别设备
- 巡检任务执行
- 问题拍照上传
- 巡检报告生成

**2. 资产盘点**
- 扫码盘点资产
- 实时同步盘点结果
- 盘点差异处理
- 盘点报告导出

## 🔌 API 接口

系统提供完整的RESTful API，支持第三方系统集成。

### API 文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 主要接口端点

| 模块 | 端点 | 描述 |
|------|------|------|
| 资产管理 | `/api/assets` | 资产CRUD操作 |
| 资产分类 | `/api/asset-categories` | 分类管理 |
| 设备监控 | `/api/monitoring` | 监控数据查询 |
| 网络设备 | `/api/network-devices` | 网络设备管理 |
| 打印机 | `/api/printers` | 打印机监控 |
| SNMP数据 | `/api/snmp-data` | SNMP数据采集 |
| 电话分机 | `/api/phone-extensions` | 分机管理 |
| 告警管理 | `/api/alerts` | 告警规则和通知 |

### API 认证
```bash
# 获取访问令牌
curl -X POST "http://localhost:8000/api/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"password"}'

# 使用令牌访问API
curl -X GET "http://localhost:8000/api/assets" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🛠️ 开发指南

### 代码结构
- **后端**: 采用FastAPI + SQLAlchemy + Pydantic架构
- **前端**: 使用Next.js 13+ App Router + TypeScript
- **数据库**: PostgreSQL关系型数据库
- **缓存**: Redis用于会话和缓存

### 开发规范
- 遵循PEP 8 Python代码规范
- 使用TypeScript严格模式
- 提交前运行代码检查和测试
- 遵循语义化版本控制

### 测试
```bash
# 后端测试
cd backend
pytest tests/

# 前端测试
cd frontend
npm run test
```

## 🚀 部署指南

### 生产环境部署

**1. 服务器要求**
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 100GB以上SSD
- 网络: 100Mbps以上带宽

**2. Docker部署（推荐）**
```bash
# 生产环境配置
cp docker-compose.prod.yml docker-compose.yml

# 配置环境变量
vim .env.prod

# 启动服务
docker-compose up -d

# 配置反向代理（Nginx）
vim /etc/nginx/sites-available/rs-asset
```

**3. 数据备份**
```bash
# 数据库备份
docker-compose exec postgres pg_dump -U postgres rs_asset > backup.sql

# 恢复数据
docker-compose exec -T postgres psql -U postgres rs_asset < backup.sql
```

## 📊 监控和维护

### 系统监控
- 应用性能监控（APM）
- 数据库性能监控
- 服务器资源监控
- 日志收集和分析

### 定期维护
- 数据库备份（每日）
- 日志清理（每周）
- 系统更新（每月）
- 安全检查（每季度）

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork** 项目到你的GitHub账户
2. **创建** 功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 你的更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📝 文档改进
- 🎨 UI/UX优化
- ⚡ 性能优化
- 🔧 配置改进

## 📄 许可证

本项目采用 **MIT 许可证** - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持

### 获取帮助
- 📖 **文档**: [项目Wiki](https://github.com/your-repo/wiki)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- 💬 **讨论交流**: [GitHub Discussions](https://github.com/your-repo/discussions)
- 📧 **邮件支持**: <EMAIL>

### 常见问题

**Q: 如何重置管理员密码？**
A: 运行 `python reset_admin_password.py` 脚本

**Q: SNMP设备无法连接怎么办？**
A: 检查网络连通性、SNMP配置和防火墙设置

**Q: 如何备份和恢复数据？**
A: 使用提供的备份脚本或Docker命令进行数据库备份

## 🔄 更新日志

### v1.2.0 (2024-03-01)
- ✨ 新增移动端巡检功能
- 🔧 优化SNMP采集性能
- 🐛 修复资产导入问题
- 📱 改进响应式设计

### v1.1.0 (2024-02-01)
- ✨ 新增3D机柜视图
- 🔔 增强告警通知功能
- 📊 新增自定义报表
- 🔧 优化数据库性能

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- 📦 基础资产管理功能
- 🖥️ SNMP设备监控
- 🌐 网络设备管理
- 🖨️ 打印机监控
- 🌡️ 环境监控

---

**燃石IT资产管理系统** - 让IT资产管理更智能、更高效！ 🚀

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请提交 Issue 或联系项目维护者。