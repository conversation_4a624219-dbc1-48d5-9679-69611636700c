-- 添加新的资产字段迁移脚本
-- 执行日期: 2024年

-- 添加安防设备专用字段
ALTER TABLE assets ADD COLUMN IF NOT EXISTS security_device_type VARCHAR(100);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS video_resolution VARCHAR(50);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS recording_capacity INTEGER;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS night_vision BOOLEAN DEFAULT FALSE;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS motion_detection BOOLEAN DEFAULT FALSE;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS audio_support BOOLEAN DEFAULT FALSE;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS ptz_support BOOLEAN DEFAULT FALSE;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS weatherproof_rating VARCHAR(20);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS power_over_ethernet BOOLEAN DEFAULT FALSE;

-- 添加耗材相关字段
ALTER TABLE assets ADD COLUMN IF NOT EXISTS is_consumable BOOLEAN DEFAULT FALSE;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS quantity INTEGER;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS unit VARCHAR(20);
ALTER TABLE assets ADD COLUMN IF NOT EXISTS min_stock_level INTEGER;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS max_stock_level INTEGER;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS reorder_point INTEGER;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS expiry_date DATE;
ALTER TABLE assets ADD COLUMN IF NOT EXISTS batch_number VARCHAR(100);

-- 添加字段注释
COMMENT ON COLUMN assets.security_device_type IS '安防设备类型';
COMMENT ON COLUMN assets.video_resolution IS '视频分辨率';
COMMENT ON COLUMN assets.recording_capacity IS '录像容量(GB)';
COMMENT ON COLUMN assets.night_vision IS '是否支持夜视';
COMMENT ON COLUMN assets.motion_detection IS '是否支持动态检测';
COMMENT ON COLUMN assets.audio_support IS '是否支持音频';
COMMENT ON COLUMN assets.ptz_support IS '是否支持云台控制';
COMMENT ON COLUMN assets.weatherproof_rating IS '防护等级';
COMMENT ON COLUMN assets.power_over_ethernet IS '是否支持PoE供电';

COMMENT ON COLUMN assets.is_consumable IS '是否为耗材';
COMMENT ON COLUMN assets.quantity IS '数量';
COMMENT ON COLUMN assets.unit IS '单位';
COMMENT ON COLUMN assets.min_stock_level IS '最低库存';
COMMENT ON COLUMN assets.max_stock_level IS '最高库存';
COMMENT ON COLUMN assets.reorder_point IS '补货点';
COMMENT ON COLUMN assets.expiry_date IS '过期日期';
COMMENT ON COLUMN assets.batch_number IS '批次号';

-- 更新资产状态枚举类型，添加新的状态值
-- 注意：PostgreSQL中修改枚举类型需要特殊处理
DO $$
BEGIN
    -- 检查枚举值是否已存在，如果不存在则添加
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'scrapped' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'assetstatus')) THEN
        ALTER TYPE assetstatus ADD VALUE 'scrapped';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'stored' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'assetstatus')) THEN
        ALTER TYPE assetstatus ADD VALUE 'stored';
    END IF;
EXCEPTION
    WHEN others THEN
        -- 如果枚举类型不存在，可能需要先创建
        RAISE NOTICE '枚举类型可能不存在，请检查数据库结构';
END$$;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_assets_is_consumable ON assets(is_consumable);
CREATE INDEX IF NOT EXISTS idx_assets_security_device_type ON assets(security_device_type);
CREATE INDEX IF NOT EXISTS idx_assets_expiry_date ON assets(expiry_date);
CREATE INDEX IF NOT EXISTS idx_assets_quantity ON assets(quantity);

-- 添加约束检查
ALTER TABLE assets ADD CONSTRAINT IF NOT EXISTS chk_quantity_positive CHECK (quantity IS NULL OR quantity >= 0);
ALTER TABLE assets ADD CONSTRAINT IF NOT EXISTS chk_stock_levels CHECK (
    (min_stock_level IS NULL OR min_stock_level >= 0) AND
    (max_stock_level IS NULL OR max_stock_level >= 0) AND
    (reorder_point IS NULL OR reorder_point >= 0) AND
    (min_stock_level IS NULL OR max_stock_level IS NULL OR min_stock_level <= max_stock_level)
);

COMMIT;