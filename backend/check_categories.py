import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models.asset import AssetCategory
from sqlalchemy.future import select

async def check_categories():
    """检查数据库中的资产分类"""
    async for db in get_db():
        try:
            # 查询所有分类
            result = await db.execute(
                select(AssetCategory.name, AssetCategory.code, AssetCategory.level)
                .order_by(AssetCategory.level, AssetCategory.name)
            )
            categories = result.fetchall()
            
            print(f"数据库中共有 {len(categories)} 个资产分类:")
            print("="*50)
            
            # 按级别分组显示
            current_level = None
            for cat in categories:
                if current_level != cat.level:
                    current_level = cat.level
                    print(f"\n第 {cat.level} 级分类:")
                    print("-" * 30)
                print(f"  {cat.name} ({cat.code})")
            
            # 检查重复的名称
            print("\n\n检查重复的分类名称:")
            print("="*50)
            name_counts = {}
            for cat in categories:
                if cat.name in name_counts:
                    name_counts[cat.name].append(cat.code)
                else:
                    name_counts[cat.name] = [cat.code]
            
            duplicates_found = False
            for name, codes in name_counts.items():
                if len(codes) > 1:
                    print(f"重复分类名称: {name} -> {codes}")
                    duplicates_found = True
            
            if not duplicates_found:
                print("未发现重复的分类名称")
            
            # 检查重复的代码
            print("\n检查重复的分类代码:")
            print("="*50)
            code_counts = {}
            for cat in categories:
                if cat.code in code_counts:
                    code_counts[cat.code].append(cat.name)
                else:
                    code_counts[cat.code] = [cat.name]
            
            duplicates_found = False
            for code, names in code_counts.items():
                if len(names) > 1:
                    print(f"重复分类代码: {code} -> {names}")
                    duplicates_found = True
            
            if not duplicates_found:
                print("未发现重复的分类代码")
                
        except Exception as e:
            print(f"查询失败: {str(e)}")
        break

if __name__ == "__main__":
    asyncio.run(check_categories())