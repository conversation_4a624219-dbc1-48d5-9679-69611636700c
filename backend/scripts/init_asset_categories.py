import asyncio
import logging
import sys
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db, init_db
from models.asset import AssetCategory

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 预定义的资产分类
PREDEFINED_CATEGORIES = [
    # 一级分类 - 硬件设备
    {
        "name": "硬件设备",
        "code": "HW",
        "description": "各类硬件设备资产",
        "level": 1,
        "parent_id": None,
        "is_system": True,
        "attributes": {
            "required": ["serial_number", "manufacturer"],
            "optional": ["warranty_expire_date", "supplier"],
            "codeRule": {"prefix": "HW", "digits": 4}
        },
        "children": [
            # 二级分类 - 服务器
            {
                "name": "服务器",
                "code": "HW-SRV",
                "description": "各类服务器设备",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["cpu_model", "memory_size", "storage_size"],
                    "optional": ["operating_system", "os_version"],
                    "codeRule": {"prefix": "SRV", "digits": 4}
                },
                "children": [
                    # 三级分类 - 物理服务器
                    {
                        "name": "物理服务器",
                        "code": "HW-SRV-PHY",
                        "description": "物理服务器设备",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["rack_location", "power_consumption"],
                            "codeRule": {"prefix": "PSRV", "digits": 4}
                        }
                    },
                    # 三级分类 - 虚拟服务器
                    {
                        "name": "虚拟服务器",
                        "code": "HW-SRV-VM",
                        "description": "虚拟服务器实例",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["host_server", "vm_platform"],
                            "codeRule": {"prefix": "VSRV", "digits": 4}
                        }
                    }
                ]
            },
            # 二级分类 - 网络设备
            {
                "name": "网络设备",
                "code": "HW-NET",
                "description": "各类网络设备",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["ip_address", "mac_address"],
                    "optional": ["firmware_version", "port_count"],
                    "codeRule": {"prefix": "NET", "digits": 4}
                },
                "children": [
                    # 三级分类 - 交换机
                    {
                        "name": "交换机",
                        "code": "HW-NET-SW",
                        "description": "网络交换设备",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["port_speed", "vlan_config"],
                            "codeRule": {"prefix": "SW", "digits": 4}
                        }
                    },
                    # 三级分类 - 路由器
                    {
                        "name": "路由器",
                        "code": "HW-NET-RT",
                        "description": "网络路由设备",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["routing_protocol", "wan_interface"],
                            "codeRule": {"prefix": "RT", "digits": 4}
                        }
                    },
                    # 三级分类 - 防火墙
                    {
                        "name": "防火墙",
                        "code": "HW-NET-FW",
                        "description": "网络安全防火墙",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["security_level", "throughput"],
                            "codeRule": {"prefix": "FW", "digits": 4}
                        }
                    }
                ]
            },
            # 二级分类 - 存储设备
            {
                "name": "存储设备",
                "code": "HW-STR",
                "description": "各类存储设备",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["total_capacity", "raid_level"],
                    "optional": ["connection_type"],
                    "codeRule": {"prefix": "STR", "digits": 4}
                }
            },
            # 二级分类 - 终端设备
            {
                "name": "终端设备",
                "code": "HW-END",
                "description": "各类终端设备",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["user", "department"],
                    "optional": ["screen_size"],
                    "codeRule": {"prefix": "END", "digits": 4}
                },
                "children": [
                    # 三级分类 - 台式电脑
                    {
                        "name": "台式电脑",
                        "code": "HW-END-PC",
                        "description": "台式电脑设备",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["graphics_card", "monitor_model"],
                            "codeRule": {"prefix": "PC", "digits": 4}
                        }
                    },
                    # 三级分类 - 笔记本电脑
                    {
                        "name": "笔记本电脑",
                        "code": "HW-END-LT",
                        "description": "笔记本电脑设备",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["battery_life", "weight"],
                            "codeRule": {"prefix": "LT", "digits": 4}
                        }
                    },
                    # 三级分类 - 打印机
                    {
                        "name": "打印机",
                        "code": "HW-END-PR",
                        "description": "打印设备",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["print_type", "color_support"],
                            "codeRule": {"prefix": "PR", "digits": 4}
                        }
                    }
                ]
            }
        ]
    },
    # 一级分类 - 软件资产
    {
        "name": "软件资产",
        "code": "SW",
        "description": "各类软件资产",
        "level": 1,
        "parent_id": None,
        "is_system": True,
        "attributes": {
            "required": ["license_type", "expiration_date"],
            "optional": ["vendor", "support_contact"],
            "codeRule": {"prefix": "SW", "digits": 4}
        },
        "children": [
            # 二级分类 - 操作系统
            {
                "name": "操作系统",
                "code": "SW-OS",
                "description": "各类操作系统软件",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["version", "edition"],
                    "optional": ["support_end_date"],
                    "codeRule": {"prefix": "OS", "digits": 4}
                }
            },
            # 二级分类 - 数据库软件
            {
                "name": "数据库软件",
                "code": "SW-DB",
                "description": "各类数据库软件",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["version", "instance_count"],
                    "optional": ["clustering_support"],
                    "codeRule": {"prefix": "DB", "digits": 4}
                }
            },
            # 二级分类 - 办公软件
            {
                "name": "办公软件",
                "code": "SW-OFF",
                "description": "各类办公软件",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["version", "user_count"],
                    "optional": ["cloud_enabled"],
                    "codeRule": {"prefix": "OFF", "digits": 4}
                }
            },
            # 二级分类 - 安全软件
            {
                "name": "安全软件",
                "code": "SW-SEC",
                "description": "各类安全软件",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["version", "protection_type"],
                    "optional": ["update_frequency"],
                    "codeRule": {"prefix": "SEC", "digits": 4}
                }
            }
        ]
    },
    # 一级分类 - 基础设施
    {
        "name": "基础设施",
        "code": "INF",
        "description": "IT基础设施资产",
        "level": 1,
        "parent_id": None,
        "is_system": True,
        "attributes": {
            "required": ["location", "installation_date"],
            "optional": ["maintenance_cycle"],
            "codeRule": {"prefix": "INF", "digits": 4}
        },
        "children": [
            # 二级分类 - 机柜
            {
                "name": "机柜",
                "code": "INF-RACK",
                "description": "服务器机柜",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["total_u", "used_u"],
                    "optional": ["power_capacity"],
                    "codeRule": {"prefix": "RACK", "digits": 3}
                }
            },
            # 二级分类 - UPS
            {
                "name": "UPS",
                "code": "INF-UPS",
                "description": "不间断电源",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["capacity", "battery_runtime"],
                    "optional": ["output_type"],
                    "codeRule": {"prefix": "UPS", "digits": 3}
                }
            },
            # 二级分类 - 空调
            {
                "name": "空调",
                "code": "INF-AC",
                "description": "精密空调",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["cooling_capacity", "power_consumption"],
                    "optional": ["humidity_control"],
                    "codeRule": {"prefix": "AC", "digits": 3}
                }
            }
        ]
    },
    # 一级分类 - 消耗品
    {
        "name": "消耗品",
        "code": "CONS",
        "description": "IT消耗品资产",
        "level": 1,
        "parent_id": None,
        "is_system": True,
        "attributes": {
            "required": ["quantity", "unit"],
            "optional": ["min_stock_level", "reorder_level"],
            "codeRule": {"prefix": "CONS", "digits": 4}
        },
        "children": [
            # 二级分类 - 打印耗材
            {
                "name": "打印耗材",
                "code": "CONS-PRN",
                "description": "打印机耗材",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["quantity", "unit"],
                    "optional": ["compatible_models", "color", "page_yield"],
                    "codeRule": {"prefix": "PRN", "digits": 4}
                },
                "children": [
                    {
                        "name": "墨盒",
                        "code": "CONS-PRN-INK",
                        "description": "打印机墨盒",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "color"],
                            "optional": ["compatible_models", "page_yield"],
                            "codeRule": {"prefix": "INK", "digits": 4}
                        }
                    },
                    {
                        "name": "硒鼓",
                        "code": "CONS-PRN-DRUM",
                        "description": "打印机硒鼓",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["compatible_models", "page_yield"],
                            "codeRule": {"prefix": "DRUM", "digits": 4}
                        }
                    },
                    {
                        "name": "碳粉",
                        "code": "CONS-PRN-TONER",
                        "description": "打印机碳粉",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["compatible_models", "color"],
                            "codeRule": {"prefix": "TONER", "digits": 4}
                        }
                    },
                    {
                        "name": "打印纸",
                        "code": "CONS-PRN-PAPER",
                        "description": "各类打印纸张",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "paper_size"],
                            "optional": ["paper_type", "weight"],
                            "codeRule": {"prefix": "PAPER", "digits": 4}
                        }
                    }
                ]
            },
            # 二级分类 - 网络耗材
            {
                "name": "网络耗材",
                "code": "CONS-NET",
                "description": "网络设备配件耗材",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["quantity", "unit"],
                    "optional": ["specifications", "compatibility"],
                    "codeRule": {"prefix": "NCON", "digits": 4}
                },
                "children": [
                    {
                        "name": "内存条",
                        "code": "CONS-NET-RAM",
                        "description": "计算机内存条",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "memory_size", "memory_type"],
                            "optional": ["frequency", "brand"],
                            "codeRule": {"prefix": "RAM", "digits": 4}
                        }
                    },
                    {
                        "name": "硬盘",
                        "code": "CONS-NET-HDD",
                        "description": "存储硬盘",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "storage_size", "storage_type"],
                            "optional": ["interface_type", "rpm"],
                            "codeRule": {"prefix": "HDD", "digits": 4}
                        }
                    },
                    {
                        "name": "CPU处理器",
                        "code": "CONS-NET-CPU",
                        "description": "中央处理器",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "cpu_model"],
                            "optional": ["cpu_cores", "cpu_frequency", "socket_type"],
                            "codeRule": {"prefix": "CPU", "digits": 4}
                        }
                    },
                    {
                        "name": "移动硬盘",
                        "code": "CONS-NET-PORTABLE",
                        "description": "便携式移动硬盘",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "storage_size"],
                            "optional": ["interface_type", "brand"],
                            "codeRule": {"prefix": "PORTABLE", "digits": 4}
                        }
                    },
                    {
                        "name": "转接头",
                        "code": "CONS-NET-ADAPTER",
                        "description": "各类转接头配件",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "connector_type"],
                            "optional": ["input_type", "output_type"],
                            "codeRule": {"prefix": "ADAPTER", "digits": 4}
                        }
                    },
                    {
                        "name": "其他配件",
                        "code": "CONS-NET-OTHER",
                        "description": "其他网络设备配件",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["specifications", "compatibility"],
                            "codeRule": {"prefix": "NETOTHER", "digits": 4}
                        }
                    }
                ]
            },
            # 二级分类 - 办公耗材
            {
                "name": "办公耗材",
                "code": "CONS-OFF",
                "description": "办公用品耗材",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["quantity", "unit"],
                    "optional": ["specifications", "brand"],
                    "codeRule": {"prefix": "OFF", "digits": 4}
                },
                "children": [
                    {
                        "name": "网线",
                        "code": "CONS-OFF-CABLE",
                        "description": "网络连接线缆",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "cable_type", "length"],
                            "optional": ["category", "shielding"],
                            "codeRule": {"prefix": "CABLE", "digits": 4}
                        }
                    },
                    {
                        "name": "标签",
                        "code": "CONS-OFF-LABEL",
                        "description": "各类标签贴纸",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit", "label_size"],
                            "optional": ["material", "adhesive_type"],
                            "codeRule": {"prefix": "LABEL", "digits": 4}
                        }
                    },
                    {
                        "name": "书包",
                        "code": "CONS-OFF-BAG",
                        "description": "办公用书包背包",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["material", "size", "color"],
                            "codeRule": {"prefix": "BAG", "digits": 4}
                        }
                    },
                    {
                        "name": "鼠标垫",
                        "code": "CONS-OFF-MOUSEPAD",
                        "description": "电脑鼠标垫",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["material", "size", "thickness"],
                            "codeRule": {"prefix": "MOUSEPAD", "digits": 4}
                        }
                    },
                    {
                        "name": "文具用品",
                        "code": "CONS-OFF-STATIONERY",
                        "description": "各类文具用品",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["specifications", "brand"],
                            "codeRule": {"prefix": "STAT", "digits": 4}
                        }
                    },
                    {
                        "name": "清洁用品",
                        "code": "CONS-OFF-CLEAN",
                        "description": "办公设备清洁用品",
                        "level": 3,
                        "is_system": True,
                        "attributes": {
                            "required": ["quantity", "unit"],
                            "optional": ["usage_type", "brand"],
                            "codeRule": {"prefix": "CLEAN", "digits": 4}
                        }
                    }
                ]
            }
        ]
    },
    # 一级分类 - 合同资产
    {
        "name": "合同资产",
        "code": "CONTRACT",
        "description": "各类服务合同资产",
        "level": 1,
        "parent_id": None,
        "is_system": True,
        "attributes": {
            "required": ["contract_number", "supplier", "start_date", "end_date"],
            "optional": ["renewal_option", "sla_requirements"],
            "codeRule": {"prefix": "CON", "digits": 4}
        },
        "children": [
            # 二级分类 - 软件许可合同
            {
                "name": "软件许可合同",
                "code": "CONTRACT-SW",
                "description": "软件许可服务合同",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["license_count", "software_name"],
                    "optional": ["upgrade_rights"],
                    "codeRule": {"prefix": "SWCON", "digits": 4}
                }
            },
            # 二级分类 - 云服务合同
            {
                "name": "云服务合同",
                "code": "CONTRACT-CLOUD",
                "description": "云计算服务合同",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["service_type", "resource_quota"],
                    "optional": ["auto_scaling"],
                    "codeRule": {"prefix": "CLOUD", "digits": 4}
                }
            },
            # 二级分类 - 技术支持合同
            {
                "name": "技术支持合同",
                "code": "CONTRACT-SUPPORT",
                "description": "技术支持服务合同",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["support_level", "response_time"],
                    "optional": ["on_site_support"],
                    "codeRule": {"prefix": "SUPP", "digits": 4}
                }
            }
        ]
    },
    # 一级分类 - 维保资产
    {
        "name": "维保资产",
        "code": "MAINT",
        "description": "设备维护保养合同",
        "level": 1,
        "parent_id": None,
        "is_system": True,
        "attributes": {
            "required": ["service_provider", "covered_assets", "maintenance_frequency"],
            "optional": ["emergency_response", "parts_included"],
            "codeRule": {"prefix": "MAINT", "digits": 4}
        },
        "children": [
            # 二级分类 - 硬件维保
            {
                "name": "硬件维保",
                "code": "MAINT-HW",
                "description": "硬件设备维护保养",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["equipment_type", "warranty_level"],
                    "optional": ["replacement_guarantee"],
                    "codeRule": {"prefix": "HWMNT", "digits": 4}
                }
            },
            # 二级分类 - 软件维保
            {
                "name": "软件维保",
                "code": "MAINT-SW",
                "description": "软件系统维护保养",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["software_name", "update_included"],
                    "optional": ["customization_support"],
                    "codeRule": {"prefix": "SWMNT", "digits": 4}
                }
            },
            # 二级分类 - 基础设施维保
            {
                "name": "基础设施维保",
                "code": "MAINT-INF",
                "description": "基础设施维护保养",
                "level": 2,
                "is_system": True,
                "attributes": {
                    "required": ["facility_type", "preventive_maintenance"],
                    "optional": ["environmental_monitoring"],
                    "codeRule": {"prefix": "INFMNT", "digits": 4}
                }
            }
        ]
    }
]

async def create_category(db: AsyncSession, category_data: dict, parent_id=None):
    """创建资产分类及其子分类"""
    try:
        # 复制分类数据并移除children字段
        category_dict = category_data.copy()
        children = category_dict.pop('children', [])
        
        # 设置父分类ID
        if parent_id is not None:
            category_dict['parent_id'] = parent_id
        
        # 检查分类是否已存在
        result = await db.execute(
            select(AssetCategory).filter(
                AssetCategory.code == category_dict['code']
            )
        )
        existing_category = result.scalars().first()
        
        if existing_category:
            logger.info(f"分类已存在: {category_dict['name']} ({category_dict['code']})")
            category_id = existing_category.id
        else:
            # 创建新分类
            new_category = AssetCategory(**category_dict)
            db.add(new_category)
            await db.commit()
            await db.refresh(new_category)
            category_id = new_category.id
            logger.info(f"创建分类: {category_dict['name']} ({category_dict['code']})")
        
        # 递归创建子分类
        for child in children:
            await create_category(db, child, category_id)
            
    except IntegrityError as e:
        await db.rollback()
        logger.error(f"创建分类失败 {category_dict['name']}: {str(e)}")
    except Exception as e:
        await db.rollback()
        logger.error(f"创建分类时发生错误: {str(e)}")

async def init_asset_categories():
    """初始化资产分类"""
    logger.info("开始初始化资产分类...")
    
    # 确保数据库已初始化
    await init_db()
    
    # 获取数据库会话
    async for db in get_db():
        try:
            # 检查是否已有分类
            result = await db.execute(select(AssetCategory))
            existing_categories = result.scalars().all()
            
            if existing_categories:
                logger.info(f"数据库中已有 {len(existing_categories)} 个资产分类")
            
            # 创建预定义分类
            for category_data in PREDEFINED_CATEGORIES:
                await create_category(db, category_data)
                
            logger.info("资产分类初始化完成")
            break
        except Exception as e:
            logger.error(f"初始化资产分类失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(init_asset_categories())