import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from datetime import datetime

from database import get_db
from models.monitoring import MonitoringDevice

# 示例环境监控设备配置
ENVIRONMENT_DEVICES = [
    {
        "device_id": "env-dc-3f",
        "name": "数据中心环境监控",
        "device_type": "environment",
        "location": "数据中心",
        "floor": "3F",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "temperature": "*******.4.1.318.********.2.3.0",  # 温度OID
            "humidity": "*******.4.1.318.********.2.4.0",     # 湿度OID
            "smoke": "*******.4.1.318.********.2.5.0",        # 烟感OID
            "water": "*******.4.1.318.********.2.6.0"         # 水浸OID
        }),
        "status": "active"
    },
    {
        "device_id": "env-weak-2f",
        "name": "2楼弱电间环境监控",
        "device_type": "environment",
        "location": "弱电间",
        "floor": "2F",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "temperature": "*******.4.1.318.********.2.3.0",
            "humidity": "*******.4.1.318.********.2.4.0",
            "smoke": "*******.4.1.318.********.2.5.0",
            "water": "*******.4.1.318.********.2.6.0"
        }),
        "status": "active"
    },
    {
        "device_id": "env-weak-4f",
        "name": "4楼弱电间环境监控",
        "device_type": "environment",
        "location": "弱电间",
        "floor": "4F",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "temperature": "*******.4.1.318.********.2.3.0",
            "humidity": "*******.4.1.318.********.2.4.0",
            "smoke": "*******.4.1.318.********.2.5.0",
            "water": "*******.4.1.318.********.2.6.0"
        }),
        "status": "active"
    },
    {
        "device_id": "env-weak-7f",
        "name": "7楼弱电间环境监控",
        "device_type": "environment",
        "location": "弱电间",
        "floor": "7F",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "temperature": "*******.4.1.318.********.2.3.0",
            "humidity": "*******.4.1.318.********.2.4.0",
            "smoke": "*******.4.1.318.********.2.5.0",
            "water": "*******.4.1.318.********.2.6.0"
        }),
        "status": "active"
    }
]

# 示例UPS设备配置
UPS_DEVICES = [
    {
        "device_id": "ups-01",
        "name": "UPS-01",
        "device_type": "ups",
        "location": "1楼机房",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "load": "*******.4.1.318.*******.2.3.0",              # 负载百分比
            "battery_level": "*******.4.1.318.*******.2.1.0",     # 电池电量百分比
            "battery_time_remaining": "*******.4.1.318.*******.2.3.0",  # 电池剩余时间(分钟)
            "input_voltage": "*******.4.1.318.*******.2.1.0",     # 输入电压
            "output_voltage": "*******.4.1.318.*******.2.1.0",    # 输出电压
            "input_frequency": "*******.4.1.318.*******.2.4.0",   # 输入频率
            "output_frequency": "*******.4.1.318.*******.2.2.0",  # 输出频率
            "temperature": "*******.4.1.318.*******.2.2.0",       # 温度
            "battery_voltage": "*******.4.1.318.*******.2.8.0"    # 电池电压
        }),
        "status": "active"
    },
    {
        "device_id": "ups-02",
        "name": "UPS-02",
        "device_type": "ups",
        "location": "2楼机房",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "load": "*******.4.1.318.*******.2.3.0",
            "battery_level": "*******.4.1.318.*******.2.1.0",
            "battery_time_remaining": "*******.4.1.318.*******.2.3.0",
            "input_voltage": "*******.4.1.318.*******.2.1.0",
            "output_voltage": "*******.4.1.318.*******.2.1.0",
            "input_frequency": "*******.4.1.318.*******.2.4.0",
            "output_frequency": "*******.4.1.318.*******.2.2.0",
            "temperature": "*******.4.1.318.*******.2.2.0",
            "battery_voltage": "*******.4.1.318.*******.2.8.0"
        }),
        "status": "active"
    }
]

# 示例市电监控设备配置
MAINS_DEVICES = [
    {
        "device_id": "mains-01",
        "name": "市电监控-01",
        "device_type": "mains",
        "location": "配电室",
        "protocol": "snmp",
        "host": "*************",
        "port": 161,
        "community": "public",
        "version": 2,
        "oids": json.dumps({
            "voltage": "*******.4.1.318.*******.2.1.0",        # 电压
            "frequency": "*******.4.1.318.*******.2.4.0",      # 频率
            "current": "*******.4.1.318.*******.2.2.0",        # 电流
            "power": "*******.4.1.318.*******.2.3.0",          # 功率
            "power_factor": "*******.4.1.318.*******.2.5.0",   # 功率因数
            "energy_consumption": "*******.4.1.318.*******.2.6.0"  # 能耗
        }),
        "status": "active"
    }
]

async def add_monitoring_devices():
    """添加示例监控设备配置"""
    try:
        async for db in get_db():
            # 添加环境监控设备
            for device_config in ENVIRONMENT_DEVICES:
                # 检查设备是否已存在
                result = await db.execute(
                    select(MonitoringDevice).filter(MonitoringDevice.device_id == device_config["device_id"])
                )
                existing_device = result.scalars().first()
                
                if not existing_device:
                    device = MonitoringDevice(**device_config)
                    db.add(device)
                    print(f"添加环境监控设备: {device_config['name']}")
            
            # 添加UPS设备
            for device_config in UPS_DEVICES:
                # 检查设备是否已存在
                result = await db.execute(
                    select(MonitoringDevice).filter(MonitoringDevice.device_id == device_config["device_id"])
                )
                existing_device = result.scalars().first()
                
                if not existing_device:
                    device = MonitoringDevice(**device_config)
                    db.add(device)
                    print(f"添加UPS设备: {device_config['name']}")
            
            # 添加市电监控设备
            for device_config in MAINS_DEVICES:
                # 检查设备是否已存在
                result = await db.execute(
                    select(MonitoringDevice).filter(MonitoringDevice.device_id == device_config["device_id"])
                )
                existing_device = result.scalars().first()
                
                if not existing_device:
                    device = MonitoringDevice(**device_config)
                    db.add(device)
                    print(f"添加市电监控设备: {device_config['name']}")
            
            # 提交事务
            await db.commit()
            print("所有设备添加完成")
            break
    
    except Exception as e:
        print(f"添加监控设备失败: {e}")

if __name__ == "__main__":
    asyncio.run(add_monitoring_devices())
