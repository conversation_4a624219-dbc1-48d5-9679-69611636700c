#!/usr/bin/env python3
import psycopg2

try:
    # 连接数据库
    conn = psycopg2.connect(
        host='localhost',
        port=5432,
        user='user_PwWEyE',
        password='password_QM8NyB',
        database='asset_management'
    )
    cur = conn.cursor()
    
    # 检查assets表是否存在
    cur.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'assets'
        );
    """)
    table_exists = cur.fetchone()[0]
    print(f"Assets表是否存在: {table_exists}")
    
    if table_exists:
        # 获取assets表的所有字段
        cur.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'assets'
            ORDER BY ordinal_position;
        """)
        columns = cur.fetchall()
        print(f"\nAssets表字段总数: {len(columns)}")
        print("\n所有字段:")
        for col in columns:
            print(f"  {col[0]} ({col[1]}) - 可空: {col[2]}")
        
        # 检查IT网络资产相关字段
        it_fields = ['ip_address', 'mac_address', 'cpu_model', 'cpu_cores', 'memory_size', 
                    'port_count', 'operating_system', 'firmware_version', 'security_level']
        
        print("\nIT网络资产字段检查:")
        for field in it_fields:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'assets' AND column_name = %s
                );
            """, (field,))
            exists = cur.fetchone()[0]
            print(f"  {field}: {'✓' if exists else '✗'}")
    
    conn.close()
    print("\n数据库连接已关闭")
    
except Exception as e:
    print(f"错误: {e}")