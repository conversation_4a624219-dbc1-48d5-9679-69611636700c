from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn

from database import init_db
from api import phone_extensions, assets, monitoring, snmp_items, snmp_data, snmp_config
from services.monitoring_service import get_monitoring_service
from services.snmp_collection_service import get_snmp_collection_service

app = FastAPI(
    title="RS Asset Management System",
    description="燃石资产管理系统 API",
    version="1.0.0"
)

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化数据库
    await init_db()

    # 启动监控服务
    monitoring_service = get_monitoring_service()
    await monitoring_service.start()

    # 启动SNMP采集服务
    snmp_service = get_snmp_collection_service()
    await snmp_service.start()

    yield

    # 关闭时的清理操作
    await monitoring_service.stop()
    await snmp_service.stop()

# 设置 lifespan
app.router.lifespan_context = lifespan

# 注册路由
app.include_router(phone_extensions.router)
app.include_router(assets.router)
app.include_router(monitoring.router)
app.include_router(snmp_items.router)
app.include_router(snmp_data.router)
app.include_router(snmp_config.router)

@app.get("/")
async def root():
    return {
        "message": "Welcome to RS Asset Management System API",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)