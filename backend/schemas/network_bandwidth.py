from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class NetworkDeviceType(str, Enum):
    """网络设备类型枚举"""
    ROUTER = "router"
    SWITCH = "switch"
    FIREWALL = "firewall"
    LOAD_BALANCER = "load_balancer"
    WIRELESS_AP = "wireless_ap"
    GATEWAY = "gateway"
    OTHER = "other"


class InterfaceType(str, Enum):
    """接口类型枚举"""
    ETHERNET = "ethernet"
    FAST_ETHERNET = "fast_ethernet"
    GIGABIT_ETHERNET = "gigabit_ethernet"
    TEN_GIGABIT_ETHERNET = "ten_gigabit_ethernet"
    FIBER = "fiber"
    WIRELESS = "wireless"
    LOOPBACK = "loopback"
    VLAN = "vlan"
    TUNNEL = "tunnel"
    OTHER = "other"


# 网络设备模型
class NetworkDeviceBase(BaseModel):
    """网络设备基础模型"""
    name: str
    model: Optional[str] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    location: Optional[str] = None
    ip_address: str
    mac_address: Optional[str] = None
    device_type: str
    
    # SNMP配置
    snmp_enabled: bool = True
    snmp_community: str = "public"
    snmp_version: int = 2
    snmp_port: int = 161


class NetworkDeviceCreate(NetworkDeviceBase):
    """创建网络设备模型"""
    pass


class NetworkDeviceUpdate(BaseModel):
    """更新网络设备模型"""
    name: Optional[str] = None
    model: Optional[str] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    location: Optional[str] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    device_type: Optional[str] = None
    
    # SNMP配置
    snmp_enabled: Optional[bool] = None
    snmp_community: Optional[str] = None
    snmp_version: Optional[int] = None
    snmp_port: Optional[int] = None
    
    # 状态信息
    status: Optional[str] = None
    firmware_version: Optional[str] = None
    uptime: Optional[int] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    temperature: Optional[float] = None


class NetworkDeviceResponse(NetworkDeviceBase):
    """网络设备响应模型"""
    id: int
    status: str = "offline"
    firmware_version: Optional[str] = None
    uptime: Optional[int] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    temperature: Optional[float] = None
    
    # 时间戳
    last_seen: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 网络接口模型
class NetworkInterfaceBase(BaseModel):
    """网络接口基础模型"""
    name: str
    description: Optional[str] = None
    interface_type: Optional[str] = None
    mac_address: Optional[str] = None
    ip_address: Optional[str] = None
    subnet_mask: Optional[str] = None
    speed: Optional[int] = None
    duplex: Optional[str] = None
    admin_status: Optional[bool] = None
    operational_status: Optional[bool] = None
    mtu: Optional[int] = None
    vlan: Optional[int] = None


class NetworkInterfaceCreate(NetworkInterfaceBase):
    """创建网络接口模型"""
    pass


class NetworkInterfaceUpdate(BaseModel):
    """更新网络接口模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    interface_type: Optional[str] = None
    mac_address: Optional[str] = None
    ip_address: Optional[str] = None
    subnet_mask: Optional[str] = None
    speed: Optional[int] = None
    duplex: Optional[str] = None
    admin_status: Optional[bool] = None
    operational_status: Optional[bool] = None
    mtu: Optional[int] = None
    vlan: Optional[int] = None


class NetworkInterfaceResponse(NetworkInterfaceBase):
    """网络接口响应模型"""
    id: int
    device_id: int
    
    # 流量统计
    in_octets: Optional[int] = None
    out_octets: Optional[int] = None
    in_packets: Optional[int] = None
    out_packets: Optional[int] = None
    in_errors: Optional[int] = None
    out_errors: Optional[int] = None
    in_discards: Optional[int] = None
    out_discards: Optional[int] = None
    
    # 时间戳
    last_updated: Optional[datetime] = None
    
    class Config:
        orm_mode = True


# 带宽记录模型
class BandwidthRecordBase(BaseModel):
    """带宽记录基础模型"""
    timestamp: datetime = Field(default_factory=datetime.now)
    in_octets: int
    out_octets: int
    in_packets: int
    out_packets: int
    in_errors: int
    out_errors: int
    in_discards: int
    out_discards: int
    in_bandwidth: float
    out_bandwidth: float
    in_utilization: float
    out_utilization: float


class BandwidthRecordCreate(BandwidthRecordBase):
    """创建带宽记录模型"""
    device_id: int
    interface_id: int


class BandwidthRecordResponse(BandwidthRecordBase):
    """带宽记录响应模型"""
    id: int
    device_id: int
    interface_id: int
    
    class Config:
        orm_mode = True


# 流量阈值模型
class TrafficThresholdBase(BaseModel):
    """流量阈值基础模型"""
    name: str
    description: Optional[str] = None
    device_id: Optional[int] = None
    interface_id: Optional[int] = None
    
    # 阈值设置
    in_bandwidth_warning: Optional[float] = None
    in_bandwidth_critical: Optional[float] = None
    out_bandwidth_warning: Optional[float] = None
    out_bandwidth_critical: Optional[float] = None
    in_utilization_warning: Optional[float] = None
    in_utilization_critical: Optional[float] = None
    out_utilization_warning: Optional[float] = None
    out_utilization_critical: Optional[float] = None
    
    # 通知设置
    notify_email: bool = True
    notify_sms: bool = False
    notify_webhook: bool = False
    notification_cooldown: int = 3600


class TrafficThresholdCreate(TrafficThresholdBase):
    """创建流量阈值模型"""
    pass


class TrafficThresholdUpdate(BaseModel):
    """更新流量阈值模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    device_id: Optional[int] = None
    interface_id: Optional[int] = None
    
    # 阈值设置
    in_bandwidth_warning: Optional[float] = None
    in_bandwidth_critical: Optional[float] = None
    out_bandwidth_warning: Optional[float] = None
    out_bandwidth_critical: Optional[float] = None
    in_utilization_warning: Optional[float] = None
    in_utilization_critical: Optional[float] = None
    out_utilization_warning: Optional[float] = None
    out_utilization_critical: Optional[float] = None
    
    # 通知设置
    notify_email: Optional[bool] = None
    notify_sms: Optional[bool] = None
    notify_webhook: Optional[bool] = None
    notification_cooldown: Optional[int] = None


class TrafficThresholdResponse(TrafficThresholdBase):
    """流量阈值响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True
