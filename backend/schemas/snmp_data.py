from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# SNMP数据基础模型
class SNMPDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    item_id: int = Field(..., description="监控项ID")
    key: str = Field(..., description="键值")
    value: str = Field(..., description="值")
    value_type: str = Field(..., description="值类型")
    unit: Optional[str] = Field(None, description="单位")
    item_type: str = Field(..., description="监控项类型")
    location: str = Field(..., description="位置")
    position: Optional[str] = Field(None, description="具体位置")


class SNMPDataCreate(SNMPDataBase):
    pass


class SNMPDataResponse(SNMPDataBase):
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# 网络设备数据基础模型
class NetworkDeviceDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    name: str = Field(..., description="设备名称")
    location: str = Field(..., description="位置")
    status: str = Field(..., description="状态")
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    temperature: Optional[float] = Field(None, description="温度")
    uptime: Optional[int] = Field(None, description="运行时间(秒)")
    interfaces_count: int = Field(..., description="接口数量")
    interfaces_up: int = Field(..., description="启用接口数量")
    interfaces_data: Optional[str] = Field(None, description="接口数据(JSON)")
    bandwidth_in: float = Field(..., description="入站带宽(Mbps)")
    bandwidth_out: float = Field(..., description="出站带宽(Mbps)")


class NetworkDeviceDataCreate(NetworkDeviceDataBase):
    pass


class NetworkDeviceDataResponse(NetworkDeviceDataBase):
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# 打印机数据基础模型
class PrinterDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    name: str = Field(..., description="打印机名称")
    location: str = Field(..., description="位置")
    status: str = Field(..., description="状态")
    model: str = Field(..., description="型号")
    serial_number: str = Field(..., description="序列号")
    toner_black: float = Field(..., description="黑色墨粉剩余百分比")
    toner_cyan: Optional[float] = Field(None, description="青色墨粉剩余百分比")
    toner_magenta: Optional[float] = Field(None, description="品红色墨粉剩余百分比")
    toner_yellow: Optional[float] = Field(None, description="黄色墨粉剩余百分比")
    pages_total: int = Field(..., description="总打印页数")
    pages_since_last: Optional[int] = Field(None, description="上次统计后打印页数")
    error_state: Optional[str] = Field(None, description="错误状态")
    warning_state: Optional[str] = Field(None, description="警告状态")


class PrinterDataCreate(PrinterDataBase):
    pass


class PrinterDataResponse(PrinterDataBase):
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# 批量查询参数
class SNMPDataQueryParams(BaseModel):
    device_id: Optional[str] = None
    item_id: Optional[int] = None
    key: Optional[str] = None
    item_type: Optional[str] = None
    location: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = 100
    offset: int = 0


# 历史数据统计参数
class SNMPDataStatsParams(BaseModel):
    device_id: str
    key: str
    start_time: datetime
    end_time: datetime
    interval: str = "hour"  # hour, day, week, month


# 历史数据统计结果
class SNMPDataStatsResult(BaseModel):
    timestamp: datetime
    min_value: float
    max_value: float
    avg_value: float
    count: int
