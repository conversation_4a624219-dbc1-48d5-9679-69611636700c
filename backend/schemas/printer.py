from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class PrinterStatus(str, Enum):
    """打印机状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    WARNING = "warning"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class PrinterType(str, Enum):
    """打印机类型枚举"""
    LASER = "laser"
    INKJET = "inkjet"
    DOT_MATRIX = "dot_matrix"
    THERMAL = "thermal"
    MULTIFUNCTION = "multifunction"
    PLOTTER = "plotter"
    LABEL = "label"
    OTHER = "other"


class ConsumableType(str, Enum):
    """耗材类型枚举"""
    TONER_BLACK = "toner_black"
    TONER_CYAN = "toner_cyan"
    TONER_MAGENTA = "toner_magenta"
    TONER_YELLOW = "toner_yellow"
    INK_BLACK = "ink_black"
    INK_CYAN = "ink_cyan"
    INK_MAGENTA = "ink_magenta"
    INK_YELLOW = "ink_yellow"
    DRUM = "drum"
    MAINTENANCE_KIT = "maintenance_kit"
    WASTE_TONER = "waste_toner"
    FUSER = "fuser"
    TRANSFER_BELT = "transfer_belt"
    OTHER = "other"


class MaintenanceType(str, Enum):
    """维护类型枚举"""
    SCHEDULED = "scheduled"
    REPAIR = "repair"
    CONSUMABLE_REPLACEMENT = "consumable_replacement"
    CLEANING = "cleaning"
    CALIBRATION = "calibration"
    OTHER = "other"


# 打印机模型
class PrinterBase(BaseModel):
    """打印机基础模型"""
    name: str
    model: str
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    location: Optional[str] = None
    department: Optional[str] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    printer_type: str
    
    # SNMP配置
    snmp_enabled: bool = True
    snmp_community: str = "public"
    snmp_version: int = 2
    snmp_port: int = 161
    
    # 打印机属性
    is_color: bool = False
    is_duplex: bool = False
    paper_sizes: Optional[str] = None
    print_speed: Optional[int] = None
    resolution: Optional[str] = None


class PrinterCreate(PrinterBase):
    """创建打印机模型"""
    pass


class PrinterUpdate(BaseModel):
    """更新打印机模型"""
    name: Optional[str] = None
    model: Optional[str] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    location: Optional[str] = None
    department: Optional[str] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    printer_type: Optional[str] = None
    
    # SNMP配置
    snmp_enabled: Optional[bool] = None
    snmp_community: Optional[str] = None
    snmp_version: Optional[int] = None
    snmp_port: Optional[int] = None
    
    # 打印机属性
    is_color: Optional[bool] = None
    is_duplex: Optional[bool] = None
    paper_sizes: Optional[str] = None
    print_speed: Optional[int] = None
    resolution: Optional[str] = None
    
    # 状态信息
    status: Optional[str] = None
    error_message: Optional[str] = None
    warning_message: Optional[str] = None


class PrinterResponse(PrinterBase):
    """打印机响应模型"""
    id: int
    status: str = "offline"
    
    # 耗材信息
    toner_black_level: Optional[float] = None
    toner_cyan_level: Optional[float] = None
    toner_magenta_level: Optional[float] = None
    toner_yellow_level: Optional[float] = None
    drum_level: Optional[float] = None
    maintenance_kit_level: Optional[float] = None
    
    # 计数器
    total_pages: Optional[int] = None
    color_pages: Optional[int] = None
    mono_pages: Optional[int] = None
    
    # 状态信息
    error_message: Optional[str] = None
    warning_message: Optional[str] = None
    
    # 时间戳
    last_seen: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


# 耗材模型
class ConsumableBase(BaseModel):
    """耗材基础模型"""
    consumable_type: str
    model_number: Optional[str] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    level: Optional[float] = None
    capacity: Optional[int] = None
    pages_printed: Optional[int] = None
    install_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None


class ConsumableCreate(ConsumableBase):
    """创建耗材模型"""
    pass


class ConsumableUpdate(BaseModel):
    """更新耗材模型"""
    consumable_type: Optional[str] = None
    model_number: Optional[str] = None
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    level: Optional[float] = None
    capacity: Optional[int] = None
    pages_printed: Optional[int] = None
    install_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None


class ConsumableResponse(ConsumableBase):
    """耗材响应模型"""
    id: int
    printer_id: int
    
    class Config:
        orm_mode = True


# 维护记录模型
class MaintenanceBase(BaseModel):
    """维护记录基础模型"""
    maintenance_type: str
    description: Optional[str] = None
    performed_by: str
    cost: Optional[float] = None
    parts_replaced: Optional[Dict[str, Any]] = None
    maintenance_date: datetime = Field(default_factory=datetime.now)
    next_maintenance_date: Optional[datetime] = None


class MaintenanceCreate(MaintenanceBase):
    """创建维护记录模型"""
    pass


class MaintenanceResponse(MaintenanceBase):
    """维护记录响应模型"""
    id: int
    printer_id: int
    
    class Config:
        orm_mode = True
