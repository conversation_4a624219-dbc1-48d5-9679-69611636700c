from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum
import re
from models.asset import AssetStatus, LifecycleStatus, DepreciationMethod, StorageType, SecurityLevel



# 资产分类相关模式
class AssetCategoryBase(BaseModel):
    name: str = Field(..., description="分类名称")
    code: str = Field(..., description="分类编码")
    description: Optional[str] = Field(None, description="分类描述")
    level: int = Field(1, description="分类层级")
    parent_id: Optional[int] = Field(None, description="父分类ID")
    is_system: bool = Field(False, description="是否系统预设")
    custom_fields: Optional[dict] = Field(None, description="自定义字段配置")

class AssetCategoryCreate(AssetCategoryBase):
    pass

class AssetCategoryUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    level: Optional[int] = None
    parent_id: Optional[int] = None
    custom_fields: Optional[dict] = None

class AssetCategoryResponse(AssetCategoryBase):
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# 维护记录模式
class MaintenanceRecordBase(BaseModel):
    asset_id: int
    date: datetime
    type: str  # routine, repair, upgrade
    description: str
    cost: Optional[float] = None
    performed_by: Optional[str] = None
    next_scheduled_date: Optional[datetime] = None

class MaintenanceRecordCreate(MaintenanceRecordBase):
    pass

class MaintenanceRecordResponse(MaintenanceRecordBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

# 资产相关模式
class AssetBase(BaseModel):
    name: str = Field(..., min_length=2, description="资产名称")
    code: str = Field(..., min_length=3, description="资产编码")
    category_id: int = Field(..., description="资产分类ID")
    manufacturer: Optional[str] = Field(None, description="制造商")
    model: Optional[str] = Field(None, description="型号")
    serial_number: Optional[str] = Field(None, description="序列号")
    status: Optional[str] = Field("active", description="资产状态")
    location: Optional[str] = Field(None, description="位置")
    department: Optional[str] = Field(None, description="部门")
    responsible_person: Optional[str] = Field(None, description="负责人")
    description: Optional[str] = Field(None, description="描述")

    # 采购信息
    purchase_date: Optional[datetime] = Field(None, description="采购日期")
    supplier: Optional[str] = Field(None, description="供应商")
    purchase_order_number: Optional[str] = Field(None, description="采购订单号")
    price: Optional[float] = Field(None, ge=0, description="采购价格")
    warranty_expire_date: Optional[datetime] = Field(None, description="保修到期日期")
    asset_value: Optional[float] = Field(None, ge=0, description="资产价值")

    # 生命周期
    lifecycle_status: Optional[str] = Field("planning", description="生命周期状态")

    # 折旧信息
    depreciation_method: Optional[str] = Field(None, description="折旧方法")
    depreciation_period: Optional[int] = Field(None, ge=1, description="折旧年限")
    depreciation_rate: Optional[float] = Field(None, ge=0, description="折旧率")
    
    # IT网络资产专用字段
    ip_address: Optional[str] = Field(None, description="IP地址")
    business_ip_address: Optional[str] = Field(None, description="业务IP地址")
    management_ip_address: Optional[str] = Field(None, description="管理IP地址")
    mac_address: Optional[str] = Field(None, description="MAC地址")
    hostname: Optional[str] = Field(None, description="主机名")
    domain: Optional[str] = Field(None, description="域名")
    subnet_mask: Optional[str] = Field(None, description="子网掩码")
    gateway: Optional[str] = Field(None, description="网关")
    dns_servers: Optional[str] = Field(None, description="DNS服务器")
    vlan_id: Optional[int] = Field(None, ge=1, le=4094, description="VLAN ID")
    
    # 硬件规格
    cpu_model: Optional[str] = Field(None, description="CPU型号")
    cpu_cores: Optional[int] = Field(None, ge=1, description="CPU核心数")
    memory_size: Optional[int] = Field(None, ge=1, description="内存大小(GB)")
    storage_size: Optional[int] = Field(None, ge=1, description="存储大小(GB)")
    storage_type: Optional[str] = Field(None, description="存储类型")
    port_count: Optional[int] = Field(None, ge=1, description="端口数量")
    port_speed: Optional[str] = Field(None, description="端口速度")
    power_consumption: Optional[float] = Field(None, ge=0, description="功耗(W)")
    operating_temperature: Optional[str] = Field(None, description="工作温度")

    # 软件信息
    operating_system: Optional[str] = Field(None, description="操作系统")
    os_version: Optional[str] = Field(None, description="系统版本")
    firmware_version: Optional[str] = Field(None, description="固件版本")
    software_licenses: Optional[Dict[str, Any]] = Field(None, description="软件许可证信息")

    # 安全相关
    security_level: Optional[str] = Field(None, description="安全等级")
    encryption_enabled: Optional[bool] = Field(False, description="是否启用加密")
    access_control: Optional[str] = Field(None, description="访问控制方式")
    
    # 监控和管理
    snmp_community: Optional[str] = Field(None, description="SNMP团体字符串")
    snmp_version: Optional[str] = Field(None, description="SNMP版本")
    monitoring_enabled: bool = Field(False, description="是否启用监控")
    backup_schedule: Optional[str] = Field(None, description="备份计划")
    
    # 物理位置详细信息
    rack_position: Optional[str] = Field(None, description="机架位置")
    rack_unit: Optional[int] = Field(None, description="机架单元(U)")
    cable_management: Optional[str] = Field(None, description="线缆管理信息")
    
    # 环境要求
    power_requirements: Optional[str] = Field(None, description="电源要求")
    cooling_requirements: Optional[str] = Field(None, description="散热要求")
    
    # 合规性
    compliance_standards: Optional[Dict[str, Any]] = Field(None, description="合规标准")
    certification: Optional[str] = Field(None, description="认证信息")
    
    # 自定义属性
    attributes: Optional[Dict[str, Any]] = Field(None, description="自定义属性")
    
    # 标签
    tags: Optional[List[str]] = Field(None, description="标签")
    
    # 安防设备专用字段
    security_device_type: Optional[str] = Field(None, description="安防设备类型")
    video_resolution: Optional[str] = Field(None, description="视频分辨率")
    recording_capacity: Optional[int] = Field(None, ge=0, description="录像容量(GB)")
    night_vision: Optional[bool] = Field(None, description="是否支持夜视")
    motion_detection: Optional[bool] = Field(None, description="是否支持动态检测")
    audio_support: Optional[bool] = Field(None, description="是否支持音频")
    ptz_support: Optional[bool] = Field(None, description="是否支持云台控制")
    weatherproof_rating: Optional[str] = Field(None, description="防护等级")
    power_over_ethernet: Optional[bool] = Field(None, description="是否支持PoE供电")
    
    # 耗材相关字段
    is_consumable: Optional[bool] = Field(False, description="是否为耗材")
    quantity: Optional[int] = Field(None, ge=0, description="数量")
    unit: Optional[str] = Field(None, description="单位")
    min_stock_level: Optional[int] = Field(None, ge=0, description="最低库存")
    max_stock_level: Optional[int] = Field(None, ge=0, description="最高库存")
    reorder_point: Optional[int] = Field(None, ge=0, description="补货点")
    expiry_date: Optional[datetime] = Field(None, description="过期日期")
    batch_number: Optional[str] = Field(None, description="批次号")
    
    @validator('ip_address', 'business_ip_address', 'management_ip_address')
    def validate_ip_address(cls, v):
        if v and v.strip():
            ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
            if not re.match(ip_pattern, v):
                raise ValueError('Invalid IP address format')
        return v
    
    @validator('mac_address')
    def validate_mac_address(cls, v):
        if v and v.strip():
            mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
            if not re.match(mac_pattern, v):
                raise ValueError('Invalid MAC address format')
        return v
    
    @validator('code')
    def validate_code(cls, v):
        if not v or len(v.strip()) < 3:
            raise ValueError('Asset code must be at least 3 characters long')
        return v.strip().upper()

class AssetCreate(AssetBase):
    """创建资产的模式"""
    pass

class AssetUpdate(BaseModel):
    """更新资产的模式"""
    name: Optional[str] = Field(None, min_length=2, description="资产名称")
    code: Optional[str] = Field(None, min_length=3, description="资产编码")
    category_id: Optional[int] = Field(None, description="资产分类ID")
    manufacturer: Optional[str] = Field(None, description="制造商")
    model: Optional[str] = Field(None, description="型号")
    serial_number: Optional[str] = Field(None, description="序列号")
    status: Optional[str] = Field(None, description="资产状态")
    location: Optional[str] = Field(None, description="位置")
    department: Optional[str] = Field(None, description="部门")
    responsible_person: Optional[str] = Field(None, description="负责人")
    description: Optional[str] = Field(None, description="描述")

    # 采购信息
    purchase_date: Optional[datetime] = Field(None, description="采购日期")
    supplier: Optional[str] = Field(None, description="供应商")
    purchase_order_number: Optional[str] = Field(None, description="采购订单号")
    price: Optional[float] = Field(None, ge=0, description="采购价格")
    warranty_expire_date: Optional[datetime] = Field(None, description="保修到期日期")
    asset_value: Optional[float] = Field(None, ge=0, description="资产价值")

    # 生命周期
    lifecycle_status: Optional[str] = Field(None, description="生命周期状态")

    # 折旧信息
    depreciation_method: Optional[str] = Field(None, description="折旧方法")
    depreciation_period: Optional[int] = Field(None, ge=1, description="折旧年限")
    depreciation_rate: Optional[float] = Field(None, ge=0, description="折旧率")
    
    # IT网络资产专用字段
    ip_address: Optional[str] = Field(None, description="IP地址")
    mac_address: Optional[str] = Field(None, description="MAC地址")
    hostname: Optional[str] = Field(None, description="主机名")
    domain: Optional[str] = Field(None, description="域名")
    subnet_mask: Optional[str] = Field(None, description="子网掩码")
    gateway: Optional[str] = Field(None, description="网关")
    dns_servers: Optional[str] = Field(None, description="DNS服务器")
    vlan_id: Optional[int] = Field(None, ge=1, le=4094, description="VLAN ID")
    
    # 硬件规格
    cpu_model: Optional[str] = Field(None, description="CPU型号")
    cpu_cores: Optional[int] = Field(None, ge=1, description="CPU核心数")
    memory_size: Optional[int] = Field(None, ge=1, description="内存大小(GB)")
    storage_size: Optional[int] = Field(None, ge=1, description="存储大小(GB)")
    storage_type: Optional[str] = Field(None, description="存储类型")
    port_count: Optional[int] = Field(None, ge=1, description="端口数量")
    port_speed: Optional[str] = Field(None, description="端口速度")
    power_consumption: Optional[float] = Field(None, ge=0, description="功耗(W)")
    operating_temperature: Optional[str] = Field(None, description="工作温度")

    # 软件信息
    operating_system: Optional[str] = Field(None, description="操作系统")
    os_version: Optional[str] = Field(None, description="系统版本")
    firmware_version: Optional[str] = Field(None, description="固件版本")
    software_licenses: Optional[Dict[str, Any]] = Field(None, description="软件许可证信息")

    # 安全相关
    security_level: Optional[str] = Field(None, description="安全等级")
    encryption_enabled: Optional[bool] = Field(None, description="是否启用加密")
    access_control: Optional[str] = Field(None, description="访问控制方式")
    
    # 监控和管理
    snmp_community: Optional[str] = Field(None, description="SNMP团体字符串")
    snmp_version: Optional[str] = Field(None, description="SNMP版本")
    monitoring_enabled: Optional[bool] = Field(None, description="是否启用监控")
    backup_schedule: Optional[str] = Field(None, description="备份计划")
    
    # 物理位置详细信息
    rack_position: Optional[str] = Field(None, description="机架位置")
    rack_unit: Optional[int] = Field(None, description="机架单元(U)")
    cable_management: Optional[str] = Field(None, description="线缆管理信息")
    
    # 环境要求
    power_requirements: Optional[str] = Field(None, description="电源要求")
    cooling_requirements: Optional[str] = Field(None, description="散热要求")
    
    # 合规性
    compliance_standards: Optional[Dict[str, Any]] = Field(None, description="合规标准")
    certification: Optional[str] = Field(None, description="认证信息")
    
    # 自定义属性
    attributes: Optional[Dict[str, Any]] = Field(None, description="自定义属性")
    
    # 标签
    tags: Optional[List[str]] = Field(None, description="标签")
    
    # 安防设备专用字段
    security_device_type: Optional[str] = Field(None, description="安防设备类型")
    video_resolution: Optional[str] = Field(None, description="视频分辨率")
    recording_capacity: Optional[int] = Field(None, ge=0, description="录像容量(GB)")
    night_vision: Optional[bool] = Field(None, description="是否支持夜视")
    motion_detection: Optional[bool] = Field(None, description="是否支持动态检测")
    audio_support: Optional[bool] = Field(None, description="是否支持音频")
    ptz_support: Optional[bool] = Field(None, description="是否支持云台控制")
    weatherproof_rating: Optional[str] = Field(None, description="防护等级")
    power_over_ethernet: Optional[bool] = Field(None, description="是否支持PoE供电")
    
    # 耗材相关字段
    is_consumable: Optional[bool] = Field(None, description="是否为耗材")
    quantity: Optional[int] = Field(None, ge=0, description="数量")
    unit: Optional[str] = Field(None, description="单位")
    min_stock_level: Optional[int] = Field(None, ge=0, description="最低库存")
    max_stock_level: Optional[int] = Field(None, ge=0, description="最高库存")
    reorder_point: Optional[int] = Field(None, ge=0, description="补货点")
    expiry_date: Optional[datetime] = Field(None, description="过期日期")
    batch_number: Optional[str] = Field(None, description="批次号")
    
    @validator('ip_address')
    def validate_ip_address(cls, v):
        if v and v.strip():
            ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
            if not re.match(ip_pattern, v):
                raise ValueError('Invalid IP address format')
        return v
    
    @validator('mac_address')
    def validate_mac_address(cls, v):
        if v and v.strip():
            mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
            if not re.match(mac_pattern, v):
                raise ValueError('Invalid MAC address format')
        return v
    
    @validator('code')
    def validate_code(cls, v):
        if v and len(v.strip()) < 3:
            raise ValueError('Asset code must be at least 3 characters long')
        return v.strip().upper() if v else v

class AssetResponse(AssetBase):
    """资产响应模式"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    maintenance_records: Optional[List[MaintenanceRecordResponse]] = []

    # 关联的分类信息
    category: Optional[AssetCategoryResponse] = None

    class Config:
        from_attributes = True

# 批量操作相关模式
class BulkImportResult(BaseModel):
    """批量导入结果"""
    created_count: int
    error_count: int
    errors: List[dict]
    created_assets: List[AssetResponse]

class AssetStatistics(BaseModel):
    """资产统计信息"""
    total_assets: int
    status_statistics: dict
    category_statistics: List[dict]
