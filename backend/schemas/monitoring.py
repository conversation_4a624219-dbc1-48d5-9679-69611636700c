from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# 环境监控数据模型
class EnvironmentDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    location: str = Field(..., description="位置")
    floor: str = Field(..., description="楼层")
    temperature: float = Field(..., description="温度")
    humidity: float = Field(..., description="湿度")
    smoke: bool = Field(False, description="烟感")
    water: bool = Field(False, description="水浸")
    status: str = Field(..., description="状态")

    # 冷通道温度数据
    cold_aisle1_temp1: Optional[float] = Field(None, description="冷通道1-温度01")
    cold_aisle1_temp2: Optional[float] = Field(None, description="冷通道1-温度02")
    cold_aisle2_temp1: Optional[float] = Field(None, description="冷通道2-温度01")
    cold_aisle2_temp2: Optional[float] = Field(None, description="冷通道2-温度02")

    # 冷通道湿度数据
    cold_aisle1_humidity1: Optional[float] = Field(None, description="冷通道1-湿度01")
    cold_aisle1_humidity2: Optional[float] = Field(None, description="冷通道1-湿度02")
    cold_aisle2_humidity1: Optional[float] = Field(None, description="冷通道2-湿度01")
    cold_aisle2_humidity2: Optional[float] = Field(None, description="冷通道2-湿度02")


class EnvironmentDataCreate(EnvironmentDataBase):
    pass


class EnvironmentDataResponse(EnvironmentDataBase):
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# UPS监控数据模型
class UPSDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    name: str = Field(..., description="UPS名称")
    location: str = Field(..., description="位置")
    status: str = Field(..., description="状态")
    load: float = Field(..., description="负载百分比")
    battery_level: float = Field(..., description="电池电量百分比")
    battery_time_remaining: int = Field(..., description="电池剩余时间(分钟)")
    input_voltage: float = Field(..., description="输入电压")
    output_voltage: float = Field(..., description="输出电压")
    input_frequency: float = Field(..., description="输入频率")
    output_frequency: float = Field(..., description="输出频率")
    temperature: float = Field(..., description="温度")
    battery_voltage: float = Field(..., description="电池电压")


class UPSDataCreate(UPSDataBase):
    pass


class UPSDataResponse(UPSDataBase):
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# 市电监控数据模型
class MainsPowerDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    location: str = Field(..., description="位置")
    status: str = Field(..., description="状态")
    voltage: float = Field(..., description="电压")
    frequency: float = Field(..., description="频率")
    current: float = Field(..., description="电流")
    power: float = Field(..., description="功率")
    power_factor: float = Field(..., description="功率因数")
    energy_consumption: float = Field(..., description="能耗")


class MainsPowerDataCreate(MainsPowerDataBase):
    pass


class MainsPowerDataResponse(MainsPowerDataBase):
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# 监控设备配置模型
class MonitoringDeviceBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    name: str = Field(..., description="设备名称")
    device_type: str = Field(..., description="设备类型")
    location: str = Field(..., description="位置")
    floor: Optional[str] = Field(None, description="楼层")
    protocol: str = Field(..., description="协议")
    host: str = Field(..., description="主机地址")
    port: int = Field(161, description="端口")
    community: Optional[str] = Field(None, description="SNMP社区")
    version: int = Field(2, description="SNMP版本")
    oids: Optional[str] = Field(None, description="OID配置")
    status: str = Field("active", description="设备状态")


class MonitoringDeviceCreate(MonitoringDeviceBase):
    pass


class MonitoringDeviceUpdate(BaseModel):
    name: Optional[str] = None
    location: Optional[str] = None
    floor: Optional[str] = None
    protocol: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    community: Optional[str] = None
    version: Optional[int] = None
    oids: Optional[str] = None
    status: Optional[str] = None


class MonitoringDeviceResponse(MonitoringDeviceBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


# 历史数据查询参数
class HistoricalDataQuery(BaseModel):
    device_id: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    interval: Optional[str] = "hour"  # hour, day, week, month
    limit: Optional[int] = 100
