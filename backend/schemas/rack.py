from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# Rack Schemas
class RackBase(BaseModel):
    name: str = Field(..., description="机柜名称")
    location: str = Field(..., description="机柜位置")
    total_u: int = Field(..., ge=1, description="机柜总U数")
    description: Optional[str] = Field(None, description="机柜描述")

class RackCreate(RackBase):
    pass

class RackUpdate(BaseModel):
    name: Optional[str] = Field(None, description="机柜名称")
    location: Optional[str] = Field(None, description="机柜位置")
    description: Optional[str] = Field(None, description="机柜描述")

class RackResponse(RackBase):
    id: int
    used_u: int = Field(default=0, description="已使用U数")
    utilization: float = Field(default=0.0, description="使用率(%)")
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# Device Schemas
class DeviceBase(BaseModel):
    name: str = Field(..., description="设备名称")
    model: str = Field(..., description="设备型号")
    position_start: int = Field(..., ge=1, description="起始U位")
    position_end: int = Field(..., ge=1, description="结束U位")
    status: str = Field(..., description="设备状态")
    ip_address: Optional[str] = Field(None, description="IP地址")
    description: Optional[str] = Field(None, description="设备描述")

class DeviceCreate(DeviceBase):
    pass

class DeviceUpdate(BaseModel):
    name: Optional[str] = Field(None, description="设备名称")
    model: Optional[str] = Field(None, description="设备型号")
    status: Optional[str] = Field(None, description="设备状态")
    ip_address: Optional[str] = Field(None, description="IP地址")
    description: Optional[str] = Field(None, description="设备描述")

class DeviceResponse(DeviceBase):
    id: int
    rack_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# Maintenance Record Schemas
class MaintenanceRecordBase(BaseModel):
    maintenance_type: str = Field(..., description="维护类型")
    description: str = Field(..., description="维护描述")
    maintainer: str = Field(..., description="维护人员")
    status: str = Field(..., description="维护状态")
    scheduled_at: Optional[datetime] = Field(None, description="计划维护时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

class MaintenanceRecordCreate(MaintenanceRecordBase):
    pass

class MaintenanceRecordResponse(MaintenanceRecordBase):
    id: int
    device_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True 