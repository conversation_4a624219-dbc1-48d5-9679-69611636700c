from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# 无线设备基础模型
class WirelessDeviceBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    name: str = Field(..., description="设备名称")
    model: Optional[str] = Field(None, description="设备型号")
    manufacturer: Optional[str] = Field(None, description="制造商")
    location: str = Field(..., description="位置")
    floor: str = Field(..., description="楼层")
    ip_address: str = Field(..., description="IP地址")
    mac_address: Optional[str] = Field(None, description="MAC地址")
    
    # SNMP配置
    snmp_enabled: bool = Field(True, description="是否启用SNMP")
    snmp_community: str = Field("public", description="SNMP社区")
    snmp_version: int = Field(2, description="SNMP版本")
    snmp_port: int = Field(161, description="SNMP端口")
    
    # 设备位置坐标
    position_x: Optional[float] = Field(None, description="X坐标")
    position_y: Optional[float] = Field(None, description="Y坐标")


class WirelessDeviceCreate(WirelessDeviceBase):
    """创建无线设备请求"""
    pass


class WirelessDeviceUpdate(BaseModel):
    """更新无线设备请求"""
    name: Optional[str] = None
    model: Optional[str] = None
    manufacturer: Optional[str] = None
    location: Optional[str] = None
    floor: Optional[str] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    snmp_enabled: Optional[bool] = None
    snmp_community: Optional[str] = None
    snmp_version: Optional[int] = None
    snmp_port: Optional[int] = None
    position_x: Optional[float] = None
    position_y: Optional[float] = None
    status: Optional[str] = None


class WirelessDeviceResponse(WirelessDeviceBase):
    """无线设备响应"""
    id: int
    status: str
    firmware_version: Optional[str] = None
    uptime: Optional[int] = None
    last_seen: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


# 无线设备数据基础模型
class WirelessDeviceDataBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    status: str = Field(..., description="状态")
    cpu_usage: float = Field(..., description="CPU使用率")
    memory_usage: float = Field(..., description="内存使用率")
    temperature: Optional[float] = Field(None, description="温度")
    clients_count: int = Field(..., description="客户端数量")
    clients_2g: Optional[int] = Field(None, description="2.4GHz客户端数量")
    clients_5g: Optional[int] = Field(None, description="5GHz客户端数量")
    channel_2g: Optional[int] = Field(None, description="2.4GHz信道")
    channel_5g: Optional[int] = Field(None, description="5GHz信道")
    signal_strength: Optional[float] = Field(None, description="信号强度")
    noise_level: Optional[float] = Field(None, description="噪声水平")
    tx_power: Optional[float] = Field(None, description="发射功率")
    rx_bytes: Optional[int] = Field(None, description="接收字节数")
    tx_bytes: Optional[int] = Field(None, description="发送字节数")


class WirelessDeviceDataCreate(WirelessDeviceDataBase):
    """创建无线设备数据请求"""
    pass


class WirelessDeviceDataResponse(WirelessDeviceDataBase):
    """无线设备数据响应"""
    id: int
    timestamp: datetime

    class Config:
        orm_mode = True


# 无线客户端基础模型
class WirelessClientBase(BaseModel):
    device_id: str = Field(..., description="设备ID")
    mac_address: str = Field(..., description="MAC地址")
    ip_address: Optional[str] = Field(None, description="IP地址")
    hostname: Optional[str] = Field(None, description="主机名")
    signal_strength: Optional[float] = Field(None, description="信号强度")
    frequency_band: Optional[str] = Field(None, description="频段")
    tx_rate: Optional[float] = Field(None, description="发送速率")
    rx_rate: Optional[float] = Field(None, description="接收速率")
    tx_bytes: Optional[int] = Field(None, description="发送字节数")
    rx_bytes: Optional[int] = Field(None, description="接收字节数")


class WirelessClientCreate(WirelessClientBase):
    """创建无线客户端请求"""
    pass


class WirelessClientResponse(WirelessClientBase):
    """无线客户端响应"""
    id: int
    connected_at: datetime
    last_seen: datetime

    class Config:
        orm_mode = True


# 查询参数
class WirelessDeviceQueryParams(BaseModel):
    """无线设备查询参数"""
    floor: Optional[str] = None
    status: Optional[str] = None
    limit: int = 100
    offset: int = 0


# 历史数据查询参数
class WirelessDataHistoryParams(BaseModel):
    """无线设备历史数据查询参数"""
    device_id: str
    start_time: datetime
    end_time: datetime
    interval: str = "hour"  # hour, day, week, month
