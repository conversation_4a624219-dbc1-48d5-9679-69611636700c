from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# SNMP监控项基础模型
class SNMPItemBase(BaseModel):
    device_id: str = Field(..., description="关联的设备ID")
    name: str = Field(..., description="监控项名称")
    key: str = Field(..., description="键值")
    oid: str = Field(..., description="SNMP OID")
    unit: Optional[str] = Field(None, description="单位")
    item_type: str = Field(..., description="监控项类型")
    location: str = Field(..., description="位置")
    position: Optional[str] = Field(None, description="具体位置")
    description: Optional[str] = Field(None, description="描述")
    status: str = Field("active", description="状态")

# 创建SNMP监控项
class SNMPItemCreate(SNMPItemBase):
    pass

# 更新SNMP监控项
class SNMPItemUpdate(BaseModel):
    name: Optional[str] = None
    key: Optional[str] = None
    oid: Optional[str] = None
    unit: Optional[str] = None
    item_type: Optional[str] = None
    location: Optional[str] = None
    position: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None

# SNMP监控项响应
class SNMPItemResponse(SNMPItemBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# 批量创建SNMP监控项
class SNMPItemBatchCreate(BaseModel):
    items: List[SNMPItemCreate]
