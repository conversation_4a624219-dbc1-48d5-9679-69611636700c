from pydantic import BaseModel, constr
from typing import Optional
from datetime import datetime

class PhoneExtensionBase(BaseModel):
    extension: constr(min_length=1, max_length=20)
    name: constr(min_length=1, max_length=50)
    department: constr(min_length=1, max_length=50)
    location: constr(min_length=1, max_length=100)
    type: str

    class Config:
        use_enum_values = True

class PhoneExtensionCreate(PhoneExtensionBase):
    pass

class PhoneExtensionUpdate(BaseModel):
    extension: Optional[constr(min_length=1, max_length=20)] = None
    name: Optional[constr(min_length=1, max_length=50)] = None
    department: Optional[constr(min_length=1, max_length=50)] = None
    location: Optional[constr(min_length=1, max_length=100)] = None
    type: Optional[str] = None
    status: Optional[str] = None

    class Config:
        use_enum_values = True

class PhoneExtensionResponse(PhoneExtensionBase):
    id: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True 