#!/usr/bin/env python3
import psycopg2
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

try:
    # 连接数据库
    conn = psycopg2.connect(
        host=os.getenv('DB_HOST'),
        database=os.getenv('DB_NAME'),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASSWORD')
    )
    
    cur = conn.cursor()
    
    # 查询所有软件相关的分类
    cur.execute("""
        SELECT id, name, code, description, level, parent_id 
        FROM asset_categories 
        WHERE name LIKE '%软件%' OR code LIKE '%SW%' OR code LIKE '%MAINT%'
        ORDER BY id
    """)
    
    results = cur.fetchall()
    
    print("数据库中的软件相关分类:")
    for row in results:
        print(f"ID: {row[0]}, 名称: {row[1]}, 编码: {row[2]}, 描述: {row[3]}, 级别: {row[4]}, 父级ID: {row[5]}")
    
    # 特别查询维保相关的分类
    print("\n维保相关分类:")
    cur.execute("""
        SELECT id, name, code, description 
        FROM asset_categories 
        WHERE name LIKE '%维保%' OR name LIKE '%授权%'
        ORDER BY id
    """)
    
    maint_results = cur.fetchall()
    for row in maint_results:
        print(f"ID: {row[0]}, 名称: {row[1]}, 编码: {row[2]}, 描述: {row[3]}")
    
    conn.close()
    
except Exception as e:
    print(f"查询失败: {e}")