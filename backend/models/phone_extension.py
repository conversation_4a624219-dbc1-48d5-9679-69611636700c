from sqlalchemy import Column, String, DateTime, Enum
from sqlalchemy.sql import func
import uuid
from database import Base

class PhoneExtension(Base):
    __tablename__ = "phone_extensions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    extension = Column(String, unique=True, nullable=False)
    name = Column(String, nullable=False)
    department = Column(String, nullable=False)
    location = Column(String, nullable=False)
    type = Column(Enum('analog', 'digital', 'ip', name='extension_type'), nullable=False)
    status = Column(Enum('active', 'inactive', name='extension_status'), nullable=False, default='active')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())