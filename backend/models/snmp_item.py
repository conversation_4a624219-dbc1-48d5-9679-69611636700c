from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from database import Base

class SNMPItem(Base):
    """SNMP监控项模型"""
    __tablename__ = "snmp_items"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 关联的设备ID
    name = Column(String)  # 监控项名称
    key = Column(String)  # 键值
    oid = Column(String)  # SNMP OID
    unit = Column(String, nullable=True)  # 单位
    item_type = Column(String)  # 监控项类型 (temperature/humidity/smoke/water)
    device_type = Column(String)  # 设备类型 (switch/router/printer)
    vendor = Column(String)  # 设备厂商
    ip_address = Column(String)  # 设备IP地址
    snmp_version = Column(String)  # SNMP版本(v1/v2c/v3)
    community = Column(String)  # SNMP社区字符串
    location = Column(String)  # 位置 (cold_aisle1/cold_aisle2)
    position = Column(String, nullable=True)  # 具体位置 (01/02)
    description = Column(String, nullable=True)  # 描述
    status = Column(String, default="active")  # 状态(online/offline)
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

    class Config:
        orm_mode = True
