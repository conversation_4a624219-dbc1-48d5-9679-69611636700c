from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from database import Base

class SNMPData(Base):
    """SNMP监控数据模型，用于存储自定义SNMP监控项的历史数据"""
    __tablename__ = "snmp_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    item_id = Column(Integer, index=True)   # 监控项ID
    key = Column(String, index=True)        # 键值
    value = Column(String)                  # 值
    value_type = Column(String)             # 值类型 (string/float/integer/boolean)
    unit = Column(String, nullable=True)    # 单位
    item_type = Column(String)              # 监控项类型
    location = Column(String)               # 位置
    position = Column(String, nullable=True)  # 具体位置
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)  # 时间戳

    class Config:
        orm_mode = True


class NetworkDeviceData(Base):
    """网络设备监控数据模型"""
    __tablename__ = "network_device_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    name = Column(String)                   # 设备名称
    location = Column(String)               # 位置
    status = Column(String)                 # 状态
    cpu_usage = Column(Float)               # CPU使用率
    memory_usage = Column(Float)            # 内存使用率
    temperature = Column(Float, nullable=True)  # 温度
    uptime = Column(Integer, nullable=True)  # 运行时间(秒)
    interfaces_count = Column(Integer)      # 接口数量
    interfaces_up = Column(Integer)         # 启用接口数量
    interfaces_data = Column(Text, nullable=True)  # 接口数据(JSON)
    bandwidth_in = Column(Float)            # 入站带宽(Mbps)
    bandwidth_out = Column(Float)           # 出站带宽(Mbps)
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)  # 时间戳

    class Config:
        orm_mode = True


class PrinterData(Base):
    """打印机监控数据模型"""
    __tablename__ = "printer_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    name = Column(String)                   # 打印机名称
    location = Column(String)               # 位置
    status = Column(String)                 # 状态
    model = Column(String)                  # 型号
    serial_number = Column(String)          # 序列号
    toner_black = Column(Float)             # 黑色墨粉剩余百分比
    toner_cyan = Column(Float, nullable=True)  # 青色墨粉剩余百分比
    toner_magenta = Column(Float, nullable=True)  # 品红色墨粉剩余百分比
    toner_yellow = Column(Float, nullable=True)  # 黄色墨粉剩余百分比
    pages_total = Column(Integer)           # 总打印页数
    pages_since_last = Column(Integer, nullable=True)  # 上次统计后打印页数
    error_state = Column(String, nullable=True)  # 错误状态
    warning_state = Column(String, nullable=True)  # 警告状态
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)  # 时间戳

    class Config:
        orm_mode = True
