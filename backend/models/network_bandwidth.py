from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, JSON, Text, Enum, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime

from database import Base


class NetworkDeviceType(str, enum.Enum):
    """网络设备类型枚举"""
    ROUTER = "router"
    SWITCH = "switch"
    FIREWALL = "firewall"
    LOAD_BALANCER = "load_balancer"
    WIRELESS_AP = "wireless_ap"
    GATEWAY = "gateway"
    OTHER = "other"


class NetworkDevice(Base):
    """网络设备模型"""
    __tablename__ = "network_devices"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    model = Column(String(100))
    serial_number = Column(String(100), unique=True)
    manufacturer = Column(String(100))
    location = Column(String(100))
    ip_address = Column(String(50), unique=True)
    mac_address = Column(String(50))
    device_type = Column(String(50), nullable=False)
    status = Column(String(20), default="offline")
    
    # SNMP配置
    snmp_enabled = Column(Boolean, default=True)
    snmp_community = Column(String(50), default="public")
    snmp_version = Column(Integer, default=2)
    snmp_port = Column(Integer, default=161)
    
    # 设备属性
    firmware_version = Column(String(100))
    uptime = Column(BigInteger)  # 以秒为单位
    cpu_usage = Column(Float)
    memory_usage = Column(Float)
    temperature = Column(Float)
    
    # 时间戳
    last_seen = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    interfaces = relationship("NetworkInterface", back_populates="device", cascade="all, delete-orphan")
    bandwidth_records = relationship("BandwidthRecord", back_populates="device", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<NetworkDevice {self.name} ({self.model})>"


class InterfaceType(str, enum.Enum):
    """接口类型枚举"""
    ETHERNET = "ethernet"
    FAST_ETHERNET = "fast_ethernet"
    GIGABIT_ETHERNET = "gigabit_ethernet"
    TEN_GIGABIT_ETHERNET = "ten_gigabit_ethernet"
    FIBER = "fiber"
    WIRELESS = "wireless"
    LOOPBACK = "loopback"
    VLAN = "vlan"
    TUNNEL = "tunnel"
    OTHER = "other"


class NetworkInterface(Base):
    """网络接口模型"""
    __tablename__ = "network_interfaces"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("network_devices.id", ondelete="CASCADE"))
    name = Column(String(100), nullable=False)
    description = Column(String(255))
    interface_type = Column(String(50))
    mac_address = Column(String(50))
    ip_address = Column(String(50))
    subnet_mask = Column(String(50))
    speed = Column(BigInteger)  # 以bps为单位
    duplex = Column(String(20))  # full, half
    admin_status = Column(Boolean, default=True)  # 管理状态：up/down
    operational_status = Column(Boolean, default=False)  # 运行状态：up/down
    mtu = Column(Integer)
    vlan = Column(Integer)
    
    # 流量统计
    in_octets = Column(BigInteger, default=0)  # 入方向字节数
    out_octets = Column(BigInteger, default=0)  # 出方向字节数
    in_packets = Column(BigInteger, default=0)  # 入方向包数
    out_packets = Column(BigInteger, default=0)  # 出方向包数
    in_errors = Column(BigInteger, default=0)  # 入方向错误数
    out_errors = Column(BigInteger, default=0)  # 出方向错误数
    in_discards = Column(BigInteger, default=0)  # 入方向丢弃数
    out_discards = Column(BigInteger, default=0)  # 出方向丢弃数
    
    # 时间戳
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    device = relationship("NetworkDevice", back_populates="interfaces")
    bandwidth_records = relationship("BandwidthRecord", back_populates="interface", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<NetworkInterface {self.name} on {self.device_id}>"


class BandwidthRecord(Base):
    """带宽记录模型"""
    __tablename__ = "bandwidth_records"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("network_devices.id", ondelete="CASCADE"))
    interface_id = Column(Integer, ForeignKey("network_interfaces.id", ondelete="CASCADE"))
    
    # 流量数据
    timestamp = Column(DateTime, default=func.now())
    in_octets = Column(BigInteger)  # 入方向字节数
    out_octets = Column(BigInteger)  # 出方向字节数
    in_packets = Column(BigInteger)  # 入方向包数
    out_packets = Column(BigInteger)  # 出方向包数
    in_errors = Column(BigInteger)  # 入方向错误数
    out_errors = Column(BigInteger)  # 出方向错误数
    in_discards = Column(BigInteger)  # 入方向丢弃数
    out_discards = Column(BigInteger)  # 出方向丢弃数
    
    # 计算值
    in_bandwidth = Column(Float)  # 入方向带宽 (bps)
    out_bandwidth = Column(Float)  # 出方向带宽 (bps)
    in_utilization = Column(Float)  # 入方向利用率 (%)
    out_utilization = Column(Float)  # 出方向利用率 (%)
    
    # 关系
    device = relationship("NetworkDevice", back_populates="bandwidth_records")
    interface = relationship("NetworkInterface", back_populates="bandwidth_records")
    
    def __repr__(self):
        return f"<BandwidthRecord for {self.interface_id} at {self.timestamp}>"


class TrafficThreshold(Base):
    """流量阈值模型"""
    __tablename__ = "traffic_thresholds"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(Integer, ForeignKey("network_devices.id", ondelete="CASCADE"), nullable=True)
    interface_id = Column(Integer, ForeignKey("network_interfaces.id", ondelete="CASCADE"), nullable=True)
    
    # 阈值设置
    name = Column(String(100), nullable=False)
    description = Column(String(255))
    in_bandwidth_warning = Column(Float)  # 入方向带宽警告阈值 (bps)
    in_bandwidth_critical = Column(Float)  # 入方向带宽严重阈值 (bps)
    out_bandwidth_warning = Column(Float)  # 出方向带宽警告阈值 (bps)
    out_bandwidth_critical = Column(Float)  # 出方向带宽严重阈值 (bps)
    in_utilization_warning = Column(Float)  # 入方向利用率警告阈值 (%)
    in_utilization_critical = Column(Float)  # 入方向利用率严重阈值 (%)
    out_utilization_warning = Column(Float)  # 出方向利用率警告阈值 (%)
    out_utilization_critical = Column(Float)  # 出方向利用率严重阈值 (%)
    
    # 通知设置
    notify_email = Column(Boolean, default=True)
    notify_sms = Column(Boolean, default=False)
    notify_webhook = Column(Boolean, default=False)
    notification_cooldown = Column(Integer, default=3600)  # 通知冷却时间（秒）
    
    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<TrafficThreshold {self.name}>"
