from sqlalchemy import Column, Integer, String, Float, Text, DateTime
from sqlalchemy.sql import func
from datetime import datetime

from database import Base

class CollectionLog(Base):
    """SNMP采集日志记录模型"""
    __tablename__ = "collection_logs"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    status = Column(String)  # 采集状态(success/failed)
    duration = Column(Float)  # 采集耗时(秒)
    values = Column(Text)  # 采集到的数据(JSON格式)
    error_message = Column(Text, nullable=True)  # 错误信息
    collected_at = Column(DateTime(timezone=True), default=func.now())  # 采集时间

    class Config:
        orm_mode = True
