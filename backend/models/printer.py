from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, JSON, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime

from database import Base


class PrinterStatus(str, enum.Enum):
    """打印机状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    WARNING = "warning"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class PrinterType(str, enum.Enum):
    """打印机类型枚举"""
    LASER = "laser"
    INKJET = "inkjet"
    DOT_MATRIX = "dot_matrix"
    THERMAL = "thermal"
    MULTIFUNCTION = "multifunction"
    PLOTTER = "plotter"
    LABEL = "label"
    OTHER = "other"


class Printer(Base):
    """打印机设备模型"""
    __tablename__ = "printers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    model = Column(String(100), nullable=False)
    serial_number = Column(String(100), unique=True)
    manufacturer = Column(String(100))
    location = Column(String(100))
    department = Column(String(100))
    ip_address = Column(String(50), unique=True)
    mac_address = Column(String(50))
    printer_type = Column(String(50), nullable=False)
    status = Column(String(20), default=PrinterStatus.OFFLINE)
    
    # SNMP配置
    snmp_enabled = Column(Boolean, default=True)
    snmp_community = Column(String(50), default="public")
    snmp_version = Column(Integer, default=2)
    snmp_port = Column(Integer, default=161)
    
    # 打印机属性
    is_color = Column(Boolean, default=False)
    is_duplex = Column(Boolean, default=False)
    paper_sizes = Column(String(255))  # 支持的纸张尺寸，逗号分隔
    print_speed = Column(Integer)  # 每分钟页数
    resolution = Column(String(50))  # 分辨率，如"600x600 dpi"
    
    # 耗材信息
    toner_black_level = Column(Float)  # 黑色墨粉/墨水剩余百分比
    toner_cyan_level = Column(Float)  # 青色墨粉/墨水剩余百分比
    toner_magenta_level = Column(Float)  # 品红色墨粉/墨水剩余百分比
    toner_yellow_level = Column(Float)  # 黄色墨粉/墨水剩余百分比
    drum_level = Column(Float)  # 硒鼓剩余百分比
    maintenance_kit_level = Column(Float)  # 维护套件剩余百分比
    
    # 计数器
    total_pages = Column(Integer, default=0)  # 总打印页数
    color_pages = Column(Integer, default=0)  # 彩色打印页数
    mono_pages = Column(Integer, default=0)  # 黑白打印页数
    
    # 状态信息
    error_message = Column(String(255))
    warning_message = Column(String(255))
    
    # 时间戳
    last_seen = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    consumables = relationship("PrinterConsumable", back_populates="printer", cascade="all, delete-orphan")
    maintenance_records = relationship("PrinterMaintenance", back_populates="printer", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Printer {self.name} ({self.model})>"


class ConsumableType(str, enum.Enum):
    """耗材类型枚举"""
    TONER_BLACK = "toner_black"
    TONER_CYAN = "toner_cyan"
    TONER_MAGENTA = "toner_magenta"
    TONER_YELLOW = "toner_yellow"
    INK_BLACK = "ink_black"
    INK_CYAN = "ink_cyan"
    INK_MAGENTA = "ink_magenta"
    INK_YELLOW = "ink_yellow"
    DRUM = "drum"
    MAINTENANCE_KIT = "maintenance_kit"
    WASTE_TONER = "waste_toner"
    FUSER = "fuser"
    TRANSFER_BELT = "transfer_belt"
    OTHER = "other"


class PrinterConsumable(Base):
    """打印机耗材模型"""
    __tablename__ = "printer_consumables"

    id = Column(Integer, primary_key=True, index=True)
    printer_id = Column(Integer, ForeignKey("printers.id", ondelete="CASCADE"))
    consumable_type = Column(String(50), nullable=False)
    model_number = Column(String(100))
    serial_number = Column(String(100))
    manufacturer = Column(String(100))
    level = Column(Float)  # 剩余百分比
    capacity = Column(Integer)  # 容量（页数）
    pages_printed = Column(Integer, default=0)
    install_date = Column(DateTime)
    expiry_date = Column(DateTime)
    
    # 关系
    printer = relationship("Printer", back_populates="consumables")
    
    def __repr__(self):
        return f"<PrinterConsumable {self.consumable_type} for Printer {self.printer_id}>"


class MaintenanceType(str, enum.Enum):
    """维护类型枚举"""
    SCHEDULED = "scheduled"
    REPAIR = "repair"
    CONSUMABLE_REPLACEMENT = "consumable_replacement"
    CLEANING = "cleaning"
    CALIBRATION = "calibration"
    OTHER = "other"


class PrinterMaintenance(Base):
    """打印机维护记录模型"""
    __tablename__ = "printer_maintenance"

    id = Column(Integer, primary_key=True, index=True)
    printer_id = Column(Integer, ForeignKey("printers.id", ondelete="CASCADE"))
    maintenance_type = Column(String(50), nullable=False)
    description = Column(Text)
    performed_by = Column(String(100))
    cost = Column(Float)
    parts_replaced = Column(JSON)  # 更换的部件列表
    maintenance_date = Column(DateTime, default=func.now())
    next_maintenance_date = Column(DateTime)
    
    # 关系
    printer = relationship("Printer", back_populates="maintenance_records")
    
    def __repr__(self):
        return f"<PrinterMaintenance {self.maintenance_type} for Printer {self.printer_id}>"
