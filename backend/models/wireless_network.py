from sqlalchemy import Column, Integer, String, Float, <PERSON>olean, DateTime, ForeignKey, Text, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class WirelessDevice(Base):
    """无线网络设备模型"""
    __tablename__ = "wireless_devices"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, unique=True, index=True)  # 设备ID
    name = Column(String)  # 设备名称
    model = Column(String)  # 设备型号
    manufacturer = Column(String)  # 制造商
    location = Column(String)  # 位置
    floor = Column(String)  # 楼层
    ip_address = Column(String)  # IP地址
    mac_address = Column(String)  # MAC地址
    status = Column(String, default="offline")  # 状态 (online/offline/warning)
    
    # SNMP配置
    snmp_enabled = Column(Boolean, default=True)
    snmp_community = Column(String, default="public")
    snmp_version = Column(Integer, default=2)
    snmp_port = Column(Integer, default=161)
    
    # 设备位置坐标（用于地图显示）
    position_x = Column(Float)
    position_y = Column(Float)
    
    # 设备信息
    firmware_version = Column(String)
    uptime = Column(BigInteger)  # 运行时间（秒）
    last_seen = Column(DateTime(timezone=True), default=func.now())
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    
    # 关联数据
    data = relationship("WirelessDeviceData", back_populates="device", cascade="all, delete-orphan")
    clients = relationship("WirelessClient", back_populates="device", cascade="all, delete-orphan")


class WirelessDeviceData(Base):
    """无线网络设备监控数据模型"""
    __tablename__ = "wireless_device_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, ForeignKey("wireless_devices.device_id", ondelete="CASCADE"), index=True)
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)
    
    # 基本状态
    status = Column(String)  # 状态 (online/offline/warning)
    cpu_usage = Column(Float)  # CPU使用率
    memory_usage = Column(Float)  # 内存使用率
    temperature = Column(Float)  # 温度
    
    # 无线网络指标
    clients_count = Column(Integer)  # 连接的客户端数量
    clients_2g = Column(Integer)  # 2.4GHz客户端数量
    clients_5g = Column(Integer)  # 5GHz客户端数量
    channel_2g = Column(Integer)  # 2.4GHz信道
    channel_5g = Column(Integer)  # 5GHz信道
    signal_strength = Column(Float)  # 信号强度
    noise_level = Column(Float)  # 噪声水平
    tx_power = Column(Float)  # 发射功率
    
    # 流量数据
    rx_bytes = Column(BigInteger)  # 接收字节数
    tx_bytes = Column(BigInteger)  # 发送字节数
    rx_packets = Column(BigInteger)  # 接收包数
    tx_packets = Column(BigInteger)  # 发送包数
    rx_errors = Column(BigInteger)  # 接收错误数
    tx_errors = Column(BigInteger)  # 发送错误数
    
    # 关联
    device = relationship("WirelessDevice", back_populates="data")


class WirelessClient(Base):
    """无线网络客户端模型"""
    __tablename__ = "wireless_clients"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, ForeignKey("wireless_devices.device_id", ondelete="CASCADE"), index=True)
    mac_address = Column(String, index=True)  # 客户端MAC地址
    ip_address = Column(String)  # 客户端IP地址
    hostname = Column(String)  # 客户端主机名
    connected_at = Column(DateTime(timezone=True), default=func.now())  # 连接时间
    last_seen = Column(DateTime(timezone=True), default=func.now())  # 最后活动时间
    signal_strength = Column(Float)  # 信号强度
    frequency_band = Column(String)  # 频段 (2.4GHz/5GHz)
    tx_rate = Column(Float)  # 发送速率
    rx_rate = Column(Float)  # 接收速率
    tx_bytes = Column(BigInteger)  # 发送字节数
    rx_bytes = Column(BigInteger)  # 接收字节数
    
    # 关联
    device = relationship("WirelessDevice", back_populates="clients")
