from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, DateTime, Date, Text, ForeignKey, JSO<PERSON>, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime, date
from typing import Optional

from database import Base

class LifecycleStatus(enum.Enum):
    PLANNING = "planning"  # 规划中
    PROCUREMENT = "procurement"  # 采购中
    DEPLOYMENT = "deployment"  # 部署中
    PRODUCTION = "production"  # 生产环境
    MAINTENANCE = "maintenance"  # 维护中
    RETIRED = "retired"  # 已退役
    DISPOSED = "disposed"  # 已处置

class DepreciationMethod(enum.Enum):
    STRAIGHT_LINE = "straight_line"  # 直线法
    DECLINING_BALANCE = "declining_balance"  # 余额递减法
    UNITS_OF_PRODUCTION = "units_of_production"  # 工作量法

class AssetStatus(enum.Enum):
    ACTIVE = "ACTIVE"  # 活跃
    INACTIVE = "INACTIVE"  # 非活跃
    MAINTENANCE = "MAINTENANCE"  # 维护中
    FAULTY = "FAULTY"  # 故障
    RETIRED = "RETIRED"  # 已退役
    SCRAPPED = "SCRAPPED"  # 报废
    STORED = "STORED"  # 存放

class StorageType(enum.Enum):
    HDD = "HDD"  # 机械硬盘
    SSD = "SSD"  # 固态硬盘
    NVME = "NVME"  # NVMe固态硬盘
    HYBRID = "HYBRID"  # 混合硬盘

class SecurityLevel(enum.Enum):
    PUBLIC = "PUBLIC"  # 公开
    INTERNAL = "INTERNAL"  # 内部
    CONFIDENTIAL = "CONFIDENTIAL"  # 机密
    RESTRICTED = "RESTRICTED"  # 限制

class AssetCategory(Base):
    __tablename__ = "asset_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    code = Column(String(20), nullable=True)
    description = Column(Text, nullable=True)
    level = Column(Integer, default=1)
    parent_id = Column(Integer, ForeignKey("asset_categories.id"), nullable=True)
    attributes = Column(JSON, nullable=True)
    is_system = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    assets = relationship("Asset", back_populates="category")
    children = relationship("AssetCategory", backref="parent", remote_side=[id])

class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, index=True)
    # 基础信息
    name = Column(String(255), nullable=False, comment="资产名称")
    code = Column(String(100), unique=True, nullable=False, comment="资产编码")
    model = Column(String(255), comment="型号")
    category_id = Column(Integer, ForeignKey('asset_categories.id'), nullable=False, comment="分类ID")
    status = Column(String(50), comment="资产状态")

    # 采购信息
    purchase_date = Column(DateTime, comment="购买日期")
    price = Column(Float, comment="购买价格")
    lifecycle_status = Column(String(20), comment="生命周期状态")
    warranty_expire_date = Column(DateTime, comment="保修到期日期")
    manufacturer = Column(String(255), comment="制造商")
    supplier = Column(String(255), comment="供应商")
    serial_number = Column(String(255), comment="序列号")
    
    # 折旧信息
    depreciation_method = Column(String(20), comment="折旧方法")

    description = Column(Text, nullable=True)
    purchase_order_number = Column(String(50), nullable=True)
    asset_value = Column(Float, nullable=True)
    depreciation_period = Column(Integer, nullable=True)  # 折旧年限(月)
    depreciation_rate = Column(Float, nullable=True)  # 折旧率
    last_check_date = Column(DateTime, nullable=True)
    responsible_person = Column(String(255), comment="负责人")
    department = Column(String(255), comment="部门")
    attributes = Column(JSON, comment="属性信息")
    tags = Column(JSON, comment="标签信息")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # IT网络资产专用字段
    ip_address = Column(String(45), comment="IP地址")
    mac_address = Column(String(17), comment="MAC地址")
    hostname = Column(String(255), comment="主机名")
    domain = Column(String(255), comment="域名")
    subnet_mask = Column(String(15), comment="子网掩码")
    gateway = Column(String(45), comment="网关")
    dns_servers = Column(String(200), comment="DNS服务器")
    vlan_id = Column(Integer, comment="VLAN ID")

    # 硬件规格
    cpu_model = Column(String(255), comment="CPU型号")
    cpu_cores = Column(Integer, comment="CPU核心数")
    memory_size = Column(Integer, comment="内存大小(GB)")
    storage_size = Column(Integer, comment="存储大小(GB)")
    storage_type = Column(String(20), comment="存储类型")
    port_count = Column(Integer, comment="端口数量")
    port_speed = Column(String(20), comment="端口速度")
    power_consumption = Column(Float, comment="功耗(W)")
    operating_temperature = Column(String(20), comment="工作温度")

    # 软件信息
    operating_system = Column(String(255), comment="操作系统")
    os_version = Column(String(100), comment="操作系统版本")
    firmware_version = Column(String(100), comment="固件版本")
    software_licenses = Column(JSON, comment="软件许可证信息")

    # 安全相关
    security_level = Column(String(20), comment="安全级别")
    encryption_enabled = Column(Boolean, default=False, comment="是否启用加密")
    access_control = Column(String(255), comment="访问控制")
    snmp_community = Column(String(100), comment="SNMP团体名")
    snmp_version = Column(String(10), comment="SNMP版本")
    monitoring_enabled = Column(Boolean, default=False, comment="是否启用监控")
    backup_schedule = Column(String(255), comment="备份计划")
    
    # 安防设备专用字段
    security_device_type = Column(String(100), comment="安防设备类型")
    video_resolution = Column(String(50), comment="视频分辨率")
    recording_capacity = Column(Integer, comment="录像容量(GB)")
    night_vision = Column(Boolean, default=False, comment="是否支持夜视")
    motion_detection = Column(Boolean, default=False, comment="是否支持动态检测")
    audio_support = Column(Boolean, default=False, comment="是否支持音频")
    ptz_support = Column(Boolean, default=False, comment="是否支持云台控制")
    weatherproof_rating = Column(String(20), comment="防护等级")
    power_over_ethernet = Column(Boolean, default=False, comment="是否支持PoE供电")
    
    # 耗材相关字段
    is_consumable = Column(Boolean, default=False, comment="是否为耗材")
    quantity = Column(Integer, comment="数量")
    unit = Column(String(20), comment="单位")
    min_stock_level = Column(Integer, comment="最低库存")
    max_stock_level = Column(Integer, comment="最高库存")
    reorder_point = Column(Integer, comment="补货点")
    expiry_date = Column(Date, comment="过期日期")
    batch_number = Column(String(100), comment="批次号")
    
    # 位置信息
    location = Column(String(255), comment="位置")
    rack_position = Column(String(50), comment="机架位置")
    rack_unit = Column(Integer, comment="机架单元数")
    cable_management = Column(Text, comment="线缆管理")
    power_requirements = Column(String(50), comment="电源需求")
    cooling_requirements = Column(String(50), comment="冷却需求")
    compliance_standards = Column(JSON, comment="合规标准")
    certification = Column(String(200), comment="认证信息")
    
    # 关系
    category = relationship("AssetCategory", back_populates="assets")
    maintenance_records = relationship("MaintenanceRecord", back_populates="asset")

class MaintenanceRecord(Base):
    __tablename__ = "maintenance_records"

    id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=False)
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    type = Column(String(20), nullable=False)  # routine, repair, upgrade
    description = Column(Text, nullable=False)
    cost = Column(Float, nullable=True)
    performed_by = Column(String(100), nullable=True)
    next_scheduled_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    asset = relationship("Asset", back_populates="maintenance_records")
