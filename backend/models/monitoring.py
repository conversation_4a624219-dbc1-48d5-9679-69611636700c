from sqlalchemy import Column, Integer, String, Float, Bo<PERSON>an, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from database import Base

class EnvironmentData(Base):
    """环境监控数据模型"""
    __tablename__ = "environment_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    location = Column(String)  # 位置
    floor = Column(String)  # 楼层
    temperature = Column(Float)  # 温度
    humidity = Column(Float)  # 湿度
    smoke = Column(Boolean, default=False)  # 烟感
    water = Column(Boolean, default=False)  # 水浸
    status = Column(String)  # 状态
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)  # 时间戳

    # 冷通道温度数据
    cold_aisle1_temp1 = Column(Float, nullable=True)  # 冷通道1-温度01
    cold_aisle1_temp2 = Column(Float, nullable=True)  # 冷通道1-温度02
    cold_aisle2_temp1 = Column(Float, nullable=True)  # 冷通道2-温度01
    cold_aisle2_temp2 = Column(Float, nullable=True)  # 冷通道2-温度02

    # 冷通道湿度数据
    cold_aisle1_humidity1 = Column(Float, nullable=True)  # 冷通道1-湿度01
    cold_aisle1_humidity2 = Column(Float, nullable=True)  # 冷通道1-湿度02
    cold_aisle2_humidity1 = Column(Float, nullable=True)  # 冷通道2-湿度01
    cold_aisle2_humidity2 = Column(Float, nullable=True)  # 冷通道2-湿度02

    class Config:
        orm_mode = True


class UPSData(Base):
    """UPS监控数据模型"""
    __tablename__ = "ups_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    name = Column(String)  # UPS名称
    location = Column(String)  # 位置
    status = Column(String)  # 状态 (online/warning/offline)
    load = Column(Float)  # 负载百分比
    battery_level = Column(Float)  # 电池电量百分比
    battery_time_remaining = Column(Integer)  # 电池剩余时间(分钟)
    input_voltage = Column(Float)  # 输入电压
    output_voltage = Column(Float)  # 输出电压
    input_frequency = Column(Float)  # 输入频率
    output_frequency = Column(Float)  # 输出频率
    temperature = Column(Float)  # 温度
    battery_voltage = Column(Float)  # 电池电压
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)  # 时间戳

    class Config:
        orm_mode = True


class MainsPowerData(Base):
    """市电监控数据模型"""
    __tablename__ = "mains_power_data"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, index=True)  # 设备ID
    location = Column(String)  # 位置
    status = Column(String)  # 状态
    voltage = Column(Float)  # 电压
    frequency = Column(Float)  # 频率
    current = Column(Float)  # 电流
    power = Column(Float)  # 功率
    power_factor = Column(Float)  # 功率因数
    energy_consumption = Column(Float)  # 能耗
    timestamp = Column(DateTime(timezone=True), default=func.now(), index=True)  # 时间戳

    class Config:
        orm_mode = True


class MonitoringDevice(Base):
    """监控设备配置模型"""
    __tablename__ = "monitoring_devices"

    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, unique=True, index=True)  # 设备ID
    name = Column(String)  # 设备名称
    device_type = Column(String)  # 设备类型 (environment/ups/mains)
    location = Column(String)  # 位置
    floor = Column(String, nullable=True)  # 楼层
    protocol = Column(String)  # 协议 (snmp/modbus/etc)
    host = Column(String)  # 主机地址
    port = Column(Integer, default=161)  # 端口
    community = Column(String, nullable=True)  # SNMP社区
    version = Column(Integer, default=2)  # SNMP版本
    oids = Column(Text, nullable=True)  # OID配置 (JSON格式)
    status = Column(String, default="active")  # 设备状态
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

    class Config:
        orm_mode = True
