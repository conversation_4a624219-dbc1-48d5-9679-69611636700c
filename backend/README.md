# 设备统一监控方案

本模块提供了一个统一的设备监控解决方案，支持通过多种协议（SNMP、SSH、WMI等）收集设备信息和性能指标。

## 功能特点

- **多协议支持**：支持SNMP、SSH、WMI等多种协议，可以监控网络设备、服务器和Windows系统
- **可扩展架构**：基于插件式连接器架构，可以轻松添加新的协议支持
- **异步处理**：使用Python异步编程，支持高效的并发监控
- **自动发现**：可以自动探测网络设备并添加到监控列表
- **实时监控**：定期收集设备状态和性能指标
- **RESTful API**：提供标准的REST API接口，方便与前端或其他系统集成

## 技术栈

- Python 3.8+
- Django 4.x
- Django REST Framework
- asyncio / aiohttp
- pysnmp (SNMP支持)
- asyncssh (SSH支持)
- wmi-client-wrapper (WMI支持)

## 安装与配置

### 依赖安装

1. 安装基础依赖:
```bash
pip install -r requirements.txt
```

2. 安装设备监控模块依赖:
```bash
pip install -r device_monitor/requirements.txt
```

### 配置

1. 在Django项目的`settings.py`文件中添加以下配置:

```python
INSTALLED_APPS = [
    # ... 其他应用
    'device_monitor',
]

# 设备监控配置
DEVICE_MONITOR_CONFIG_PATH = os.path.join(BASE_DIR, 'config', 'devices.json')
DEVICE_MONITOR_DATA_DIR = os.path.join(BASE_DIR, 'data', 'device_monitor')
```

2. 添加API路由到`urls.py`:

```python
urlpatterns = [
    # ... 其他URL路径
    path('api/device_monitor/', include('device_monitor.api.urls')),
]
```

## 使用指南

### 设备配置示例

设备配置文件(`devices.json`)示例:

```json
{
  "global": {
    "polling_interval": 300
  },
  "devices": [
    {
      "device_id": "switch-01",
      "type": "snmp",
      "host": "************",
      "port": 161,
      "community": "public",
      "version": 2,
      "device_type": "network",
      "oids": {
        "cpu": "*******.*******.*********.1.5.1",
        "memory": "*******.*******.********.6.1"
      }
    },
    {
      "device_id": "server-01",
      "type": "ssh",
      "host": "************",
      "port": 22,
      "username": "admin",
      "password": "password",
      "device_type": "linux",
      "commands": {
        "cpu": "top -bn1 | grep 'Cpu(s)' | awk '{print $2 + $4}'",
        "memory": "free | grep Mem | awk '{print $3/$2 * 100.0}'",
        "disk": "df -h / | tail -1 | awk '{print $5}' | sed 's/%//'"
      }
    },
    {
      "device_id": "win-server-01",
      "type": "wmi",
      "host": "************",
      "username": "administrator",
      "password": "password",
      "queries": {
        "cpu": "SELECT LoadPercentage FROM Win32_Processor",
        "memory": "SELECT TotalVisibleMemorySize, FreePhysicalMemory FROM Win32_OperatingSystem",
        "disk": "SELECT DeviceID, Size, FreeSpace FROM Win32_LogicalDisk WHERE DriveType=3"
      }
    }
  ]
}
```

### API接口使用

#### 获取所有设备状态

```
GET /api/device_monitor/devices/
```

响应:
```json
{
  "switch-01": {
    "status": "normal",
    "last_update": "2023-06-01T12:34:56"
  },
  "server-01": {
    "status": "warning",
    "last_update": "2023-06-01T12:35:21"
  }
}
```

#### 获取设备详情

```
GET /api/device_monitor/devices/{device_id}/
```

响应:
```json
{
  "device_id": "server-01",
  "status": "warning",
  "metrics": {
    "cpu_usage": 78.5,
    "memory_usage": 92.3,
    "disk_usage": 85.0,
    "hostname": "app-server-1",
    "uptime": "10 days, 5:23:16"
  },
  "last_update": "2023-06-01T12:35:21"
}
```

#### 刷新设备数据

```
POST /api/device_monitor/devices/{device_id}/refresh/
```

#### 添加新设备

```
POST /api/device_monitor/devices/
```

请求体:
```json
{
  "device_id": "new-switch",
  "type": "snmp",
  "host": "************",
  "port": 161,
  "community": "public",
  "version": 2
}
```

## 扩展开发

### 添加新的连接器

1. 创建新的连接器类，继承自`BaseConnector`:

```python
from device_monitor.connectors.base import BaseConnector, DeviceInfo

class MyConnector(BaseConnector):
    """自定义连接器"""
    
    def __init__(self, name, config):
        super().__init__(name, config)
        # 初始化配置
        
    async def connect(self):
        # 实现连接
        
    async def disconnect(self):
        # 实现断开连接
        
    async def get_device_info(self):
        # 获取设备信息
        
    async def get_metrics(self):
        # 获取性能指标
        
    async def check_status(self):
        # 检查设备状态
```

2. 在`service.py`中注册连接器:

```python
self.connector_types = {
    "snmp": SNMPConnector,
    "ssh": SSHConnector,
    "wmi": WMIConnector,
    "my_type": MyConnector,  # 注册新连接器
}
```

## 故障排除

常见问题及解决方案:

1. **连接失败**:
   - 检查网络连接和防火墙设置
   - 确认设备IP地址和端口是否正确
   - 验证认证信息(用户名、密码、社区名等)

2. **指标收集错误**:
   - 确认OID或命令是否正确
   - 检查设备是否支持指定的指标
   - 查看日志获取详细错误信息

3. **性能问题**:
   - 适当调整轮询间隔
   - 减少同时监控的设备数量
   - 优化查询命令或OID列表 