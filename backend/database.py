import os
import logging
from dotenv import load_dotenv
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import time

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# 从环境变量获取数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "")

# 如果未找到环境变量，使用默认值
if not DATABASE_URL:
    logger.warning("No DATABASE_URL found in environment, using default connection")
    DATABASE_URL = "postgresql+asyncpg://user_PwWEyE:password_QM8NyB@localhost:5432/asset_management"

# 将标准 PostgreSQL URL 转换为 asyncpg 格式
# 例如：将 postgresql://user:pass@host:port/db 转换为 postgresql+asyncpg://user:pass@host:port/db
if DATABASE_URL.startswith('postgresql://') and not DATABASE_URL.startswith('postgresql+asyncpg://'):
    DATABASE_URL = DATABASE_URL.replace('postgresql://', 'postgresql+asyncpg://')
    logger.info(f"Converted DATABASE_URL to asyncpg format")

logger.info(f"Using database connection: {DATABASE_URL.split('@')[0].split('://')[0]}://*****@{DATABASE_URL.split('@')[1]}")

# 创建异步数据库引擎
engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # 在开发环境中打印 SQL 语句
    future=True,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,  # 连接超时时间
    pool_recycle=1800,  # 连接回收时间（30分钟）
    pool_pre_ping=True,  # 自动检测断开的连接
    connect_args={"server_settings": {"application_name": "rs_asset_app"}},
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

# 声明基类
Base = declarative_base()

MAX_RETRIES = 3
RETRY_DELAY = 2  # 秒

async def init_db():
    """初始化数据库，创建所有表"""
    # 导入所有模型以确保它们被注册到Base.metadata中
    from models.asset import Asset
    from models.monitoring import MonitoringDevice
    from models.collection_log import CollectionLog
    from models.snmp_data import SNMPData
    from models.snmp_item import SNMPItem
    from models.network_bandwidth import NetworkDevice, NetworkInterface, BandwidthRecord, TrafficThreshold
    from models.phone_extension import PhoneExtension
    from models.printer import Printer
    from models.rack import Rack, Device, RackMaintenanceRecord
    from models.wireless_network import WirelessDevice, WirelessDeviceData, WirelessClient
    
    for attempt in range(MAX_RETRIES):
        try:
            logger.info("Attempting to initialize database (attempt %d/%d)", attempt + 1, MAX_RETRIES)
            async with engine.begin() as conn:
                # 创建所有表
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database initialized successfully")
            return
        except SQLAlchemyError as e:
            logger.error(f"Database initialization error (attempt {attempt + 1}/{MAX_RETRIES}): {e}")
            if attempt < MAX_RETRIES - 1:
                logger.info(f"Retrying in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
            else:
                logger.critical("Failed to initialize database after %d attempts", MAX_RETRIES)
                raise

async def get_db():
    """获取数据库会话的依赖函数"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except SQLAlchemyError as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()