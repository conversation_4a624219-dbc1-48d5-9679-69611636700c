from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete, func
from sqlalchemy.orm import selectinload
from typing import List, Optional
import logging
from datetime import datetime
import os
import uuid
from pathlib import Path

from database import get_db
from models.asset import Asset, AssetCategory, MaintenanceRecord
from schemas.asset import (
    AssetCreate, AssetUpdate, AssetResponse, 
    AssetCategoryCreate, AssetCategoryUpdate, AssetCategoryResponse,
    MaintenanceRecordCreate, MaintenanceRecordResponse
)
from .utils import generic_update_item_async

router = APIRouter(prefix="/api", tags=["assets"])
logger = logging.getLogger(__name__)

# 资产分类API端点
@router.post("/asset-categories", response_model=AssetCategoryResponse)
async def create_asset_category(
    category: AssetCategoryCreate, 
    db: AsyncSession = Depends(get_db)
):
    """创建新的资产分类"""
    try:
        # 检查编码是否唯一
        result = await db.execute(select(AssetCategory).filter(AssetCategory.code == category.code))
        existing_category = result.scalars().first()
        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category code already exists"
            )
        
        db_category = AssetCategory(**category.dict())
        db.add(db_category)
        await db.commit()
        await db.refresh(db_category)
        return db_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建资产分类失败: {str(e)}"
        )

@router.get("/asset-categories", response_model=List[AssetCategoryResponse])
async def get_asset_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    parent_id: Optional[int] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取资产分类列表"""
    try:
        query = select(AssetCategory)
        
        if parent_id is not None:
            query = query.filter(AssetCategory.parent_id == parent_id)
        
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        categories = result.scalars().all()
        return categories
    except Exception as e:
        logger.error(f"获取资产分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产分类失败: {str(e)}"
        )

@router.get("/asset-categories/{category_id}", response_model=AssetCategoryResponse)
async def get_asset_category(category_id: int, db: AsyncSession = Depends(get_db)):
    """获取特定资产分类"""
    try:
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        category = result.scalars().first()
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )
        return category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取资产分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产分类失败: {str(e)}"
        )

@router.put("/asset-categories/{category_id}", response_model=AssetCategoryResponse)
async def update_asset_category(
    category_id: int, 
    category_update: AssetCategoryUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新资产分类"""
    return await generic_update_item_async(
        db=db,
        model_cls=AssetCategory,
        item_id=category_id,
        update_data_schema=category_update,
        item_name="资产分类"
    )

@router.delete("/asset-categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_category(category_id: int, db: AsyncSession = Depends(get_db)):
    """删除资产分类"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        db_category = result.scalars().first()
        if not db_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )
        
        # 检查是否有资产使用此分类
        result = await db.execute(select(Asset).filter(Asset.category_id == category_id))
        if result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无法删除分类，因为有资产正在使用此分类"
            )
        
        # 删除分类
        await db.execute(delete(AssetCategory).where(AssetCategory.id == category_id))
        await db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产分类失败: {str(e)}"
        )

# 资产API端点
@router.post("/assets", response_model=AssetResponse)
async def create_asset(asset: AssetCreate, db: AsyncSession = Depends(get_db)):
    """创建新资产"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == asset.category_id))
        if not result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset.category_id}的资产分类不存在"
            )
        
        # 检查资产编码是否唯一
        if asset.code:
            # 注意：数据库中的字段名是 code，不是 asset_code
            result = await db.execute(select(Asset).filter(Asset.code == asset.code))
            if result.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"资产编码'{asset.code}'已存在"
                )
        
        # 创建资产 - 只使用数据库中实际存在的字段
        asset_data = asset.model_dump(exclude_unset=True)

        # 过滤掉数据库中不存在的字段
        valid_fields = {
            'name', 'code', 'model', 'category_id', 'status', 'purchase_date', 'price',
            'location', 'description', 'lifecycle_status', 'warranty_expire_date',
            'manufacturer', 'supplier', 'purchase_order_number', 'serial_number',
            'asset_value', 'depreciation_method', 'depreciation_period', 'depreciation_rate',
            'last_check_date', 'responsible_person', 'department', 'attributes', 'tags',
            'ip_address', 'mac_address', 'hostname', 'domain', 'subnet_mask', 'gateway',
            'dns_servers', 'vlan_id', 'cpu_model', 'cpu_cores', 'memory_size', 'storage_size',
            'storage_type', 'port_count', 'port_speed', 'power_consumption', 'operating_temperature',
            'operating_system', 'os_version', 'firmware_version', 'software_licenses',
            'security_level', 'encryption_enabled', 'access_control', 'snmp_community',
            'snmp_version', 'monitoring_enabled', 'backup_schedule', 'rack_position',
            'rack_unit', 'cable_management', 'power_requirements', 'cooling_requirements',
            'compliance_standards', 'certification', 'video_resolution', 'recording_capacity',
            'night_vision', 'motion_detection', 'audio_support', 'ptz_support',
            'weatherproof_rating', 'power_over_ethernet', 'quantity', 'unit',
            'min_stock_level', 'max_stock_level', 'reorder_point', 'expiry_date',
            'batch_number', 'security_device_type', 'is_consumable'
        }

        filtered_data = {k: v for k, v in asset_data.items() if k in valid_fields}

        db_asset = Asset(**filtered_data)
        db.add(db_asset)
        await db.commit()
        await db.refresh(db_asset)

        # 明确加载关系字段以避免序列化问题
        from sqlalchemy.orm import selectinload
        result = await db.execute(
            select(Asset)
            .options(selectinload(Asset.maintenance_records), selectinload(Asset.category))
            .where(Asset.id == db_asset.id)
        )
        loaded_asset = result.scalar_one()
        return loaded_asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建资产失败: {str(e)}"
        )

@router.get("/assets", response_model=List[AssetResponse])
async def get_assets(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category_id: Optional[int] = None,
    status: Optional[str] = None,
    search: Optional[str] = None,
    location: Optional[str] = None,
    department: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取资产列表，支持多种筛选条件"""
    try:
        query = select(Asset).options(
            selectinload(Asset.category),
            selectinload(Asset.maintenance_records)
        )

        if category_id:
            query = query.filter(Asset.category_id == category_id)
        if status:
            query = query.filter(Asset.lifecycle_status == status)
        if location:
            query = query.filter(Asset.location.ilike(f"%{location}%"))
        if department:
            query = query.filter(Asset.department.ilike(f"%{department}%"))
        if search:
            search_filter = (
                Asset.name.ilike(f"%{search}%") |
                Asset.code.ilike(f"%{search}%") |
                Asset.serial_number.ilike(f"%{search}%") |
                Asset.manufacturer.ilike(f"%{search}%") |
                Asset.model.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)

        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        assets = result.scalars().all()
        return assets
    except Exception as e:
        logger.error(f"获取资产列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产列表失败: {str(e)}"
        )

@router.get("/assets/{asset_id}", response_model=AssetResponse)
async def get_asset(asset_id: int, db: AsyncSession = Depends(get_db)):
    """获取特定资产详情"""
    try:
        result = await db.execute(
            select(Asset)
            .options(
                selectinload(Asset.category),
                selectinload(Asset.maintenance_records)
            )
            .filter(Asset.id == asset_id)
        )
        asset = result.scalars().first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )
        return asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取资产详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产详情失败: {str(e)}"
        )

async def _pre_update_asset_checks(db: AsyncSession, db_item: Asset, update_schema: AssetUpdate):
    """资产更新前的特定检查。"""
    # 1. 检查分类是否存在 (如果提供了 category_id 且与现有 category_id 不同)
    if update_schema.category_id is not None and update_schema.category_id != db_item.category_id:
        category_result = await db.execute(select(AssetCategory).filter(AssetCategory.id == update_schema.category_id))
        if not category_result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{update_schema.category_id}的资产分类不存在"
            )

    # 2. 检查资产编码是否唯一 (如果提供了 code 且与现有 code 不同)
    if update_schema.code is not None and update_schema.code != db_item.code:
        # 检查是否有其他资产（非当前资产）已使用新编码
        existing_asset_with_new_code = await db.execute(
            select(Asset).filter(Asset.code == update_schema.code, Asset.id != db_item.id)
        )
        if existing_asset_with_new_code.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"资产编码'{update_schema.code}'已被其他资产使用"
            )

@router.put("/assets/{asset_id}", response_model=AssetResponse)
async def update_asset(
    asset_id: int, 
    asset_update: AssetUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新资产信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=Asset,
        item_id=asset_id,
        update_data_schema=asset_update,
        item_name="资产",
        pre_update_checks=_pre_update_asset_checks
    )

@router.delete("/assets/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(asset_id: int, db: AsyncSession = Depends(get_db)):
    """删除资产"""
    try:
        # 检查资产是否存在
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        db_asset = result.scalars().first()
        if not db_asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )
        
        # 删除资产
        await db.execute(delete(Asset).where(Asset.id == asset_id))
        await db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产失败: {str(e)}"
        )

@router.put("/maintenance-records/{record_id}", response_model=MaintenanceRecordResponse)
async def update_maintenance_record(
    record_id: int, 
    record_update: MaintenanceRecordCreate, # Assuming MaintenanceRecordCreate can be used for updates or a specific MaintenanceRecordUpdate schema exists
    db: AsyncSession = Depends(get_db)
):
    """更新维保记录"""
    # 预更新检查：检查关联的资产是否存在
    async def _pre_update_maintenance_checks(db_session: AsyncSession, db_item: MaintenanceRecord, update_schema: MaintenanceRecordCreate):
        if update_schema.asset_id and update_schema.asset_id != db_item.asset_id:
            asset_result = await db_session.execute(select(Asset).filter(Asset.id == update_schema.asset_id))
            if not asset_result.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"ID为{update_schema.asset_id}的资产不存在"
                )

    return await generic_update_item_async(
        db=db,
        model_cls=MaintenanceRecord,
        item_id=record_id,
        update_data_schema=record_update,
        item_name="维保记录",
        pre_update_checks=_pre_update_maintenance_checks
    )

# 文件上传API
@router.post("/assets/{asset_id}/upload")
async def upload_asset_file(
    asset_id: int,
    file: UploadFile = File(...),
    file_type: str = Query(..., description="文件类型: image, document, manual"),
    db: AsyncSession = Depends(get_db)
):
    """上传资产相关文件（图片、文档等）"""
    try:
        # 检查资产是否存在
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        asset = result.scalars().first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )

        # 验证文件类型
        allowed_types = {
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp'],
            'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'],
            'manual': ['.pdf', '.doc', '.docx']
        }

        if file_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file_type}"
            )

        # 检查文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in allowed_types[file_type]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件扩展名 {file_extension} 不支持该文件类型 {file_type}"
            )

        # 创建上传目录
        upload_dir = Path("uploads") / "assets" / str(asset_id) / file_type
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # 保存文件
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)

        # 返回文件信息
        return {
            "message": "文件上传成功",
            "file_info": {
                "original_filename": file.filename,
                "saved_filename": unique_filename,
                "file_path": str(file_path),
                "file_size": len(content),
                "file_type": file_type,
                "asset_id": asset_id
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )

# 批量导入API
@router.post("/assets/bulk-import")
async def bulk_import_assets(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db)
):
    """批量导入资产（Excel/CSV文件）"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持Excel(.xlsx, .xls)或CSV文件"
            )

        # 保存上传的文件
        upload_dir = Path("uploads") / "bulk_import"
        upload_dir.mkdir(parents=True, exist_ok=True)

        temp_filename = f"{uuid.uuid4()}_{file.filename}"
        temp_file_path = upload_dir / temp_filename

        content = await file.read()
        with open(temp_file_path, "wb") as f:
            f.write(content)

        # TODO: 实现Excel/CSV解析和批量导入逻辑
        # 这里需要添加pandas或openpyxl来解析Excel文件

        return {
            "message": "文件上传成功，批量导入功能开发中",
            "temp_file": str(temp_file_path),
            "file_size": len(content)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量导入失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量导入失败: {str(e)}"
        )

# 资产统计API
@router.get("/assets/statistics")
async def get_asset_statistics(db: AsyncSession = Depends(get_db)):
    """获取资产统计信息"""
    try:
        # 总资产数
        total_result = await db.execute(select(func.count(Asset.id)))
        total_assets = total_result.scalar()

        # 按状态统计
        status_result = await db.execute(
            select(Asset.status, func.count(Asset.id))
            .group_by(Asset.status)
        )
        status_stats = {status: count for status, count in status_result.fetchall()}

        # 按分类统计
        category_result = await db.execute(
            select(AssetCategory.name, func.count(Asset.id))
            .join(Asset, AssetCategory.id == Asset.category_id)
            .group_by(AssetCategory.id, AssetCategory.name)
        )
        category_stats = [
            {"category": name, "count": count}
            for name, count in category_result.fetchall()
        ]

        return {
            "total_assets": total_assets,
            "status_statistics": status_stats,
            "category_statistics": category_stats
        }

    except Exception as e:
        logger.error(f"获取资产统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产统计失败: {str(e)}"
        )
