from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from typing import List, Optional
from datetime import datetime

from database import get_db
from models.printer import Printer, PrinterConsumable, PrinterMaintenance
from schemas.printer import (
    PrinterCreate, PrinterUpdate, PrinterResponse,
    ConsumableCreate, ConsumableUpdate, ConsumableResponse,
    MaintenanceCreate, MaintenanceResponse
)
from device_monitor.connectors.enhanced_snmp_connector import EnhancedSNMPConnector
from .utils import generic_update_item_async # 新增导入

router = APIRouter(prefix="/api/printers", tags=["printers"])

# 获取所有打印机
@router.get("", response_model=List[PrinterResponse])
async def get_printers(
    status: Optional[str] = None,
    printer_type: Optional[str] = None,
    department: Optional[str] = None,
    location: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取打印机列表，可以按状态、类型、部门和位置筛选"""
    query = select(Printer)

    # 应用筛选条件
    if status:
        query = query.filter(Printer.status == status)
    if printer_type:
        query = query.filter(Printer.printer_type == printer_type)
    if department:
        query = query.filter(Printer.department == department)
    if location:
        query = query.filter(Printer.location == location)

    result = await db.execute(query)
    printers = result.scalars().all()
    return printers

# 获取单个打印机详情
@router.get("/{printer_id}", response_model=PrinterResponse)
async def get_printer(printer_id: int, db: AsyncSession = Depends(get_db)):
    """获取单个打印机的详细信息"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    return printer

# 创建新打印机
@router.post("", response_model=PrinterResponse)
async def create_printer(printer: PrinterCreate, db: AsyncSession = Depends(get_db)):
    """创建新打印机"""
    # 检查IP地址是否已存在
    if printer.ip_address:
        result = await db.execute(select(Printer).filter(Printer.ip_address == printer.ip_address))
        existing = result.scalars().first()
        if existing:
            raise HTTPException(status_code=400, detail=f"IP地址 {printer.ip_address} 已被使用")

    # 检查序列号是否已存在
    if printer.serial_number:
        result = await db.execute(select(Printer).filter(Printer.serial_number == printer.serial_number))
        existing = result.scalars().first()
        if existing:
            raise HTTPException(status_code=400, detail=f"序列号 {printer.serial_number} 已存在")

    db_printer = Printer(**printer.model_dump())
    db.add(db_printer)
    await db.commit()
    await db.refresh(db_printer)
    return db_printer

async def _pre_update_printer_checks(db: AsyncSession, db_item: Printer, update_schema: PrinterUpdate):
    """打印机更新前的特定检查。"""
    # 1. 检查IP地址是否已被其他打印机使用 (如果提供了 ip_address 且与现有 ip_address 不同)
    if update_schema.ip_address and update_schema.ip_address != db_item.ip_address:
        existing_printer_with_new_ip = await db.execute(
            select(Printer).filter(
                Printer.ip_address == update_schema.ip_address, 
                Printer.id != db_item.id
            )
        )
        if existing_printer_with_new_ip.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"IP地址 {update_schema.ip_address} 已被其他打印机使用"
            )

    # 2. 检查序列号是否已被其他打印机使用 (如果提供了 serial_number 且与现有 serial_number 不同)
    if update_schema.serial_number and update_schema.serial_number != db_item.serial_number:
        existing_printer_with_new_sn = await db.execute(
            select(Printer).filter(
                Printer.serial_number == update_schema.serial_number, 
                Printer.id != db_item.id
            )
        )
        if existing_printer_with_new_sn.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"序列号 {update_schema.serial_number} 已被其他打印机使用"
            )

# 更新打印机信息
@router.put("/{printer_id}", response_model=PrinterResponse)
async def update_printer(
    printer_id: int,
    printer_update: PrinterUpdate, # Renamed from 'printer' for clarity
    db: AsyncSession = Depends(get_db)
):
    """更新打印机信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=Printer,
        item_id=printer_id,
        update_data_schema=printer_update,
        item_name="打印机",
        pre_update_checks=_pre_update_printer_checks
    )

# 删除打印机
@router.delete("/{printer_id}")
async def delete_printer(printer_id: int, db: AsyncSession = Depends(get_db)):
    """删除打印机"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    await db.delete(printer)
    await db.commit()
    return {"message": f"打印机 ID {printer_id} 已删除"}

# 获取打印机状态
@router.get("/{printer_id}/status")
async def get_printer_status(printer_id: int, db: AsyncSession = Depends(get_db)):
    """获取打印机当前状态（通过SNMP）"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    if not printer.snmp_enabled or not printer.ip_address:
        raise HTTPException(status_code=400, detail="该打印机未启用SNMP或未设置IP地址")

    # 创建SNMP连接器
    config = {
        "host": printer.ip_address,
        "port": printer.snmp_port,
        "community": printer.snmp_community,
        "version": printer.snmp_version,
        "device_type": "printer",
        "device_vendor": printer.manufacturer.lower() if printer.manufacturer else ""
    }

    connector = EnhancedSNMPConnector("snmp", config)

    # 检查连接
    connection_ok = await connector.check_connection()
    if not connection_ok:
        # 更新打印机状态为离线
        printer.status = "offline"
        printer.last_seen = datetime.now()
        await db.commit()
        return {"status": "offline", "message": "无法连接到打印机"}

    # 获取打印机指标
    metrics = await connector.get_metrics()

    # 更新打印机状态和指标
    if "error" in metrics:
        printer.status = "error"
        printer.error_message = metrics["error"]
    else:
        # 更新状态
        printer.status = metrics.get("status", "online")

        # 更新计数器
        if "counters" in metrics:
            if "total_pages" in metrics["counters"]:
                printer.total_pages = metrics["counters"]["total_pages"]

        # 更新耗材
        if "consumables" in metrics:
            if "toner" in metrics["consumables"]:
                printer.toner_black_level = metrics["consumables"]["toner"].get("percentage")

            # 彩色打印机
            if "toner_cyan" in metrics["consumables"]:
                printer.toner_cyan_level = metrics["consumables"]["toner_cyan"].get("percentage")
            if "toner_magenta" in metrics["consumables"]:
                printer.toner_magenta_level = metrics["consumables"]["toner_magenta"].get("percentage")
            if "toner_yellow" in metrics["consumables"]:
                printer.toner_yellow_level = metrics["consumables"]["toner_yellow"].get("percentage")

        # 更新错误信息
        if "errors" in metrics and metrics["errors"]:
            printer.error_message = "; ".join(metrics["errors"])
        else:
            printer.error_message = None

    printer.last_seen = datetime.now()
    await db.commit()

    return {
        "status": printer.status,
        "last_seen": printer.last_seen,
        "metrics": metrics
    }

# 耗材管理接口
@router.get("/{printer_id}/consumables", response_model=List[ConsumableResponse])
async def get_printer_consumables(printer_id: int, db: AsyncSession = Depends(get_db)):
    """获取打印机的耗材列表"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    result = await db.execute(
        select(PrinterConsumable).filter(PrinterConsumable.printer_id == printer_id)
    )
    consumables = result.scalars().all()
    return consumables

@router.post("/{printer_id}/consumables", response_model=ConsumableResponse)
async def add_printer_consumable(
    printer_id: int,
    consumable: ConsumableCreate,
    db: AsyncSession = Depends(get_db)
):
    """添加打印机耗材"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    db_consumable = PrinterConsumable(**consumable.model_dump(), printer_id=printer_id)
    db.add(db_consumable)
    await db.commit()
    await db.refresh(db_consumable)
    return db_consumable

@router.put("/consumables/{consumable_id}", response_model=ConsumableResponse)
async def update_consumable(
    consumable_id: int,
    consumable: ConsumableUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新耗材信息"""
    result = await db.execute(select(PrinterConsumable).filter(PrinterConsumable.id == consumable_id))
    db_consumable = result.scalars().first()

    if not db_consumable:
        raise HTTPException(status_code=404, detail=f"耗材 ID {consumable_id} 不存在")

    update_data = consumable.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_consumable, key, value)

    await db.commit()
    await db.refresh(db_consumable)
    return db_consumable

@router.delete("/consumables/{consumable_id}")
async def delete_consumable(consumable_id: int, db: AsyncSession = Depends(get_db)):
    """删除耗材"""
    result = await db.execute(select(PrinterConsumable).filter(PrinterConsumable.id == consumable_id))
    consumable = result.scalars().first()

    if not consumable:
        raise HTTPException(status_code=404, detail=f"耗材 ID {consumable_id} 不存在")

    await db.delete(consumable)
    await db.commit()
    return {"message": f"耗材 ID {consumable_id} 已删除"}

# 维护记录接口
@router.get("/{printer_id}/maintenance", response_model=List[MaintenanceResponse])
async def get_printer_maintenance(printer_id: int, db: AsyncSession = Depends(get_db)):
    """获取打印机的维护记录"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    result = await db.execute(
        select(PrinterMaintenance)
        .filter(PrinterMaintenance.printer_id == printer_id)
        .order_by(desc(PrinterMaintenance.maintenance_date))
    )
    maintenance_records = result.scalars().all()
    return maintenance_records

@router.post("/{printer_id}/maintenance", response_model=MaintenanceResponse)
async def add_maintenance_record(
    printer_id: int,
    maintenance: MaintenanceCreate,
    db: AsyncSession = Depends(get_db)
):
    """添加打印机维护记录"""
    result = await db.execute(select(Printer).filter(Printer.id == printer_id))
    printer = result.scalars().first()

    if not printer:
        raise HTTPException(status_code=404, detail=f"打印机 ID {printer_id} 不存在")

    db_maintenance = PrinterMaintenance(**maintenance.model_dump(), printer_id=printer_id)
    db.add(db_maintenance)
    await db.commit()
    await db.refresh(db_maintenance)
    return db_maintenance

@router.delete("/maintenance/{maintenance_id}")
async def delete_maintenance_record(maintenance_id: int, db: AsyncSession = Depends(get_db)):
    """删除维护记录"""
    result = await db.execute(select(PrinterMaintenance).filter(PrinterMaintenance.id == maintenance_id))
    maintenance = result.scalars().first()

    if not maintenance:
        raise HTTPException(status_code=404, detail=f"维护记录 ID {maintenance_id} 不存在")

    await db.delete(maintenance)
    await db.commit()
    return {"message": f"维护记录 ID {maintenance_id} 已删除"}

# 批量操作
@router.post("/batch-status-update")
async def update_all_printers_status(db: AsyncSession = Depends(get_db)):
    """批量更新所有启用SNMP的打印机状态"""
    result = await db.execute(
        select(Printer).filter(
            and_(
                Printer.snmp_enabled == True,
                Printer.ip_address != None
            )
        )
    )
    printers = result.scalars().all()

    update_results = []
    for printer in printers:
        try:
            # 创建SNMP连接器
            config = {
                "host": printer.ip_address,
                "port": printer.snmp_port,
                "community": printer.snmp_community,
                "version": printer.snmp_version,
                "device_type": "printer",
                "device_vendor": printer.manufacturer.lower() if printer.manufacturer else ""
            }

            connector = EnhancedSNMPConnector("snmp", config)

            # 检查连接
            connection_ok = await connector.check_connection()
            if not connection_ok:
                printer.status = "offline"
                printer.last_seen = datetime.now()
                update_results.append({
                    "id": printer.id,
                    "name": printer.name,
                    "status": "offline",
                    "success": True
                })
                continue

            # 获取打印机指标
            metrics = await connector.get_metrics()

            # 更新打印机状态和指标
            if "error" in metrics:
                printer.status = "error"
                printer.error_message = metrics["error"]
            else:
                # 更新状态
                printer.status = metrics.get("status", "online")

                # 更新计数器
                if "counters" in metrics:
                    if "total_pages" in metrics["counters"]:
                        printer.total_pages = metrics["counters"]["total_pages"]

                # 更新耗材
                if "consumables" in metrics:
                    if "toner" in metrics["consumables"]:
                        printer.toner_black_level = metrics["consumables"]["toner"].get("percentage")

                    # 彩色打印机
                    if "toner_cyan" in metrics["consumables"]:
                        printer.toner_cyan_level = metrics["consumables"]["toner_cyan"].get("percentage")
                    if "toner_magenta" in metrics["consumables"]:
                        printer.toner_magenta_level = metrics["consumables"]["toner_magenta"].get("percentage")
                    if "toner_yellow" in metrics["consumables"]:
                        printer.toner_yellow_level = metrics["consumables"]["toner_yellow"].get("percentage")

                # 更新错误信息
                if "errors" in metrics and metrics["errors"]:
                    printer.error_message = "; ".join(metrics["errors"])
                else:
                    printer.error_message = None

            printer.last_seen = datetime.now()

            update_results.append({
                "id": printer.id,
                "name": printer.name,
                "status": printer.status,
                "success": True
            })
        except Exception as e:
            update_results.append({
                "id": printer.id,
                "name": printer.name,
                "status": "error",
                "success": False,
                "error": str(e)
            })

    await db.commit()
    return {
        "total": len(printers),
        "success": sum(1 for r in update_results if r["success"]),
        "failed": sum(1 for r in update_results if not r["success"]),
        "results": update_results
    }
