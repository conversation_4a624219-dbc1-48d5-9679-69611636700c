from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, cast, Float
from typing import List, Optional
from datetime import datetime, timedelta

from database import get_db
from models.snmp_data import SNMPData, NetworkDeviceData, PrinterData
from schemas.snmp_data import (
    SNMPDataResponse, NetworkDeviceDataResponse, PrinterDataResponse,
    SNMPDataQueryParams, SNMPDataStatsParams, SNMPDataStatsResult
)

router = APIRouter(prefix="/api/snmp-data", tags=["snmp-data"])

# 获取SNMP数据
@router.get("/custom", response_model=List[SNMPDataResponse])
async def get_snmp_data(
    device_id: Optional[str] = None,
    item_id: Optional[int] = None,
    key: Optional[str] = None,
    item_type: Optional[str] = None,
    location: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    offset: int = 0,
    db: AsyncSession = Depends(get_db)
):
    """获取自定义SNMP监控项数据"""
    query = select(SNMPData)
    
    # 应用过滤条件
    if device_id:
        query = query.filter(SNMPData.device_id == device_id)
    if item_id:
        query = query.filter(SNMPData.item_id == item_id)
    if key:
        query = query.filter(SNMPData.key == key)
    if item_type:
        query = query.filter(SNMPData.item_type == item_type)
    if location:
        query = query.filter(SNMPData.location == location)
    if start_time:
        query = query.filter(SNMPData.timestamp >= start_time)
    if end_time:
        query = query.filter(SNMPData.timestamp <= end_time)
    
    # 排序和分页
    query = query.order_by(desc(SNMPData.timestamp)).offset(offset).limit(limit)
    
    result = await db.execute(query)
    return result.scalars().all()

# 获取SNMP数据统计
@router.post("/stats", response_model=List[SNMPDataStatsResult])
async def get_snmp_data_stats(
    params: SNMPDataStatsParams,
    db: AsyncSession = Depends(get_db)
):
    """获取SNMP监控项数据统计"""
    try:
        # 确定时间间隔
        interval_format = "%Y-%m-%d %H:00:00"  # 默认按小时
        if params.interval == "day":
            interval_format = "%Y-%m-%d 00:00:00"
        elif params.interval == "week":
            interval_format = "%Y-%U"  # 按周
        elif params.interval == "month":
            interval_format = "%Y-%m"  # 按月
        
        # 构建查询
        query = select(
            func.to_char(SNMPData.timestamp, interval_format).label("interval_time"),
            func.min(cast(SNMPData.value, Float)).label("min_value"),
            func.max(cast(SNMPData.value, Float)).label("max_value"),
            func.avg(cast(SNMPData.value, Float)).label("avg_value"),
            func.count(SNMPData.id).label("count")
        ).filter(
            SNMPData.device_id == params.device_id,
            SNMPData.key == params.key,
            SNMPData.timestamp >= params.start_time,
            SNMPData.timestamp <= params.end_time,
            SNMPData.value_type.in_(["float", "integer"])
        ).group_by(
            "interval_time"
        ).order_by(
            "interval_time"
        )
        
        result = await db.execute(query)
        
        # 处理结果
        stats_results = []
        for row in result:
            # 解析时间戳
            if params.interval == "week":
                # 周格式需要特殊处理
                year, week = row.interval_time.split("-")
                # 计算该年第N周的第一天
                first_day = datetime.strptime(f"{year}-01-01", "%Y-%m-%d")
                timestamp = first_day + timedelta(days=(int(week)-1)*7)
            elif params.interval == "month":
                # 月格式
                timestamp = datetime.strptime(f"{row.interval_time}-01", "%Y-%m-%d")
            else:
                # 小时或天格式
                timestamp = datetime.strptime(row.interval_time, interval_format)
            
            stats_results.append(SNMPDataStatsResult(
                timestamp=timestamp,
                min_value=row.min_value,
                max_value=row.max_value,
                avg_value=row.avg_value,
                count=row.count
            ))
        
        return stats_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")

# 获取网络设备数据
@router.get("/network", response_model=List[NetworkDeviceDataResponse])
async def get_network_device_data(
    device_id: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取网络设备监控数据"""
    query = select(NetworkDeviceData)
    
    if device_id:
        query = query.filter(NetworkDeviceData.device_id == device_id)
    if start_time:
        query = query.filter(NetworkDeviceData.timestamp >= start_time)
    if end_time:
        query = query.filter(NetworkDeviceData.timestamp <= end_time)
    
    query = query.order_by(desc(NetworkDeviceData.timestamp)).limit(limit)
    
    result = await db.execute(query)
    return result.scalars().all()

# 获取打印机数据
@router.get("/printer", response_model=List[PrinterDataResponse])
async def get_printer_data(
    device_id: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取打印机监控数据"""
    query = select(PrinterData)
    
    if device_id:
        query = query.filter(PrinterData.device_id == device_id)
    if start_time:
        query = query.filter(PrinterData.timestamp >= start_time)
    if end_time:
        query = query.filter(PrinterData.timestamp <= end_time)
    
    query = query.order_by(desc(PrinterData.timestamp)).limit(limit)
    
    result = await db.execute(query)
    return result.scalars().all()
