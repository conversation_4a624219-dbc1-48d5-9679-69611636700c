from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession # 修改导入
from sqlalchemy.future import select # 新增导入 for async
from typing import List, Optional
from database import get_db
from models.phone_extension import PhoneExtension
from schemas.phone_extension import (
    PhoneExtensionCreate,
    PhoneExtensionUpdate,
    PhoneExtensionResponse
)
from .utils import generic_update_item_async # 新增导入

router = APIRouter(
    prefix="/api/phone-extensions",
    tags=["phone_extensions"]
)

@router.get("/", response_model=List[PhoneExtensionResponse])
async def get_all_extensions( # 改为 async
    db: AsyncSession = Depends(get_db), # 改为 AsyncSession
    skip: int = 0,
    limit: int = 100
):
    """获取所有分机"""
    result = await db.execute(select(PhoneExtension).offset(skip).limit(limit)) # 改为 await
    extensions = result.scalars().all()
    return extensions

@router.get("/search", response_model=List[PhoneExtensionResponse])
async def search_extensions( # 改为 async
    q: str = Query(..., description="搜索关键词"),
    db: AsyncSession = Depends(get_db) # 改为 AsyncSession
):
    """搜索分机"""
    stmt = select(PhoneExtension).filter( # 构建查询语句
        (PhoneExtension.extension.ilike(f"%{q}%")) |
        (PhoneExtension.name.ilike(f"%{q}%")) |
        (PhoneExtension.department.ilike(f"%{q}%"))
    )
    result = await db.execute(stmt) # 改为 await
    return result.scalars().all()

@router.get("/filter", response_model=List[PhoneExtensionResponse])
async def filter_extensions( # 改为 async
    type: str = Query(..., description="分机类型"),
    db: AsyncSession = Depends(get_db) # 改为 AsyncSession
):
    """按类型筛选分机"""
    if type not in ["analog", "digital", "ip"]:
        raise HTTPException(status_code=400, detail="Invalid extension type")
    result = await db.execute(select(PhoneExtension).filter(PhoneExtension.type == type)) # 改为 await
    return result.scalars().all()

@router.get("/{extension_id}", response_model=PhoneExtensionResponse)
async def get_extension(extension_id: str, db: AsyncSession = Depends(get_db)): # 改为 async 和 AsyncSession
    """获取单个分机"""
    result = await db.execute(select(PhoneExtension).filter(PhoneExtension.id == extension_id)) # 改为 await
    extension = result.scalars().first()
    if not extension:
        raise HTTPException(status_code=404, detail="Extension not found")
    return extension

@router.post("/", response_model=PhoneExtensionResponse)
async def create_extension( # 改为 async
    extension: PhoneExtensionCreate,
    db: AsyncSession = Depends(get_db) # 改为 AsyncSession
):
    """创建新分机"""
    # 检查分机号是否已存在
    result = await db.execute(select(PhoneExtension).filter(PhoneExtension.extension == extension.extension)) # 改为 await
    existing = result.scalars().first()
    if existing:
        raise HTTPException(status_code=400, detail="Extension number already exists")

    db_extension = PhoneExtension(**extension.dict())
    db.add(db_extension)
    await db.commit()
    await db.refresh(db_extension)
    return db_extension

async def _pre_update_phone_extension_checks(db: AsyncSession, db_item: PhoneExtension, update_schema: PhoneExtensionUpdate):
    """分机更新前的特定检查。"""
    # 如果更新分机号，检查新号码是否已被其他分机使用
    if update_schema.extension and update_schema.extension != db_item.extension:
        existing_extension_with_new_number = await db.execute(
            select(PhoneExtension).filter(
                PhoneExtension.extension == update_schema.extension,
                PhoneExtension.id != db_item.id # 确保不是当前正在更新的分机
            )
        )
        if existing_extension_with_new_number.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"分机号 '{update_schema.extension}' 已被其他分机使用"
            )

@router.patch("/{extension_id}", response_model=PhoneExtensionResponse)
async def update_extension( # 改为 async
    extension_id: str,
    extension_update: PhoneExtensionUpdate, # Renamed from 'extension' for clarity
    db: AsyncSession = Depends(get_db) # 改为 AsyncSession
):
    """更新分机信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=PhoneExtension,
        item_id=extension_id,
        update_data_schema=extension_update,
        item_name="分机",
        pre_update_checks=_pre_update_phone_extension_checks
    )

@router.delete("/{extension_id}")
async def delete_extension(extension_id: str, db: AsyncSession = Depends(get_db)): # 改为 async 和 AsyncSession
    """删除分机"""
    result = await db.execute(select(PhoneExtension).filter(PhoneExtension.id == extension_id)) # 改为 await
    extension = result.scalars().first()
    if not extension:
        raise HTTPException(status_code=404, detail="Extension not found")

    await db.delete(extension) # 改为 await
    await db.commit()
    return {"message": "Extension deleted successfully"}