import logging
from typing import Type, Any, Optional, Callable, Awaitable

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
# Use an alias for sqlalchemy.update to avoid conflict if a variable named 'update' is used
from sqlalchemy import update as sqlalchemy_update_stmt 
from pydantic import BaseModel

logger = logging.getLogger(__name__)

async def generic_update_item_async(
    db: AsyncSession,
    model_cls: Type[Any],  # SQLAlchemy model class
    item_id: Any,
    update_data_schema: BaseModel,
    item_name: str = "Item",  # For user-friendly error messages
    id_attribute: str = "id",  # Model's primary key attribute name
    pre_update_checks: Optional[Callable[[AsyncSession, Any, BaseModel], Awaitable[None]]] = None,
    post_update_action: Optional[Callable[[AsyncSession, Any], Awaitable[None]]] = None,
) -> Any:
    """通用异步更新数据库条目函数。

    Args:
        db: SQLAlchemy AsyncSession.
        model_cls: SQLAlchemy 模型类。
        item_id: 要更新的条目的ID。
        update_data_schema: Pydantic schema，包含要更新的数据。
        item_name: 条目的名称，用于错误消息 (例如 "Asset Category")。
        id_attribute: 模型中ID字段的名称 (默认为 "id")。
        pre_update_checks: 可选的异步回调函数，在更新前执行特定验证。
                           签名: async def func(db: AsyncSession, db_item: Any, update_schema: BaseModel) -> None
        post_update_action: 可选的异步回调函数，在更新并提交后执行。
                            签名: async def func(db: AsyncSession, updated_item: Any) -> None

    Returns:
        更新后的 SQLAlchemy 模型实例。

    Raises:
        HTTPException: 如果条目未找到 (404) 或发生其他错误 (500)。
    """
    try:
        # 1. 获取现有条目
        stmt = select(model_cls).filter(getattr(model_cls, id_attribute) == item_id)
        result = await db.execute(stmt)
        db_item = result.scalars().first()

        # 2. 处理未找到的情况
        if not db_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{item_name} with ID {item_id} not found"
            )

        # 3. 执行更新前的检查 (特定于模型的验证)
        if pre_update_checks:
            await pre_update_checks(db, db_item, update_data_schema)

        # 4. 从请求负载更新字段
        update_values = update_data_schema.model_dump(exclude_unset=True)
        
        item_updated = False
        if update_values:
            for key, value in update_values.items():
                if hasattr(db_item, key) and getattr(db_item, key) != value:
                    setattr(db_item, key, value)
                    item_updated = True
        
        if item_updated:
            # 5. 提交更改 (仅当实际发生更改时)
            await db.commit()
            # 6. 刷新并返回更新后的条目
            await db.refresh(db_item)
        
        # 7. 执行更新后的操作
        if post_update_action:
            await post_update_action(db, db_item)
            # 如果 post_update_action 修改了条目，可能需要再次刷新
            await db.refresh(db_item) 

        return db_item
    except HTTPException:
        # 直接重新引发 HTTPExceptions (例如，来自 pre_update_checks 或 404)
        raise
    except Exception as e:
        logger.error(f"Error updating {item_name.lower()} with ID {item_id}: {e}", exc_info=True)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update {item_name.lower()}: Internal server error."
        )