from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List
from datetime import datetime

from ..database import get_db
from ..models import Rack, RackDevice, MaintenanceRecord # Ensure Rack is imported from ..models
from ..schemas.rack import (
    RackCreate,
    RackUpdate,
    RackResponse,
    DeviceCreate,
    DeviceUpdate,
    DeviceResponse,
    MaintenanceRecordCreate,
    MaintenanceRecordResponse
)
from .utils import generic_update_item_async # 新增导入

router = APIRouter(prefix="/api/racks", tags=["racks"])

# 机柜管理 API
@router.get("/", response_model=List[RackResponse])
async def get_racks(db: AsyncSession = Depends(get_db)):
    """获取所有机柜信息"""
    result = await db.execute(select(Rack))
    return result.scalars().all()

@router.post("/", response_model=RackResponse)
async def create_rack(rack: RackCreate, db: AsyncSession = Depends(get_db)):
    """创建新机柜"""
    db_rack = Rack(**rack.dict())
    db.add(db_rack)
    await db.commit()
    await db.refresh(db_rack)
    return db_rack

@router.get("/{rack_id}", response_model=RackResponse)
async def get_rack(rack_id: int, db: AsyncSession = Depends(get_db)):
    """获取指定机柜信息"""
    result = await db.execute(select(Rack).where(Rack.id == rack_id))
    rack = result.scalar_one_or_none()
    if not rack:
        raise HTTPException(status_code=404, detail="机柜不存在")
    return rack

@router.put("/{rack_id}", response_model=RackResponse)
async def update_rack(
    rack_id: int,
    rack_update: RackUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新机柜信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=Rack,
        item_id=rack_id,
        update_data_schema=rack_update,
        item_name="机柜"
    )

# 设备管理 API
@router.get("/{rack_id}/devices", response_model=List[DeviceResponse])
async def get_rack_devices(rack_id: int, db: AsyncSession = Depends(get_db)):
    """获取机柜中的所有设备"""
    result = await db.execute(
        select(RackDevice).where(RackDevice.rack_id == rack_id)
    )
    return result.scalars().all()

@router.post("/{rack_id}/devices", response_model=DeviceResponse)
async def add_device(
    rack_id: int,
    device: DeviceCreate,
    db: AsyncSession = Depends(get_db)
):
    """添加设备到机柜"""
    # 检查机柜是否存在
    rack_result = await db.execute(select(Rack).where(Rack.id == rack_id))
    rack = rack_result.scalar_one_or_none()
    if not rack:
        raise HTTPException(status_code=404, detail="机柜不存在")
    
    # 检查U位是否可用
    device_result = await db.execute(
        select(RackDevice).where(
            RackDevice.rack_id == rack_id,
            (
                (RackDevice.position_start <= device.position_end) &
                (RackDevice.position_end >= device.position_start)
            )
        )
    )
    if device_result.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="U位已被占用")
    
    # 创建设备
    db_device = RackDevice(**device.dict(), rack_id=rack_id)
    db.add(db_device)
    
    # 更新机柜使用情况
    rack.used_u += (device.position_end - device.position_start + 1)
    rack.utilization = (rack.used_u / rack.total_u) * 100
    
    await db.commit()
    await db.refresh(db_device)
    return db_device

# 维护记录 API
@router.get("/devices/{device_id}/maintenance", response_model=List[MaintenanceRecordResponse])
async def get_maintenance_records(device_id: int, db: AsyncSession = Depends(get_db)):
    """获取设备的维护记录"""
    result = await db.execute(
        select(MaintenanceRecord).where(MaintenanceRecord.device_id == device_id)
    )
    return result.scalars().all()

@router.post("/devices/{device_id}/maintenance", response_model=MaintenanceRecordResponse)
async def add_maintenance_record(
    device_id: int,
    record: MaintenanceRecordCreate,
    db: AsyncSession = Depends(get_db)
):
    """添加维护记录"""
    # 检查设备是否存在
    device_result = await db.execute(select(RackDevice).where(RackDevice.id == device_id))
    device = device_result.scalar_one_or_none()
    if not device:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    db_record = MaintenanceRecord(**record.dict(), device_id=device_id)
    db.add(db_record)
    await db.commit()
    await db.refresh(db_record)
    return db_record