from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from typing import List, Optional
from datetime import datetime

from database import get_db
from models.snmp_item import SNMPItem
from schemas.snmp_item import (
    SNMPItemCreate, SNMPItemUpdate, SNMPItemResponse, SNMPItemBatchCreate
)
from .utils import generic_update_item_async # 新增导入

router = APIRouter(prefix="/api/snmp-items", tags=["snmp-items"])

# 创建SNMP监控项
@router.post("", response_model=SNMPItemResponse)
async def create_snmp_item(item: SNMPItemCreate, db: AsyncSession = Depends(get_db)):
    """创建SNMP监控项"""
    db_item = SNMPItem(**item.model_dump())
    db.add(db_item)
    await db.commit()
    await db.refresh(db_item)
    return db_item

# 批量创建SNMP监控项
@router.post("/batch", response_model=List[SNMPItemResponse])
async def create_snmp_items_batch(batch: SNMPItemBatchCreate, db: AsyncSession = Depends(get_db)):
    """批量创建SNMP监控项"""
    db_items = []
    for item in batch.items:
        db_item = SNMPItem(**item.model_dump())
        db.add(db_item)
        db_items.append(db_item)
    
    await db.commit()
    
    # 刷新所有项
    for item in db_items:
        await db.refresh(item)
    
    return db_items

# 获取SNMP监控项列表
@router.get("", response_model=List[SNMPItemResponse])
async def get_snmp_items(
    device_id: Optional[str] = None,
    item_type: Optional[str] = None,
    location: Optional[str] = None,
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取SNMP监控项列表"""
    query = select(SNMPItem)
    
    if device_id:
        query = query.filter(SNMPItem.device_id == device_id)
    if item_type:
        query = query.filter(SNMPItem.item_type == item_type)
    if location:
        query = query.filter(SNMPItem.location == location)
    if status:
        query = query.filter(SNMPItem.status == status)
    
    result = await db.execute(query)
    return result.scalars().all()

# 获取SNMP监控项详情
@router.get("/{item_id}", response_model=SNMPItemResponse)
async def get_snmp_item(item_id: int, db: AsyncSession = Depends(get_db)):
    """获取SNMP监控项详情"""
    result = await db.execute(select(SNMPItem).filter(SNMPItem.id == item_id))
    item = result.scalars().first()
    
    if not item:
        raise HTTPException(status_code=404, detail=f"SNMP监控项 ID {item_id} 不存在")
    
    return item

# 更新SNMP监控项
@router.put("/{item_id}", response_model=SNMPItemResponse)
async def update_snmp_item(
    item_id: int,
    item_update: SNMPItemUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新SNMP监控项"""
    # 预更新检查可以根据需要添加，例如检查 item_update 中的 device_id 是否有效等
    # async def _pre_update_snmp_item_checks(db_session: AsyncSession, db_item: SNMPItem, update_schema: SNMPItemUpdate):
    #     pass

    return await generic_update_item_async(
        db=db,
        model_cls=SNMPItem,
        item_id=item_id,
        update_data_schema=item_update,
        item_name="SNMP监控项"
        # pre_update_checks=_pre_update_snmp_item_checks # 如果有预检查，取消注释此行
    )

# 删除SNMP监控项
@router.delete("/{item_id}")
async def delete_snmp_item(item_id: int, db: AsyncSession = Depends(get_db)):
    """删除SNMP监控项"""
    result = await db.execute(select(SNMPItem).filter(SNMPItem.id == item_id))
    item = result.scalars().first()
    
    if not item:
        raise HTTPException(status_code=404, detail=f"SNMP监控项 ID {item_id} 不存在")
    
    await db.delete(item)
    await db.commit()
    
    return {"message": f"SNMP监控项 ID {item_id} 已成功删除"}
