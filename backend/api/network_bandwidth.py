from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, asc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from database import get_db
from models.network_bandwidth import (
    NetworkDevice, NetworkInterface, BandwidthRecord, TrafficThreshold
)
from schemas.network_bandwidth import (
    NetworkDeviceCreate, NetworkDeviceUpdate, NetworkDeviceResponse,
    NetworkInterfaceCreate, NetworkInterfaceUpdate, NetworkInterfaceResponse,
    BandwidthRecordResponse, TrafficThresholdCreate, TrafficThresholdUpdate,
    TrafficThresholdResponse
)
from device_monitor.connectors.enhanced_snmp_connector import EnhancedSNMPConnector
from .utils import generic_update_item_async # 新增导入

router = APIRouter(prefix="/api/network", tags=["network"])

# 网络设备管理
@router.get("/devices", response_model=List[NetworkDeviceResponse])
async def get_network_devices(
    status: Optional[str] = None,
    device_type: Optional[str] = None,
    location: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取网络设备列表，可以按状态、类型和位置筛选"""
    query = select(NetworkDevice)

    # 应用筛选条件
    if status:
        query = query.filter(NetworkDevice.status == status)
    if device_type:
        query = query.filter(NetworkDevice.device_type == device_type)
    if location:
        query = query.filter(NetworkDevice.location == location)

    result = await db.execute(query)
    devices = result.scalars().all()
    return devices

@router.get("/devices/{device_id}", response_model=NetworkDeviceResponse)
async def get_network_device(device_id: int, db: AsyncSession = Depends(get_db)):
    """获取单个网络设备的详细信息"""
    result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"网络设备 ID {device_id} 不存在")

    return device

@router.post("/devices", response_model=NetworkDeviceResponse)
async def create_network_device(device: NetworkDeviceCreate, db: AsyncSession = Depends(get_db)):
    """创建新网络设备"""
    # 检查IP地址是否已存在
    if device.ip_address:
        result = await db.execute(select(NetworkDevice).filter(NetworkDevice.ip_address == device.ip_address))
        existing = result.scalars().first()
        if existing:
            raise HTTPException(status_code=400, detail=f"IP地址 {device.ip_address} 已被使用")

    db_device = NetworkDevice(**device.model_dump())
    db.add(db_device)
    await db.commit()
    await db.refresh(db_device)
    return db_device

async def _pre_update_network_device_checks(db: AsyncSession, db_item: NetworkDevice, update_schema: NetworkDeviceUpdate):
    """网络设备更新前的特定检查。"""
    # 检查IP地址是否已被其他设备使用 (如果提供了 ip_address 且与现有 ip_address 不同)
    if update_schema.ip_address and update_schema.ip_address != db_item.ip_address:
        existing_device_with_new_ip = await db.execute(
            select(NetworkDevice).filter(
                NetworkDevice.ip_address == update_schema.ip_address, 
                NetworkDevice.id != db_item.id
            )
        )
        if existing_device_with_new_ip.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"IP地址 {update_schema.ip_address} 已被其他设备使用"
            )

@router.put("/devices/{device_id}", response_model=NetworkDeviceResponse)
async def update_network_device(
    device_id: int,
    device_update: NetworkDeviceUpdate, # Renamed from 'device' to 'device_update' for clarity
    db: AsyncSession = Depends(get_db)
):
    """更新网络设备信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=NetworkDevice,
        item_id=device_id,
        update_data_schema=device_update,
        item_name="网络设备",
        pre_update_checks=_pre_update_network_device_checks
    )

@router.delete("/devices/{device_id}")
async def delete_network_device(device_id: int, db: AsyncSession = Depends(get_db)):
    """删除网络设备"""
    result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"网络设备 ID {device_id} 不存在")

    await db.delete(device)
    await db.commit()
    return {"message": f"网络设备 ID {device_id} 已删除"}

# 网络接口管理
@router.get("/devices/{device_id}/interfaces", response_model=List[NetworkInterfaceResponse])
async def get_device_interfaces(device_id: int, db: AsyncSession = Depends(get_db)):
    """获取设备的网络接口列表"""
    result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"网络设备 ID {device_id} 不存在")

    result = await db.execute(
        select(NetworkInterface).filter(NetworkInterface.device_id == device_id)
    )
    interfaces = result.scalars().all()
    return interfaces

@router.post("/devices/{device_id}/interfaces", response_model=NetworkInterfaceResponse)
async def add_device_interface(
    device_id: int,
    interface: NetworkInterfaceCreate,
    db: AsyncSession = Depends(get_db)
):
    """添加网络接口"""
    result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"网络设备 ID {device_id} 不存在")

    # 检查接口名称是否已存在
    result = await db.execute(
        select(NetworkInterface).filter(
            and_(
                NetworkInterface.device_id == device_id,
                NetworkInterface.name == interface.name
            )
        )
    )
    existing = result.scalars().first()
    if existing:
        raise HTTPException(status_code=400, detail=f"接口名称 {interface.name} 已存在于该设备")

    db_interface = NetworkInterface(**interface.model_dump(), device_id=device_id)
    db.add(db_interface)
    await db.commit()
    await db.refresh(db_interface)
    return db_interface

async def _pre_update_network_interface_checks(db: AsyncSession, db_item: NetworkInterface, update_schema: NetworkInterfaceUpdate):
    """网络接口更新前的特定检查。"""
    # 检查接口名称是否已被同一设备上的其他接口使用
    if update_schema.name and update_schema.name != db_item.name:
        existing_interface_with_new_name = await db.execute(
            select(NetworkInterface).filter(
                NetworkInterface.device_id == db_item.device_id,
                NetworkInterface.name == update_schema.name,
                NetworkInterface.id != db_item.id
            )
        )
        if existing_interface_with_new_name.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"接口名称 {update_schema.name} 已被该设备上的其他接口使用"
            )

@router.put("/interfaces/{interface_id}", response_model=NetworkInterfaceResponse)
async def update_interface(
    interface_id: int,
    interface_update: NetworkInterfaceUpdate, # Renamed from 'interface' for clarity
    db: AsyncSession = Depends(get_db)
):
    """更新网络接口信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=NetworkInterface,
        item_id=interface_id,
        update_data_schema=interface_update,
        item_name="网络接口",
        pre_update_checks=_pre_update_network_interface_checks
    )

# 带宽记录管理
@router.get("/devices/{device_id}/bandwidth")
async def get_device_bandwidth(
    device_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取设备当前带宽使用情况（通过SNMP）"""
    result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"网络设备 ID {device_id} 不存在")

    if not device.snmp_enabled or not device.ip_address:
        raise HTTPException(status_code=400, detail="该设备未启用SNMP或未设置IP地址")

    # 创建SNMP连接器
    config = {
        "host": device.ip_address,
        "port": device.snmp_port,
        "community": device.snmp_community,
        "version": device.snmp_version,
        "device_type": "network",
        "device_vendor": device.manufacturer.lower() if device.manufacturer else ""
    }

    connector = EnhancedSNMPConnector("snmp", config)

    # 检查连接
    connection_ok = await connector.check_connection()
    if not connection_ok:
        # 更新设备状态为离线
        device.status = "offline"
        device.last_seen = datetime.now()
        await db.commit()
        return {"status": "offline", "message": "无法连接到设备"}

    # 获取网络指标
    metrics = await connector.get_metrics()

    # 更新设备状态和指标
    if "error" in metrics:
        device.status = "error"
        device.last_seen = datetime.now()
        await db.commit()
        return {"status": "error", "message": metrics["error"]}

    # 更新设备信息
    device.status = "online"
    device.last_seen = datetime.now()

    if "system" in metrics:
        if "cpu_usage" in metrics["system"]:
            device.cpu_usage = metrics["system"]["cpu_usage"]
        if "memory_usage" in metrics["system"]:
            device.memory_usage = metrics["system"]["memory_usage"]

    await db.commit()

    # 保存接口信息
    if "interfaces" in metrics:
        for if_data in metrics["interfaces"]:
            if_name = if_data.get("name")
            if not if_name:
                continue

            # 查找或创建接口
            result = await db.execute(
                select(NetworkInterface).filter(
                    and_(
                        NetworkInterface.device_id == device_id,
                        NetworkInterface.name == if_name
                    )
                )
            )
            interface = result.scalars().first()

            if not interface:
                # 创建新接口
                interface = NetworkInterface(
                    device_id=device_id,
                    name=if_name,
                    description=if_name,
                    interface_type="unknown",
                    mac_address=if_data.get("mac_address"),
                    speed=if_data.get("speed", 0),
                    admin_status=if_data.get("admin_status", False),
                    operational_status=if_data.get("oper_status", False)
                )
                db.add(interface)
                await db.commit()
                await db.refresh(interface)
            else:
                # 更新接口信息
                interface.mac_address = if_data.get("mac_address", interface.mac_address)
                interface.speed = if_data.get("speed", interface.speed)
                interface.admin_status = if_data.get("admin_status", interface.admin_status)
                interface.operational_status = if_data.get("oper_status", interface.operational_status)
                interface.in_octets = if_data.get("in_octets", interface.in_octets)
                interface.out_octets = if_data.get("out_octets", interface.out_octets)
                interface.in_packets = if_data.get("in_packets", interface.in_packets)
                interface.out_packets = if_data.get("out_packets", interface.out_packets)
                interface.in_errors = if_data.get("in_errors", interface.in_errors)
                interface.out_errors = if_data.get("out_errors", interface.out_errors)
                interface.in_discards = if_data.get("in_discards", interface.in_discards)
                interface.out_discards = if_data.get("out_discards", interface.out_discards)
                interface.last_updated = datetime.now()
                await db.commit()

            # 保存带宽记录
            if "bandwidth" in metrics and if_name in metrics["bandwidth"]:
                bw_data = metrics["bandwidth"][if_name]

                # 创建带宽记录
                bandwidth_record = BandwidthRecord(
                    device_id=device_id,
                    interface_id=interface.id,
                    timestamp=datetime.now(),
                    in_octets=if_data.get("in_octets", 0),
                    out_octets=if_data.get("out_octets", 0),
                    in_packets=if_data.get("in_packets", 0),
                    out_packets=if_data.get("out_packets", 0),
                    in_errors=if_data.get("in_errors", 0),
                    out_errors=if_data.get("out_errors", 0),
                    in_discards=if_data.get("in_discards", 0),
                    out_discards=if_data.get("out_discards", 0),
                    in_bandwidth=bw_data.get("in_bps", 0),
                    out_bandwidth=bw_data.get("out_bps", 0),
                    in_utilization=bw_data.get("in_utilization", 0),
                    out_utilization=bw_data.get("out_utilization", 0)
                )
                db.add(bandwidth_record)

    await db.commit()

    return {
        "status": "online",
        "last_seen": device.last_seen,
        "metrics": metrics
    }

@router.get("/interfaces/{interface_id}/bandwidth-history", response_model=List[BandwidthRecordResponse])
async def get_interface_bandwidth_history(
    interface_id: int,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db)
):
    """获取接口的带宽历史记录"""
    result = await db.execute(select(NetworkInterface).filter(NetworkInterface.id == interface_id))
    interface = result.scalars().first()

    if not interface:
        raise HTTPException(status_code=404, detail=f"网络接口 ID {interface_id} 不存在")

    # 构建查询
    query = select(BandwidthRecord).filter(BandwidthRecord.interface_id == interface_id)

    if start_time:
        query = query.filter(BandwidthRecord.timestamp >= start_time)
    else:
        # 默认查询最近24小时
        query = query.filter(BandwidthRecord.timestamp >= datetime.now() - timedelta(days=1))

    if end_time:
        query = query.filter(BandwidthRecord.timestamp <= end_time)

    # 按时间排序并限制结果数量
    query = query.order_by(asc(BandwidthRecord.timestamp)).limit(limit)

    result = await db.execute(query)
    records = result.scalars().all()
    return records

# 流量阈值管理
@router.get("/thresholds", response_model=List[TrafficThresholdResponse])
async def get_traffic_thresholds(
    device_id: Optional[int] = None,
    interface_id: Optional[int] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取流量阈值列表"""
    query = select(TrafficThreshold)

    if device_id:
        query = query.filter(TrafficThreshold.device_id == device_id)
    if interface_id:
        query = query.filter(TrafficThreshold.interface_id == interface_id)

    result = await db.execute(query)
    thresholds = result.scalars().all()
    return thresholds

@router.post("/thresholds", response_model=TrafficThresholdResponse)
async def create_traffic_threshold(
    threshold: TrafficThresholdCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建流量阈值"""
    # 检查设备是否存在
    if threshold.device_id:
        result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == threshold.device_id))
        device = result.scalars().first()
        if not device:
            raise HTTPException(status_code=404, detail=f"网络设备 ID {threshold.device_id} 不存在")

    # 检查接口是否存在
    if threshold.interface_id:
        result = await db.execute(select(NetworkInterface).filter(NetworkInterface.id == threshold.interface_id))
        interface = result.scalars().first()
        if not interface:
            raise HTTPException(status_code=404, detail=f"网络接口 ID {threshold.interface_id} 不存在")

    db_threshold = TrafficThreshold(**threshold.model_dump())
    db.add(db_threshold)
    await db.commit()
    await db.refresh(db_threshold)
    return db_threshold

@router.put("/thresholds/{threshold_id}", response_model=TrafficThresholdResponse)
async def update_traffic_threshold(
    threshold_id: int,
    threshold: TrafficThresholdUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新流量阈值"""
    result = await db.execute(select(TrafficThreshold).filter(TrafficThreshold.id == threshold_id))
    db_threshold = result.scalars().first()

    if not db_threshold:
        raise HTTPException(status_code=404, detail=f"流量阈值 ID {threshold_id} 不存在")

    # 检查设备是否存在
    if threshold.device_id and threshold.device_id != db_threshold.device_id:
        result = await db.execute(select(NetworkDevice).filter(NetworkDevice.id == threshold.device_id))
        device = result.scalars().first()
        if not device:
            raise HTTPException(status_code=404, detail=f"网络设备 ID {threshold.device_id} 不存在")

    # 检查接口是否存在
    if threshold.interface_id and threshold.interface_id != db_threshold.interface_id:
        result = await db.execute(select(NetworkInterface).filter(NetworkInterface.id == threshold.interface_id))
        interface = result.scalars().first()
        if not interface:
            raise HTTPException(status_code=404, detail=f"网络接口 ID {threshold.interface_id} 不存在")

    # 更新阈值信息
    update_data = threshold.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_threshold, key, value)

    await db.commit()
    await db.refresh(db_threshold)
    return db_threshold

@router.delete("/thresholds/{threshold_id}")
async def delete_traffic_threshold(threshold_id: int, db: AsyncSession = Depends(get_db)):
    """删除流量阈值"""
    result = await db.execute(select(TrafficThreshold).filter(TrafficThreshold.id == threshold_id))
    threshold = result.scalars().first()

    if not threshold:
        raise HTTPException(status_code=404, detail=f"流量阈值 ID {threshold_id} 不存在")

    await db.delete(threshold)
    await db.commit()
    return {"message": f"流量阈值 ID {threshold_id} 已删除"}

# 批量操作
@router.post("/batch-status-update")
async def update_all_network_devices_status(db: AsyncSession = Depends(get_db)):
    """批量更新所有启用SNMP的网络设备状态"""
    result = await db.execute(
        select(NetworkDevice).filter(
            and_(
                NetworkDevice.snmp_enabled == True,
                NetworkDevice.ip_address != None
            )
        )
    )
    devices = result.scalars().all()

    update_results = []
    for device in devices:
        try:
            # 创建SNMP连接器
            config = {
                "host": device.ip_address,
                "port": device.snmp_port,
                "community": device.snmp_community,
                "version": device.snmp_version,
                "device_type": "network",
                "device_vendor": device.manufacturer.lower() if device.manufacturer else ""
            }

            connector = EnhancedSNMPConnector("snmp", config)

            # 检查连接
            connection_ok = await connector.check_connection()
            if not connection_ok:
                device.status = "offline"
                device.last_seen = datetime.now()
                update_results.append({
                    "id": device.id,
                    "name": device.name,
                    "status": "offline",
                    "success": True
                })
                continue

            # 获取网络指标
            metrics = await connector.get_metrics()

            # 更新设备状态和指标
            if "error" in metrics:
                device.status = "error"
                device.last_seen = datetime.now()
            else:
                device.status = "online"
                device.last_seen = datetime.now()

                if "system" in metrics:
                    if "cpu_usage" in metrics["system"]:
                        device.cpu_usage = metrics["system"]["cpu_usage"]
                    if "memory_usage" in metrics["system"]:
                        device.memory_usage = metrics["system"]["memory_usage"]

            update_results.append({
                "id": device.id,
                "name": device.name,
                "status": device.status,
                "success": True
            })
        except Exception as e:
            update_results.append({
                "id": device.id,
                "name": device.name,
                "status": "error",
                "success": False,
                "error": str(e)
            })

    await db.commit()
    return {
        "total": len(devices),
        "success": sum(1 for r in update_results if r["success"]),
        "failed": sum(1 for r in update_results if not r["success"]),
        "results": update_results
    }
