import asyncio
import logging
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc

from database import get_db
from models.monitoring import EnvironmentData, UPSData, MainsPowerData, MonitoringDevice
# 简单的SNMP连接器实现
class SNMPConnector:
    def __init__(self, protocol, config):
        self.protocol = protocol
        self.config = config
        self.device_id = config.get('device_id')
        self.host = config.get('host')
        self.port = config.get('port', 161)
        self.community = config.get('community', 'public')
        self.version = config.get('version', 2)
        self.device_type = config.get('device_type')
        self.oids = config.get('oids', {})
    
    def get(self, oid):
        # 简单的SNMP GET实现，实际项目中应使用pysnmp等库
        return None
    
    def walk(self, oid):
        # 简单的SNMP WALK实现
        return []
    
    async def get_device_info(self):
        """获取设备信息"""
        # 模拟设备信息
        class DeviceInfo:
            def __init__(self, device_id):
                self.status = "online"
                self.name = f"Device-{device_id or 'unknown'}"
                self.uptime = 3600
        
        return DeviceInfo(self.device_id)
    
    async def get_metrics(self):
        """获取设备指标"""
        # 根据设备类型返回模拟数据
        if self.device_type == "environment":
            return {
                "temperature": 25.5,
                "humidity": 60.0,
                "smoke": False,
                "water": False
            }
        elif self.device_type == "ups":
            return {
                "battery_level": 85,
                "load_percentage": 45,
                "input_voltage": 220,
                "output_voltage": 220,
                "runtime_remaining": 1800
            }
        elif self.device_type == "mains":
            return {
                "voltage": 220,
                "current": 10.5,
                "power": 2310,
                "frequency": 50
            }
        else:
            return {}

logger = logging.getLogger(__name__)

class MonitoringService:
    """监控服务，负责从设备获取数据并存储到数据库"""

    def __init__(self):
        self.connectors = {}  # 设备连接器
        self.polling_interval = 300  # 默认5分钟
        self.is_running = False
        self.polling_task = None

    async def start(self):
        """启动监控服务"""
        if self.is_running:
            logger.warning("监控服务已经在运行")
            return

        try:
            # 加载设备配置
            await self.load_devices()

            # 启动轮询任务
            self.is_running = True
            self.polling_task = asyncio.create_task(self._polling_loop())

            logger.info(f"监控服务已启动，轮询间隔：{self.polling_interval}秒")

        except Exception as e:
            logger.error(f"启动监控服务失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止监控服务"""
        if not self.is_running:
            logger.warning("监控服务未在运行")
            return

        try:
            self.is_running = False
            if self.polling_task:
                self.polling_task.cancel()
                try:
                    await self.polling_task
                except asyncio.CancelledError:
                    pass
                self.polling_task = None

            logger.info("监控服务已停止")

        except Exception as e:
            logger.error(f"停止监控服务失败: {e}")

    async def load_devices(self):
        """从数据库加载设备配置"""
        try:
            async for db in get_db():
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.status == "active"))
                devices = result.scalars().all()

                # 创建设备连接器
                for device in devices:
                    if device.protocol == "snmp":
                        config = {
                            "device_id": device.device_id,
                            "host": device.host,
                            "port": device.port,
                            "community": device.community,
                            "version": device.version,
                            "device_type": device.device_type,
                            "oids": json.loads(device.oids) if device.oids else {}
                        }
                        self.connectors[device.device_id] = SNMPConnector("snmp", config)

                logger.info(f"已加载 {len(self.connectors)} 个监控设备")
                break

        except Exception as e:
            logger.error(f"加载设备配置失败: {e}")

    async def _polling_loop(self):
        """轮询所有设备的数据收集任务"""
        try:
            while self.is_running:
                start_time = datetime.now().timestamp()

                # 对每个设备创建一个收集任务
                tasks = []
                for device_id in self.connectors:
                    task = asyncio.create_task(self.collect_device_data(device_id))
                    tasks.append(task)

                # 等待所有任务完成
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)

                # 计算下一次轮询的延迟
                elapsed = datetime.now().timestamp() - start_time
                next_poll = max(1, self.polling_interval - elapsed)

                logger.debug(f"完成轮询周期，耗时: {elapsed:.2f}秒, 下一次在 {next_poll:.2f}秒后")

                # 等待下一个轮询周期
                await asyncio.sleep(next_poll)

        except asyncio.CancelledError:
            # 正常取消
            pass
        except Exception as e:
            logger.error(f"轮询循环异常: {e}")
            self.is_running = False

    async def collect_device_data(self, device_id: str):
        """收集指定设备的数据并存储到数据库"""
        if device_id not in self.connectors:
            logger.warning(f"未知设备ID: {device_id}")
            return

        connector = self.connectors[device_id]
        try:
            # 获取设备信息
            device_info = await connector.get_device_info()

            # 获取设备指标
            metrics = await connector.get_metrics()

            # 根据设备类型存储数据
            if connector.device_type == "environment":
                await self._store_environment_data(device_id, device_info, metrics)
            elif connector.device_type == "ups":
                await self._store_ups_data(device_id, device_info, metrics)
            elif connector.device_type == "mains":
                await self._store_mains_data(device_id, device_info, metrics)

            logger.info(f"已收集设备数据: {device_id}, 状态: {device_info.status}")

        except Exception as e:
            logger.error(f"收集设备数据失败 {device_id}: {e}")

    async def _store_environment_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储环境监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建环境数据记录
                env_data = EnvironmentData(
                    device_id=device_id,
                    location=device.location,
                    floor=device.floor,
                    temperature=metrics.get("temperature", 0),
                    humidity=metrics.get("humidity", 0),
                    smoke=metrics.get("smoke", False),
                    water=metrics.get("water", False),
                    status=device_info.status
                )

                db.add(env_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储环境监控数据失败 {device_id}: {e}")

    async def _store_ups_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储UPS监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建UPS数据记录
                ups_data = UPSData(
                    device_id=device_id,
                    name=device.name,
                    location=device.location,
                    status=device_info.status,
                    load=metrics.get("load", 0),
                    battery_level=metrics.get("battery_level", 0),
                    battery_time_remaining=metrics.get("battery_time_remaining", 0),
                    input_voltage=metrics.get("input_voltage", 0),
                    output_voltage=metrics.get("output_voltage", 0),
                    input_frequency=metrics.get("input_frequency", 0),
                    output_frequency=metrics.get("output_frequency", 0),
                    temperature=metrics.get("temperature", 0),
                    battery_voltage=metrics.get("battery_voltage", 0)
                )

                db.add(ups_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储UPS监控数据失败 {device_id}: {e}")

    async def _store_mains_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储市电监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建市电数据记录
                mains_data = MainsPowerData(
                    device_id=device_id,
                    location=device.location,
                    status=device_info.status,
                    voltage=metrics.get("voltage", 0),
                    frequency=metrics.get("frequency", 0),
                    current=metrics.get("current", 0),
                    power=metrics.get("power", 0),
                    power_factor=metrics.get("power_factor", 0),
                    energy_consumption=metrics.get("energy_consumption", 0)
                )

                db.add(mains_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储市电监控数据失败 {device_id}: {e}")

# 全局服务实例
monitoring_service = None

def get_monitoring_service():
    """获取或创建监控服务实例"""
    global monitoring_service
    if monitoring_service is None:
        monitoring_service = MonitoringService()
    return monitoring_service
