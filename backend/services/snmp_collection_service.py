import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from database import get_db
from models.monitoring import EnvironmentData, UPSData, MainsPowerData, MonitoringDevice
from models.wireless_network import WirelessDevice, WirelessDeviceData, WirelessClient
from models.snmp_item import SNMPItem
# 简单的SNMP连接器实现
class SNMPConnector:
    def __init__(self, protocol, config):
        self.protocol = protocol
        self.config = config
        self.device_id = config.get('device_id')
        self.host = config.get('host')
        self.port = config.get('port', 161)
        self.community = config.get('community', 'public')
        self.version = config.get('version', 2)
        self.device_type = config.get('device_type')
        self.oids = config.get('oids', {})
    
    def get(self, oid):
        # 简单的SNMP GET实现，实际项目中应使用pysnmp等库
        return None
    
    def walk(self, oid):
        # 简单的SNMP WALK实现
        return []
    
    async def get_device_info(self):
        """获取设备信息"""
        # 模拟设备信息
        class DeviceInfo:
            def __init__(self, device_id):
                self.status = "online"
                self.name = f"Device-{device_id or 'unknown'}"
                self.uptime = 3600
        
        return DeviceInfo(self.device_id)
    
    async def get_metrics(self):
        """获取设备指标"""
        # 根据设备类型返回模拟数据
        if self.device_type == "environment":
            return {
                "temperature": 25.5,
                "humidity": 60.0,
                "smoke": False,
                "water": False
            }
        elif self.device_type == "ups":
            return {
                "battery_level": 85,
                "load_percentage": 45,
                "input_voltage": 220,
                "output_voltage": 220,
                "runtime_remaining": 1800
            }
        elif self.device_type == "mains":
            return {
                "voltage": 220,
                "current": 10.5,
                "power": 2310,
                "frequency": 50
            }
        elif self.device_type == "network":
            return {
                "bandwidth_in": 1000000,
                "bandwidth_out": 500000,
                "packets_in": 1000,
                "packets_out": 800
            }
        elif self.device_type == "printer":
            return {
                "pages_printed": 12345,
                "toner_level": 75,
                "paper_level": 90,
                "status": "ready"
            }
        elif self.device_type == "wireless":
            return {
                "connected_clients": 25,
                "signal_strength": -45,
                "channel": 6,
                "bandwidth_usage": 60
            }
        else:
            return {}

logger = logging.getLogger(__name__)

class SNMPCollectionService:
    """
    统一SNMP采集服务

    负责管理所有SNMP采集任务，包括：
    - 打印机管理
    - 网络带宽监控
    - 环境监控
    - UPS监控
    - 市电监控
    """

    def __init__(self):
        self.connectors: Dict[str, SNMPConnector] = {}  # 设备连接器
        self.snmp_items: Dict[str, List[SNMPItem]] = {}  # 设备监控项
        self.polling_intervals: Dict[str, int] = {
            "default": 300,  # 默认5分钟
            "environment": 60,  # 环境监控1分钟
            "ups": 60,  # UPS监控1分钟
            "mains": 60,  # 市电监控1分钟
            "network": 300,  # 网络监控5分钟
            "printer": 900,  # 打印机监控15分钟
            "wireless": 300,  # 无线网络5分钟
        }
        self.is_running = False
        self.polling_tasks = {}
        self.last_collection_time = {}

    async def start(self):
        """启动SNMP采集服务"""
        if self.is_running:
            logger.warning("SNMP采集服务已经在运行")
            return

        try:
            # 加载设备配置
            await self.load_devices()

            # 加载SNMP监控项
            await self.load_snmp_items()

            # 启动轮询任务
            self.is_running = True

            # 为每种设备类型创建单独的轮询任务
            for device_type in set([connector.device_type for connector in self.connectors.values()]):
                interval = self.polling_intervals.get(device_type, self.polling_intervals["default"])
                self.polling_tasks[device_type] = asyncio.create_task(
                    self._polling_loop(device_type, interval)
                )

            logger.info(f"SNMP采集服务已启动，共 {len(self.connectors)} 个设备")

        except Exception as e:
            logger.error(f"启动SNMP采集服务失败: {e}")
            self.is_running = False

    async def stop(self):
        """停止SNMP采集服务"""
        if not self.is_running:
            logger.warning("SNMP采集服务未在运行")
            return

        try:
            self.is_running = False

            # 取消所有轮询任务
            for device_type, task in self.polling_tasks.items():
                if task:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            self.polling_tasks = {}
            logger.info("SNMP采集服务已停止")

        except Exception as e:
            logger.error(f"停止SNMP采集服务失败: {e}")

    async def load_devices(self):
        """从数据库加载设备配置"""
        try:
            async for db in get_db():
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.status == "active"))
                devices = result.scalars().all()

                # 创建设备连接器
                for device in devices:
                    if device.protocol == "snmp":
                        config = {
                            "device_id": device.device_id,
                            "host": device.host,
                            "port": device.port,
                            "community": device.community,
                            "version": device.version,
                            "device_type": device.device_type,
                            "oids": json.loads(device.oids) if device.oids else {}
                        }
                        self.connectors[device.device_id] = SNMPConnector("snmp", config)

                logger.info(f"已加载 {len(self.connectors)} 个SNMP设备")
                break

        except Exception as e:
            logger.error(f"加载SNMP设备配置失败: {e}")

    async def load_snmp_items(self):
        """从数据库加载SNMP监控项配置"""
        try:
            async for db in get_db():
                result = await db.execute(select(SNMPItem).filter(SNMPItem.status == "active"))
                items = result.scalars().all()

                # 按设备ID分组
                for item in items:
                    if item.device_id not in self.snmp_items:
                        self.snmp_items[item.device_id] = []
                    self.snmp_items[item.device_id].append(item)

                logger.info(f"已加载 {len(items)} 个SNMP监控项，涉及 {len(self.snmp_items)} 个设备")
                break

        except Exception as e:
            logger.error(f"加载SNMP监控项配置失败: {e}")

    async def _polling_loop(self, device_type: str, interval: int):
        """特定设备类型的轮询循环"""
        try:
            while self.is_running:
                start_time = time.time()
                logger.debug(f"开始 {device_type} 设备轮询周期")

                # 获取此类型的所有设备
                device_ids = [
                    device_id for device_id, connector in self.connectors.items()
                    if connector.device_type == device_type
                ]

                # 对每个设备创建一个收集任务
                tasks = []
                for device_id in device_ids:
                    task = asyncio.create_task(self.collect_device_data(device_id))
                    tasks.append(task)

                # 等待所有任务完成
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)

                # 计算下一次轮询的延迟
                elapsed = time.time() - start_time
                next_poll = max(1, interval - elapsed)

                logger.debug(f"完成 {device_type} 轮询周期，耗时: {elapsed:.2f}秒，下一次在 {next_poll:.2f}秒后")

                # 等待下一个轮询周期
                await asyncio.sleep(next_poll)

        except asyncio.CancelledError:
            # 正常取消
            pass
        except Exception as e:
            logger.error(f"{device_type} 轮询循环异常: {e}")
            if device_type in self.polling_tasks:
                del self.polling_tasks[device_type]

    async def collect_device_data(self, device_id: str):
        """收集指定设备的数据并存储到数据库"""
        if device_id not in self.connectors:
            logger.warning(f"未知设备ID: {device_id}")
            return

        connector = self.connectors[device_id]
        start_time = time.time()
        status = "success"
        error_msg = ""
        metrics = {}
        
        try:
            # 记录采集开始时间
            self.last_collection_time[device_id] = datetime.now()

            # 获取设备信息
            device_info = await connector.get_device_info()

            # 获取设备指标
            metrics = await connector.get_metrics()

            # 根据设备类型存储数据
            if connector.device_type == "environment":
                await self._store_environment_data(device_id, device_info, metrics)
            elif connector.device_type == "ups":
                await self._store_ups_data(device_id, device_info, metrics)
            elif connector.device_type == "mains":
                await self._store_mains_data(device_id, device_info, metrics)
            elif connector.device_type == "network":
                await self._store_network_data(device_id, device_info, metrics)
            elif connector.device_type == "printer":
                await self._store_printer_data(device_id, device_info, metrics)
            elif connector.device_type == "wireless":
                await self._store_wireless_data(device_id, device_info, metrics)

            # 处理自定义SNMP监控项
            if device_id in self.snmp_items:
                await self._process_custom_snmp_items(device_id, metrics)

            logger.info(f"已收集设备数据: {device_id}, 状态: {device_info.status}")

        except Exception as e:
            status = "failed"
            error_msg = str(e)
            logger.error(f"收集设备数据失败 {device_id}: {error_msg}")
        finally:
            # 记录采集日志
            duration = time.time() - start_time
            await self._log_collection(
                device_id,
                status,
                duration,
                metrics,
                error_msg
            )

    async def _store_environment_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储环境监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建环境数据记录
                env_data = EnvironmentData(
                    device_id=device_id,
                    location=device.location,
                    floor=device.floor,
                    temperature=metrics.get("temperature", 0),
                    humidity=metrics.get("humidity", 0),
                    smoke=metrics.get("smoke", False),
                    water=metrics.get("water", False),
                    status=device_info.status,
                    # 冷通道温度数据
                    cold_aisle1_temp1=metrics.get("cold_aisle1_temp1"),
                    cold_aisle1_temp2=metrics.get("cold_aisle1_temp2"),
                    cold_aisle2_temp1=metrics.get("cold_aisle2_temp1"),
                    cold_aisle2_temp2=metrics.get("cold_aisle2_temp2"),
                    # 冷通道湿度数据
                    cold_aisle1_humidity1=metrics.get("cold_aisle1_humidity1"),
                    cold_aisle1_humidity2=metrics.get("cold_aisle1_humidity2"),
                    cold_aisle2_humidity1=metrics.get("cold_aisle2_humidity1"),
                    cold_aisle2_humidity2=metrics.get("cold_aisle2_humidity2")
                )

                db.add(env_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储环境监控数据失败 {device_id}: {e}")

    async def _store_ups_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储UPS监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建UPS数据记录
                ups_data = UPSData(
                    device_id=device_id,
                    name=device.name,
                    location=device.location,
                    status=device_info.status,
                    load=metrics.get("load", 0),
                    battery_level=metrics.get("battery_level", 0),
                    battery_time_remaining=metrics.get("battery_time_remaining", 0),
                    input_voltage=metrics.get("input_voltage", 0),
                    output_voltage=metrics.get("output_voltage", 0),
                    input_frequency=metrics.get("input_frequency", 0),
                    output_frequency=metrics.get("output_frequency", 0),
                    temperature=metrics.get("temperature", 0),
                    battery_voltage=metrics.get("battery_voltage", 0)
                )

                db.add(ups_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储UPS监控数据失败 {device_id}: {e}")

    async def _store_mains_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储市电监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建市电数据记录
                mains_data = MainsPowerData(
                    device_id=device_id,
                    location=device.location,
                    status=device_info.status,
                    voltage=metrics.get("voltage", 0),
                    frequency=metrics.get("frequency", 0),
                    current=metrics.get("current", 0),
                    power=metrics.get("power", 0),
                    power_factor=metrics.get("power_factor", 0),
                    energy_consumption=metrics.get("energy_consumption", 0)
                )

                db.add(mains_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储市电监控数据失败 {device_id}: {e}")

    async def _store_network_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储网络设备监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 处理接口数据
                interfaces_data = metrics.get("interfaces", [])
                interfaces_count = len(interfaces_data)
                interfaces_up = sum(1 for iface in interfaces_data if iface.get("status") == "up")

                # 计算带宽
                bandwidth_in = sum(iface.get("bandwidth_in", 0) for iface in interfaces_data)
                bandwidth_out = sum(iface.get("bandwidth_out", 0) for iface in interfaces_data)

                # 创建网络设备数据记录
                from models.snmp_data import NetworkDeviceData
                network_data = NetworkDeviceData(
                    device_id=device_id,
                    name=device.name,
                    location=device.location,
                    status=device_info.status,
                    cpu_usage=metrics.get("cpu_usage", 0),
                    memory_usage=metrics.get("memory_usage", 0),
                    temperature=metrics.get("temperature"),
                    uptime=metrics.get("uptime"),
                    interfaces_count=interfaces_count,
                    interfaces_up=interfaces_up,
                    interfaces_data=json.dumps(interfaces_data) if interfaces_data else None,
                    bandwidth_in=bandwidth_in,
                    bandwidth_out=bandwidth_out
                )

                db.add(network_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储网络设备监控数据失败 {device_id}: {e}")

    async def _store_printer_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储打印机监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到设备配置: {device_id}")
                    return

                # 创建打印机数据记录
                from models.snmp_data import PrinterData
                printer_data = PrinterData(
                    device_id=device_id,
                    name=device.name,
                    location=device.location,
                    status=device_info.status,
                    model=metrics.get("model", "Unknown"),
                    serial_number=metrics.get("serial_number", "Unknown"),
                    toner_black=metrics.get("toner_black", 0),
                    toner_cyan=metrics.get("toner_cyan"),
                    toner_magenta=metrics.get("toner_magenta"),
                    toner_yellow=metrics.get("toner_yellow"),
                    pages_total=metrics.get("pages_total", 0),
                    pages_since_last=metrics.get("pages_since_last"),
                    error_state=metrics.get("error_state"),
                    warning_state=metrics.get("warning_state")
                )

                db.add(printer_data)
                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储打印机监控数据失败 {device_id}: {e}")

    async def _store_wireless_data(self, device_id: str, device_info: Any, metrics: Dict[str, Any]):
        """存储无线网络设备监控数据"""
        try:
            async for db in get_db():
                # 获取设备配置
                result = await db.execute(select(WirelessDevice).filter(WirelessDevice.device_id == device_id))
                device = result.scalars().first()

                if not device:
                    logger.warning(f"未找到无线设备配置: {device_id}")
                    return

                # 更新设备状态
                device.status = device_info.status
                device.last_seen = datetime.now()
                if "firmware_version" in metrics:
                    device.firmware_version = metrics["firmware_version"]
                if "uptime" in metrics:
                    device.uptime = metrics["uptime"]

                # 创建无线设备数据记录
                wireless_data = WirelessDeviceData(
                    device_id=device_id,
                    status=device_info.status,
                    cpu_usage=metrics.get("cpu_usage", 0),
                    memory_usage=metrics.get("memory_usage", 0),
                    temperature=metrics.get("temperature"),
                    clients_count=metrics.get("clients_count", 0),
                    clients_2g=metrics.get("clients_2g", 0),
                    clients_5g=metrics.get("clients_5g", 0),
                    channel_2g=metrics.get("channel_2g"),
                    channel_5g=metrics.get("channel_5g"),
                    signal_strength=metrics.get("signal_strength"),
                    noise_level=metrics.get("noise_level"),
                    tx_power=metrics.get("tx_power"),
                    rx_bytes=metrics.get("rx_bytes", 0),
                    tx_bytes=metrics.get("tx_bytes", 0),
                    rx_packets=metrics.get("rx_packets", 0),
                    tx_packets=metrics.get("tx_packets", 0),
                    rx_errors=metrics.get("rx_errors", 0),
                    tx_errors=metrics.get("tx_errors", 0)
                )

                db.add(wireless_data)

                # 处理客户端数据
                if "clients" in metrics and isinstance(metrics["clients"], list):
                    for client_data in metrics["clients"]:
                        # 检查客户端是否已存在
                        mac = client_data.get("mac_address")
                        if not mac:
                            continue

                        result = await db.execute(
                            select(WirelessClient).filter(
                                WirelessClient.device_id == device_id,
                                WirelessClient.mac_address == mac
                            )
                        )
                        client = result.scalars().first()

                        if client:
                            # 更新现有客户端
                            client.last_seen = datetime.now()
                            client.ip_address = client_data.get("ip_address", client.ip_address)
                            client.hostname = client_data.get("hostname", client.hostname)
                            client.signal_strength = client_data.get("signal_strength", client.signal_strength)
                            client.frequency_band = client_data.get("frequency_band", client.frequency_band)
                            client.tx_rate = client_data.get("tx_rate", client.tx_rate)
                            client.rx_rate = client_data.get("rx_rate", client.rx_rate)
                            client.tx_bytes = client_data.get("tx_bytes", client.tx_bytes)
                            client.rx_bytes = client_data.get("rx_bytes", client.rx_bytes)
                        else:
                            # 创建新客户端
                            new_client = WirelessClient(
                                device_id=device_id,
                                mac_address=mac,
                                ip_address=client_data.get("ip_address"),
                                hostname=client_data.get("hostname"),
                                signal_strength=client_data.get("signal_strength"),
                                frequency_band=client_data.get("frequency_band"),
                                tx_rate=client_data.get("tx_rate"),
                                rx_rate=client_data.get("rx_rate"),
                                tx_bytes=client_data.get("tx_bytes"),
                                rx_bytes=client_data.get("rx_bytes")
                            )
                            db.add(new_client)

                await db.commit()
                break

        except Exception as e:
            logger.error(f"存储无线网络设备监控数据失败 {device_id}: {e}")

    async def _log_collection(self, device_id: str, status: str, duration: float, 
                            values: Dict[str, Any], error_msg: str = ""):
        """记录采集日志到数据库"""
        try:
            async for db in get_db():
                from models.collection_log import CollectionLog
                log = CollectionLog(
                    device_id=device_id,
                    status=status,
                    duration=duration,
                    values=json.dumps(values),
                    error_message=error_msg
                )
                db.add(log)
                await db.commit()
        except Exception as e:
            logger.error(f"记录采集日志失败 {device_id}: {e}")

    async def _process_custom_snmp_items(self, device_id: str, metrics: Dict[str, Any]):
        """处理自定义SNMP监控项"""
        try:
            if device_id not in self.snmp_items or not self.snmp_items[device_id]:
                return

            async for db in get_db():
                # 获取设备的所有监控项
                items = self.snmp_items[device_id]

                # 处理每个监控项
                for item in items:
                    # 检查监控项的键是否在metrics中
                    if item.key in metrics:
                        value = metrics[item.key]

                        # 确定值类型
                        value_type = "string"
                        if isinstance(value, (int, float)):
                            value_type = "float" if isinstance(value, float) else "integer"
                            # 转换为字符串存储
                            value = str(value)
                        elif isinstance(value, bool):
                            value_type = "boolean"
                            value = str(value).lower()

                        # 创建SNMP数据记录
                        from models.snmp_data import SNMPData
                        snmp_data = SNMPData(
                            device_id=device_id,
                            item_id=item.id,
                            key=item.key,
                            value=value,
                            value_type=value_type,
                            unit=item.unit,
                            item_type=item.item_type,
                            location=item.location,
                            position=item.position
                        )

                        db.add(snmp_data)

                # 提交所有数据
                await db.commit()
                break

        except Exception as e:
            logger.error(f"处理自定义SNMP监控项失败 {device_id}: {e}")

# 全局服务实例
snmp_collection_service = None

def get_snmp_collection_service():
    """获取或创建SNMP采集服务实例"""
    global snmp_collection_service
    if snmp_collection_service is None:
        snmp_collection_service = SNMPCollectionService()
    return snmp_collection_service
