#!/usr/bin/env python3
"""
资产字段迁移脚本
添加安防设备、耗材相关字段和新的资产状态
"""

import psycopg2
import os
from pathlib import Path

def run_migration():
    """执行资产字段迁移"""
    try:
        # 数据库连接配置
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='user_PwWEyE',
            password='password_QM8NyB',
            database='asset_management'
        )
        
        # 设置自动提交
        conn.autocommit = True
        cur = conn.cursor()
        
        print("开始执行资产字段迁移...")
        
        # 读取迁移SQL文件
        migration_file = Path(__file__).parent / 'migrations' / 'add_new_asset_fields.sql'
        
        if not migration_file.exists():
            print(f"迁移文件不存在: {migration_file}")
            return False
            
        with open(migration_file, 'r', encoding='utf-8') as f:
            migration_sql = f.read()
        
        # 分割SQL语句并执行
        sql_statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
        
        for i, statement in enumerate(sql_statements, 1):
            if statement.upper().startswith(('ALTER', 'CREATE', 'COMMENT', 'DO')):
                try:
                    print(f"执行语句 {i}/{len(sql_statements)}: {statement[:50]}...")
                    cur.execute(statement)
                    print(f"✓ 语句 {i} 执行成功")
                except Exception as e:
                    print(f"✗ 语句 {i} 执行失败: {e}")
                    # 继续执行其他语句
                    continue
        
        # 验证新字段是否添加成功
        print("\n验证新字段...")
        
        # 检查安防设备字段
        security_fields = [
            'security_device_type', 'video_resolution', 'recording_capacity',
            'night_vision', 'motion_detection', 'audio_support', 'ptz_support',
            'weatherproof_rating', 'power_over_ethernet'
        ]
        
        # 检查耗材字段
        consumable_fields = [
            'is_consumable', 'quantity', 'unit', 'min_stock_level',
            'max_stock_level', 'reorder_point', 'expiry_date', 'batch_number'
        ]
        
        all_new_fields = security_fields + consumable_fields
        
        for field in all_new_fields:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'assets' AND column_name = %s
                );
            """, (field,))
            
            exists = cur.fetchone()[0]
            status = "✓" if exists else "✗"
            print(f"  {status} {field}: {'已添加' if exists else '未找到'}")
        
        # 检查资产状态枚举值
        print("\n检查资产状态枚举值...")
        cur.execute("""
            SELECT enumlabel FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'assetstatus')
            ORDER BY enumlabel;
        """)
        
        enum_values = [row[0] for row in cur.fetchall()]
        print(f"当前资产状态枚举值: {enum_values}")
        
        required_values = ['scrapped', 'stored']
        for value in required_values:
            if value in enum_values:
                print(f"  ✓ {value}: 已存在")
            else:
                print(f"  ✗ {value}: 未找到")
        
        print("\n迁移完成!")
        
        cur.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        return False

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("\n🎉 资产字段迁移成功完成!")
        print("\n新增功能:")
        print("1. 资产状态新增: 故障(faulty)、报废(scrapped)、存放(stored)")
        print("2. 安防设备字段: 网络配置、硬件规格等专用字段")
        print("3. 耗材管理字段: 数量、库存管理、过期日期等")
    else:
        print("\n❌ 迁移失败，请检查错误信息")
        exit(1)