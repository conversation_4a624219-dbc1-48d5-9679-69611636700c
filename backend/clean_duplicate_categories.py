import asyncio
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models.asset import AssetCategory
from sqlalchemy.future import select
from sqlalchemy import delete, text

async def clean_duplicate_categories():
    """清理重复的资产分类"""
    async for db in get_db():
        try:
            print("开始清理重复的资产分类...")
            
            # 直接使用SQL来处理重复分类
            # 首先查看重复的分类
            result = await db.execute(text("""
                SELECT name, array_agg(id ORDER BY id) as ids, array_agg(code ORDER BY id) as codes
                FROM asset_categories 
                GROUP BY name 
                HAVING count(*) > 1
            """))
            duplicates = result.fetchall()
            
            if not duplicates:
                print("未发现重复的分类")
                return
            
            print(f"发现 {len(duplicates)} 组重复分类:")
            for dup in duplicates:
                print(f"  {dup.name}: IDs {dup.ids}, Codes {dup.codes}")
            
            # 对每组重复分类进行处理
            for dup in duplicates:
                name = dup.name
                ids = dup.ids
                codes = dup.codes
                
                # 保留第一个ID，删除其他的
                keep_id = ids[0]
                delete_ids = ids[1:]
                
                print(f"\n处理重复分类: {name}")
                print(f"  保留 ID: {keep_id} (Code: {codes[0]})")
                print(f"  删除 IDs: {delete_ids} (Codes: {codes[1:]})")
                
                for delete_id in delete_ids:
                    # 更新子分类的父分类引用
                    await db.execute(text("""
                        UPDATE asset_categories 
                        SET parent_id = :keep_id 
                        WHERE parent_id = :delete_id
                    """), {"keep_id": keep_id, "delete_id": delete_id})
                    
                    # 更新资产的分类引用
                    await db.execute(text("""
                        UPDATE assets 
                        SET category_id = :keep_id 
                        WHERE category_id = :delete_id
                    """), {"keep_id": keep_id, "delete_id": delete_id})
                    
                    print(f"    已更新引用 ID {delete_id} -> {keep_id}")
                
                # 删除重复的分类
                for delete_id in delete_ids:
                    await db.execute(text("""
                        DELETE FROM asset_categories WHERE id = :delete_id
                    """), {"delete_id": delete_id})
                    print(f"    已删除分类 ID: {delete_id}")
            
            # 提交所有更改
            await db.commit()
            print("\n✓ 重复分类清理完成")
            
            # 验证结果
            result = await db.execute(text("""
                SELECT name, count(*) as cnt
                FROM asset_categories 
                GROUP BY name 
                HAVING count(*) > 1
            """))
            remaining_duplicates = result.fetchall()
            
            if remaining_duplicates:
                print("警告: 仍有重复分类:")
                for dup in remaining_duplicates:
                    print(f"  {dup.name}: {dup.cnt} 个")
            else:
                print("✓ 确认无重复分类")
            
            # 显示最终分类统计
            result = await db.execute(text("SELECT count(*) FROM asset_categories"))
            total_count = result.scalar()
            print(f"\n数据库中共有 {total_count} 个资产分类")
            
        except Exception as e:
            await db.rollback()
            print(f"清理失败: {str(e)}")
            import traceback
            traceback.print_exc()
        break

if __name__ == "__main__":
    asyncio.run(clean_duplicate_categories())