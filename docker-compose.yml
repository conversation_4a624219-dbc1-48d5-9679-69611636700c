version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: rs_asset_db
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-rs_asset}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: rs_asset_redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: rs_asset_backend
    environment:
      - DATABASE_URL=********************************************/rs_asset
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - ./backend:/app
    working_dir: /app

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: rs_asset_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "443:443"
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped
    volumes:
      - ./frontend:/app
      - ./frontend/ssl:/app/ssl
    working_dir: /app

volumes:
  postgres_data: